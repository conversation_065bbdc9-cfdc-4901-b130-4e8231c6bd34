/**
 * 
 */
package org.changneng.framework.frameworkapi.service;

import java.util.List;
import java.util.TreeMap;

import org.changneng.framework.frameworkbusiness.entity.LawCircle;
import org.changneng.framework.frameworkbusiness.entity.LawCircleTimeline;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkcore.utils.PageBean;

/** 执法圈service接口
 * <AUTHOR>
 * @date 2018年6月26日 下午5:06:22
 */
public interface LawCircleService {
	/**
	 * 查询所有可见执法圈信息
	 * @param pageNumber
	 * @param pageSize
	 * @param searchField
	 * @return
	 * <AUTHOR>
	 */
	PageBean<LawCircle> selectOthersSignIn(Integer pageNumber, Integer pageSize, String searchField);
	/**
	 * 查询本人的执法圈信息
	 * @param creatUserId
	 * @param pageNumber
	 * @param pageSize
	 * @param searchField
	 * @return
	 * <AUTHOR>
	 */
	PageBean<LawCircleTimeline> selectMySignIn(String creatUserId, Integer pageNumber, Integer pageSize);
	/**
	 * 根据id删除指定执法圈信息
	 * @param id
	 * <AUTHOR>
	 */
	void deleteMySignIn(String id);
	/**
	 * 查询指定人员的执法圈信息
	 * @param pageNumber 
	 * @param pageSize 
	 * @param createUserId
	 * @param searchField 
	 * @return
	 * <AUTHOR>
	
	 */
	PageBean<LawCircleTimeline> selectOtherVisibleSignIn(Integer pageNumber, Integer pageSize, String creatUserId);
	/**
	 * 发布执法圈
	 * @param lawCircle
	 * @param fileIds
	 * @param sysUsers
	 * <AUTHOR>
	 */
	void saveLawCircleInfos(LawCircle lawCircle, String fileIds, SysUsers sysUsers);

}
