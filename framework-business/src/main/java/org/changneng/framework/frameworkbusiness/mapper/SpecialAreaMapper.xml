<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SpecialAreaMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SpecialArea">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SCENE_SYSSPECAIL_ID" jdbcType="VARCHAR" property="sceneSysspecailId" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="AREA_NAME" jdbcType="VARCHAR" property="areaName" />
    <result column="TEMPLATE_TYPE" jdbcType="VARCHAR" property="templateType" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SCENE_SYSSPECAIL_ID, CREATE_TIME, AREA_CODE, AREA_NAME, TEMPLATE_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from SPECIAL_AREA
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SPECIAL_AREA
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  
  <delete id="deleteBySceneSysSpecialId" parameterType="java.lang.String">
    delete from SPECIAL_AREA
    where SCENE_SYSSPECAIL_ID = #{sceneSysSpecialId,jdbcType=VARCHAR}
  </delete>
  
  <!-- 批量插入 专项区划中间表 -->
   <insert id="batchInsertQuesList" parameterType="java.util.List">
  	insert into SPECIAL_AREA ( ID, SCENE_SYSSPECAIL_ID, CREATE_TIME, AREA_CODE, AREA_NAME, TEMPLATE_TYPE)
  	<foreach collection="dataList" item="data" index="index" separator ="UNION ALL">
  	select 
     sys_guid(), #{data.sceneSysspecailId,jdbcType=VARCHAR}, #{data.createTime, jdbcType=TIMESTAMP},
      #{data.areaCode,jdbcType=VARCHAR}, #{data.areaName,jdbcType=VARCHAR}, #{data.templateType,jdbcType=VARCHAR} from dual
  	</foreach>
  </insert>
  
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SpecialArea">
    insert into SPECIAL_AREA (ID, SCENE_SYSSPECAIL_ID, CREATE_TIME, 
      AREA_CODE, AREA_NAME, TEMPLATE_TYPE
      )
    values (#{id,jdbcType=VARCHAR}, #{sceneSysspecailId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{areaCode,jdbcType=VARCHAR}, #{areaName,jdbcType=VARCHAR}, #{templateType,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SpecialArea">
    insert into SPECIAL_AREA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="sceneSysspecailId != null">
        SCENE_SYSSPECAIL_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="areaName != null">
        AREA_NAME,
      </if>
      <if test="templateType != null">
        TEMPLATE_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sceneSysspecailId != null">
        #{sceneSysspecailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        #{templateType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SpecialArea">
    update SPECIAL_AREA
    <set>
      <if test="sceneSysspecailId != null">
        SCENE_SYSSPECAIL_ID = #{sceneSysspecailId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="areaCode != null">
        AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="areaName != null">
        AREA_NAME = #{areaName,jdbcType=VARCHAR},
      </if>
      <if test="templateType != null">
        TEMPLATE_TYPE = #{templateType,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.SpecialArea">
    update SPECIAL_AREA
    set SCENE_SYSSPECAIL_ID = #{sceneSysspecailId,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      AREA_CODE = #{areaCode,jdbcType=VARCHAR},
      AREA_NAME = #{areaName,jdbcType=VARCHAR},
      TEMPLATE_TYPE = #{templateType,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>