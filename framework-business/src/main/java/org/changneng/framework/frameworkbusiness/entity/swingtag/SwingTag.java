package org.changneng.framework.frameworkbusiness.entity.swingtag;

import java.util.Date;
import java.util.List;

import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

public class SwingTag {
    private String id;

	@NotEmpty(message = "挂牌通知文书名称不能为空")
    private String swingTagName;

	@NotEmpty(message = "挂牌通知文号不能为空")
    private String swingTagMark;

    private String releaseUnit;

    private String releaseUnitId;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+08:00")
    private Date releaseDate;

    private Integer releaseStatus;

    private Integer delmark;

    private Date updatetime;

    private Date createtime;

    private String lastupdateman;

    private Integer isSwingTag;

    private String creatUserName;

    private String creatUserId;

    private String unitAreaCode;

    private String unitAreaName;

    private List<SwingTagSnapIn> swingTagSnapInList;//管理部门集合
    
    private List<SwingTagAdjunct> swingTagAdjunctUpList;//挂牌附件集合；
    
    private List<SwingTagAdjunct> swingTagAdjunctDownList; //解牌附件集合

    private String SnapInList; //用于接收页面的集合信息字符串类型
    private String AdjunctList; //用于接收页面的集合信息字符串类型
    
    private String sendAttId;//存放附件的组合id
    
    private String releaseStatusName;//发起状态 转换名称  用于xls下载
    private String releaseDateTr;//发起日期 转换名称
    
    
    public String getReleaseDateTr() {
		return releaseDateTr;
	}

	public void setReleaseDateTr(String releaseDateTr) {
		this.releaseDateTr = releaseDateTr;
	}

	public String getSendAttId() {
		return sendAttId;
	}

	public void setSendAttId(String sendAttId) {
		this.sendAttId = sendAttId;
	}

	public String getReleaseStatusName() {
		return releaseStatusName;
	}

	public void setReleaseStatusName(String releaseStatusName) {
		this.releaseStatusName = releaseStatusName;
	}

	public String getSnapInList() {
		return SnapInList;
	}

	public void setSnapInList(String snapInList) {
		SnapInList = snapInList;
	}

	public String getAdjunctList() {
		return AdjunctList;
	}

	public void setAdjunctList(String adjunctList) {
		AdjunctList = adjunctList;
	}

	public List<SwingTagSnapIn> getSwingTagSnapInList() {
		return swingTagSnapInList;
	}

	public void setSwingTagSnapInList(List<SwingTagSnapIn> swingTagSnapInList) {
		this.swingTagSnapInList = swingTagSnapInList;
	}

	public List<SwingTagAdjunct> getSwingTagAdjunctUpList() {
		return swingTagAdjunctUpList;
	}

	public void setSwingTagAdjunctUpList(List<SwingTagAdjunct> swingTagAdjunctUpList) {
		this.swingTagAdjunctUpList = swingTagAdjunctUpList;
	}

	public List<SwingTagAdjunct> getSwingTagAdjunctDownList() {
		return swingTagAdjunctDownList;
	}

	public void setSwingTagAdjunctDownList(
			List<SwingTagAdjunct> swingTagAdjunctDownList) {
		this.swingTagAdjunctDownList = swingTagAdjunctDownList;
	}

	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getSwingTagName() {
        return swingTagName;
    }

    public void setSwingTagName(String swingTagName) {
        this.swingTagName = swingTagName == null ? null : swingTagName.trim();
    }

    public String getSwingTagMark() {
        return swingTagMark;
    }

    public void setSwingTagMark(String swingTagMark) {
        this.swingTagMark = swingTagMark == null ? null : swingTagMark.trim();
    }

    public String getReleaseUnit() {
        return releaseUnit;
    }

    public void setReleaseUnit(String releaseUnit) {
        this.releaseUnit = releaseUnit == null ? null : releaseUnit.trim();
    }

    public String getReleaseUnitId() {
        return releaseUnitId;
    }

    public void setReleaseUnitId(String releaseUnitId) {
        this.releaseUnitId = releaseUnitId == null ? null : releaseUnitId.trim();
    }

    public Date getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    public Integer getReleaseStatus() {
        return releaseStatus;
    }

    public void setReleaseStatus(Integer releaseStatus) {
        this.releaseStatus = releaseStatus;
    }

    public Integer getDelmark() {
        return delmark;
    }

    public void setDelmark(Integer delmark) {
        this.delmark = delmark;
    }

    public Date getUpdatetime() {
        return updatetime;
    }

    public void setUpdatetime(Date updatetime) {
        this.updatetime = updatetime;
    }

    public Date getCreatetime() {
        return createtime;
    }

    public void setCreatetime(Date createtime) {
        this.createtime = createtime;
    }

    public String getLastupdateman() {
        return lastupdateman;
    }

    public void setLastupdateman(String lastupdateman) {
        this.lastupdateman = lastupdateman == null ? null : lastupdateman.trim();
    }

    public Integer getIsSwingTag() {
        return isSwingTag;
    }

    public void setIsSwingTag(Integer isSwingTag) {
        this.isSwingTag = isSwingTag;
    }

    public String getCreatUserName() {
        return creatUserName;
    }

    public void setCreatUserName(String creatUserName) {
        this.creatUserName = creatUserName == null ? null : creatUserName.trim();
    }

    public String getCreatUserId() {
        return creatUserId;
    }

    public void setCreatUserId(String creatUserId) {
        this.creatUserId = creatUserId == null ? null : creatUserId.trim();
    }

    public String getUnitAreaCode() {
        return unitAreaCode;
    }

    public void setUnitAreaCode(String unitAreaCode) {
        this.unitAreaCode = unitAreaCode == null ? null : unitAreaCode.trim();
    }

    public String getUnitAreaName() {
        return unitAreaName;
    }

    public void setUnitAreaName(String unitAreaName) {
        this.unitAreaName = unitAreaName == null ? null : unitAreaName.trim();
    }
}