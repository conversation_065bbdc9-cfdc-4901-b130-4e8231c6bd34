package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
  * 辅助 附件初始化类，只用一次 
  * <p>Title:FileCaseInitUtil </p>
  * <p>Description: </p>
  * <p>Company: </p> 
  * <AUTHOR>
  * @date 2018年9月13日-上午10:54:47
 */
public class FileCaseInitUtil {

	
	/**
	 * 默认规则抽取的条目code <老code，新code>
	 */
	public Map<String,String> defaultRuleSet =  new HashMap<>();
	/**
	 * 按日计罚指定的初始化规则code
	 */
	public Map<String,String> penaltyRuleMap = new HashMap<>();
	/**
	 * 按日计罚具体的code对应的名称
	 */
	public Map<String,String> penaltyRuleNameMap = new HashMap<>();
	
	
	/**
	 * 查封扣押
	 */
	public Map<String,String> detainRuleMap = new HashMap<>();
	
	/**
	 * 行政拘留
	 */
	public Map<String,String> detentionRuleMap = new HashMap<>();
	
	/**
	 * 环境污染犯罪
	 */
	public Map<String,String> pollutionRuleMap = new HashMap<>();
	
	/**
	 * 限产停产
	 */
	public Map<String,String> limtStopRuleMap = new HashMap<>();
	
	/**
	 * 其他移送
	 */
	public Map<String,String> otherRuleMap = new HashMap<>();
	
	/**
	 * 申请法院强制执行
	 */
	public Map<String,String> applyRuleMap = new HashMap<>();
	
	/**
	 * 最新的Code对应的名称
	 */
	public Map<String,String> newCodeMap = new HashMap<>();
	
	/**
	 * 无参构造函数
	 */
	public FileCaseInitUtil(){
		defaultRuleSet.put("-3","-3");
		defaultRuleSet.put("-2","-2");
		defaultRuleSet.put("86","86");
		defaultRuleSet.put("-1","-1");
		defaultRuleSet.put("0","0");
		defaultRuleSet.put("61","61");
		defaultRuleSet.put("91","91");
		defaultRuleSet.put("92","92");
		defaultRuleSet.put("93","93");
		defaultRuleSet.put("58","58");
		defaultRuleSet.put("94","94");
		defaultRuleSet.put("95","95");
		defaultRuleSet.put("96","96");
		defaultRuleSet.put("97","97");
		defaultRuleSet.put("99","99");
		defaultRuleSet.put("59","59");
		defaultRuleSet.put("101","101");
		defaultRuleSet.put("100","100");
		defaultRuleSet.put("102","102");
		defaultRuleSet.put("103","103");
		defaultRuleSet.put("104","104");
		defaultRuleSet.put("60","60");
		
		defaultRuleSet.put("6","6");
		defaultRuleSet.put("57","6");
		defaultRuleSet.put("7","7");
		defaultRuleSet.put("77","7");
		defaultRuleSet.put("64","64");
		defaultRuleSet.put("75","64");
		defaultRuleSet.put("76","10");
		defaultRuleSet.put("10","10");
		
		
		defaultRuleSet.put("12","378,384,386");
		defaultRuleSet.put("9","385,387");
		defaultRuleSet.put("1","1");
		defaultRuleSet.put("2","2");
		defaultRuleSet.put("8","8");
		defaultRuleSet.put("11","11");
		defaultRuleSet.put("74","74");
		defaultRuleSet.put("17","17");
		defaultRuleSet.put("18","18");
	
		defaultRuleSet.put("63","63");
		defaultRuleSet.put("13","13");
		defaultRuleSet.put("14","14");
		defaultRuleSet.put("15","15");
		defaultRuleSet.put("16","16");
		defaultRuleSet.put("3","3");
		defaultRuleSet.put("4","4");
		/****按日计罚****/
		penaltyRuleMap.put("51","51,380");
		penaltyRuleMap.put("47","47");
		penaltyRuleMap.put("48","48");
		penaltyRuleMap.put("66","66");
		penaltyRuleMap.put("50","50");
		penaltyRuleMap.put("49","49");
		penaltyRuleMap.put("65","65");
		penaltyRuleMap.put("52","52");
		penaltyRuleMap.put("53","53");
		penaltyRuleMap.put("54","54");
		penaltyRuleMap.put("46","54");
		penaltyRuleMap.put("55","55");
 
		
		/*************按日计罚私有的新增的时候我需要知道名称********/		
		penaltyRuleNameMap.put("51", "按日计罚-案件集体审议记录（告知前）");
		penaltyRuleNameMap.put("380","按日计罚-案件集体审议记录（处罚前）");
		penaltyRuleNameMap.put("379","按日计罚-案件处理内部审批表（告知前）");
		penaltyRuleNameMap.put("383","按日计罚-案件处理内部审批表（处罚前）");
		penaltyRuleNameMap.put("47","按日连续处罚决定书");
		penaltyRuleNameMap.put("48","按日连续处罚决定书送达回证");
		penaltyRuleNameMap.put("66","按日计罚-行政处罚事先告知书");
		penaltyRuleNameMap.put("50","按日计罚-行政处罚事先告知书送达回证");
		penaltyRuleNameMap.put("45","按日计罚-再次责令改正违法行为决定书及送达回证");		
		penaltyRuleNameMap.put("49","按日计罚听证告知书");
		penaltyRuleNameMap.put("65","按日计罚听证告知书送达回证");
		penaltyRuleNameMap.put("52","按日计罚听证笔录");	
		penaltyRuleNameMap.put("53","按日计罚陈述申辩书");		
		
		/****查封扣押******/
		detainRuleMap.put("19","19");
		detainRuleMap.put("71","71");
		detainRuleMap.put("73","73");
		detainRuleMap.put("21","21");
		detainRuleMap.put("69","69");
		detainRuleMap.put("23","23");
		detainRuleMap.put("72","23");
		detainRuleMap.put("24","24");
		detainRuleMap.put("20","20");
		detainRuleMap.put("22","20");
		 
		/****行政拘留*****/
		detentionRuleMap.put("32","32");
		detentionRuleMap.put("30","30");
		detentionRuleMap.put("67","67");
		detentionRuleMap.put("31","31");
		detentionRuleMap.put("33","33");
		/***环境污染犯罪*****/
		pollutionRuleMap.put("36","36");
		pollutionRuleMap.put("34","34");
		pollutionRuleMap.put("68","68");
		pollutionRuleMap.put("35","35");
		pollutionRuleMap.put("37","37");
		/****限产停产*****/
		limtStopRuleMap.put("26","389,391");
		limtStopRuleMap.put("25","389,391");
		limtStopRuleMap.put("27","27,392");
		/*****其他移送*****/
		otherRuleMap.put("43","43");
		otherRuleMap.put("44","44");
		/*****申请法院强制执行*/
		applyRuleMap.put("38","38");
		applyRuleMap.put("39","39");
		applyRuleMap.put("40","40");
		applyRuleMap.put("41","41");
		applyRuleMap.put("70","70");			
		applyRuleMap.put("42","42");
		/*****心在来源是任务的code*****/
		newCodeMap.put("-3","现场检查表");
		newCodeMap.put("-2","勘察笔录");
		newCodeMap.put("-1","调查询问笔录");
		newCodeMap.put("0","现场取证照片");
		newCodeMap.put("1","简易程序处罚-当场行政处罚决定书");
		newCodeMap.put("2","简易程序处罚-送达回证");
		newCodeMap.put("3","备案-案件备案登记表");
		newCodeMap.put("4","行政处罚-立案审批表");
		newCodeMap.put("6","销案审批表");
		newCodeMap.put("7","行政处罚决定书");
		newCodeMap.put("8","行政处罚决定书送达回证");
		newCodeMap.put("10","行政处罚听证告知书");
		newCodeMap.put("11","行政处罚-行政处罚事先告知书送达回证");
		newCodeMap.put("13","行政处罚听证通知书");
		newCodeMap.put("14","行政处罚听证笔录");
		newCodeMap.put("15","行政处罚陈述申辩书");
		newCodeMap.put("16","行政处罚-结案报告(含审批表）");
		newCodeMap.put("17","责令改正违法行为决定书");
		newCodeMap.put("18","责令改正违法行为决定书送达回证");
		newCodeMap.put("19","查封扣押-查封扣押审批表");
		newCodeMap.put("20","查封扣押决定书");
		newCodeMap.put("21","查封扣押决定书送达回证");
		newCodeMap.put("23","解除查封扣押决定书");
		newCodeMap.put("24","解除查封扣押决定书送达回证");
		newCodeMap.put("27","责令限制生产决定书送达回证");
		newCodeMap.put("28","解除停产整治决定书");
		newCodeMap.put("29","解除停产整治送达回证");
		newCodeMap.put("30","涉嫌环境违法适用行政拘留处罚案件移送书");
		newCodeMap.put("31","移送涉嫌环境违法适用行政拘留处罚案件审批表");
		newCodeMap.put("32","行政拘留-现场取证说明");
		newCodeMap.put("33","行政拘留-受案回执");
		newCodeMap.put("34","涉嫌环境犯罪案件移送书");
		newCodeMap.put("35","移送涉嫌环境犯罪案件审批表");
		newCodeMap.put("36","环境污染犯罪-现场取证说明");
		newCodeMap.put("37","环境污染犯罪-受案回执");
		newCodeMap.put("38","申请法院强制执行-督促履行义务催告书");
		newCodeMap.put("39","申请法院强制执行-督促履行义务催告书送达回证");
		newCodeMap.put("40","申请法院强制执行-行政处罚强制执行申请书");
		newCodeMap.put("41","申请法院强制执行-行政处罚强制执行申请书送达回执");
		newCodeMap.put("42","申请法院强制执行-法院裁定书");
		newCodeMap.put("43","移送书");
		newCodeMap.put("44","移送审批表");
		newCodeMap.put("45","按日计罚-再次责令改正违法行为决定书及送达回证");
		newCodeMap.put("47","按日连续处罚决定书");
		newCodeMap.put("48","按日连续处罚决定书送达回证");
		newCodeMap.put("49","按日计罚听证告知书");
		newCodeMap.put("50","按日计罚-行政处罚事先告知书送达回证");
		newCodeMap.put("51","按日计罚-案件集体审议记录（告知前）");
		newCodeMap.put("52","按日计罚听证笔录");
		newCodeMap.put("53","按日计罚陈述申辩书");
		newCodeMap.put("54","按日计罚-立案审批表");
		newCodeMap.put("55","按日计罚-结案报告（含审批表）");
		newCodeMap.put("58","书证-环评及验收相关材料");
		newCodeMap.put("59","监测报告和鉴定结论-监测报告");
		newCodeMap.put("60","行政处罚案件调查报告");
		newCodeMap.put("61","书证-授权委托书");
		newCodeMap.put("63","行政处罚听证告知书送达回证");
		newCodeMap.put("64","行政处罚-行政处罚事先告知书");
		newCodeMap.put("65","按日计罚听证告知书送达回证");
		newCodeMap.put("66","按日计罚-行政处罚事先告知书");
		newCodeMap.put("67","涉嫌环境违法适用行政拘留处罚案件移送材料清单");
		newCodeMap.put("68","涉嫌环境犯罪案件移送材料清单");
		newCodeMap.put("69","查封扣押延期决定书");
		newCodeMap.put("70","申请法院强制执行-申请强制执行授权委托书");
		newCodeMap.put("71","查封扣押-查封扣押延期审批表");
		newCodeMap.put("73","查封扣押-解除查封扣押审批表");
		newCodeMap.put("74","责令改正违法行为决定书审批表");
		newCodeMap.put("86","勘察笔录附图");
		newCodeMap.put("90","书证-机构统一社会信用代码证");
		newCodeMap.put("91","书证-机构法人代表身份证");
		newCodeMap.put("92","书证-机构被授权人身份证");
		newCodeMap.put("93","书证-执法人员执法证复印件");
		newCodeMap.put("94","书证-其它书证");
		newCodeMap.put("95","物证-先行登记保存证据通知书");
		newCodeMap.put("96","物证-解除先行登记保存证据通知书");
		newCodeMap.put("97","视听资料和自动监控数据-视听资料信息登记表");
		newCodeMap.put("98","视听资料和自动监控数据-视听资料信息登记表1");
		newCodeMap.put("99","监测报告和鉴定结论-采样取证登记单");
		newCodeMap.put("100","监测报告和鉴定结论-检测报告");
		newCodeMap.put("101","监测报告和鉴定结论-监测被委托方资质证明文件");
		newCodeMap.put("102","监测报告和鉴定结论-检测被委托方资质证明文件");
		newCodeMap.put("103","监测报告和鉴定结论-鉴定报告");
		newCodeMap.put("104","监测报告和鉴定结论-鉴定被委托方资质证明文件");
		newCodeMap.put("308","视听资料和自动监控数据-自动监控数据");
		newCodeMap.put("317","书证-《建设项目环境影响评价分类管理名录》截录");
		newCodeMap.put("318","监测报告和鉴定结论-监测报告送达回证");
		newCodeMap.put("319","查封扣押笔录");
		newCodeMap.put("320","书证-污染物排放标准");
		newCodeMap.put("321","法律条款截录");
		newCodeMap.put("323","查封扣押延期决定书送达回证");
		newCodeMap.put("324","责令限制生产事先告知书");
		newCodeMap.put("325","责令限制生产事先告知书送达回证");
		newCodeMap.put("326","责令停产整治事先告知书");
		newCodeMap.put("327","责令停产整治事先告知书送达回证");
		newCodeMap.put("328","移送材料清单");
		newCodeMap.put("329","行政拘留-行政拘留结果反馈材料");
		newCodeMap.put("330","行政拘留-立案告知书");
		newCodeMap.put("331","环境污染犯罪-立案告知书");
		newCodeMap.put("332","环境污染犯罪-公安和检察院其它反馈材料");
		newCodeMap.put("333","其他移送-受案回执");
		newCodeMap.put("334","行政处罚听证通知书送达回证");
		newCodeMap.put("335","行政处罚听证报告");
		newCodeMap.put("336","按日计罚听证通知书");
		newCodeMap.put("337","按日计罚听证通知书送达回证");
		newCodeMap.put("341","按日计罚听证报告");
		newCodeMap.put("342","责令限制生产事先听证告知书");
		newCodeMap.put("343","责令限制生产事先听证告知书送达回证");
		newCodeMap.put("344","责令限制生产听证通知书");
		newCodeMap.put("345","责令限制生产听证通知书送达回证");
		newCodeMap.put("346","责令限制生产听证笔录");
		newCodeMap.put("347","责令限制生产听证报告");
		newCodeMap.put("348","责令限制生产陈述申辩书");
		newCodeMap.put("349","责令停产整治事先听证告知书");
		newCodeMap.put("350","责令停产整治事先听证告知书送达回证");
		newCodeMap.put("351","责令停产整治听证通知书");
		newCodeMap.put("352","责令停产整治听证通知书送达回证");
		newCodeMap.put("353","责令停产整治听证笔录");
		newCodeMap.put("354","责令停产整治听证报告");
		newCodeMap.put("355","责令停产整治陈述申辩书");
		newCodeMap.put("356","行政处罚-同意分期（延期）缴纳罚款通知书");
		newCodeMap.put("357","行政处罚-同意分期（延期）缴纳罚款通知书送达回证");
		newCodeMap.put("358","按日计罚-同意分期（延期）缴纳罚款通知书");
		newCodeMap.put("359","按日计罚-同意分期（延期）缴纳罚款通知书送达回证");
		newCodeMap.put("360","行政处罚-环境行政执法后督察现场检查记录");
		newCodeMap.put("361","行政处罚-环境行政执法后督察报告");
		newCodeMap.put("362","行政处罚-整改材料");
		newCodeMap.put("363","行政处罚-其它后督查材料");
		newCodeMap.put("365","信息公开证据材料");
		newCodeMap.put("366","按日计罚-复议申请书");
		newCodeMap.put("367","按日计罚-复议裁决");
		newCodeMap.put("368","行政处罚-复议申请书");
		newCodeMap.put("369","行政处罚-复议裁决");
		newCodeMap.put("370","按日计罚-法院受理通知书（附起诉状）");
		newCodeMap.put("371","按日计罚-环保局应诉答辩状");
		newCodeMap.put("372","按日计罚-法院判决（裁定）书");
		newCodeMap.put("373","行政处罚-法院受理通知书");
		newCodeMap.put("374","行政处罚-环保局应诉答辩状");
		newCodeMap.put("375","行政处罚-法院判决（裁定）书");
		newCodeMap.put("376","限制生产-跟踪检查记录");
		newCodeMap.put("378","查封扣押-案件集体审议记录");
		newCodeMap.put("379","按日计罚-案件处理内部审批表（告知前）");
		newCodeMap.put("380","按日计罚-案件集体审议记录（处罚前）");
		newCodeMap.put("383","按日计罚-案件处理内部审批表（处罚前）");
		newCodeMap.put("384","行政处罚-案件集体审议记录（告知前）");
		newCodeMap.put("385","行政处罚-案件处理内部审批表（告知前）");
		newCodeMap.put("386","行政处罚-案件集体审议记录（处罚前）");
		newCodeMap.put("387","行政处罚-案件处理内部审批表（处罚前）");
		newCodeMap.put("389","责令限制生产决定书");
		newCodeMap.put("391","责令停产整治决定书");
		newCodeMap.put("392","责令停产整治决定书送达回证");
		newCodeMap.put("405","查封扣押-申请解封材料");
		newCodeMap.put("406","责令限制生产集体讨论记录（告知前）");
		newCodeMap.put("407","责令限制生产内部审批表（告知前）");
		newCodeMap.put("408","责令限制生产集体讨论记录（决定前）");
		newCodeMap.put("409","责令限制生产内部审批表（决定前）");
		newCodeMap.put("410","限制生产-企业整改方案");
		newCodeMap.put("411","限制生产-后督察材料");
		newCodeMap.put("412","限制生产-企业整改完成情况、信息公开和备案材料");
		newCodeMap.put("413","按日计罚-首次责令改正违法行为决定书及送达回证");
		newCodeMap.put("414","按日计罚-勘察笔录");
		newCodeMap.put("415","按日计罚-询问笔录");
		newCodeMap.put("416","查封扣押-查封后的设施、设备封存情况记录");
		newCodeMap.put("417","按日计罚-调查报告");
		newCodeMap.put("418","按日计罚-环境行政执法后督察现场检查记录");
		newCodeMap.put("419","按日计罚-环境行政执法后督察报告");
		newCodeMap.put("420","按日计罚-整改材料");
		newCodeMap.put("421","按日计罚-其它后督查材料");
		newCodeMap.put("422","责令停产整治集体讨论记录（告知前）");
		newCodeMap.put("423","责令停产整治内部审批表（告知前）");
		newCodeMap.put("424","责令停产整治集体讨论记录（决定前）");
		newCodeMap.put("425","责令停产整治内部审批表（决定前）");
		newCodeMap.put("426","停产整治-企业整改方案");
		newCodeMap.put("427","停产整治-后督察材料");
		newCodeMap.put("428","停产整治-企业整改完成情况、信息公开和备案材料");
		newCodeMap.put("429","停产整治-跟踪检查记录");
		newCodeMap.put("430","按日计罚-监测报告");
		newCodeMap.put("56","公共-其它");
	}
	
	
}
