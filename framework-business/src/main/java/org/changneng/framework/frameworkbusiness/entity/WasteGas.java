package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonProperty;

public class WasteGas extends WasteGasKey {
	@JsonProperty("CHKYEAR")
    private String chkyear;
	@JsonProperty("STNAME")
    private String stname;
	@JsonProperty("STNAMES")
    private String stnames;
	@JsonProperty("ITEMNAME")
    private String itemname;
	@JsonProperty("CALVALUE")
    private String checkvalue;
	@JsonProperty("LIMITUNIT")
    private String limitunit;
	@JsonProperty("STLIMIT")
    private String stlimit;
	@JsonProperty("ISOVER")
    private String isover;
	@JsonProperty("OVERS")
    private String overs;
	@JsonProperty("SHSTATE")
    private String shstate;

    private Date createTime;

    private Date updateTime;

    public String getChkyear() {
        return chkyear;
    }

    public void setChkyear(String chkyear) {
        this.chkyear = chkyear;
    }

    public String getStname() {
        return stname;
    }

    public void setStname(String stname) {
        this.stname = stname == null ? null : stname.trim();
    }

    public String getStnames() {
        return stnames;
    }

    public void setStnames(String stnames) {
        this.stnames = stnames == null ? null : stnames.trim();
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname == null ? null : itemname.trim();
    }

    public String getCheckvalue() {
        return checkvalue;
    }

    public void setCheckvalue(String checkvalue) {
        this.checkvalue = checkvalue;
    }

    public String getLimitunit() {
        return limitunit;
    }

    public void setLimitunit(String limitunit) {
        this.limitunit = limitunit == null ? null : limitunit.trim();
    }

    public String getStlimit() {
        return stlimit;
    }

    public void setStlimit(String stlimit) {
        this.stlimit = stlimit;
    }

    public String getIsover() {
        return isover;
    }

    public void setIsover(String isover) {
        this.isover = isover == null ? null : isover.trim();
    }

    public String getOvers() {
        return overs;
    }

    public void setOvers(String overs) {
       if(overs!=null){
    	   String reg = "^[0-9]+(.[0-9]+)?$";  
    	   if(!overs.matches(reg)){
    		  this.overs="0"+overs;
    	   }else{
    		  this.overs = overs;
    	   }
       }else{
    	   this.overs = overs;
       }
    }

    public String getShstate() {
        return shstate;
    }

    public void setShstate(String shstate) {
        this.shstate = shstate == null ? null : shstate.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}