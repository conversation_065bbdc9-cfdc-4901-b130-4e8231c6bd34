package org.changneng.framework.frameworkbusiness.service.impl;

import org.changneng.framework.frameworkbusiness.dao.SceneSysSpecialModelMapper;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.service.SpecialTaskService;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
@Service
public class SpecialTaskServiceImpl implements SpecialTaskService {

	@Autowired
	private SceneSysSpecialModelMapper  sceneSysSpecialModelMapper;
	@Override
	public PageBean<SceneSysSpecialModel> specialTaskList(SceneSysSpecialModel specialTask ,Integer pageNum,Integer pageSize,SysUsers sysUser) {
		if(ChangnengUtil.isNull(pageNum)){
			pageNum = 1;
		}
		if(ChangnengUtil.isNull(pageSize)){
			pageSize = 10;
		}
		PageHelper.startPage( pageNum, pageSize);
		//二期查询逻辑为只展示本级创建的
		String belongAreaId = sysUser.getBelongAreaId();
		specialTask.setBelongAreaCode(belongAreaId);
		return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.specialTaskList(specialTask));
		/*//查询本级和上级
				String belongAreaId = sysUser.getBelongAreaId();
				String belongAreaIdTemp = belongAreaId.substring(4);
				String arealevel = null;
				String code = belongAreaId.substring(0, 4);
				
				if ("35000000".equals(belongAreaId)) {
					
					arealevel = "1";
				} 
				if ("0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					arealevel = "2";
				}
				if (!"0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					arealevel = "3";
				}
				return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.specialTaskList(specialTask,arealevel,belongAreaId,code.concat("0000")));*/
		//List<SceneSysSpecialModel> specialTaskList = sceneSysSpecialModelMapper.specialTaskList(specialTask);
		//return new PageBean<SceneSysSpecialModel>(specialTaskList);
	}
	@Override
	public PageBean<SceneSysSpecialModel> specialTaskApplyList(SceneSysSpecialModel specialTask, Integer pageNum,
			Integer pageSize, SysUsers sysUser) {
		if(ChangnengUtil.isNull(pageNum)){
			pageNum = 1;
		}
		if(ChangnengUtil.isNull(pageSize)){
			pageSize = 15;
		}
		PageHelper.startPage(pageNum, pageSize);
		specialTask.setBelongAreaCode(sysUser.getBelongAreaId());
		return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.fromSpecialAreaJoinSceneSysSpecialModel(specialTask));
	}
	@Override
	public PageBean<SceneSysSpecialModel> specialTaskLedgerList(SceneSysSpecialModel specialTask, Integer pageNum,
			Integer pageSize, SysUsers sysUser) throws BusinessException {
		String code = sysUser.getBelongAreaId();
		if(ChangnengUtil.isNull(pageNum)){
			pageNum = 1;
		}
		if(ChangnengUtil.isNull(pageSize)){
			pageSize = 10;
		}
	
		// 由于我们是福建省的区划，那么判断用户是否已经是最高和最低两个极端权限，来书写尽量少去like
		if (code == null || "".equals(code)) {
			throw new BusinessException("用户区划code为空");
		}
		if ("000000".equals(code.substring(2))) {// 省
			//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
			if ("0".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA ) ");
			} else if ("1".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
			}else if("2".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(" ( select SCENE_SYSSPECAIL_ID from SPECIAL_AREA MINUS	SELECT SCENE_SYSSPECAIL_ID FROM	SPECIAL_AREA WHERE area_code = '" + code + "' )  ");
			} else {
				throw new BusinessException("查询等级不明确");
			}
		} else if ("0000".equals(code.substring(4))) {// 市
			//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
			if ("0".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA where  area_code like '"
						+ code.substring(0, 4) + "%'  )  ");
			} else if ("1".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA where  area_code = '" + code + "' )  ");
			}else if("2".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select  SCENE_SYSSPECAIL_ID from SPECIAL_AREA MINUS	SELECT  SCENE_SYSSPECAIL_ID	FROM SPECIAL_AREA WHERE area_code like '"+code.substring(0, 4) + "%'"+")  ");
			}  else {
				throw new BusinessException("查询等级不明确");
			}
		} else if ("00".equals(code.substring(6))) {// 县
			//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
			if ("0".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
			} else if ("1".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
			}else if("2".equals(specialTask.getIsLevelCity())) {
				specialTask.setMainJoinSql(
						" ( select  SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA MINUS SELECT SCENE_SYSSPECAIL_ID FROM	SPECIAL_AREA WHERE area_code ='"+code+"')  ");
			}  else {
				throw new BusinessException("查询等级不明确");
			}
		} else {
			specialTask.setMainJoinSql(null);
		}

		if (specialTask.getMainJoinSql() == null || "".equals(specialTask.getMainJoinSql())) {
			throw new BusinessException("sql语句无法组装");
		}
		PageHelper.startPage( pageNum, pageSize);
		
		return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.specialTaskLedgerList(specialTask));
	}
	@Override
	public PageBean<SceneSysSpecialModel> specialTaskListforModel(SceneSysSpecialModel specialTask, Integer pageNum,
			Integer pageSize, SysUsers sysUser) {
		if(ChangnengUtil.isNull(pageNum)){
			pageNum = 1;
		}
		if(ChangnengUtil.isNull(pageSize)){
			pageSize = 10;
		}
		PageHelper.startPage( pageNum, pageSize);
		//二期查询逻辑为只展示本级创建的
		String belongAreaId = sysUser.getBelongAreaId();
		specialTask.setBelongAreaCode(belongAreaId);
		return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.specialTaskListforModel(specialTask));
	}

}
