<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SceneUsuallyModelMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SceneUsuallyModel">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USERID" jdbcType="VARCHAR" property="userid" />
    <result column="CUSTOM_DATABASE_ID" jdbcType="VARCHAR" property="customDatabaseId" />
    <result column="TEMPLATE_OBJECT_TYPE" jdbcType="VARCHAR" property="templateObjectType" />
  </resultMap>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SceneUsuallyModel">
    insert into SCENE_USUALLY_MODEL (ID, USERID, CUSTOM_DATABASE_ID, 
      TEMPLATE_OBJECT_TYPE)
    values (#{id,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, #{customDatabaseId,jdbcType=VARCHAR}, 
      #{templateObjectType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SceneUsuallyModel">
   <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into SCENE_USUALLY_MODEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="userid != null">
        USERID,
      </if>
      <if test="customDatabaseId != null">
        CUSTOM_DATABASE_ID,
      </if>
      <if test="templateObjectType != null">
        TEMPLATE_OBJECT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="userid != null">
        #{userid,jdbcType=VARCHAR},
      </if>
      <if test="customDatabaseId != null">
        #{customDatabaseId,jdbcType=VARCHAR},
      </if>
      <if test="templateObjectType != null">
        #{templateObjectType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <delete id="deleteByPrimaryKeyByUser" parameterType="java.lang.String">
    delete from SCENE_USUALLY_MODEL
    where USERID = #{userId}  and CUSTOM_DATABASE_ID = #{customDatabaseId}
  </delete>
  
   <delete id="deleteByPrimaryKeyByCustomDatabaseId" parameterType="java.lang.String">
    delete from SCENE_USUALLY_MODEL
    where  CUSTOM_DATABASE_ID = #{customDatabaseId}
  </delete>
</mapper>