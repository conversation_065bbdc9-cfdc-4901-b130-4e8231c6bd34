<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper">

    <!-- 基础结果映射 -->
    <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.CheckItemConfig">
        <id column="ID" jdbcType="VARCHAR" property="id" />
        <result column="ITEM_NAME" jdbcType="VARCHAR" property="itemName" />
        <result column="PARENT_ID" jdbcType="VARCHAR" property="parentId" />
        <result column="ITEM_SORT" jdbcType="DECIMAL" property="itemSort" />
        <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
        <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    </resultMap>

    <!-- 基础字段列表 -->
    <sql id="Base_Column_List">
        ID, ITEM_NAME, PARENT_ID, ITEM_SORT, CREATE_TIME, REMARK
    </sql>

    <!-- 查询所有记录 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 根据父级ID查询直接子项 -->
    <select id="selectByParentId" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID = #{parentId,jdbcType=VARCHAR}
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 查询顶级检查项 -->
    <select id="selectTopLevelItems" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE PARENT_ID IS NULL OR PARENT_ID = ''
        ORDER BY ITEM_SORT ASC, CREATE_TIME ASC
    </select>

    <!-- 根据主键查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM CHECK_ITEM_CONFIG
        WHERE ID = #{id,jdbcType=VARCHAR}
    </select>

</mapper>
