package org.changneng.framework.frameworkbusiness.entity.swingtag;

import java.util.Date;

public class SwingTagQuery {
	
	private String id ;//主体id
	private  String releaseUnit;//发起单位
	private String lawObjectName;//违法主体；
	private String lawObjectId;
	private String  belongAreaCode;  //违法主体所在行政区
	private String  belongAreaName; 
	private Integer isSwingTag;	//挂牌状态
	
	private String nodeTimeoutState; //办理状态
	
    private Date releaseDate;//发布日期
    
    private String swingTagName;//挂牌通知文书名称
    
    private String swingTagMark; //挂牌通知文号
    
    private String manageUnitName;//办理单位
    
    private Date limitTime;//限办日期
    
    private Date relieveTime;//解牌日期
    
    private String creatUserName;//填报人
   
    /**
     * xls文件显示 转换字段
     */
    private String isSwingTagTr;
    
    private String nodeTimeoutStateTr;
    
    private String releaseDateTr;
    
    private String limitTimeTr;
    
    private String relieveTimeTr;
    
   
    
	public String getIsSwingTagTr() {
		return isSwingTagTr;
	}

	public void setIsSwingTagTr(String isSwingTagTr) {
		this.isSwingTagTr = isSwingTagTr;
	}

	public String getNodeTimeoutStateTr() {
		return nodeTimeoutStateTr;
	}

	public void setNodeTimeoutStateTr(String nodeTimeoutStateTr) {
		this.nodeTimeoutStateTr = nodeTimeoutStateTr;
	}

	public String getReleaseDateTr() {
		return releaseDateTr;
	}

	public void setReleaseDateTr(String releaseDateTr) {
		this.releaseDateTr = releaseDateTr;
	}

	public String getLimitTimeTr() {
		return limitTimeTr;
	}

	public void setLimitTimeTr(String limitTimeTr) {
		this.limitTimeTr = limitTimeTr;
	}

	public String getRelieveTimeTr() {
		return relieveTimeTr;
	}

	public void setRelieveTimeTr(String relieveTimeTr) {
		this.relieveTimeTr = relieveTimeTr;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getReleaseUnit() {
		return releaseUnit;
	}

	public void setReleaseUnit(String releaseUnit) {
		this.releaseUnit = releaseUnit;
	}

	public String getLawObjectName() {
		return lawObjectName;
	}

	public void setLawObjectName(String lawObjectName) {
		this.lawObjectName = lawObjectName;
	}

	public String getLawObjectId() {
		return lawObjectId;
	}

	public void setLawObjectId(String lawObjectId) {
		this.lawObjectId = lawObjectId;
	}

	public String getBelongAreaCode() {
		return belongAreaCode;
	}

	public void setBelongAreaCode(String belongAreaCode) {
		this.belongAreaCode = belongAreaCode;
	}

	public String getBelongAreaName() {
		return belongAreaName;
	}

	public void setBelongAreaName(String belongAreaName) {
		this.belongAreaName = belongAreaName;
	}


	public Integer getIsSwingTag() {
		return isSwingTag;
	}

	public void setIsSwingTag(Integer isSwingTag) {
		this.isSwingTag = isSwingTag;
	}

	public String getNodeTimeoutState() {
		return nodeTimeoutState;
	}

	public void setNodeTimeoutState(String nodeTimeoutState) {
		this.nodeTimeoutState = nodeTimeoutState;
	}

	public Date getReleaseDate() {
		return releaseDate;
	}

	public void setReleaseDate(Date releaseDate) {
		this.releaseDate = releaseDate;
	}

	public String getSwingTagName() {
		return swingTagName;
	}

	public void setSwingTagName(String swingTagName) {
		this.swingTagName = swingTagName;
	}

	public String getSwingTagMark() {
		return swingTagMark;
	}

	public void setSwingTagMark(String swingTagMark) {
		this.swingTagMark = swingTagMark;
	}

	public String getManageUnitName() {
		return manageUnitName;
	}

	public void setManageUnitName(String manageUnitName) {
		this.manageUnitName = manageUnitName;
	}

	public Date getLimitTime() {
		return limitTime;
	}

	public void setLimitTime(Date limitTime) {
		this.limitTime = limitTime;
	}

	public Date getRelieveTime() {
		return relieveTime;
	}

	public void setRelieveTime(Date relieveTime) {
		this.relieveTime = relieveTime;
	}

	public String getCreatUserName() {
		return creatUserName;
	}

	public void setCreatUserName(String creatUserName) {
		this.creatUserName = creatUserName;
	}
    
}	
