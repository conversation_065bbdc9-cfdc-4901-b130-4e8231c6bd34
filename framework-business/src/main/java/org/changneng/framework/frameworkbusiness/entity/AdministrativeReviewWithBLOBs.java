package org.changneng.framework.frameworkbusiness.entity;

public class AdministrativeReviewWithBLOBs extends AdministrativeReview {
    private String specificItem;

    private String reason;

    private String decisionBasis;

    private String remark;

    public String getSpecificItem() {
        return specificItem;
    }

    public void setSpecificItem(String specificItem) {
        this.specificItem = specificItem == null ? null : specificItem.trim();
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason == null ? null : reason.trim();
    }

    public String getDecisionBasis() {
        return decisionBasis;
    }

    public void setDecisionBasis(String decisionBasis) {
        this.decisionBasis = decisionBasis == null ? null : decisionBasis.trim();
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

}