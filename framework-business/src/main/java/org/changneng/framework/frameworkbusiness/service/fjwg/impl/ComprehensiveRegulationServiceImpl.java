package org.changneng.framework.frameworkbusiness.service.fjwg.impl;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.entity.SiLuSearch;
import org.changneng.framework.frameworkbusiness.entity.SiLuTaskBean;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.ZfGrid;
import org.changneng.framework.frameworkbusiness.entity.silu.OneMapTask;
import org.changneng.framework.frameworkbusiness.service.fjwg.ComprehensiveRegulationService;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;

@Service
public class ComprehensiveRegulationServiceImpl implements ComprehensiveRegulationService {
	
	@Autowired
	private TaskMapper taskMapper;
	@Autowired
	private SysUsersMapper sysUsersMapper;

	@Override
	public PageBean<SiLuTaskBean> getTaskGpsListWg(SiLuSearch bean)throws Exception {
			if (bean == null) {
				bean = new SiLuSearch();
			}
			//福州一张图会传areaCode字段查询福州的数据
			if(ChangnengUtil.isNull(bean.getAreaCode())){
				if(ChangnengUtil.isNull(bean.getLoginUserName())){
					throw new BusinessException("未知的用户!");
				}
				SysUsers sysUsers = sysUsersMapper.getByUsername(bean.getLoginUserName());
				if(ChangnengUtil.isNull(sysUsers)){
					throw new BusinessException("用户"+bean.getLoginUserName()+"不存在!");
				}
				String areaCode = sysUsers.getBelongAreaId();
				if (areaCode != null && areaCode.endsWith("000000")) {
					//省级
					bean.setAreaCode(areaCode.substring(0, 2));
				} else if (areaCode != null && areaCode.endsWith("0000")) {
					//市级
					bean.setAreaCode(areaCode.substring(0, 4));
				} else if (areaCode != null && areaCode.endsWith("00")) {
					//县
					bean.setAreaCode(areaCode);
				}
			}
			if (ChangnengUtil.isNull(bean.getEndDate())) {
				bean.setEndDate(DateUtil.format(new Date()));
			}
			if (ChangnengUtil.isNull(bean.getBegDate())) {
				bean.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(bean.getEndDate()), -30)));
			}
			if(ChangnengUtil.isNull(bean.getGetAll())){
				bean.setGetAll("1");
			}
			List<SiLuTaskBean>  allList = null;
			if(bean.getGetAll().equals("1")){
				allList  = taskMapper.getTaskGpsListFJWG(bean);
			}
			PageHelper.startPage(bean.getPageNum(), bean.getPageSize());
			PageBean<SiLuTaskBean> pageBean = new PageBean<SiLuTaskBean>(taskMapper.getTaskGpsListFJWG(bean));
			String url = PropertiesHandlerUtil.getValue("onemap_local_law_detail_url", "resources");
			if(pageBean.getList().size()>0){
				for(SiLuTaskBean temp:pageBean.getList()){
					temp.setUrl(url+temp.getId());
				}
			}
			if(allList!=null && allList.size()>0){
				for(SiLuTaskBean temp:allList){
					temp.setUrl(url+temp.getId());
				}
				pageBean.setAllList(allList);
			}
			return pageBean;
	}

	@Override
	public PageBean<SiLuTaskBean> getLawCircleGpsListWg(SiLuSearch bean) throws Exception {
			if (bean == null) {
				bean = new SiLuSearch();
			}
			if(ChangnengUtil.isNull(bean.getAreaCode())){
				if(ChangnengUtil.isNull(bean.getLoginUserName())){
					throw new BusinessException("未知的用户!");
				}
				SysUsers sysUsers = sysUsersMapper.getByUsername(bean.getLoginUserName());
				if(ChangnengUtil.isNull(sysUsers)){
					throw new BusinessException("用户"+bean.getLoginUserName()+"不存在!");
				}
				String areaCode = sysUsers.getBelongAreaId();
				if (areaCode != null && areaCode.endsWith("000000")) {
					//省级
					bean.setAreaCode(areaCode.substring(0, 2));
				} else if (areaCode != null && areaCode.endsWith("0000")) {
					//市级
					bean.setAreaCode(areaCode.substring(0, 4));
				} else if (areaCode != null && areaCode.endsWith("00")) {
					//县
					bean.setAreaCode(areaCode);
				}
			}
			if (ChangnengUtil.isNull(bean.getEndDate())) {
				bean.setEndDate(DateUtil.format(new Date()));
			}
			if (ChangnengUtil.isNull(bean.getBegDate())) {
				bean.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(bean.getEndDate()), -30)));
			}
			List<SiLuTaskBean>  allList = null;
			if(bean.getGetAll().equals("1")){
				allList  = taskMapper.getLawCircleGpsListFJWG(bean);
			}
			PageHelper.startPage(bean.getPageNum(), bean.getPageSize());
			PageBean<SiLuTaskBean> pageBean  = new PageBean<SiLuTaskBean>(taskMapper.getLawCircleGpsListFJWG(bean));
			String url = PropertiesHandlerUtil.getValue("onemap_law_circle_detail_url", "resources");
			if(pageBean.getList().size()>0){
				for(SiLuTaskBean temp:pageBean.getList()){
					temp.setUrl(url+temp.getId());
				}
			}
			if(allList!=null && allList.size()>0){
				for(SiLuTaskBean temp:allList){
					temp.setUrl(url+temp.getId());
				}
				pageBean.setAllList(allList);
			}
			return pageBean;
	}

	@Override
	public PageBean<SiLuTaskBean> getCaseListWg(SiLuSearch bean) throws Exception {
			if (bean == null) {
				bean = new SiLuSearch();
			}
			if(ChangnengUtil.isNull(bean.getAreaCode())){
				if(ChangnengUtil.isNull(bean.getLoginUserName())){
					throw new BusinessException("未知的用户!");
				}
				SysUsers sysUsers = sysUsersMapper.getByUsername(bean.getLoginUserName());
				if(ChangnengUtil.isNull(sysUsers)){
					throw new BusinessException("用户"+bean.getLoginUserName()+"不存在!");
				}
				String areaCode = sysUsers.getBelongAreaId();
				if (areaCode != null && areaCode.endsWith("000000")) {
					//省级
					bean.setAreaCode(areaCode.substring(0, 2));
				} else if (areaCode != null && areaCode.endsWith("0000")) {
					//市级
					bean.setAreaCode(areaCode.substring(0, 4));
				} else if (areaCode != null && areaCode.endsWith("00")) {
					//县
					bean.setAreaCode(areaCode);
				}
			}
			if (ChangnengUtil.isNull(bean.getEndDate())) {
				bean.setEndDate(DateUtil.format(new Date()));
			}
			if (ChangnengUtil.isNull(bean.getBegDate())) {
				bean.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(bean.getEndDate()), -30)));
			}
			List<SiLuTaskBean>  allList = null;
			if(bean.getGetAll().equals("1")){
				allList  = taskMapper.getCaseListFJWG(bean);
			}
			PageHelper.startPage(bean.getPageNum(), bean.getPageSize());
			PageBean<SiLuTaskBean> pageBean  = new PageBean<SiLuTaskBean>(taskMapper.getCaseListFJWG(bean));
			String url = PropertiesHandlerUtil.getValue("onemap_case_info_detail_url", "resources");
			if(pageBean.getList().size()>0){
				for(SiLuTaskBean temp:pageBean.getList()){
					temp.setUrl(url+temp.getId());
				}
			}
			if(allList!=null && allList.size()>0){
				for(SiLuTaskBean temp:allList){
					temp.setUrl(url+temp.getId());
				}
				pageBean.setAllList(allList);
			}
			return pageBean;
	}

	@Override
	public int getApiTaskListByGrid(SiLuSearch search) {
		if (ChangnengUtil.isNull(search.getEndDate())) {
			search.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(search.getBegDate())) {
			try {
				search.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(search.getEndDate()), -30)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		SiLuTaskBean bena = taskMapper.getApiTaskCount(search);
		return bena.getCount();
	}

	@Override
	public List<ZfGrid> getZfNumByGrid(SiLuSearch search) {
		if (ChangnengUtil.isNull(search.getEndDate())) {
			search.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(search.getBegDate())) {
			try {
				search.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(search.getEndDate()), -30)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		List<ZfGrid> list = taskMapper.getZfNumByGrid(search);
		return list;
	}

	@Override
	public List<ZfGrid> getZfLawCircle(SiLuSearch search) {
		
		if (ChangnengUtil.isNull(search.getEndDate())) {
			search.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(search.getBegDate())) {
			try {
				search.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(search.getEndDate()), -30)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		List<ZfGrid> list = taskMapper.getZfLawCircle(search);
		return list;
	}

	@Override
	public List<ZfGrid> getZfCaseList(SiLuSearch search) {
		if (ChangnengUtil.isNull(search.getEndDate())) {
			search.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(search.getBegDate())) {
			try {
				search.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(search.getEndDate()), -30)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		List<ZfGrid> list = taskMapper.getZfCaseList(search);
		return list;
	}

	//一张图-案件查办
	@Override
	public List<SiLuTaskBean> getOneMapTaskGpsListWg(SiLuSearch bean) {
		if (ChangnengUtil.isNull(bean.getEndDate())) {
			bean.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(bean.getBegDate())) {
			try {
				bean.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(bean.getEndDate()), -180)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		if(ChangnengUtil.isNull(bean.getGetAll())){
			bean.setGetAll("1");
		}
		List<SiLuTaskBean> allList  = taskMapper.getCaseListFJWG(bean);
		return allList;
	}


	//一张图-现场执法
	@Override
	public PageBean<SiLuTaskBean> getOneMapTaskLawList(SiLuSearch bean) {
		if (ChangnengUtil.isNull(bean.getEndDate())) {
			bean.setEndDate(DateUtil.format(new Date()));
		}
		if (ChangnengUtil.isNull(bean.getBegDate())) {
			try {
				bean.setBegDate(DateUtil.format(DateUtil.addDay(DateUtil.getdefaulSimpleFormate(bean.getEndDate()), -30)));
			} catch (ParseException e) {
				e.printStackTrace();
			}
		}
		if(ChangnengUtil.isNull(bean.getGetAll())){
			bean.setGetAll("1");
		}
		List<SiLuTaskBean> allList  = taskMapper.getTaskGpsListFJWG(bean);

		PageHelper.startPage(bean.getPageNum(), bean.getPageSize());
		PageBean<SiLuTaskBean> pageBean = new PageBean<SiLuTaskBean>(taskMapper.getTaskGpsListFJWG(bean));
//		String url = PropertiesHandlerUtil.getValue("onemap_local_law_detail_url", "resources");
//		if(pageBean.getList().size()>0){
//			for(SiLuTaskBean temp:pageBean.getList()){
//				temp.setUrl(url+temp.getId());
//			}
//		}
		if(allList!=null && allList.size()>0){
//			for(SiLuTaskBean temp:allList){
//				temp.setUrl(url+temp.getId());
//			}
			pageBean.setAllList(allList);
		}
		return pageBean;

	}


	@Override
	public List<OneMapTask> getOneMapTaskTableList() {

		return null;
	}

}
