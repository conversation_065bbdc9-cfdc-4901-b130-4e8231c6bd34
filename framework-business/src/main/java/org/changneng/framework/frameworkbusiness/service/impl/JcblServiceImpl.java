package org.changneng.framework.frameworkbusiness.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.CodeTaskTableMapper;
import org.changneng.framework.frameworkbusiness.dao.LawEnforceObjectMapper;
import org.changneng.framework.frameworkbusiness.dao.LawobjUpdateLogMapper;
import org.changneng.framework.frameworkbusiness.dao.SceneSysSpecialModelMapper;
import org.changneng.framework.frameworkbusiness.dao.SpecialActionMapper;
import org.changneng.framework.frameworkbusiness.dao.SysDepartmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskFlowMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskNodeMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskRequireFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.TaskSpecialMapper;
import org.changneng.framework.frameworkbusiness.dao.TcDictionaryMapper;
import org.changneng.framework.frameworkbusiness.dao.UserTaskTableMapper;
import org.changneng.framework.frameworkbusiness.dao.historyLawEnforceObjectMapper;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.pdf.GeneratePdfService;
import org.changneng.framework.frameworkbusiness.produceNumber.ProduceNumber;
import org.changneng.framework.frameworkbusiness.service.JcblService;
import org.changneng.framework.frameworkbusiness.service.NewInfoTableService;
import org.changneng.framework.frameworkbusiness.service.SystemCodingService;
import org.changneng.framework.frameworkbusiness.service.TaskFlowService;
import org.changneng.framework.frameworkbusiness.utils.CompareObjModified;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.APIResponseJson;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.Gps;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.PageBeanUserDefined;
import org.changneng.framework.frameworkcore.utils.PositionUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
@Service
public class JcblServiceImpl implements JcblService {
	
	private static  Logger logger = LogManager.getLogger(TaskFlowServiceImpl.class.getName());

	@Autowired 
	private TcDictionaryMapper tcDictionaryMapper;
	
	@Autowired
	private SpecialActionMapper specialActionMapper;
	@Autowired
	private TaskMapper taskMapper;
	@Autowired
	private CodeTaskTableMapper codeTaskTableMapper;
	
	@Autowired
	private TaskFlowMapper taskFlowMapper;
	
	@Autowired
	private TaskNodeMapper tasknodeMapper;
	
	@Autowired
	private TaskSpecialMapper  taskSpecialMapper;
	
	@Autowired
	private SysUsersMapper sysUserMapper;
	
	@Autowired
	private TaskRequireFilesMapper taskRequireFilesMapper;
	
	@Autowired
	private SysFilesMapper sysFilesMapper;
	
	@Autowired
	private TaskFlowService taskFlowService;
	@Autowired
	private LawEnforceObjectMapper lawEnforceObjectMapper;
	
	@Autowired
	private SystemCodingService systemCodingService;
	
	@Autowired
	private UserTaskTableMapper userTaskTableMapper;
	
	
	@Autowired
	private  GeneratePdfService  generatePdfService;

	@Autowired
	private LawobjUpdateLogMapper lawobjUpdateLogMapper;
	@Autowired
    private SysDepartmentMapper sysDepartmentMapper;
	@Autowired
	private historyLawEnforceObjectMapper historyLawEnforceObjectMapper;
	
	@Autowired
	private NewInfoTableService newInfoTableService;
	
	@Autowired
	private ProduceNumber produceNumber;
	@Autowired
	private SceneSysSpecialModelMapper sceneSysSpecialModelMapper;
	 
	
	@Override
	public 	List<TcDictionary>  TcDictionaryList(String type) throws Exception {
		
		List<TcDictionary> list = tcDictionaryMapper.tcDictionaryList(type);
		
		return list;
	}


	public List<SceneSysSpecialModel> SpecialActionList() {
		
		return specialActionMapper.selectSpecialActionMapperList();
		
	}


	@Override
	public Integer saveOrUpdateTask(Task task) {
		
		return taskMapper.insertSelective(task);
	}


	@Override
	public void saveFlowTask(TaskFlow taskFlowBean) {
		taskFlowMapper.insertSelective(taskFlowBean);
	}


	@Override
	public void saveTaskNode(TaskNode taskNodeBean) {
		tasknodeMapper.insertSelective(taskNodeBean);
	}


	@Override
	public void saveTaskSpecial(TaskSpecial taskSpecial) {
		taskSpecialMapper.insertSelective(taskSpecial);
	}


	
	@Override
	public void updateTask(Task task) {
		taskMapper.updateByPrimaryKeySelective(task);
	}


	@Override
	public TaskFlow getFlowTaskByTaskId(String tasKId) {
		return taskFlowMapper.selectByTaskId(tasKId);
	}


	@Override
	public void updateFlowTask(TaskFlow taskFlow) {
		taskFlowMapper.updateByPrimaryKeySelective(taskFlow);
		}


	@Override
	public TaskNode getFlowNodeByTaskId(String taskId) {
		return  tasknodeMapper.selectByTaskId(taskId);
	}

	@Override
	public void updateTaskNode(TaskNode taskNode) {
		tasknodeMapper.updateByPrimaryKeySelective(taskNode);
	}

     //v1.6废弃不用，添加新方法specialActionList2
	@Override
	public PageBean<SceneSysSpecialModel> specialActionList(int pNum ,int pageSize,String status,SysUsers sysUser) {
		PageHelper.startPage(pNum,pageSize);
		//查询本级和上级
		String belongAreaId = sysUser.getBelongAreaId();
		if("1".equals(status)){
			return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.appGetSpecialActionListV6());
		}else{
			//1.6待改造
            return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.appGetSpecialActionListV6(belongAreaId));
		}
	}


	@Override
	public Task taskByTaskId(String taskId) {
		return taskMapper.selectByPrimaryKey(taskId);
	}

	@Override
	public ComplaintReportInfo taskByTaskIdCRI(String taskId) {
		return taskMapper.selectByPrimaryKeyCRI(taskId);
	}

	@Override
	public void saveFileLinkInfo(String sysFileIds,String sysFileUrl,String sysFileType, String taskId) {
		taskRequireFilesMapper.saveFileLinkInfo(sysFileIds,sysFileUrl,sysFileType,taskId);
	}


	@Override
	public void saveTaskRequireFiels(TaskRequireFiles taskRequireFiles) {
		taskRequireFilesMapper.insertSelective(taskRequireFiles);
	}


	@Override
	public void updateSysFile(String updateByPrimaryKey) {
		sysFilesMapper.updateSysFile(updateByPrimaryKey);
	}
	@Override
	public PageBeanUserDefined<LawEnforceObject> lawListList(int pNum,
			int pageSize, String areaCode, String provinceCode,
			String cityCode, String objectName, String address,
			String lawObjectType, String belongAreaId, String arealevel,
			String code,String lawObjectStatus) {
			if("1".equals(lawObjectStatus)){
				String status =null;
				String citySubCode = null;
				if((cityCode != null && !"".equals(cityCode)) && (areaCode != null && !"".equals(areaCode)) ){
					//县级查询
					 status  = "3";
				}else if ((cityCode != null && !"".equals(cityCode)) && ("".equals(areaCode)) ){
					//市级查询
					status = "2";
					citySubCode = cityCode.substring(0, 4);
				}else {
					status ="1";
				}
				List<LawEnforceObject> list = 	lawEnforceObjectMapper.lawListAllList(areaCode,objectName,address,status,citySubCode,lawObjectType,(pNum-1)*pageSize, pNum*pageSize);
				int total= 	 lawEnforceObjectMapper.lawListAllListTotal(areaCode,objectName,address,status,citySubCode,lawObjectType);
				PageBeanUserDefined<LawEnforceObject> pageBean = new PageBeanUserDefined<LawEnforceObject>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
			}else{
				List<LawEnforceObject> list = 	lawEnforceObjectMapper.lawListList(belongAreaId ,arealevel,lawObjectType,code,(pNum-1)*pageSize, pNum*pageSize);
				int total= 	 lawEnforceObjectMapper.lawListListTotal(belongAreaId ,arealevel,lawObjectType,code);
				//默认加载自己本级和本级一下的执法对象
				PageBeanUserDefined<LawEnforceObject> pageBean = new PageBeanUserDefined<LawEnforceObject>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
		}
	}
	/**
	 * 任务的保存并发起功能 分析：获取用户信息和输入信息 
	 * 流转跟踪信息里新建一条待办任务；(状态:"0", "发起任务", "3", "已完成", "0", "任务分配") 
	 * 流转跟踪信息里新建一条待办任务；("0","发起任务", "1", "待处理", "1", "办理中")
	 * 在任务信息表里更新一条任务信息；（状态："0", "任务分配" "0", "任务分配" ）
	 * 在环节信息表里新建一条环节信息；（状态："1", "现场执法" "1", "办理中" ） 
	 * 在任务专项行动关联表 添加一条信息;
	 * 关联文件系统表；
	 * 维护用户和任务中间表；
	 * 维护area表
	 * @param request
	 * @param response
	 * @param task
	 * @param status  判断任务是发起还是 保存并发起 0为发起 1 为保存发起
	 * @return
	 * @throws Exception 
	 * @throws ParseException
	 */
	@Transactional(rollbackFor = Exception.class)
	public	 ResponseJson  jcblTaskSaveAndStart(String taskId,String taskFlowId, String sysFileIds,
			String sysFileUrl, String sysFileType, Task task) throws Exception {
		ResponseJson json = new ResponseJson();
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		String taskFromName = task.getTaskFromName();
		//维护执法对象信息
		//维护执法对象信息 保存或者修改都维护 
		String lawObjectId = task.getLawObjectId();
		if(!"".equals(taskFromName) && taskFromName != null ){
			String[] tempTaskFromName = taskFromName.split("#");
			if(tempTaskFromName.length != 0){
				task.setTaskFromName(tempTaskFromName[0]);
				task.setTaskFromCode(tempTaskFromName[1]);
			}
		}
		String cardTypeName = task.getCardTypeName();
		if(!"1".equals(task.getLawObjectType()) && !"4".equals(task.getLawObjectType())  ){
			if( !"".equals(cardTypeName) && cardTypeName !=null){
				String[] tempCardTypeName = cardTypeName.split("#");
				if(tempCardTypeName.length != 0 ){
					task.setCardTypeName(tempCardTypeName[0]);
					task.setCardTypeCode(tempCardTypeName[1]);
				}
			}
		}
		SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
		LawEnforceObjectWithBLOBs record = lawEnforceObjectMapper.selectByPrimaryKey(lawObjectId);
		historyLawEnforceObjectWithBLOBs historyLawEnforceObjectWithBLOBs = new historyLawEnforceObjectWithBLOBs();
		BeanUtils.copyProperties(record, historyLawEnforceObjectWithBLOBs);
		historyLawEnforceObjectWithBLOBs.setLawObjectId(record.getId());
		historyLawEnforceObjectWithBLOBs.setId(null);
		String address = task.getAddress();
		String cardTypeName2 = task.getCardTypeName();
		String cardCode = task.getCardCode();
		String linkman = task.getLinkman();
		String legalPhone = task.getLegalPhone();
		String lawObjectType = task.getLawObjectType();
		historyLawEnforceObjectWithBLOBs.setAddress(address);
		if("1".equals(lawObjectType)){
			//企业类型
			historyLawEnforceObjectWithBLOBs.setChargePerson(linkman);
			historyLawEnforceObjectWithBLOBs.setChargePersonPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setSocialCreditCode(cardCode);
		}else if("2".equals(lawObjectType)){
			//个人
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
			historyLawEnforceObjectWithBLOBs.setPersoncardTypeName(cardTypeName2);
		}else if("3".equals(lawObjectType)){
			//个体
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}else if("4".equals(lawObjectType)){
			//自然保护区
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setOrgCode(cardCode);
		}else{
			//无助
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}
		if("1".equals(task.getUpdateObjectState())){
			//同步执法对象信息
			record.setId(lawObjectId);
			record.setAddress(address);
			record.setUpdatetime(new Date());
			LawEnforceObjectWithBLOBs oldBLOBS = new LawEnforceObjectWithBLOBs();
			BeanUtils.copyProperties(record, oldBLOBS);
			record.setId(lawObjectId);
			record.setAddress(address);
			if("1".equals(lawObjectType)){
				//企业类型
				record.setChargePerson(linkman);
				record.setChargePersonPhone(legalPhone);
				record.setSocialCreditCode(cardCode);
			}else if("2".equals(lawObjectType)){
				//个人
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardNumber(cardCode);
				record.setPersoncardTypeName(cardTypeName2);
			}else if("3".equals(lawObjectType)){
				//个体
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
			}else if("4".equals(lawObjectType)){
				//自然保护区
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setOrgCode(cardCode);
			}else{
				//无助
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
			}
			try {
				lawEnforceObjectMapper.updateByPrimaryKeySelective(record);
				if(!ChangnengUtil.isNull(sysDepartment)){
					sysUser.setBelongDepartmentName(sysDepartment.getDepartmentName());
				}
				List<LawobjUpdateLog> contrastLawObjectModified =  CompareObjModified.contrastLawObjectModified(oldBLOBS, record, sysUser); 
				if(contrastLawObjectModified.size()>0){
					lawobjUpdateLogMapper.insertSelectiveList(contrastLawObjectModified);
				}
			} catch (Exception e) {
				logger.info("维护执法对象信息失败！");
				e.printStackTrace();
				throw e;
			}
		}
		if (taskId == null || "".equals(taskId) ) {
			// 任务保存并发起操作
			try {
				// 在任务信息表里添加一条任务信息
				Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), task.getLimitTime());
				task.setCreatUserId(sysUser.getId());
				task.setCreatUserName(sysUser.getLoginname());
				task.setTaskStateCode("1");
				task.setTaskStateName("办理中 ");
				task.setBelongYear(Integer.toString(DateUtil.year()));
				task.setQuarter(Integer.toString(DateUtil.getSeason(new Date())));
				if(ChangnengUtil.isNull(task.getTaskFromType())){
					task.setTaskFromType(1);
					task.setTaskFromTypeName("非特定专项检查");
					task.setIsSpecial(0);
				}else if (1==task.getTaskFromType()) {
					task.setTaskFromTypeName("非特定专项检查");
					task.setIsSpecial(0);
				}else{
					SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
					if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
						task.setTaskFromTypeName(sceneSysSpecialModel.getTemplateName());
						task.setIsSpecial(1);
					}
					task.setTaskLawObjectStatus("3");
				}
				//所属部门
				task.setCreatDepartmentId(sysUser.getBelongDepartmentId());
				if(!ChangnengUtil.isNull(sysDepartment.getDepartmentName())){
					task.setCreatDepartmentName(sysDepartment.getDepartmentName());
				}
				//维护任务的taskID 
				//String sysTaskCoding = systemCodingService.getSysTaskCoding(sysUser.getBelongAreaId());
				String sysTaskCoding = produceNumber.ProduceTaskNumber(sysUser.getBelongAreaId());
				task.setTaskId(sysTaskCoding);
				task.setCreatDate(new Date());
				task.setNodeTimeoutState(code.toString());
				taskMapper.insertSelective(task);
				//生成快照信息
				historyLawEnforceObjectWithBLOBs.setTaskId(task.getId());
				historyLawEnforceObjectMapper.insertSelective(historyLawEnforceObjectWithBLOBs);
				//维护用户和任务中间表
				String checUserIds = task.getChecUserIds();
				if(checUserIds!= null && !"".equals(checUserIds) ){
					if(!checUserIds.contains(sysUser.getId())){
						checUserIds =	checUserIds.concat(",").concat(sysUser.getId());
					}
				}
				String[] checUserIdsTemp = checUserIds.split(",");
				if(checUserIdsTemp != null && checUserIdsTemp.length >0){
					for(int i=0;i<checUserIdsTemp.length;i++){
						UserTaskTable userTaskTable = new UserTaskTable();
						userTaskTable.setTaskId(task.getId());
						userTaskTable.setSysUserId(checUserIdsTemp[i]);
						userTaskTableMapper.insertSelective(userTaskTable);
					}
				}
				// 新增第一条流转跟踪信息记录信息
				// 1个人，2部门handling_type  task_reach_time
				TaskBean taskUtils = new TaskBean();
					TaskFlow	taskFlowBean = taskUtils.taskFlowBean(task,sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName(),code.toString(), "任务分配",
							"0", null,sysUser.getId(), sysUser.getLoginname(), 
							new Date(), new Date(),sysUser.getLoginname(), "0", "发起任务", "3", "已完成", "0", "任务分配",
							null, null, null, null, null, null,new Date());
				
				taskFlowMapper.insertSelective(taskFlowBean);
				// 新建第二条流转跟踪信息记录
				TaskFlow taskFlowBean2 = taskUtils.taskFlowBean(task,null,null, code.toString(),"现场执法",
						"1", null, null, null, new Date(), null, task.getChecUserNames(), null,
						null, "1", "待处理", "1", "办理中",null, taskFlowBean.getId(),
						 "0", "任务分配", sysUser.getId(),
						sysUser.getLoginname(),null);
				
				taskFlowMapper.insertSelective(taskFlowBean2);
				// 新增第一条环节信息表里新建一条环节信息
				// 环节状态：1当前环节，0已完成环节
				TaskNode taskNodeBean = taskUtils.TaskNodeBean(task.getId(),
						"0", "任务分配", "0", null, sysUser.getId(),
						sysUser.getLoginname(), "0", "任务分配",sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName());
				tasknodeMapper.insertSelective(taskNodeBean);
				// 新增第二条环节信息
				TaskNode taskNodeBean2 = taskUtils.TaskNodeBean(task.getId(),
						"1", "现场执法", "1", null, null,
						null, "1", "办理中",null,null);
				tasknodeMapper.insertSelective(taskNodeBean2);
				//维护area表checUserIds
				String replaceAll = checUserIds.replaceAll(",", "\',\'");
				List<SysUsers> areaList = sysUserMapper.getAreaList("\'"+replaceAll+"\'");
				for( SysUsers sysUsers :areaList){
					String belongAreaId = sysUsers.getBelongAreaId();
					CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(belongAreaId, task.getId());
					if(codeTable==null){
						CodeTaskTable newCodeTable = new CodeTaskTable();
						newCodeTable.setBelongAreaId(belongAreaId);
						newCodeTable.setTaskId(task.getId());
						codeTaskTableMapper.insertSelective(newCodeTable);
					}
				}
				 if(record!=null && record.getBelongAreaId()!=null){
				        CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(record.getBelongAreaId(), task.getId());
				        if(codeTable==null){
				          CodeTaskTable newCodeTable = new CodeTaskTable();
				          newCodeTable.setBelongAreaId(record.getBelongAreaId());
				          newCodeTable.setTaskId(task.getId());
				          codeTaskTableMapper.insertSelective(newCodeTable);
				        }
				      }
				// 任务专项行动关联表 添加一条信息;
				if(task.getSpecialActionIds()!= null && !"".equals(task.getSpecialActionIds())){
					String specialActionIds = task.getSpecialActionIds();
					String[] split = specialActionIds.split(",");
					for(String specialAction :split){
						TaskSpecial taskSpecial = new TaskSpecial(); 
						taskSpecial.setSpecialActionId(specialAction);
						taskSpecial.setTaskId(task.getId());
						taskSpecialMapper.insertSelective(taskSpecial);
					}
				}
				/*if(!ChangnengUtil.isNull(task.getIsSpecial()) && task.getIsSpecial()==1) {
					SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
					if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
						TaskSpecial taskSpecial = new TaskSpecial(); 
						taskSpecial.setSpecialActionId(sceneSysSpecialModel.getId());
						taskSpecial.setTaskId(taskId);
						taskSpecialMapper.insertSelective(taskSpecial);							
					}
				}*/
				Map<String,String> map = new HashMap<String, String>();
				map.put("taskId", task.getId());
				// 双随机下发任务成功后，发送消息，通知到被分配的人  lhl 20171227
				newInfoTableService.creatSendTaskInsertNewInfoTable(task);
				return json.success("200", "200", "添加任务成功!", null, map);

			} catch (Exception e) {
				logger.info("添加任务失败");
				e.printStackTrace();
				throw e;
			}
		} else {
			try {
				// 任务发起操作
				// 根据任务id查询任务
				Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), task.getLimitTime());
				task.setTaskStateCode("1");
				task.setTaskStateName("办理中 ");
				task.setId(taskId);
				task.setTaskId(null);
				task.setNodeTimeoutState(code.toString());
				// 修改任务信息表信息
				taskMapper.updateByPrimaryKeySelective(task);
				//生成快照信息
				historyLawEnforceObjectWithBLOBs.setTaskId(task.getId());
				historyLawEnforceObjectMapper.updateBytaskIdAndObjectId(historyLawEnforceObjectWithBLOBs);
				//维护用户和任务中间表
				String checUserIds = task.getChecUserIds();
				//判断当前操作者是否在检查人中，如果没有追加操作
				if(checUserIds!= null && !"".equals(checUserIds) ){
					if(!checUserIds.contains(sysUser.getId())){
						checUserIds =	checUserIds.concat(",").concat(sysUser.getId());
						}
				}
				String[] checUserIdsTemp = checUserIds.split(",");
				if(checUserIdsTemp != null && checUserIdsTemp.length >0){
					for(int i=0;i<checUserIdsTemp.length;i++){
						UserTaskTable userTaskTable = new UserTaskTable();
						UserTaskTable userTaskTableTemp = userTaskTableMapper.getCheckUserIds(taskId,checUserIdsTemp[i]);
						  if(userTaskTableTemp ==null ){
							userTaskTable.setTaskId(taskId);
							userTaskTable.setSysUserId(checUserIdsTemp[i]);
							userTaskTableMapper.insertSelective(userTaskTable);
						  }
					}
				}
				// 修改第一条流转跟踪信息记录信息
				// 1个人，2部门handling_type
				// 根据任务id查找上一条流转信息
				TaskFlow taskFlow =  taskFlowMapper.selectByTaskIdAndTaskFlowId(taskId,taskFlowId);
				 if(taskFlow !=null && "0".equals(taskFlow.getHandlingStateCode())){
					 	//退回保存流转任务
					 taskFlow.setTaskStatesCode(code.toString());
					 taskFlow.setManagementModeCode("0");
					 taskFlow.setManagementModeName("发起任务");
					 taskFlow.setHandlingStateCode("3");
					 taskFlow.setHandlingStateName("已完成");
					 taskFlow.setLimitTime(task.getLimitTime());
					 taskFlow.setProcessingTime(new Date());
					 taskFlow.setTodoObjectId(task.getChecUserIds());
					 taskFlow.setTodoObjectName(task.getChecUserNames());
					 taskFlow.setHandlingUnitId(sysUser.getBelongDepartmentId());
					 taskFlow.setHandlingUnitName(sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId()).getDepartmentName());
					 taskFlow.setFlowDirection(task.getChecUserNames());
					 taskFlowMapper.updateByPrimaryKeySelective(taskFlow);
				 }else{
					 //正常保存流转任务
					 taskFlow.setProcessingTime(new Date());
					 taskFlow.setHandlingStateCode("3");
					 taskFlow.setHandlingStateName("已完成");
					 taskFlow.setTodoObjectId(task.getChecUserIds());
					 taskFlow.setTodoObjectName(task.getChecUserNames());
					 taskFlow.setLimitTime(task.getLimitTime());
					 taskFlow.setFlowDirection(task.getChecUserNames());
					 taskFlowMapper.updateByPrimaryKeySelective(taskFlow);
				 }
				// 新建第一条流转跟踪信息记录
				TaskBean taskUtils = new TaskBean();
				// 新建第二条流转跟踪信息记录
				TaskFlow taskFlowBean2 = taskUtils.taskFlowBean(task,null,null, code.toString(),"现场执法",
						"1", "1", null, null, new Date(), null, null, null,
						null, "1", "待处理", "1", "办理中",null, taskFlow.getId(),
						 "1", "办理中", sysUser.getId(),
						sysUser.getLoginname(),null);
				taskFlowMapper.insertSelective(taskFlowBean2);
				// 修改第一条环节信息表里新建一条环节信息
				// 环节状态：1当前环节，0已完成环节
				TaskNode taskNode = tasknodeMapper.selectByTaskId(taskId);
				taskNode.setNodeState("0");
				tasknodeMapper.updateByPrimaryKeySelective(taskNode);
				// 新增第二条环节信息
				TaskNode taskNodeBean2 = taskUtils.TaskNodeBean(task.getId(),
						"1", "现场执法", "1", null, taskNode.getHandleUserId(),
						taskNode.getHandleUserName(), "1", "办理中",null,null);
				tasknodeMapper.insertSelective(taskNodeBean2);
				taskSpecialMapper.deleteTaskSpecialByTaskId(taskId);
				//维护area表
				String replaceAll = checUserIds.replaceAll(",", "\',\'");
				List<SysUsers> areaList = sysUserMapper.getAreaList("\'"+replaceAll+"\'");
				for( SysUsers sysUsers :areaList){
					String belongAreaId = sysUsers.getBelongAreaId();
					CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(belongAreaId, task.getId());
					if(codeTable==null){
						CodeTaskTable newCodeTable = new CodeTaskTable();
						newCodeTable.setBelongAreaId(belongAreaId);
						newCodeTable.setTaskId(task.getId());
						codeTaskTableMapper.insertSelective(newCodeTable);
					}
				}
				 if(record!=null && record.getBelongAreaId()!=null){
				        CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(record.getBelongAreaId(), task.getId());
				        if(codeTable==null){
				          CodeTaskTable newCodeTable = new CodeTaskTable();
				          newCodeTable.setBelongAreaId(record.getBelongAreaId());
				          newCodeTable.setTaskId(task.getId());
				          codeTaskTableMapper.insertSelective(newCodeTable);
				        }
				      }
				//根据任务id修改任务专项行动关联表
				String specialActionIds = task.getSpecialActionIds();
				if(specialActionIds != null && !"".equals(specialActionIds)){
					String[] split = specialActionIds.split(",");
					for(String specialAction :split){
						TaskSpecial taskSpecial = new TaskSpecial(); 
						taskSpecial.setSpecialActionId(specialAction);
						taskSpecial.setTaskId(taskId);
						taskSpecialMapper.insertSelective(taskSpecial);
					}
				}else{
					//删除专项行动关联表信息
					taskSpecialMapper.deleteTaskSpecialByTaskId(taskId);
				}
				/*if(!ChangnengUtil.isNull(task.getIsSpecial()) && task.getIsSpecial()==1) {
					SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
					if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
						TaskSpecial taskSpecial = new TaskSpecial(); 
						taskSpecial.setSpecialActionId(sceneSysSpecialModel.getId());
						taskSpecial.setTaskId(taskId);
						taskSpecialMapper.insertSelective(taskSpecial);							
					}
				}*/
				Map<String,String> map = new HashMap<String, String>();
				map.put("taskId", taskId);
				
				// 双随机下发任务成功后，发送消息，通知到被分配的人  lhl 20171227
				//newInfoTableService.creatSendTaskInsertNewInfoTable(task);
				return json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "发起任务成功!", null, map);
			} catch (Exception e) {
				logger.info("添加任务失败");
				e.printStackTrace();
				throw e;
			}
		}
	}
	/**
	 * 任务的保存功能 分析：获取用户信息和输入信息 
	 * 流转跟踪信息里新建一条待办任务；(状态: "0", "任务分配", "0", "发起任务", "2", "处理中", "0", "任务分配") 
	 * 在任务信息表里添加一条任务信息；（状态：任务状态为：任务分配 0）
	 * 在环节信息表里新建一条环节信息；（状态：环节状态："0", "任务分配" 任务状态为："0", "任务分配"） 
	 * 在任务专项行动关联表 添加一条信息;
	 * 关联文件系统表；
	 * 维护用户和任务中间表；
	 * @param request
	 * @param response
	 * @param task
	 * @throws Exception 
	 * @return
	 * @throws ParseException
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson jcblTaskSave(Task task,String taskFlowId, String sysFileIds,
			String sysFileUrl, String sysFileType, String taskId, String loginToken, SysUsers sysUsersApi) throws Exception {
		ResponseJson json = new ResponseJson();
		SysUsers sysUser = new SysUsers();
		if (StringUtils.isNotEmpty(loginToken)) {
			sysUser = sysUsersApi;
		} else {

			sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		}
		
		String taskFromName = task.getTaskFromName();
		if(!"".equals(taskFromName) && taskFromName != null){
			String[] tempTaskFromName = taskFromName.split("#");
			if(tempTaskFromName.length != 0){
				task.setTaskFromName(tempTaskFromName[0]);
				
				task.setTaskFromCode(tempTaskFromName[1]);
			}
		}
		String cardTypeName = task.getCardTypeName();
		if(!"1".equals(task.getLawObjectType()) && !"4".equals(task.getLawObjectType())  ){
		if( !"".equals(cardTypeName) && cardTypeName != null){
			String[] tempCardTypeName = cardTypeName.split("#");
			if(tempCardTypeName.length != 0 ){
				task.setCardTypeName(tempCardTypeName[0]);
				task.setCardTypeCode(tempCardTypeName[1]);
			}
		}}
		//维护执法对象信息 保存或者修改都维护 
		LawEnforceObjectWithBLOBs record = lawEnforceObjectMapper.selectByPrimaryKey(task.getLawObjectId());
		historyLawEnforceObjectWithBLOBs historyLawEnforceObjectWithBLOBs = new historyLawEnforceObjectWithBLOBs();
		BeanUtils.copyProperties(record, historyLawEnforceObjectWithBLOBs);
		historyLawEnforceObjectWithBLOBs.setLawObjectId(record.getId());
		historyLawEnforceObjectWithBLOBs.setId(null);
		String lawObjectId = task.getLawObjectId();
		String address = task.getAddress();
		String cardTypeName2 = task.getCardTypeName();
		String cardCode = task.getCardCode();
		String linkman = task.getLinkman();
		String legalPhone = task.getLegalPhone();
		String lawObjectType = task.getLawObjectType();
		historyLawEnforceObjectWithBLOBs.setAddress(address);
		if("1".equals(lawObjectType)){
			//企业类型
			historyLawEnforceObjectWithBLOBs.setChargePerson(linkman);
			historyLawEnforceObjectWithBLOBs.setChargePersonPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setSocialCreditCode(cardCode);
		}else if("2".equals(lawObjectType)){
			//个人
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
			historyLawEnforceObjectWithBLOBs.setPersoncardTypeName(cardTypeName2);
		}else if("3".equals(lawObjectType)){
			//个体
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}else if("4".equals(lawObjectType)){
			//自然保护区
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setOrgCode(cardCode);
		}else{
			//无助
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}
		if("1".equals(task.getUpdateObjectState())){
			//同步污染源信息
			LawEnforceObjectWithBLOBs oldBLOBS = new LawEnforceObjectWithBLOBs();
			BeanUtils.copyProperties(record, oldBLOBS);
			record.setId(lawObjectId);
			record.setAddress(address);
			record.setUpdatetime(new Date());
			if("1".equals(lawObjectType)){
				//企业类型
				record.setChargePerson(linkman);
				record.setChargePersonPhone(legalPhone);
				record.setSocialCreditCode(cardCode);
			}else if("2".equals(lawObjectType)){
				//个人
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardNumber(cardCode);
				record.setPersoncardTypeName(cardTypeName2);
			}else if("3".equals(lawObjectType)){
				//个体
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
			}else if("4".equals(lawObjectType)){
				//自然保护区
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setOrgCode(cardCode);
			}else{
				//无助
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
			}
			try {
				lawEnforceObjectMapper.updateByPrimaryKeySelective(record);
				SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
				sysUser.setBelongDepartmentName(sysDepartment.getDepartmentName());
				List<LawobjUpdateLog> contrastLawObjectModified =  CompareObjModified.contrastLawObjectModified(oldBLOBS, record, sysUser); 
				if(contrastLawObjectModified.size()>0){
					lawobjUpdateLogMapper.insertSelectiveList(contrastLawObjectModified);
				}
			} catch (Exception e) {
				logger.info("维护执法对象信息失败！");
				e.printStackTrace();
				throw e;
			}
		}
		if ((taskId == null || "".equals(taskId)) && (taskFlowId == null || "".equals(taskFlowId)) ) {
		//获取登录用户的信息
		try {
			Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), task.getLimitTime());
			//在任务信息表里添加一条任务信息
			task.setCreatUserId(sysUser.getId());
			task.setCreatUserName(sysUser.getLoginname());
			task.setTaskStateCode("0");
			task.setTaskStateName("任务分配");
			task.setNodeTimeoutState(code.toString());
			task.setBelongYear(Integer.toString(DateUtil.year()));
			task.setQuarter(Integer.toString(DateUtil.getSeason(new Date())));
			if(!ChangnengUtil.isNull(task.getTaskFromType())){
				if(task.getTaskFromType() ==1){
					task.setTaskFromType(1);
					task.setTaskFromTypeName("非特定专项检查");
					task.setIsSpecial(0);
					
				}else{
					SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
					if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
						task.setTaskFromTypeName(sceneSysSpecialModel.getTemplateName());
					}
					task.setIsSpecial(1);
					task.setTaskLawObjectStatus("3");
				}
			}else{
				task.setTaskFromType(1);
				task.setTaskFromTypeName("非特定专项检查");
				task.setIsSpecial(0);
			}
			//维护任务的taskID
			//String sysTaskCoding = systemCodingService.getSysTaskCoding(sysUser.getBelongAreaId());
			String sysTaskCoding = produceNumber.ProduceTaskNumber(sysUser.getBelongAreaId());
			task.setTaskId(sysTaskCoding);
			//所属部门
			task.setCreatDepartmentId(sysUser.getBelongDepartmentId());
			task.setCreatDepartmentName(sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId()).getDepartmentName());
			Date date = new Date();
			task.setCreatDate( date);
			taskMapper.insertSelective(task);
			//生成快照信息
			historyLawEnforceObjectWithBLOBs.setTaskId(task.getId());
			historyLawEnforceObjectMapper.insertSelective(historyLawEnforceObjectWithBLOBs);
			//流转跟踪信息记录信息
			//1个人，2部门handling_type
			TaskBean taskUtils = new TaskBean();
			SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
			TaskFlow taskFlowBean = taskUtils.taskFlowBean
					(task,sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName(),code.toString(),"任务分配","0", "1", sysUser.getId(), sysUser.getLoginname(), date, null, sysUser.getLoginname(), "0", "发起任务", "2", "处理中", "0", "任务分配", null, null, null, null, null, null,null);
			taskFlowMapper.insertSelective(taskFlowBean);
			//在环节信息表里新建一条环节信息
			//环节状态：1当前环节，0已完成环节
			TaskNode taskNodeBean = taskUtils.TaskNodeBean(task.getId(), "0", "任务分配", "1", null, sysUser.getId(), sysUser.getLoginname(), "0", "任务分配",sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName());
			tasknodeMapper.insertSelective(taskNodeBean);
			//任务专项行动关联表 添加一条信息;
			if(task.getSpecialActionIds()!= null && !"".equals(task.getSpecialActionIds())){
				String specialActionIds = task.getSpecialActionIds();
				String[] split = specialActionIds.split(",");
				for(String specialAction :split){
					TaskSpecial taskSpecial = new TaskSpecial(); 
					taskSpecial.setSpecialActionId(specialAction);
					taskSpecial.setTaskId(task.getId());
					taskSpecialMapper.insertSelective(taskSpecial);
				}
			}
			/*if(!ChangnengUtil.isNull(task.getIsSpecial()) && task.getIsSpecial()==1) {
				SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
				if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
					TaskSpecial taskSpecial = new TaskSpecial(); 
					taskSpecial.setSpecialActionId(sceneSysSpecialModel.getId());
					taskSpecial.setTaskId(taskId);
					taskSpecialMapper.insertSelective(taskSpecial);							
				}
			}*/
			//维护area表
			//维护用户和任务中间表
			String checUserIds = task.getChecUserIds();
			if(checUserIds!= null && !"".equals(checUserIds) ){
				if(!checUserIds.contains(sysUser.getId())){
					checUserIds =	checUserIds.concat(",").concat(sysUser.getId());
				}
			}
			String replaceAll = checUserIds.replaceAll(",", "\',\'");
			List<SysUsers> areaList = sysUserMapper.getAreaList("\'"+replaceAll+"\'");
			for( SysUsers sysUsers :areaList){
				String belongAreaId = sysUsers.getBelongAreaId();
				CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(belongAreaId, task.getId());
				if(codeTable==null){
					CodeTaskTable newCodeTable = new CodeTaskTable();
					newCodeTable.setBelongAreaId(belongAreaId);
					newCodeTable.setTaskId(task.getId());
					codeTaskTableMapper.insertSelective(newCodeTable);
				}
			}
		      if(record!=null && record.getBelongAreaId()!=null){
		        CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(record.getBelongAreaId(), task.getId());
		        if(codeTable==null){
		          CodeTaskTable newCodeTable = new CodeTaskTable();
		          newCodeTable.setBelongAreaId(record.getBelongAreaId());
		          newCodeTable.setTaskId(task.getId());
		          codeTaskTableMapper.insertSelective(newCodeTable);
		        }
		      }
			
			Map<String,String> map = new HashMap<String, String>();
			map.put("taskId", task.getId());
			return json.success("200", "200", "添加任务成功!", null, map);
			
		} catch (Exception e) {
			logger.info("添加任务失败");
			e.printStackTrace();
			throw e;
		}
		}else{
			//修改任务信息
			try {
				Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), task.getLimitTime());
				//获取登录用户的信息
					//在任务信息表里添加一条任务信息
				    task.setNodeTimeoutState(code.toString());
					task.setCreatDepartmentName(sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId()).getDepartmentName());
					task.setId(taskId);
					task.setTaskId(null);
					taskMapper.updateByPrimaryKeySelective(task);
					historyLawEnforceObjectWithBLOBs.setTaskId(task.getId());
					historyLawEnforceObjectMapper.updateBytaskIdAndObjectId(historyLawEnforceObjectWithBLOBs);
					TaskFlow taskFlow =  taskFlowMapper.selectByTaskIdAndTaskFlowId(taskId,taskFlowId);
					 if(taskFlow !=null){
						 //被退回的任务信息保存时，流转表要修改其状态
						 taskFlow.setManagementModeCode("0");
						 taskFlow.setManagementModeName("发起任务");
						 taskFlow.setHandlingStateCode("2");
						 taskFlow.setHandlingStateName("处理中");
						 taskFlow.setNodeTimeoutState(code.toString());
						 taskFlow.setLimitTime(task.getLimitTime());
						 taskFlowMapper.updateByPrimaryKeySelective(taskFlow);
					 }
					 taskSpecialMapper.deleteTaskSpecialByTaskId(taskId);
					String specialActionIds = task.getSpecialActionIds();
					if(specialActionIds != null && !"".equals(specialActionIds)){
						String[] split = specialActionIds.split(",");
						for(String specialAction :split){
							TaskSpecial taskSpecial = new TaskSpecial(); 
							taskSpecial.setSpecialActionId(specialAction);
							taskSpecial.setTaskId(taskId);
							taskSpecialMapper.insertSelective(taskSpecial);
						}
					}else{
						//删除专项行动关联表信息
						taskSpecialMapper.deleteTaskSpecialByTaskId(taskId);
					}
					/*if(!ChangnengUtil.isNull(task.getIsSpecial()) && task.getIsSpecial()==1) {
						SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
						if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
							TaskSpecial taskSpecial = new TaskSpecial(); 
							taskSpecial.setSpecialActionId(sceneSysSpecialModel.getId());
							taskSpecial.setTaskId(taskId);
							taskSpecialMapper.insertSelective(taskSpecial);							
						}
					}*/
					Map<String,String> map = new HashMap<String, String>();
					map.put("taskId", taskId);
				return json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "添加任务成功!", null, map);
			} catch (Exception e) {
				logger.info("添加任务失败");
				e.printStackTrace();
				throw e;
			}
		}
	}
	/**
	 * 根据对象的id查询对象信息
	 */
	@Override
	public LawEnforceObject lawEnforceObjectById(String lawObjectId) {
		return lawEnforceObjectMapper.selectByPrimaryKey(lawObjectId);
	}

	@Override
	public List<TaskRequireFiles> getTaskRequireFilesByTaskId(String taskId) {
		return taskRequireFilesMapper.getTaskRequireFilesByTaskId(taskId);
	}
	@Override
	public PageBeanUserDefined<CheckUserChooseBean> CheckUserChooseList(int pNum,
			int pageSize11, String areaCode, String userName,
			String provinceCode, String cityCode, String checkPersonStatus,String status) {
		if(!ChangnengUtil.isNull(userName)){
			userName = userName.trim();
		}
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		if("".equals(status) || status ==null){
			//查询检查人包含用户自己
			String levelstatus =null;
			String citySubCode = null;
			if( "1".equals(checkPersonStatus) ){
				if((cityCode != null && !"".equals(cityCode)) && (areaCode != null && !"".equals(areaCode)) ){
					//县级查询
					levelstatus  = "3";
				}else if ((cityCode != null && !"".equals(cityCode)) && ("".equals(areaCode)) ){
					//市级查询
					levelstatus = "2";
					citySubCode = cityCode.substring(0, 4);
				}else {
					levelstatus ="1";
				}
				List<CheckUserChooseBean> list = 	sysUserMapper.CheckUserChooseAllList(areaCode,userName,citySubCode,levelstatus,null,(pNum-1)*pageSize11, pNum*pageSize11);
				for(CheckUserChooseBean checkUser : list){
					if(checkUser.getLawCertificateEnabled() ==0){
						checkUser.setLawEnforcId("");
					}
					if(checkUser.getSupervisioCertificateEnabled()==0){
						checkUser.setSupervisionCertificateId("");
					}
				}
				int total= 	 sysUserMapper.CheckUserAllTotal(areaCode,userName,citySubCode,levelstatus,null);
				PageBeanUserDefined<CheckUserChooseBean> pageBean = new PageBeanUserDefined<CheckUserChooseBean>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize11);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize11);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
			}else{
				// 获取登录人的区划code
				// 获取登录用户的信息
				String belongAreaId = sysUser.getBelongAreaId();
				String belongAreaIdTemp = belongAreaId.substring(4);
				citySubCode = belongAreaId.substring(0, 4);
				
				if ("35000000".equals(belongAreaId)) {
					
					levelstatus = "1";
				} 
				if ("0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					levelstatus = "2";
				}
				if (!"0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					levelstatus = "3";
					areaCode = belongAreaId;
				}
				List<CheckUserChooseBean> list = 	sysUserMapper.CheckUserChooseList(areaCode,levelstatus,citySubCode,null,(pNum-1)*pageSize11, pNum*pageSize11);
				for(CheckUserChooseBean checkUser : list){
					if(checkUser.getLawCertificateEnabled() ==0){
						checkUser.setLawEnforcId("");
					}
					if(checkUser.getSupervisioCertificateEnabled()==0){
						checkUser.setSupervisionCertificateId("");
					}
				}
				int total= 	 sysUserMapper.CheckUserTotal(areaCode,levelstatus,citySubCode,null);
				PageBeanUserDefined<CheckUserChooseBean> pageBean = new PageBeanUserDefined<CheckUserChooseBean>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize11);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize11);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
			}
		}else{
			//查询检查人不包含用户自己 
			String levelstatus =null;
			String citySubCode = null;
			if( "1".equals(checkPersonStatus) ){
				if((cityCode != null && !"".equals(cityCode)) && (areaCode != null && !"".equals(areaCode)) ){
					//县级查询
					levelstatus  = "3";
				}else if ((cityCode != null && !"".equals(cityCode)) && ("".equals(areaCode)) ){
					//市级查询
					levelstatus = "2";
					citySubCode = cityCode.substring(0, 4);
				}else {
					levelstatus ="1";
				}
				List<CheckUserChooseBean> list = 	sysUserMapper.CheckUserChooseAllList(areaCode,userName,citySubCode,levelstatus,sysUser.getId(),(pNum-1)*pageSize11, pNum*pageSize11);
				for(CheckUserChooseBean checkUser : list){
					if(checkUser.getLawCertificateEnabled() ==0){
						checkUser.setLawEnforcId("");
					}
					if(checkUser.getSupervisioCertificateEnabled()==0){
						checkUser.setSupervisionCertificateId("");
					}
				}
				int total= 	 sysUserMapper.CheckUserAllTotal(areaCode,userName,citySubCode,levelstatus,sysUser.getId());
				PageBeanUserDefined<CheckUserChooseBean> pageBean = new PageBeanUserDefined<CheckUserChooseBean>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize11);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize11);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
			}else{
				// 获取登录人的区划code
				// 获取登录用户的信息
				String belongAreaId = sysUser.getBelongAreaId();
				String belongAreaIdTemp = belongAreaId.substring(4);
				citySubCode = belongAreaId.substring(0, 4);
				if ("35000000".equals(belongAreaId)) {
					levelstatus = "1";
				} 
				if ("0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					levelstatus = "2";
				}
				if (!"0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
					levelstatus = "3";
					areaCode = belongAreaId;
				}
				List<CheckUserChooseBean> list = 	sysUserMapper.CheckUserChooseList(areaCode,levelstatus,citySubCode,sysUser.getId(),(pNum-1)*pageSize11, pNum*pageSize11);
				for(CheckUserChooseBean checkUser : list){
					if(checkUser.getLawCertificateEnabled() ==0){
						checkUser.setLawEnforcId("");
					}
					if(checkUser.getSupervisioCertificateEnabled()==0){
						checkUser.setSupervisionCertificateId("");
					}
				}
				int total= 	 sysUserMapper.CheckUserTotal(areaCode,levelstatus,citySubCode,sysUser.getId());
				PageBeanUserDefined<CheckUserChooseBean> pageBean = new PageBeanUserDefined<CheckUserChooseBean>();
				pageBean.setList(list);
				pageBean.setPageNum(pageSize11);
				pageBean.setPages(pNum); //总页数
				pageBean.setSize(total/pageSize11);  //总页数
				pageBean.setTotal(total); //总记录数
				return pageBean;
			}
		}
	}
	@Override
	public ResponseJson textpdf() {
		return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), null,null,null);
	}
	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseJson jcblTaskSave(Task task,String gisCoordinateX,String gisCoordinateY) throws Exception {
		ResponseJson json = new ResponseJson();
		String taskFromName = task.getTaskFromName();
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		if(!"".equals(taskFromName) && taskFromName != null){
			String[] tempTaskFromName = taskFromName.split("#");
			if(tempTaskFromName.length != 0){
				task.setTaskFromName(tempTaskFromName[0]);
				task.setTaskFromCode(tempTaskFromName[1]);
			}
		}
		String cardTypeName = task.getCardTypeName();
		if(!"1".equals(task.getLawObjectType()) && !"4".equals(task.getLawObjectType())  ){
		if( !"".equals(cardTypeName) && cardTypeName != null){
			String[] tempCardTypeName = cardTypeName.split("#");
			if(tempCardTypeName.length != 0 ){
				task.setCardTypeName(tempCardTypeName[0]);
				task.setCardTypeCode(tempCardTypeName[1]);
			}
		}}
		try {
		//维护执法对象信息 保存或者修改都维护 
		LawEnforceObjectWithBLOBs record = lawEnforceObjectMapper.selectByPrimaryKey(task.getLawObjectId());
		historyLawEnforceObjectWithBLOBs historyLawEnforceObjectWithBLOBs = new historyLawEnforceObjectWithBLOBs();
		BeanUtils.copyProperties(record, historyLawEnforceObjectWithBLOBs);
		historyLawEnforceObjectWithBLOBs.setLawObjectId(record.getId());
		historyLawEnforceObjectWithBLOBs.setId(null);
		String lawObjectType = task.getLawObjectType();
		SysDepartment sysDepartment = null;
		String lawObjectId = task.getLawObjectId();
		String address = task.getAddress();
		String cardTypeCode = task.getCardTypeCode();
		String cardTypeName2 = task.getCardTypeName();
		String cardCode = task.getCardCode();
		String linkman = task.getLinkman();
		String legalPhone = task.getLegalPhone();
		//V2.0.3 新增
		//小产乱污
		String xclw = task.getXslw();
		//适用排污许可行业技术规范
		String sypwxkhyjsgfCode = task.getSypwxkhyjsgfCode();
		String sypwxkhyjsgfName = task.getSypwxkhyjsgfName();


		String sewageClassify = task.getSewageClassify();
		String managementType = task.getManagementType();
		String licenseNumber = task.getLicenseNumber();
		String certifyingAuthority = task.getCertifyingAuthority();
		String sewageClassifyName = task.getSewageClassifyName();
/*2020.03.06 wjy新增是否发证*/
		String isCertification = task.getIsCertification();

		
		historyLawEnforceObjectWithBLOBs.setXslw(xclw);
		historyLawEnforceObjectWithBLOBs.setSypwxkhyjsgfCode(sypwxkhyjsgfCode);
		historyLawEnforceObjectWithBLOBs.setSypwxkhyjsgfName(sypwxkhyjsgfName);
		historyLawEnforceObjectWithBLOBs.setAddress(address);
		historyLawEnforceObjectWithBLOBs.setGisCoordinateX(gisCoordinateX);
		historyLawEnforceObjectWithBLOBs.setGisCoordinateY(gisCoordinateY);


		historyLawEnforceObjectWithBLOBs.setSewageClassify(sewageClassify);
		historyLawEnforceObjectWithBLOBs.setManagementType(managementType);
		historyLawEnforceObjectWithBLOBs.setLicenseNumber(licenseNumber);
		historyLawEnforceObjectWithBLOBs.setCertifyingAuthority(certifyingAuthority);
		historyLawEnforceObjectWithBLOBs.setSewageClassifyName(sewageClassifyName);
			/*2020.03.06 wjy新增是否发证*/
		historyLawEnforceObjectWithBLOBs.setIsCertification(isCertification);
		//后台入库84坐标系坐标
		if(!ChangnengUtil.isNull(historyLawEnforceObjectWithBLOBs.getGisCoordinateX())&&!ChangnengUtil.isNull(historyLawEnforceObjectWithBLOBs.getGisCoordinateY())){
			double bd_lat = Double.parseDouble(historyLawEnforceObjectWithBLOBs.getGisCoordinateY());//纬度
			double bd_lon = Double.parseDouble(historyLawEnforceObjectWithBLOBs.getGisCoordinateX());//经度
			Gps gps84 = PositionUtil.bd09_To_Gps84(bd_lat, bd_lon);
			historyLawEnforceObjectWithBLOBs.setGisCoordinateX84(String.format("%.6f", gps84.getWgLon()));//84坐标经度
			historyLawEnforceObjectWithBLOBs.setGisCoordinateY84(String.format("%.6f", gps84.getWgLat()));//84坐标纬度
		}
		if("1".equals(lawObjectType)){
			//企业类型
			historyLawEnforceObjectWithBLOBs.setChargePerson(linkman);
			historyLawEnforceObjectWithBLOBs.setChargePersonPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setSocialCreditCode(cardCode);
		}else if("2".equals(lawObjectType)){
			//个人
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
			historyLawEnforceObjectWithBLOBs.setPersoncardTypeName(cardTypeName2);
		}else if("3".equals(lawObjectType)){
			//个体
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}else if("4".equals(lawObjectType)){
			//自然保护区
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setOrgCode(cardCode);
		}else{
			//无助
			historyLawEnforceObjectWithBLOBs.setLegalPerson(linkman);
			historyLawEnforceObjectWithBLOBs.setLegalPhone(legalPhone);
			historyLawEnforceObjectWithBLOBs.setCardTypeName(cardTypeName2);
			historyLawEnforceObjectWithBLOBs.setCardNumber(cardCode);
		}
		
		   sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
		if("1".equals(task.getUpdateObjectState())){
			//同步污染源信息
			//维护执法对象信息 保存或者修改都维护 
			LawEnforceObjectWithBLOBs oldBLOBS = new LawEnforceObjectWithBLOBs();
			BeanUtils.copyProperties(record, oldBLOBS);
			record.setId(lawObjectId);
			record.setAddress(address);
			record.setGisCoordinateX(gisCoordinateX);
			record.setGisCoordinateY(gisCoordinateY);
			
			//后台入库84坐标系坐标
			if(!ChangnengUtil.isNull(record.getGisCoordinateX())&&!ChangnengUtil.isNull(record.getGisCoordinateY())){
				double bd_lat = Double.parseDouble(record.getGisCoordinateY());//纬度
				double bd_lon = Double.parseDouble(record.getGisCoordinateX());//经度
				Gps gps84 = PositionUtil.bd09_To_Gps84(bd_lat, bd_lon);
				record.setGisCoordinateX84(String.format("%.6f", gps84.getWgLon()));//84坐标经度
				record.setGisCoordinateY84(String.format("%.6f", gps84.getWgLat()));//84坐标纬度
			}
			//不为空时 回写小产乱污
			if(!ChangnengUtil.isNull(xclw)) {
				record.setXslw(xclw);
				
			}
			//不为空时回写排污许可行业技术规范
			if(!ChangnengUtil.isNull(sypwxkhyjsgfCode)) {
				record.setSypwxkhyjsgfCode(sypwxkhyjsgfCode);
				record.setSypwxkhyjsgfName(sypwxkhyjsgfName);
				record.setSewageClassify(sewageClassify);
				record.setManagementType(managementType);
				record.setLicenseNumber(licenseNumber);
				record.setCertifyingAuthority(certifyingAuthority);
				record.setSewageClassifyName(sewageClassifyName);
				record.setIsCertification(isCertification);
				//
			}
			
			if("1".equals(lawObjectType)){
				//企业类型
				record.setChargePerson(linkman);
				record.setChargePersonPhone(legalPhone);
				record.setSocialCreditCode(cardCode);
			}
			else if("2".equals(lawObjectType)){
				//个人
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardNumber(cardCode);
				record.setPersoncardTypeCode(cardTypeCode);
				record.setPersoncardTypeName(cardTypeName2);
			}else if("3".equals(lawObjectType)){
				//个体
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeCode(cardTypeCode);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
				
			}else if("4".equals(lawObjectType)){
				//自然保护区
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setOrgCode(cardCode);
				
			}else{
				//无助
				record.setLegalPerson(linkman);
				record.setLegalPhone(legalPhone);
				record.setCardTypeCode(cardTypeCode);
				record.setCardTypeName(cardTypeName2);
				record.setCardNumber(cardCode);
			}
			record.setUpdatetime(new Date());
				lawEnforceObjectMapper.updateByPrimaryKeySelective(record);
				sysUser.setBelongDepartmentName(sysDepartment.getDepartmentName());
				List<LawobjUpdateLog> contrastLawObjectModified =  CompareObjModified.contrastLawObjectModified(oldBLOBS, record, sysUser); 
				if(contrastLawObjectModified.size()>0){
					lawobjUpdateLogMapper.insertSelectiveList(contrastLawObjectModified);
				}
		}
	
			Integer  code =  ChangnengUtil.getTaskTimeoutState(new Date(), task.getLimitTime());
			//在任务信息表里添加一条任务信息
			task.setCreatUserId(sysUser.getId());
			task.setCreatUserName(sysUser.getLoginname());
			task.setTaskStateCode("1");
			task.setTaskStateName("办理中");
			task.setNodeTimeoutState(code.toString());
			task.setBelongYear(Integer.toString(DateUtil.year()));
			task.setQuarter(Integer.toString(DateUtil.getSeason(new Date())));
			if(!ChangnengUtil.isNull(task.getTaskFromType())){
				if(task.getTaskFromType() ==1){
					task.setTaskFromType(1);
					task.setTaskFromTypeName("非特定专项检查");
					//标记状态为直接现场执法
					task.setTaskLawObjectStatus("1");
					//维护isSpecial
					task.setIsSpecial(0);
				}else{
					SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
					if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
						task.setTaskFromTypeName(sceneSysSpecialModel.getTemplateName());
					}
					task.setTaskLawObjectStatus("3");
					task.setIsSpecial(1);

				}
			}else{
				task.setTaskFromType(1);
				task.setTaskFromTypeName("非特定专项检查");
				task.setTaskLawObjectStatus("1");
				task.setIsSpecial(0);
			}
			//task.setTaskFromType(1);
			//task.setTaskFromTypeName("非特定专项检查");
			//维护任务的taskID
			//String sysTaskCoding = systemCodingService.getSysTaskCoding(sysUser.getBelongAreaId());
			String sysTaskCoding = produceNumber.ProduceTaskNumber(sysUser.getBelongAreaId());
			task.setTaskId(sysTaskCoding);
			task.setSynchronizationStatus(Integer.valueOf(task.getUpdateObjectState()));
			task.setCreatDepartmentId(sysUser.getBelongDepartmentId());
			String departmentName = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId()).getDepartmentName();
			task.setCreatDepartmentName(departmentName);
			task.setCreatDate( new Date());
			//标记状态为直接现场执法
			//task.setTaskLawObjectStatus("1");
			taskMapper.insertSelective(task);
			//生成快照信息
			historyLawEnforceObjectWithBLOBs.setTaskId(task.getId());
			historyLawEnforceObjectMapper.insertSelective(historyLawEnforceObjectWithBLOBs);
			TaskFlow	taskFlowBean = new TaskBean().taskFlowBean(task,sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName(),code.toString(), "任务分配",
					"0", null,sysUser.getId(), sysUser.getLoginname(), 
					new Date(), new Date(),sysUser.getLoginname(), "0", "发起任务", "3", "已完成", "0", "任务分配",
					null, null, null, null, null, null,new Date());
			taskFlowMapper.insertSelective(taskFlowBean);
			//1个人，2部门handling_type
			TaskBean taskUtils = new TaskBean();
			//任务流转表 实际办理人是发起人
			TaskFlow taskFlowBean2 = taskUtils.taskFlowBean(task,sysUser.getBelongDepartmentId(),departmentName, code.toString(),
					"现场执法","1", null, sysUser.getId(), sysUser.getLoginname(), new Date(),
					new Date(), sysUser.getLoginname(), "3","开始办理", "2", "处理中", "1", "办理中",null, null, null, null, null,null,null);
			taskFlowMapper.insertSelective(taskFlowBean2);
			//在环节信息表里新建一条环节信息
			TaskNode taskNodeBean = taskUtils.TaskNodeBean(task.getId(),
					"0", "任务分配", "0", null, sysUser.getId(),
					sysUser.getLoginname(), "0", "任务分配",sysUser.getBelongDepartmentId(),sysDepartment.getDepartmentName());
			tasknodeMapper.insertSelective(taskNodeBean);
			//环节状态：1当前环节，0已完成环节
			TaskNode taskNodeBean2 = taskUtils.TaskNodeBean(task.getId(), "1", "现场执法", "1", null, sysUser.getId(), sysUser.getLoginname(), "1", "办理中",sysUser.getBelongDepartmentId(),departmentName);
			tasknodeMapper.insertSelective(taskNodeBean2);
			//维护流转表信息
			//任务流转表 实际办理人是发起人
			//维护用户和任务中间表
			String checUserIds = task.getChecUserIds();
			if(checUserIds!= null && !"".equals(checUserIds) ){
				if(!checUserIds.contains(sysUser.getId())){
					checUserIds =	checUserIds.concat(",").concat(sysUser.getId());
				}
			}
			String[] checUserIdsTemp = checUserIds.split(",");
			if(checUserIdsTemp != null && checUserIdsTemp.length >0){
				for(int i=0;i<checUserIdsTemp.length;i++){
					UserTaskTable userTaskTable = new UserTaskTable();
					userTaskTable.setTaskId(task.getId());
					userTaskTable.setSysUserId(checUserIdsTemp[i]);
					userTaskTableMapper.insertSelective(userTaskTable);
				}
			}
			//维护area表
			String replaceAll = checUserIds.replaceAll(",", "\',\'");
			List<SysUsers> areaList = sysUserMapper.getAreaList("\'"+replaceAll+"\'");
			for( SysUsers sysUsers :areaList){
				String belongAreaId = sysUsers.getBelongAreaId();
				CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(belongAreaId, task.getId());
				if(codeTable==null){
					CodeTaskTable newCodeTable = new CodeTaskTable();
					newCodeTable.setBelongAreaId(belongAreaId);
					newCodeTable.setTaskId(task.getId());
					codeTaskTableMapper.insertSelective(newCodeTable);
				}
			}
			 if(record!=null && record.getBelongAreaId()!=null){
			        CodeTaskTable codeTable  =  codeTaskTableMapper.selectCodeTaskTableByCodeAndTaskId(record.getBelongAreaId(), task.getId());
			        if(codeTable==null){
			          CodeTaskTable newCodeTable = new CodeTaskTable();
			          newCodeTable.setBelongAreaId(record.getBelongAreaId());
			          newCodeTable.setTaskId(task.getId());
			          codeTaskTableMapper.insertSelective(newCodeTable);
			        }
			      }
			//任务专项行动关联表 添加一条信息;
			if(task.getSpecialActionIds()!= null && !"".equals(task.getSpecialActionIds())){
				String specialActionIds = task.getSpecialActionIds();
				String[] split = specialActionIds.split(",");
				for(String specialAction :split){
					TaskSpecial taskSpecial = new TaskSpecial(); 
					taskSpecial.setSpecialActionId(specialAction);
					taskSpecial.setTaskId(task.getId());
					taskSpecialMapper.insertSelective(taskSpecial);
				}
			}
			/*if(!ChangnengUtil.isNull(task.getIsSpecial()) && task.getIsSpecial()==1) {
				SceneSysSpecialModel sceneSysSpecialModel = sceneSysSpecialModelMapper.selectByTemplateType(task.getTaskFromType().toString());
				if(!ChangnengUtil.isNull(sceneSysSpecialModel)) {
					TaskSpecial taskSpecial = new TaskSpecial(); 
					taskSpecial.setSpecialActionId(sceneSysSpecialModel.getId());
					taskSpecial.setTaskId(task.getId());
					taskSpecialMapper.insertSelective(taskSpecial);							
				}
			}*/
			//row.id+"','"+row.taskId+"','"+row.lawObjectType+"',1,'"+row.nodeCode
			String taskId = task.getId();
			Map map = new  HashMap<String, String>();
			map.put("taskId", taskId);
			map.put("lawObjectType", lawObjectType);
			map.put("status", "1");
			map.put("nodeCode", "1");
			map.put("taskFlowId",taskFlowBean2.getId());
			
			// 双随机下发任务成功后，发送消息，通知到被分配的人  lhl 20171227
			newInfoTableService.creatSendTaskInsertNewInfoTable(task);
			
			return json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "发起现场执法成功!", "发起现场执法成功!", map);
		} catch (Exception e) {
			//throw new BusinessException("添加任务失败");
			e.printStackTrace();
			throw e;
		}
	}

	@Override
	public TaskRequireFiles selectByPrimaryKeyVal(String id) {
		return taskRequireFilesMapper.selectByPrimaryKey(id);
	}

	@Override
	public void deletefileInfo(String id) {
		TaskRequireFiles selectByPrimaryKey = taskRequireFilesMapper.selectByPrimaryKey(id);
		taskRequireFilesMapper.deletefileInfo(id);
		//设置系统文件为脏数据
		sysFilesMapper.updateSysFileDidable(selectByPrimaryKey.getFileId());
	}

	@Override
	public TaskRequireFiles findDocUrlByTaskRequireFilesId(String taskRequireFilesId) {
		return  taskRequireFilesMapper.selectByPrimaryKey(taskRequireFilesId);
	}


	@Override
	public void taskManagerXczfZjfjSave(String taskId, List<SysFiles> list) {
		
		try {
			for (SysFiles sf:list) {
				TaskRequireFiles trf = new TaskRequireFiles();
				trf.setTaskId(taskId);
				trf.setFileId(sf.getId());
				trf.setFileName(sf.getFileName());
				trf.setFileUrl(sf.getFileUrl());
				trf.setFileType(sf.getFileType());
				trf.setTaskId(taskId);
				taskRequireFilesMapper.insertSelective(trf);
				sysFilesMapper.updateSysFile(sf.getId());
			}
		} catch (Exception e) {
		}
	}

	@Override
	public List<CheckUserChooseBean> chickUserInfo(String checUserIds) {
		List<CheckUserChooseBean> cucblist = new ArrayList<CheckUserChooseBean>();
		if(checUserIds != null && !"".equals(checUserIds)){
			String[] split = checUserIds.split(",");
			if(split!=null && split.length>0){
				for(String id :split){
					CheckUserChooseBean cucb  = new CheckUserChooseBean();
					SysUsers sysUser = sysUserMapper.selectByPrimaryKey(id);
					if(ChangnengUtil.isNull(sysUser.getSupervisioCertificateEnabled())&&sysUser.getLawCertificateEnabled()==0){
						sysUser.setLawEnforcId("无");
						sysUser.setSupervisionCertificateId("无");
					}
					if(ChangnengUtil.isNull(sysUser.getSupervisioCertificateEnabled()) && ChangnengUtil.isNull(sysUser.getLawCertificateEnabled())){
						sysUser.setLawEnforcId("无");
						sysUser.setSupervisionCertificateId("无");
					}
					BeanUtils.copyProperties(sysUser, cucb);
					SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
					if(sysDepartment != null ){
						String departmentName = sysDepartment.getDepartmentName();
						cucb.setBelongDepartmentName(departmentName);
					}
					cucblist.add(cucb);
				}
			}
		}
		return cucblist;
	}
	@Override
	public List<SceneSysSpecialModel> specialActionInfo(String specialActionIds) {
		List<SceneSysSpecialModel> specialActionList = new ArrayList<SceneSysSpecialModel>();
		if(specialActionIds != null && !"".equals(specialActionIds)){
			String[] split = specialActionIds.split(",");
			if(split!=null && split.length>0){
				for(String id :split){
					SceneSysSpecialModel specialAction = specialActionMapper.selectByPrimaryKey(id);
					if(specialAction != null ){
						specialActionList.add(specialAction);
					}
				}
			}
		}
		return specialActionList;
	}

	@Override
	public PageBeanUserDefined<LawEnforceObject> conditionLawObjectList(
			int pNum, int pageSize, String areaCode, String provinceCode,
			String cityCode, String objectName, String address,
			String lawObjectType, String swingTagSnapInId, String lawObjectIds) {
		String status =null;
		String citySubCode = null;
		String  belongAreacode = null;
		if(!ChangnengUtil.isNull(swingTagSnapInId)){
			SysDepartment sysDepartment = sysDepartmentMapper.selectByPrimaryKey(swingTagSnapInId);
			belongAreacode = sysDepartment.getBelongAreacode();
		}
		if((cityCode != null && !"".equals(cityCode)) && (areaCode != null && !"".equals(areaCode)) ){
			//县级查询
			 status  = "3";
		}else if ((cityCode != null && !"".equals(cityCode)) && ("".equals(areaCode)) ){
			//市级查询
			status = "2";
			citySubCode = cityCode.substring(0, 4);
		}else {
			status ="1";
		}
		List<LawEnforceObject> list = 	lawEnforceObjectMapper.conditionLawListAllList(areaCode,objectName,address,status,citySubCode,lawObjectType,(pNum-1)*pageSize, pNum*pageSize,lawObjectIds,belongAreacode);
		int total= 	 lawEnforceObjectMapper.conditionLawListAllListTotal(areaCode,objectName,address,status,citySubCode,lawObjectType,lawObjectIds,belongAreacode);
		PageBeanUserDefined<LawEnforceObject> pageBean = new PageBeanUserDefined<LawEnforceObject>();
		pageBean.setList(list);
		pageBean.setPageNum(pageSize);
		pageBean.setPages(pNum); //总页数
		pageBean.setSize(total/pageSize);  //总页数
		pageBean.setTotal(total); //总记录数
		return pageBean;
	}


	@Override
	public PageBean<SceneSysSpecialModel> specialActionList2(int pNum, int pageSize11, String status,
			SysUsers sysUser, SceneSysSpecialModel specialTask) throws BusinessException{
		    PageHelper.startPage(pNum,pageSize11);
		    String code = sysUser.getBelongAreaId();
		       // 由于我们是福建省的区划，那么判断用户是否已经是最高和最低两个极端权限，来书写尽量少去like
				if (code == null || "".equals(code)) {
					throw new BusinessException("用户区划code为空");
				}
				if ("000000".equals(code.substring(2))) {// 省
					//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
					if ("0".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA ) ");
					} else if ("1".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
					}else if("2".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(" ( select SCENE_SYSSPECAIL_ID from SPECIAL_AREA MINUS	SELECT SCENE_SYSSPECAIL_ID FROM	SPECIAL_AREA WHERE area_code = '" + code + "' )  ");
					} else {
						throw new BusinessException("查询等级不明确");
					}
				} else if ("0000".equals(code.substring(4))) {// 市
					//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
					if ("0".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA where  area_code like '"
								+ code.substring(0, 4) + "%'  )  ");
					} else if ("1".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA where  area_code = '" + code + "' )  ");
					}else if("2".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select  SCENE_SYSSPECAIL_ID from SPECIAL_AREA MINUS	SELECT  SCENE_SYSSPECAIL_ID	FROM SPECIAL_AREA WHERE area_code like '"+code.substring(0, 4) + "%'"+")  ");
					}  else {
						throw new BusinessException("查询等级不明确");
					}
				} else if ("00".equals(code.substring(6))) {// 县
					//isLevelCity是否适用本单位  0.请选择  查询本级及下级  1. 查询本级  2.查询下级
					if ("0".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select distinct SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
					} else if ("1".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA  where area_code = '" + code + "' )  ");
					}else if("2".equals(specialTask.getIsLevelCity())) {
						specialTask.setMainJoinSql(
								" ( select  SCENE_SYSSPECAIL_ID FROM SPECIAL_AREA MINUS SELECT SCENE_SYSSPECAIL_ID FROM	SPECIAL_AREA WHERE area_code ='"+code+"')  ");
					}  else {
						throw new BusinessException("查询等级不明确");
					}
				} else {
					specialTask.setMainJoinSql(null);
				}

				if (specialTask.getMainJoinSql() == null || "".equals(specialTask.getMainJoinSql())) {
					throw new BusinessException("sql语句无法组装");
				}
				if("1".equals(status)){
					//status为1的逻辑不明确  本次没改造
					return new PageBean<SceneSysSpecialModel>(specialActionMapper.selectSpecialActionList());
				}else{
		            return new PageBean<SceneSysSpecialModel>(specialActionMapper.specialActionList2(specialTask));
				}
	}


	@Override
	public PageBean<SceneSysSpecialModel> appGetspecialActionList(int pNum, int pageSize, String status, SysUsers sysUser) {
		PageHelper.startPage(pNum,pageSize);
		//查询本级和上级
		String belongAreaId = sysUser.getBelongAreaId();
		String belongAreaIdTemp = belongAreaId.substring(4);
		String arealevel = null;
		String code = belongAreaId.substring(0, 4);
		if ("35000000".equals(belongAreaId)) {
			arealevel = "1";
		} 
		if ("0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
			arealevel = "2";
		}
		if (!"0000".equals(belongAreaIdTemp) && !"35000000".equals(belongAreaId)) {
			arealevel = "3";
		}
		if("1".equals(status)){
			return new PageBean<SceneSysSpecialModel>(sceneSysSpecialModelMapper.appGetSpecialActionList());
		}else{
			//1.6待改造
           // return new PageBean<SceneSysSpecialModel>(specialActionMapper.specialActionList(arealevel,belongAreaId,code.concat("0000")));
			return null;
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class)
	public void lawEnforSummarySave(Task task) throws Exception {
		if(ChangnengUtil.isNull(task.getCheckSummary())) {
			throw new Exception("监察小结不能为空！");
		}
		if(ChangnengUtil.isNull(task.getIsLawEnforSummary())) {
			task.setIsLawEnforSummary("1");
			task.setLawEnforSummaryTime(new Date());
		}
		int count = taskMapper.updateByPrimaryKeySelective(task);
	}


	@Override
	public Task lawEnforSummaryInfo(String taskId) {
		Task task = taskMapper.selectZCXJByTaskId(taskId);
		return task;
	}


	@Override
	public APIResponseJson lawEnforSummarySaveForAPP(Task task) {
		APIResponseJson json = new APIResponseJson();
		if(ChangnengUtil.isNull(task.getCheckSummary())) {
			json.success("400", "400", "监察小结不能为空！", "监察小结不能为空！", null);
			return json;
		}
		if(ChangnengUtil.isNull(task.getIsLawEnforSummary())) {
			task.setIsLawEnforSummary("1");
			task.setLawEnforSummaryTime(new Date());
		}
		int count = taskMapper.updateByPrimaryKeySelective(task);
		if(count==1) {
			json.success(HttpStatus.OK.toString(), SystemStatusCode.UPDATE_SUCCESS.toString(), "保存成功", "保存成功", null);
			return json;
		}else {
			json.failure(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.UPDATE_FAILURE.toString(), "系统错误，保存失败","保存失败", null);
			return json;
		}
	}




}
