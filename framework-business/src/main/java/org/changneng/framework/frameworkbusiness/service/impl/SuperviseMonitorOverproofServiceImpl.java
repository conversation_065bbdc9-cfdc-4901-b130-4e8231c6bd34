package org.changneng.framework.frameworkbusiness.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.*;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.supervise.*;
import org.changneng.framework.frameworkbusiness.service.ISuperviseMonitorOverproofService;
import org.changneng.framework.frameworkcore.utils.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigInteger;
import java.net.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("iSuperviseMonitorOverproofService")
public class SuperviseMonitorOverproofServiceImpl implements ISuperviseMonitorOverproofService {

    //日志引擎
    private static Logger logger = LogManager.getLogger(SuperviseMonitorOverproofServiceImpl.class.getName());

    //从配置文件中获取监督性监测废水的webservice接口方法
    private static final String WASTE_WATER_METHOD = PropertiesHandlerUtil.getValue("WASTE_WATER_METHOD", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测废气的webservice接口方法
    private static final String WASTE_GAS_METHOD = PropertiesHandlerUtil.getValue("WASTE_GAS_METHOD", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测点位的webservice接口方法
    private static final String POINT_INFO_METHOD = PropertiesHandlerUtil.getValue("POINT_INFO_METHOD", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测企业基本信息的webservice接口方法
    private static final String STANDARD_ENTERPRISE_METHOD = PropertiesHandlerUtil.getValue("STANDARD_ENTERPRISE_METHOD", "supervise_monitor_overproof");
    //从配置文件中获取企业基本信息专调的webservice接口方法
    private static final String STANDARD_ENTERPRISE_PROPRIETARY = PropertiesHandlerUtil.getValue("STANDARD_ENTERPRISE_PROPRIETARY", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测的webservice接口的处理方法
    private static final String WEBSERVICE_METHOD = PropertiesHandlerUtil.getValue("WEBSERVICE_METHOD", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测的webservice接口的命名空间
    private static final String QNAME_VALUE = PropertiesHandlerUtil.getValue("QNAME_VALUE", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测的webservice接口的地址
    private static final String WEBSERVICE_URL = PropertiesHandlerUtil.getValue("WEBSERVICE_URL", "supervise_monitor_overproof");
    //从配置文件中获取监督性监测的webservice接口的TOKEN
    private static final String WEBSERVICE_TOKEN = PropertiesHandlerUtil.getValue("WEBSERVICE_TOKEN", "supervise_monitor_overproof");
    //从配置文件中获取获取污染源统一编码的URL
    private static final String STANDARDID_URL = PropertiesHandlerUtil.getValue("STANDARDID_URL", "supervise_monitor_overproof");

    //从配置文件中获STANDARDID_URL_NEW取获取污染源统一编码的URL新的 生态云一企一档
    private static final String STANDARDID_URL_NEW = PropertiesHandlerUtil.getValue("STANDARDID_URL_NEW", "supervise_monitor_overproof");

    //###	//生态云互联网地址=从亲情平台污染源库获取获取污染源统一编码的URL
    private static final String STANDARDID_URL_QQPT = PropertiesHandlerUtil.getValue("STANDARDID_URL_QQPT", "supervise_monitor_overproof");

    // 获取Get请求地址
    private static final String HttpURLConnection = PropertiesHandlerUtil.getValue("HttpURLConnection", "supervise_monitor_overproof");


    @Autowired
    private StandardEnterpriseMapper standardEnterpriseMapper;

    @Autowired
    private PointInfoMapper pointInfoMapper;

    @Autowired
    private WasteWaterMapper wasteWaterMapper;

    @Autowired
    private WasteGasMapper wasteGasMapper;

    @Autowired
    private LawEnforceObjectMapper lawEnforceObjectMapper;

    /**
     * 同步废水废气数据
     */
    @Override
    //@Transactional(rollbackFor=Exception.class)
    public void syncDataWasteWaterAndGas(String checkDate, String updatetimeRjwa) {
        try {
            logger.info(" 开始 : --------------------------------------------------------------------------");

            // 存放废水数据
            List<WasteWater> wasteWaterList = new ArrayList<WasteWater>();
            wasteWater(wasteWaterList, checkDate, updatetimeRjwa);
            // 存放废气数据
            List<WasteGas> wasteGasList = new ArrayList<WasteGas>();
            exhaustGasData(wasteGasList, checkDate, updatetimeRjwa);
            // 废水的超标信息 
            int wasteWaterListSize = wasteWaterList.size();
            // 废气的超标信息
            int wasteGasListSize = wasteGasList.size();
            // 创建Set集合用来去重           
            TreeSet<String> pointInfoTreeSet = new TreeSet<String>();
            int cont = wasteWaterListSize >= wasteGasListSize ? wasteWaterListSize : wasteGasListSize;
            for (int i = 0; i < cont; i++) {
                if (i < wasteWaterListSize) {
                    pointInfoTreeSet.add(wasteWaterList.get(i).getCpId());
                }
                if (i < wasteGasListSize) {
                    pointInfoTreeSet.add(wasteGasList.get(i).getCpId());
                }
            }
            // 存放点位信息
            List<PointInfo> pointInfoList = new ArrayList<PointInfo>();
            // 点位数据
            pointPosition(pointInfoList, pointInfoTreeSet, updatetimeRjwa);
            // 存放企业基本信息
            List<StandardEnterprise> standardEnterpriseList = new ArrayList<StandardEnterprise>();
            // 使用Set来去重点位ID
            TreeSet<String> enterpriseListTreeSet = new TreeSet<String>();
            pointInfoList.stream().forEach(n -> {
                enterpriseListTreeSet.add(n.getComId());
            });
            // 企业信息
            enterpriseInformation(standardEnterpriseList, enterpriseListTreeSet, updatetimeRjwa);

            /**入库操作开始**/
            //点位信息分批入库，每次500条,否则数据量大时会报错
            PageModel<StandardEnterprise> batchInsertEnterprise = new PageModel<StandardEnterprise>(standardEnterpriseList, 500);
            for (int i = 0; i < batchInsertEnterprise.getTotalPages(); i++) {
                //企业基本信息入库操作,插入或更新
                standardEnterpriseMapper.insertBatch(batchInsertEnterprise.getObjects(i + 1));
            }
            //点位信息分批入库，每次500条,否则数据量大时会报错
            PageModel<PointInfo> batchInsertPoint = new PageModel<PointInfo>(pointInfoList, 500);
            for (int i = 0; i < batchInsertPoint.getTotalPages(); i++) {
                //点位信息入库操作，插入或更新
                pointInfoMapper.insertBatch(batchInsertPoint.getObjects(i + 1));
            }

            //废水信息分批入库，每次500条,否则数据量大时会报错
            PageModel<WasteWater> batchInsertWater = new PageModel<WasteWater>(wasteWaterList, 500);
            for (int i = 0; i < batchInsertWater.getTotalPages(); i++) {
                //废水信息入库操作，插入或更新、
                wasteWaterMapper.insertBatch(batchInsertWater.getObjects(i + 1));
            }

            //废气信息分批入库，每次500条,否则数据量大时会报错
            PageModel<WasteGas> batchInsertGas = new PageModel<WasteGas>(wasteGasList, 500);
            for (int i = 0; i < batchInsertGas.getTotalPages(); i++) {
                //废气信息入库操作，插入或更新、
                wasteGasMapper.insertBatch(batchInsertGas.getObjects(i + 1));
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }


    /**
     * 根据日期组合超标数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateSuperviseMonitorOverproof(String startDate, String endDate) {
        standardEnterpriseMapper.generateSuperviseMonitorOverproof(startDate, endDate);
    }

    /**
     * 同步企业统一编码
     *
     * @param type 不为空则增量更新
     */
    @Override
    public void syncStandenterid(String type) {
        syncStandenteridNew();
        /**
         //try {
         List<LawEnforceObject> list= new ArrayList();
         if(ChangnengUtil.isNull(type)) {//如果为空则全量更新
         list = lawEnforceObjectMapper.queryOrgCodeandSocialCreditCode(type);
         }else {//如果不为空则增量更新
         list = lawEnforceObjectMapper.queryOrgCodeandSocialCreditCode(type);
         }


         List<LawEnforceObject> listUpdate=new ArrayList<LawEnforceObject>();

         int forSize=list.size();

         int pi = 0;

         for (int i = 0; i <forSize; i++) {
         LawEnforceObject law=list.get(i);
         int cou=i+1;
         logger.error("第"+cou+"次调用");
         String result="";
         String url=STANDARDID_URL;
         StringBuilder surl = new StringBuilder();
         \
         try {
         if((law.getOrgCode()!=null&&!"".equals(law.getOrgCode()))&&(law.getSocialCreditCode()!=null&&!"".equals(law.getSocialCreditCode()))){
         surl.append("code="+URLEncoder.encode(law.getSocialCreditCode(),"utf-8"));
         }else if(law.getOrgCode()!=null&&!"".equals(law.getOrgCode())){
         surl.append("code="+URLEncoder.encode(law.getOrgCode(),"utf-8"));
         }else if(law.getSocialCreditCode()!=null&&!"".equals(law.getSocialCreditCode())){
         surl.append("code="+URLEncoder.encode(law.getSocialCreditCode(),"utf-8"));
         }else{
         continue;
         }
         } catch (UnsupportedEncodingException e) {
         e.printStackTrace();
         }
         logger.error(surl);
         try {
         result=HttpClientUtil.getInstance().httpGet(surl.toString(), "utf-8");
         } catch (Exception e) {
         e.printStackTrace();
         }
         logger.error(result);
         List<SyncStandenterid> le=new ArrayList<SyncStandenterid>();
         le=JacksonUtils.toCollection(result,new TypeReference<List<SyncStandenterid>>() {});
         if(le!=null&&le.size()>0){
         SyncStandenterid ssi=le.get(0);
         if(ssi.getStandenterid()!=null&&!"".equals(ssi.getStandenterid())){
         law.setStandenterid(ssi.getStandenterid());
         listUpdate.add(law);
         }
         }

         int size=listUpdate.size();
         if(size==500){
         pi=pi+1;
         lawEnforceObjectMapper.updateBatch(listUpdate);
         listUpdate.clear();
         logger.error("第"+pi+"更新");
         }else if(cou==forSize){
         logger.error("最后一次更新");
         lawEnforceObjectMapper.updateBatch(listUpdate);
         }
         }
         **/
		/*} catch (Exception e) {
			e.printStackTrace();
		}*/
    }

    @Override
    public void syncObjectInfoByQQPT(String type) {
        if (!type.equals("") || type != null) {
            //0  代表首次初始化 其他代表每天定时同步亲情平台污染源
            syncObjectInfoByQQPTInit(type);
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd ");
            Calendar c = Calendar.getInstance();
            c.setTime(new Date());
            c.add(Calendar.DATE, -Integer.parseInt("2"));
            Date d = c.getTime();
            String begTime = sdf.format(d);
            String endTime = sdf.format(new Date());
            syncObjectInfoByQQPTInitDay(begTime, endTime);
        }
    }


    public void syncObjectInfoByQQPTInit(String type) {

        List<LawEnforceObject> list = new ArrayList();

        List<LawEnforceObject> listUp = new ArrayList();
        list = lawEnforceObjectMapper.queryOrgCodeandSocialCreditCode(null);

//        String securityKey = getSecurityKey("http://220.160.52.213:10063/serviceNew/serviceinterface/QCPQZFXTZR/securityKey");
//        MessageDigest md = null;
//        String md5 = "";
//        try {
//            md = MessageDigest.getInstance("MD5");
//            md.update(("securityKey=" + securityKey + "&appPassword=ZFXTZR").getBytes());
//            byte b[] = md.digest();
//            int i;
//            StringBuffer buf = new StringBuffer("");
//            for (int offset = 0; offset < b.length; offset++) {
//                i = b[offset];
//                if (i < 0)
//                    i += 256;
//                if (i < 16)
//                    buf.append("0");
//                buf.append(Integer.toHexString(i));
//            }
//            md5 = buf.toString();
//            // digest()最后确定返回md5 hash值，返回值为8位字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
//            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
//            //一个byte是八位二进制，也就是2位十六进制字符（2的8次方等于16的2次方）
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
        // 计算md5函数
        // 计算md5函数

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd ");
        Calendar c = Calendar.getInstance();
        c.setTime(new Date());
        c.add(Calendar.DATE, -Integer.parseInt(type));
        Date d = c.getTime();
        String begTime = sdf.format(d);
        String endTime = sdf.format(new Date());

        List<SyncObjectInfo> leAll = new ArrayList<SyncObjectInfo>();
        int total = 1;
        for (int i = 1; i <= total; i++) {
            logger.error("第" + i + "次调用");
            String result = "";
            String url = STANDARDID_URL_QQPT;
            StringBuilder surl = new StringBuilder();
//            surl.append(url + "&securityKey=" + securityKey + "&md5=" + md5);
            surl.append(url+"&pageSize=10000&pageNum=" + i+"&startDate=2020-05-07"+"&endDate="+endTime);

            URL restURL = null;
            try {
                restURL = new URL(surl.toString());
                HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
                conn.setRequestMethod("GET"); // POST GET PUT DELETE
                conn.setRequestProperty("Accept", "application/json");
                BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                result = br.readLine();
                br.close();
            } catch (MalformedURLException e) {
                e.printStackTrace();
            } catch (ProtocolException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }

            JSONObject jsonObject = JSONObject.parseObject(result);
            Map<String, Object> head = (Map<String, Object>) jsonObject.get("head");

            Map<String, Object> pageInfo = (Map<String, Object>) head.get("pageInfo");
            String pages = pageInfo.get("pages").toString();
            total = Integer.parseInt(pages);

            List<SyncObjectInfo> le = new ArrayList<>();
//        le = ( List<SyncStandenteridNew>) jsonObject.get("data")  ;
            JSONArray objects = (JSONArray) jsonObject.get("data");
            le = objects.toJavaList(SyncObjectInfo.class);

            if (le != null && le.size() > 0) {
                leAll.addAll(le);
            }
            for (int z = 0; z < leAll.size(); z++) {
                if (ChangnengUtil.isNull(leAll.get(z).getTyshxydm())) {
                    leAll.remove(z);
                }
            }
            for (int k = 0; k < leAll.size(); k++) {
                for (int j = 0; j < list.size(); j++) {

                    //type  1,2,3 依次为固定源名称（执法对象名称）、统一社会信用代码、固定源唯一编码
                    if (type.equals("1")) {
                        if (list.get(j).getObjectName().equals(leAll.get(k).getEnterName())){
                            try {
                                list.get(j).setStandenterid(leAll.get(k).getStandenterId());
                                list.get(j).setObjectName(leAll.get(k).getEnterName());
                                list.get(j).setAddress(leAll.get(k).getEnterAddress());
                                list.get(j).setBelongAreaId(leAll.get(k).getCodeRegion());
                                list.get(j).setBelongAreaName(leAll.get(k).getRegionName());
                                list.get(j).setGisCoordinateX84(leAll.get(k).getLongitude());
                                list.get(j).setGisCoordinateY84(leAll.get(k).getLatitude());
                                //20220808新增排污许可证代码LICENSE_NUMBER，国家下发固定污染源编码ENTERCODE

                                list.get(j).setLicenseNumber(leAll.get(k).getLicenseNum());
                                list.get(j).setEnterCode(leAll.get(k).getEnterCode());
                                if(StringUtils.isNotEmpty(leAll.get(k).getFzTime())){
                                    list.get(j).setStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getFzTime()));
                                }
                                if(StringUtils.isNotEmpty(leAll.get(k).getValiEndTime())){
                                    list.get(j).setEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getValiEndTime()));
                                }
                                list.get(j).setManagementType(leAll.get(k).getManagement());
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }

                            listUp.add(list.get(j));
                            list.remove(j);

                        }
                    }

                    if (type.equals("2")) {
                        if (list.get(j).getSocialCreditCode().equals(leAll.get(k).getTyshxydm())) {
                            try {
                                list.get(j).setStandenterid(leAll.get(k).getStandenterId());
                                list.get(j).setObjectName(leAll.get(k).getEnterName());
                                list.get(j).setAddress(leAll.get(k).getEnterAddress());
                                list.get(j).setBelongAreaId(leAll.get(k).getCodeRegion());
                                list.get(j).setBelongAreaName(leAll.get(k).getRegionName());
                                list.get(j).setGisCoordinateX84(leAll.get(k).getLongitude());
                                list.get(j).setGisCoordinateY84(leAll.get(k).getLatitude());
                                //20220808新增排污许可证代码LICENSE_NUMBER，国家下发固定污染源编码ENTERCODE

                                list.get(j).setLicenseNumber(leAll.get(k).getLicenseNum());
                                list.get(j).setEnterCode(leAll.get(k).getEnterCode());
                                if(StringUtils.isNotEmpty(leAll.get(k).getFzTime())){
                                    list.get(j).setStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getFzTime()));
                                }
                                if(StringUtils.isNotEmpty(leAll.get(k).getValiEndTime())){
                                    list.get(j).setEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getValiEndTime()));
                                }
                                list.get(j).setManagementType(leAll.get(k).getManagement());
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            listUp.add(list.get(j));
                            list.remove(j);
                        }
                    }

                    if (type.equals("3")) {
                        if (StringUtils.isNotEmpty(list.get(j).getStandenterid())&&list.get(j).getStandenterid().equals(leAll.get(k).getStandenterId())) {
                            try {
                                list.get(j).setStandenterid(leAll.get(k).getStandenterId());
                                list.get(j).setObjectName(leAll.get(k).getEnterName());
                                list.get(j).setAddress(leAll.get(k).getEnterAddress());
                                list.get(j).setBelongAreaId(leAll.get(k).getCodeRegion());
                                list.get(j).setBelongAreaName(leAll.get(k).getRegionName());
                                list.get(j).setGisCoordinateX84(leAll.get(k).getLongitude());
                                list.get(j).setGisCoordinateY84(leAll.get(k).getLatitude());
                                //20220808新增排污许可证代码LICENSE_NUMBER，国家下发固定污染源编码ENTERCODE

                                list.get(j).setLicenseNumber(leAll.get(k).getLicenseNum());
                                list.get(j).setEnterCode(leAll.get(k).getEnterCode());
                                if(StringUtils.isNotEmpty(leAll.get(k).getFzTime())){
                                    list.get(j).setStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getFzTime()));
                                }
                                if(StringUtils.isNotEmpty(leAll.get(k).getValiEndTime())){
                                    list.get(j).setEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getValiEndTime()));
                                }
                                list.get(j).setManagementType(leAll.get(k).getManagement());
                            } catch (ParseException e) {
                                e.printStackTrace();
                            }
                            listUp.add(list.get(j));
                            list.remove(j);
                        }
                    }
                }
            }


            leAll.clear();
        }


        List<LawEnforceObject> syncObjectInfoByQQPT = new ArrayList<>();
        logger.info("更新-----"+type+"---------对象总数量" + listUp.size());
        for (int i = 0; i < listUp.size(); i++) {
            if (i > 0 && i % 500 == 0) {
                lawEnforceObjectMapper.updateBatchByQQPT(syncObjectInfoByQQPT);
                syncObjectInfoByQQPT.clear();
            } else if (i < listUp.size()) {
                syncObjectInfoByQQPT.add(listUp.get(i));
            } else {
                syncObjectInfoByQQPT.add(listUp.get(i));
                lawEnforceObjectMapper.updateBatchByQQPT(syncObjectInfoByQQPT);
                logger.error("最后一次更新");
            }
        }
        logger.info("更新污染源完成---对象总数量" + listUp.size());

    }

    public void syncObjectInfoByQQPTInitDay(String stm, String etm) {

        List<LawEnforceObject> list = new ArrayList();

        List<LawEnforceObject> listUp = new ArrayList();
        list = lawEnforceObjectMapper.queryOrgCodeandSocialCreditCode(null);

//        String securityKey = getSecurityKey("http://220.160.52.213:10063/serviceNew/serviceinterface/QCPQZFXTZR/securityKey");
//        MessageDigest md = null;
//        String md5 = "";
//        try {
//            md = MessageDigest.getInstance("MD5");
//            md.update(("securityKey=" + securityKey + "&appPassword=ZFXTZR").getBytes());
//            byte b[] = md.digest();
//            int i;
//            StringBuffer buf = new StringBuffer("");
//            for (int offset = 0; offset < b.length; offset++) {
//                i = b[offset];
//                if (i < 0)
//                    i += 256;
//                if (i < 16)
//                    buf.append("0");
//                buf.append(Integer.toHexString(i));
//            }
//            md5 = buf.toString();
//            // digest()最后确定返回md5 hash值，返回值为8位字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
//            // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
//            //一个byte是八位二进制，也就是2位十六进制字符（2的8次方等于16的2次方）
//        } catch (NoSuchAlgorithmException e) {
//            e.printStackTrace();
//        }
        // 计算md5函数

        List<SyncObjectInfo> leAll = new ArrayList<SyncObjectInfo>();
        int total = 1;
        for (int i = 1; i <= total; i++) {
            logger.error("第" + i + "次调用");
            String result = "";
            String url = STANDARDID_URL_QQPT;
            StringBuilder surl = new StringBuilder();
//            surl.append(url + "&securityKey=" + securityKey + "&md5=" + md5);
            surl.append(url+"&pageSize=10000&pageNum=" + i + "&startDate=" + stm + "&endDate=" + etm);

            URL restURL = null;
            try {
                restURL = new URL(surl.toString());
                HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
                conn.setRequestMethod("GET"); // POST GET PUT DELETE
                conn.setRequestProperty("Accept", "application/json");
                BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream(), "UTF-8"));
                result = br.readLine();
                br.close();
            } catch (MalformedURLException e) {
                e.printStackTrace();
            } catch (ProtocolException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            Map<String, Object> head = (Map<String, Object>) jsonObject.get("head");

            Map<String, Object> pageInfo = (Map<String, Object>) head.get("pageInfo");
            String pages = pageInfo.get("pages").toString();
            total = Integer.parseInt(pages);

            List<SyncObjectInfo> le = new ArrayList<>();
//        le = ( List<SyncStandenteridNew>) jsonObject.get("data")  ;
            JSONArray objects = (JSONArray) jsonObject.get("data");
            le = objects.toJavaList(SyncObjectInfo.class);

            if (le != null && le.size() > 0) {
                leAll.addAll(le);
            }
            for (int z = 0; z < leAll.size(); z++) {
                if (ChangnengUtil.isNull(leAll.get(z).getTyshxydm())) {
                    leAll.remove(z);
                }
            }
            for (int k = 0; k < leAll.size(); k++) {
                for (int j = 0; j < list.size(); j++) {
                    if (list.get(j).getSocialCreditCode().equals(leAll.get(k).getTyshxydm())) {
//                        list.get(j).setStandenterid(leAll.get(k).getStandenterid());
//                        list.get(j).setObjectName(leAll.get(k).getEntername());
//                        list.get(j).setAddress(leAll.get(k).getEnteraddress());
//                        list.get(j).setBelongAreaId(leAll.get(k).getCode_region());
//                        list.get(j).setBelongAreaName(leAll.get(k).getRegionname());
//                        list.get(j).setGisCoordinateX84(leAll.get(k).getLongitude());
//                        list.get(j).setGisCoordinateY84(leAll.get(k).getLatitude());
//                        listUp.add(list.get(j));
//                        list.remove(j);

                        try {
                            list.get(j).setStandenterid(leAll.get(k).getStandenterId());
//                            list.get(j).setObjectName(leAll.get(k).getEnterName());
//                            list.get(j).setAddress(leAll.get(k).getEnterAddress());
//                            list.get(j).setBelongAreaId(leAll.get(k).getCodeRegion());
//                            list.get(j).setBelongAreaName(leAll.get(k).getRegionName());
                            list.get(j).setGisCoordinateX84(leAll.get(k).getLongitude());
                            list.get(j).setGisCoordinateY84(leAll.get(k).getLatitude());
                            //20220808新增排污许可证代码LICENSE_NUMBER，国家下发固定污染源编码ENTERCODE

                            list.get(j).setLicenseNumber(leAll.get(k).getLicenseNum());
                            list.get(j).setEnterCode(leAll.get(k).getEnterCode());
                            if(StringUtils.isNotEmpty(leAll.get(k).getFzTime())){
                                list.get(j).setStartDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getFzTime()));
                            }
                            if(StringUtils.isNotEmpty(leAll.get(k).getValiEndTime())){
                                list.get(j).setEndDate(new SimpleDateFormat("yyyy-MM-dd").parse(leAll.get(k).getValiEndTime()));
                            }
                            list.get(j).setManagementType(leAll.get(k).getManagement());
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }

                    }
                }
            }
            leAll.clear();
        }


        List<LawEnforceObject> syncObjectInfoByQQPT = new ArrayList<>();
        logger.info("更新对象总数量" + listUp.size());
        for (int i = 0; i < listUp.size(); i++) {
            if (i > 0 && i % 500 == 0) {
                lawEnforceObjectMapper.updateBatchByQQPT(syncObjectInfoByQQPT);
                syncObjectInfoByQQPT.clear();
            } else if (i < listUp.size()) {
                syncObjectInfoByQQPT.add(listUp.get(i));
            } else {
                syncObjectInfoByQQPT.add(listUp.get(i));
                lawEnforceObjectMapper.updateBatchByQQPT(syncObjectInfoByQQPT);
                logger.error("最后一次更新");
            }
        }
        logger.info("更新污染源完成---对象总数量" + listUp.size());
    }

    //亲情平台获取二次检验------调用安全认证接口获取securityKey
    //接口地址为http://{ip}:{port}/{context}/serviceinterface/{token}/securityKey
    String getSecurityKey(String url) {
        String result = "";
        StringBuilder surl = new StringBuilder();
        surl.append(url);

        URL restURL = null;
        try {
            restURL = new URL(surl.toString());
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("GET"); // POST GET PUT DELETE
            conn.setRequestProperty("Accept", "application/json");
            BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
            result = br.readLine();
            br.close();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (ProtocolException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return result;
    }

    /**
     * @Author:
     * @Date: 2021/7/29 15:03
     * @description：${根据生态云一企一档更新固定源污染源编码}
     */

    public void syncStandenteridNew() {

        List<LawEnforceObject> list = new ArrayList();

        List<LawEnforceObject> listUp = new ArrayList();
        list = lawEnforceObjectMapper.queryOrgCodeandSocialCreditCode(null);


        List<SyncStandenteridNew> leAll = new ArrayList<SyncStandenteridNew>();

        for (int i = 1; i <= 10; i++) {
            logger.error("第" + i + "次调用");
            String result = "";
            String url = STANDARDID_URL_NEW;
            StringBuilder surl = new StringBuilder();
            surl.append(url + "&pageSize=10000&pageNum=" + i);

            URL restURL = null;
            try {
                restURL = new URL(surl.toString());
                HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
                conn.setRequestMethod("GET"); // POST GET PUT DELETE
                conn.setRequestProperty("Accept", "application/json");
                BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
                result = br.readLine();
                br.close();
            } catch (MalformedURLException e) {
                e.printStackTrace();
            } catch (ProtocolException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
            JSONObject jsonObject = JSONObject.parseObject(result);
            List<SyncStandenteridNew> le = new ArrayList<>();
//        le = ( List<SyncStandenteridNew>) jsonObject.get("data")  ;
            JSONArray objects = (JSONArray) jsonObject.get("data");
            le = objects.toJavaList(SyncStandenteridNew.class);

            if (le != null && le.size() > 0) {
                leAll.addAll(le);
            }
        }

        for (int i = 0; i < leAll.size(); i++) {
            for (int j = 0; j < list.size(); j++) {
                if (list.get(j).getObjectName().equals(leAll.get(i).getEntername())) {
                    list.get(j).setStandenterid(leAll.get(i).getStandenterid());
                    listUp.add(list.get(j));

                }
            }
        }

        List<LawEnforceObject> syncStandenteridNews = new ArrayList<>();
        logger.info("更新对象总数量" + listUp.size());
        for (int i = 1; i <= listUp.size(); i++) {
            if (i % 500 == 0) {
                lawEnforceObjectMapper.updateBatch(syncStandenteridNews);
                syncStandenteridNews.clear();
            } else if (i < listUp.size()) {
                syncStandenteridNews.add(listUp.get(i));
            } else {
                syncStandenteridNews.add(listUp.get(i));
                lawEnforceObjectMapper.updateBatch(syncStandenteridNews);
                logger.error("最后一次更新");
            }
        }
        logger.info("更新污染源完成---对象总数量" + listUp.size());


    }

    ;

    public String getMethod(String url) throws IOException {
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        conn.setRequestMethod("GET"); // POST GET PUT DELETE
        conn.setRequestProperty("Accept", "application/json");
        BufferedReader br = new BufferedReader(new InputStreamReader(conn.getInputStream()));
        String line;
        while ((line = br.readLine()) != null) {
            return line;
        }
        br.close();
        return "";
    }


    /***
     *  获取废水数据
     * @param wasteWaterList 存放废水集合
     * @throws IOException
     */
    private void wasteWater(List<WasteWater> wasteWaterList, String checkDate, String updatetimeRjwa) throws IOException {
        StringBuffer sb = new StringBuffer();
        sb.append(HttpURLConnection).append("?interfaceId=").append(WASTE_WATER_METHOD).append("&token=").append(WEBSERVICE_TOKEN);
        if (StringUtils.isNotEmpty(checkDate)) {
            sb.append("&CHECKDATE=").append(checkDate);
        }
        if (StringUtils.isNotEmpty(updatetimeRjwa)) {
            sb.append("&UPDATETIME_RJWA=").append(updatetimeRjwa);
        }
        String sub = sb.toString();
        sb.append("&pageNum=").append(1).append("&pageSize=").append(100);
        String wasteWaterResult = getMethod(sb.toString());
        if (StringUtils.isNotEmpty(wasteWaterResult)) {
            // 将返回结果反序列化成java对象
            WebserviceResponseWasteWaterModel message = JacksonUtils.toObject(wasteWaterResult, WebserviceResponseWasteWaterModel.class);
            // 判断是否成功返回结果
            if (message != null && message.getHead() != null && message.getHead().getState()) {
                // 判断结果是否大于100条, 如果大于则继续调用, 否则入库
                if (message.getHead().getPageInfo() != null && message.getHead().getPageInfo().getTotal() > 100) {
                    // 将第一次查询出来的数据添加到空集合中
                    wasteWaterList.addAll(message.getData());
                    // 有多少页就调用多少次
                    for (int i = 0; i < message.getHead().getPageInfo().getPages() - 1; i++) {
                        StringBuffer sbTow = new StringBuffer();
                        sbTow.append(sub).append("&pageNum=").append(message.getHead().getPageInfo().getNextPage());
                        sbTow.append("&pageSize=").append(message.getHead().getPageInfo().getPageSize());
                        String wasteWaterResults = getMethod(sbTow.toString());
                        if (StringUtils.isNotEmpty(wasteWaterResults)) {
                            message = JacksonUtils.toObject(wasteWaterResults, WebserviceResponseWasteWaterModel.class);
                            wasteWaterList.addAll(message.getData());
                        }
                    }
                } else if (message.getHead().getPageInfo() != null && message.getHead().getPageInfo().getTotal() != null && message.getHead().getPageInfo().getTotal() > 0) {
                    // 将第一次查询出来的数据添加到空集合中
                    wasteWaterList.addAll(message.getData());
                } else {
                    logger.error("无数据");
                }
            } else {
                logger.error("调用失败!");
            }
        } else {
            logger.error("暂无废水数据!");
        }
    }


    /****
     * 监测废气数据
     * @param wasteGasList 用来存放数据
     * @throws IOException
     */
    private void exhaustGasData(List<WasteGas> wasteGasList, String checkDate, String updateDate) throws IOException {
        StringBuffer sb = new StringBuffer();
        sb.append(HttpURLConnection).append("?interfaceId=").append(WASTE_GAS_METHOD).append("&token=").append(WEBSERVICE_TOKEN);
        if (StringUtils.isNotEmpty(checkDate)) {
            sb.append("&CHECKDATE=").append(checkDate);
        }
        if (StringUtils.isNotEmpty(updateDate)) {
            sb.append("&UPDATETIME_RJWA=").append(updateDate);
        }
        String sub = sb.toString();
        sb.append("&pageNum=").append(1).append("&pageSize=").append(100);
        String wasteWaterResult = getMethod(sb.toString());
        // 存放数据集合
        //将返回结果反序列化成java对象
        if (StringUtils.isNotEmpty(wasteWaterResult)) {
            WebserviceResponseWasteGasModel message = JacksonUtils.toObject(wasteWaterResult, WebserviceResponseWasteGasModel.class);
            //判断调用是否成功
            if (message != null && message.getHead() != null && message.getHead().getState()) {
                // 判断结果是否大于100条, 如果大于则继续调用, 否则入库
                if (message.getHead().getPageInfo() != null && message.getHead().getPageInfo().getTotal() > 100) {
                    // 将第一次查询出来的数据添加到空集合中
                    wasteGasList.addAll(message.getData());
                    // 有多少页就调用多少次
                    for (int i = 0; i < message.getHead().getPageInfo().getPages() - 1; i++) {
                        StringBuffer sbTow = new StringBuffer();
                        sbTow.append(sub).append("&pageNum=").append(message.getHead().getPageInfo().getNextPage());
                        sbTow.append("&pageSize=").append(message.getHead().getPageInfo().getPageSize());
                        String wasteWaterResults = getMethod(sbTow.toString());
                        if (StringUtils.isNotEmpty(wasteWaterResults)) {
                            message = JacksonUtils.toObject(wasteWaterResults, WebserviceResponseWasteGasModel.class);
                            wasteGasList.addAll(message.getData());
                        }
                    }
                } else if (message.getHead().getPageInfo() != null && message.getHead().getPageInfo().getTotal() != null &&
                        message.getHead().getPageInfo().getTotal() > 0) {
                    // 将第一次查询出来的数据添加到空集合中
                    wasteGasList.addAll(message.getData());
                } else {
                    logger.error("无数据");
                }
            } else {
                logger.error("调用失败!");
            }
        } else {
            logger.error("暂无废气数据!");
        }

    }

    /****
     *  获取点位数据
     * @param pointInfoList 存放点位数据
     * @param pointInfoTreeSet 去重后的点位ID
     */
    private void pointPosition(List<PointInfo> pointInfoList, TreeSet<String> pointInfoTreeSet, String updateDate) throws IOException {
        // 将set转成list结合
        List<String> stringList = new ArrayList<String>();
        stringList.addAll(pointInfoTreeSet);
        if (stringList != null && stringList.size() > 0) {
            StringBuffer sb = new StringBuffer();
            sb.append(HttpURLConnection).append("?interfaceId=").append(POINT_INFO_METHOD);
            sb.append("&token=").append(WEBSERVICE_TOKEN);
            if (StringUtils.isNotEmpty(updateDate)) {
                sb.append("&UPDATETIME_RJWA=").append(updateDate);
            }
            PageModel<String> pmp = new PageModel<String>(stringList, 100);
            // 根据CP_ID来获取数据
            for (int i = 0; i < pmp.totalPages; i++) {
                String pointInfoResult = getMethod(sb.toString());
                if (StringUtils.isNotEmpty(pointInfoResult)) {
                    WebserviceResponsePointInfoModel pointInfoModel = JacksonUtils.toObject(pointInfoResult, WebserviceResponsePointInfoModel.class);
                    if (pointInfoModel != null && pointInfoModel.getData() != null) {
                        // 将第每次查询出来的数据添加到空集合中
                        pointInfoList.addAll(pointInfoModel.getData());
                    }
                }
            }
        }

        logger.info("点位数据 : {}", JacksonUtils.toJsonString(pointInfoList));
        logger.info("点位数量 : {}", pointInfoList.size());
    }

    /**
     * 获取企业信息
     *
     * @param standardEnterpriseList 存放企业信息
     * @param enterpriseListTreeSet  去重后点位信息(ID)
     */
    private void enterpriseInformation(List<StandardEnterprise> standardEnterpriseList, TreeSet<String> enterpriseListTreeSet, String updateDate) throws IOException {
        if (enterpriseListTreeSet != null && enterpriseListTreeSet.size() > 0) {
            // 将set转成list结合
            List<String> stringEnterList = new ArrayList<String>();
            stringEnterList.addAll(enterpriseListTreeSet);
            StringBuffer sb = new StringBuffer();
            sb.append(HttpURLConnection);
            sb.append("?interfaceId=").append(STANDARD_ENTERPRISE_METHOD);
            sb.append("&token=").append(WEBSERVICE_TOKEN);
            if (StringUtils.isNotEmpty(updateDate)) {
                sb.append("&UPDATETIME_RJWA=").append(updateDate);
            }
            PageModel<String> pageModel = new PageModel<String>(stringEnterList, 100);
            // 获取企业信息列表
            for (int i = 0; i < pageModel.getTotalPages(); i++) {
                String standardEnterpriseResult = getMethod(sb.toString());
                if (StringUtils.isNotEmpty(standardEnterpriseResult)) {
                    WebserviceResponseStandardEnterpriseModel standardEnterpriseModel = JacksonUtils.toObject(standardEnterpriseResult, WebserviceResponseStandardEnterpriseModel.class);
                    if (standardEnterpriseModel != null && standardEnterpriseModel.getData() != null) {
                        // 将第一次查询出来的数据添加到空集合中
                        standardEnterpriseList.addAll(standardEnterpriseModel.getData());
                    }
                }

            }
        }

        logger.info("企业信息 : {}", JacksonUtils.toJsonString(standardEnterpriseList));
        logger.info("企业信息数量: {}", standardEnterpriseList.size());
    }

}
