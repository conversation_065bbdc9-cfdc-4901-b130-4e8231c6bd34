<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.PassingInfoMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.PassingInfo">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="HANDLE_USER_NAME" jdbcType="VARCHAR" property="handleUserName" />
    <result column="HANDLE_USER_ID" jdbcType="VARCHAR" property="handleUserId" />
    <result column="HANDLE_USER_CODE" jdbcType="VARCHAR" property="handleUserCode" />
    <result column="HANDLE_USER_CODE_NAME" jdbcType="VARCHAR" property="handleUserCodeName" />
    <result column="HANDLE_USER_DEPT_ID" jdbcType="VARCHAR" property="handleUserDeptId" />
    <result column="HANDLE_USER_DEPT_NUM" jdbcType="VARCHAR" property="handleUserDeptNum" />
    <result column="HANDLE_DEPARTMENT_NAME" jdbcType="VARCHAR" property="handleDepartmentName" />
    <result column="LAW_OBJECT_ID" jdbcType="VARCHAR" property="lawObjectId" />
    <result column="LAW_OBJECT_NAME" jdbcType="VARCHAR" property="lawObjectName" />
    <result column="LAW_OBJECT_TYPE" jdbcType="VARCHAR" property="lawObjectType" />
    <result column="PASS_DATE" jdbcType="TIMESTAMP" property="passDate" />
    <result column="GIS_COORDINATE_X" jdbcType="VARCHAR" property="gisCoordinateX" />
    <result column="GIS_COORDINATE_Y" jdbcType="VARCHAR" property="gisCoordinateY" />
    <result column="DEVICE_ID" jdbcType="VARCHAR" property="deviceId" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, HANDLE_USER_NAME, HANDLE_USER_ID, HANDLE_USER_CODE, HANDLE_USER_CODE_NAME, HANDLE_USER_DEPT_ID, 
    HANDLE_USER_DEPT_NUM, HANDLE_DEPARTMENT_NAME, LAW_OBJECT_ID, LAW_OBJECT_NAME, LAW_OBJECT_TYPE, 
    PASS_DATE, GIS_COORDINATE_X, GIS_COORDINATE_Y, DEVICE_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PASSING_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from PASSING_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.PassingInfo">
    insert into PASSING_INFO (ID, HANDLE_USER_NAME, HANDLE_USER_ID, 
      HANDLE_USER_CODE, HANDLE_USER_CODE_NAME, HANDLE_USER_DEPT_ID, 
      HANDLE_USER_DEPT_NUM, HANDLE_DEPARTMENT_NAME, 
      LAW_OBJECT_ID, LAW_OBJECT_NAME, LAW_OBJECT_TYPE, 
      PASS_DATE, GIS_COORDINATE_X, GIS_COORDINATE_Y, 
      DEVICE_ID)
    values (#{id,jdbcType=VARCHAR}, #{handleUserName,jdbcType=VARCHAR}, #{handleUserId,jdbcType=VARCHAR}, 
      #{handleUserCode,jdbcType=VARCHAR}, #{handleUserCodeName,jdbcType=VARCHAR}, #{handleUserDeptId,jdbcType=VARCHAR}, 
      #{handleUserDeptNum,jdbcType=VARCHAR}, #{handleDepartmentName,jdbcType=VARCHAR}, 
      #{lawObjectId,jdbcType=VARCHAR}, #{lawObjectName,jdbcType=VARCHAR}, #{lawObjectType,jdbcType=VARCHAR}, 
      #{passDate,jdbcType=TIMESTAMP}, #{gisCoordinateX,jdbcType=VARCHAR}, #{gisCoordinateY,jdbcType=VARCHAR}, 
      #{deviceId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.PassingInfo">
  	<selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into PASSING_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="handleUserName != null">
        HANDLE_USER_NAME,
      </if>
      <if test="handleUserId != null">
        HANDLE_USER_ID,
      </if>
      <if test="handleUserCode != null">
        HANDLE_USER_CODE,
      </if>
      <if test="handleUserCodeName != null">
        HANDLE_USER_CODE_NAME,
      </if>
      <if test="handleUserDeptId != null">
        HANDLE_USER_DEPT_ID,
      </if>
      <if test="handleUserDeptNum != null">
        HANDLE_USER_DEPT_NUM,
      </if>
      <if test="handleDepartmentName != null">
        HANDLE_DEPARTMENT_NAME,
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID,
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME,
      </if>
      <if test="lawObjectType != null">
        LAW_OBJECT_TYPE,
      </if>
      <if test="passDate != null">
        PASS_DATE,
      </if>
      <if test="gisCoordinateX != null">
        GIS_COORDINATE_X,
      </if>
      <if test="gisCoordinateY != null">
        GIS_COORDINATE_Y,
      </if>
      <if test="deviceId != null">
        DEVICE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="handleUserName != null">
        #{handleUserName,jdbcType=VARCHAR},
      </if>
      <if test="handleUserId != null">
        #{handleUserId,jdbcType=VARCHAR},
      </if>
      <if test="handleUserCode != null">
        #{handleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="handleUserCodeName != null">
        #{handleUserCodeName,jdbcType=VARCHAR},
      </if>
      <if test="handleUserDeptId != null">
        #{handleUserDeptId,jdbcType=VARCHAR},
      </if>
      <if test="handleUserDeptNum != null">
        #{handleUserDeptNum,jdbcType=VARCHAR},
      </if>
      <if test="handleDepartmentName != null">
        #{handleDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectType != null">
        #{lawObjectType,jdbcType=VARCHAR},
      </if>
      <if test="passDate != null">
        #{passDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gisCoordinateX != null">
        #{gisCoordinateX,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY != null">
        #{gisCoordinateY,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.PassingInfo">
    update PASSING_INFO
    <set>
      <if test="handleUserName != null">
        HANDLE_USER_NAME = #{handleUserName,jdbcType=VARCHAR},
      </if>
      <if test="handleUserId != null">
        HANDLE_USER_ID = #{handleUserId,jdbcType=VARCHAR},
      </if>
      <if test="handleUserCode != null">
        HANDLE_USER_CODE = #{handleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="handleUserCodeName != null">
        HANDLE_USER_CODE_NAME = #{handleUserCodeName,jdbcType=VARCHAR},
      </if>
      <if test="handleUserDeptId != null">
        HANDLE_USER_DEPT_ID = #{handleUserDeptId,jdbcType=VARCHAR},
      </if>
      <if test="handleUserDeptNum != null">
        HANDLE_USER_DEPT_NUM = #{handleUserDeptNum,jdbcType=VARCHAR},
      </if>
      <if test="handleDepartmentName != null">
        HANDLE_DEPARTMENT_NAME = #{handleDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectType != null">
        LAW_OBJECT_TYPE = #{lawObjectType,jdbcType=VARCHAR},
      </if>
      <if test="passDate != null">
        PASS_DATE = #{passDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gisCoordinateX != null">
        GIS_COORDINATE_X = #{gisCoordinateX,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY != null">
        GIS_COORDINATE_Y = #{gisCoordinateY,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        DEVICE_ID = #{deviceId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.PassingInfo">
    update PASSING_INFO
    set HANDLE_USER_NAME = #{handleUserName,jdbcType=VARCHAR},
      HANDLE_USER_ID = #{handleUserId,jdbcType=VARCHAR},
      HANDLE_USER_CODE = #{handleUserCode,jdbcType=VARCHAR},
      HANDLE_USER_CODE_NAME = #{handleUserCodeName,jdbcType=VARCHAR},
      HANDLE_USER_DEPT_ID = #{handleUserDeptId,jdbcType=VARCHAR},
      HANDLE_USER_DEPT_NUM = #{handleUserDeptNum,jdbcType=VARCHAR},
      HANDLE_DEPARTMENT_NAME = #{handleDepartmentName,jdbcType=VARCHAR},
      LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      LAW_OBJECT_TYPE = #{lawObjectType,jdbcType=VARCHAR},
      PASS_DATE = #{passDate,jdbcType=TIMESTAMP},
      GIS_COORDINATE_X = #{gisCoordinateX,jdbcType=VARCHAR},
      GIS_COORDINATE_Y = #{gisCoordinateY,jdbcType=VARCHAR},
      DEVICE_ID = #{deviceId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>