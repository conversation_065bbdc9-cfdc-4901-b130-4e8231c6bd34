package org.changneng.framework.frameworkbusiness.service.fjwg;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.SiLuSearch;
import org.changneng.framework.frameworkbusiness.entity.SiLuTaskBean;
import org.changneng.framework.frameworkbusiness.entity.ZfGrid;
import org.changneng.framework.frameworkbusiness.entity.silu.OneMapTask;
import org.changneng.framework.frameworkcore.utils.PageBean;

public interface ComprehensiveRegulationService {

	PageBean<SiLuTaskBean> getTaskGpsListWg(SiLuSearch searchBean)throws Exception;

	PageBean<SiLuTaskBean> getLawCircleGpsListWg(SiLuSearch searchBean)throws Exception;

	PageBean<SiLuTaskBean> getCaseListWg(SiLuSearch searchBean)throws Exception;

	int getApiTaskListByGrid(SiLuSearch search);

	List<ZfGrid> getZfNumByGrid(SiLuSearch searchBean);

	List<ZfGrid> getZfLawCircle(SiLuSearch searchBean);

	List<ZfGrid> getZfCaseList(SiLuSearch searchBean);

	/**
	 * @Author: gengc
	 * @CreateDate:2020/1/8  17:36
	 * @Description{
	 * 	一张图中台系统改造   案件查办----打点数据。
	 * }
	 */
	List<SiLuTaskBean> getOneMapTaskGpsListWg(SiLuSearch bean);

	/**
	 * @Author: gengc
	 * @CreateDate:2020/1/8  17:36
	 * @Description{
	 * 	一张图中台系统改造   执法检查（现场执法）----打点数据。
	 * }
	 */
	PageBean<SiLuTaskBean> getOneMapTaskLawList(SiLuSearch bean);


	/**
	 * @Author: gengc
	 * @CreateDate:2020/1/8  17:36
	 * @Description{
	 * 	一张图中台系统改造   案件查办----列表数据。
	 * }
	 */
	List<OneMapTask> getOneMapTaskTableList();
}
