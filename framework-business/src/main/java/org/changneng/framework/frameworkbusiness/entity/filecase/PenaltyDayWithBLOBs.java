package org.changneng.framework.frameworkbusiness.entity.filecase;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.validator.filecase.PenaltyDayValid;

/**
 * 按日计罚主体（大字段）
 * 
 * @ClassName: PenaltyDayWithBLOBs
 * @Description: 
 * <AUTHOR>
 * @date 2017年7月20日 下午3:47:49
 *
 */
@PenaltyDayValid
public class PenaltyDayWithBLOBs extends PenaltyDay {
	private String illegalAction;

	private String orderCorrectDocid;

	private String correctBackDocid;

	private String penaltyDocid;

	private String penaltyBackDocid;

	private String penaltyNoticeDocid;
	
	private String penaltyNoticeBack;
	
	private String preHearingFile;

	private String noticeBackDocid;

	private String discussDocid;

	private String hearDocid;

	private String statementDocid;

	private String beginApprovalDocid;

	private String decisionOtherDocid;

	private String otherPublicDesc;

	private String excuteReason;

	private String remark;

	private String endApprovalDocid;

	private String excuteOtherDocid;
	
    private String investigationReport;
    
    /**
     * V1.5.6 案卷不完整原因
     */
    private String incompleteCause;
    
    /**
     * 二期加入：主要执法人员信息
     */
    private List<CaseLawEnforcement> lawUserList;
    
    /**
     * 二期加入：主要执法人员信息 -->执法证号 冗余字段 方便台账查询
     */
    private String caseLawEnforc; 
    
    /**
     * 二期加入：主要执法人员信息 -->人员信息 冗余字段 方便台账查询
     */
    private String caseLawName;
  //20180927 案件改造新增
    private Integer stageIsCompleteIns;//阶段稽查完整性
  	private Integer allIsCompleteIns;//整体稽查完整性
    
    
	public String getIncompleteCause() {
		return incompleteCause;
	}

	public void setIncompleteCause(String incompleteCause) {
		this.incompleteCause = incompleteCause;
	}
    
	public String getPenaltyNoticeBack() {
		return penaltyNoticeBack;
	}

	public void setPenaltyNoticeBack(String penaltyNoticeBack) {
		this.penaltyNoticeBack = penaltyNoticeBack;
	}

	public String getPreHearingFile() {
		return preHearingFile;
	}

	public void setPreHearingFile(String preHearingFile) {
		this.preHearingFile = preHearingFile;
	}

	public String getIllegalAction() {
		return illegalAction;
	}

	public void setIllegalAction(String illegalAction) {
		this.illegalAction = illegalAction;
	}

	public String getOrderCorrectDocid() {
		return orderCorrectDocid;
	}

	public void setOrderCorrectDocid(String orderCorrectDocid) {
		this.orderCorrectDocid = orderCorrectDocid;
	}

	public String getCorrectBackDocid() {
		return correctBackDocid;
	}

	public void setCorrectBackDocid(String correctBackDocid) {
		this.correctBackDocid = correctBackDocid;
	}

	public String getPenaltyDocid() {
		return penaltyDocid;
	}

	public void setPenaltyDocid(String penaltyDocid) {
		this.penaltyDocid = penaltyDocid;
	}

	public String getPenaltyBackDocid() {
		return penaltyBackDocid;
	}

	public void setPenaltyBackDocid(String penaltyBackDocid) {
		this.penaltyBackDocid = penaltyBackDocid;
	}

	public String getPenaltyNoticeDocid() {
		return penaltyNoticeDocid;
	}

	public void setPenaltyNoticeDocid(String penaltyNoticeDocid) {
		this.penaltyNoticeDocid = penaltyNoticeDocid;
	}

	public String getNoticeBackDocid() {
		return noticeBackDocid;
	}

	public void setNoticeBackDocid(String noticeBackDocid) {
		this.noticeBackDocid = noticeBackDocid;
	}

	public String getDiscussDocid() {
		return discussDocid;
	}

	public void setDiscussDocid(String discussDocid) {
		this.discussDocid = discussDocid;
	}

	public String getHearDocid() {
		return hearDocid;
	}

	public void setHearDocid(String hearDocid) {
		this.hearDocid = hearDocid;
	}

	public String getStatementDocid() {
		return statementDocid;
	}

	public void setStatementDocid(String statementDocid) {
		this.statementDocid = statementDocid;
	}

	public String getBeginApprovalDocid() {
		return beginApprovalDocid;
	}

	public void setBeginApprovalDocid(String beginApprovalDocid) {
		this.beginApprovalDocid = beginApprovalDocid;
	}

	public String getDecisionOtherDocid() {
		return decisionOtherDocid;
	}

	public void setDecisionOtherDocid(String decisionOtherDocid) {
		this.decisionOtherDocid = decisionOtherDocid;
	}

	public String getOtherPublicDesc() {
		return otherPublicDesc;
	}

	public void setOtherPublicDesc(String otherPublicDesc) {
		this.otherPublicDesc = otherPublicDesc;
	}

	public String getExcuteReason() {
		return excuteReason;
	}

	public void setExcuteReason(String excuteReason) {
		this.excuteReason = excuteReason;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getEndApprovalDocid() {
		return endApprovalDocid;
	}

	public void setEndApprovalDocid(String endApprovalDocid) {
		this.endApprovalDocid = endApprovalDocid;
	}

	public String getExcuteOtherDocid() {
		return excuteOtherDocid;
	}

	public void setExcuteOtherDocid(String excuteOtherDocid) {
		this.excuteOtherDocid = excuteOtherDocid;
	}

	public String getInvestigationReport() {
		return investigationReport;
	}

	public void setInvestigationReport(String investigationReport) {
		this.investigationReport = investigationReport;
	}

	public List<CaseLawEnforcement> getLawUserList() {
		return lawUserList;
	}

	public void setLawUserList(List<CaseLawEnforcement> lawUserList) {
		this.lawUserList = lawUserList;
	}

	public String getCaseLawEnforc() {
		return caseLawEnforc;
	}

	public void setCaseLawEnforc(String caseLawEnforc) {
		this.caseLawEnforc = caseLawEnforc;
	}

	public String getCaseLawName() {
		return caseLawName;
	}

	public void setCaseLawName(String caseLawName) {
		this.caseLawName = caseLawName;
	}

	public Integer getStageIsCompleteIns() {
		return stageIsCompleteIns;
	}

	public void setStageIsCompleteIns(Integer stageIsCompleteIns) {
		this.stageIsCompleteIns = stageIsCompleteIns;
	}

	public Integer getAllIsCompleteIns() {
		return allIsCompleteIns;
	}

	public void setAllIsCompleteIns(Integer allIsCompleteIns) {
		this.allIsCompleteIns = allIsCompleteIns;
	}
	
}