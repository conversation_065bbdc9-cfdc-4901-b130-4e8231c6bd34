package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.DiscretionSearch;
import org.changneng.framework.frameworkbusiness.entity.DtDiscretionFactor;
import org.changneng.framework.frameworkbusiness.entity.LawInfoBean;
import org.changneng.framework.frameworkcore.utils.PageBean;

public interface DtDiscretionFactorService {
	/**
	 * 通过自由裁量基本信息id查询法律法规列表
	 * @param infoId
	 * @return
	 */
	 public  PageBean<LawInfoBean> selectLawListByInfoId(DiscretionSearch search);
	 /**
	  * 通过主键id查询法律法规信息
	  * @param id
	  * @return
	  */
	 public DtDiscretionFactor selectLawInfoById(String id);
	 /**
	  * 新增或修改法律法规信息
	  * @param discretionFactor
	  */
	public void addOrUpdateLawInfo(DtDiscretionFactor discretionFactor) throws Exception;
	/**
	 * 上传附件
	 * @param id
	 * @param request
	 * @param response
	 * @return
	 */
	public Map<String, Object> pdfUpload(String id, HttpServletRequest request, HttpServletResponse response) throws Exception;
	/**
	 * 删除法律法规信息
	 * @param id
	 */
	public void deleteLawInfo(String id);
}
