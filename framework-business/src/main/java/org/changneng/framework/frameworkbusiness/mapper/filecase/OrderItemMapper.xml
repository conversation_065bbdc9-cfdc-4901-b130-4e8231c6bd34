<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.OrderItemMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.OrderItem">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="ITEM_TYPE" jdbcType="DECIMAL" property="itemType" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, NAME, ORDER_ID, CREATE_TIME, CONTENT_TYPE, UPDATE_DATE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ORDER_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from ORDER_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
   <select id="selectByObjId" resultType="String">
  	select id
  	from ORDER_ITEM
  	where object_id=#{objectId, jdbcType=VARCHAR}
  </select>
    <!--根据当前案件id修改条目信息 -->
   <update id="updateObjectId" >
  	update ORDER_ITEM set object_id=#{objectId, jdbcType=VARCHAR} where id=#{id, jdbcType=VARCHAR}
  </update>
   <delete id="deleteByObjId">
  	delete from ORDER_ITEM
  	where object_id = #{objectId, jdbcType=VARCHAR}
  </delete>
 
  
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.OrderItem">
    insert into ORDER_ITEM (ID, NAME, OBJECT_ID, 
      CREATE_DATE, ITEM_TYPE, UPDATE_DATE
      )
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{objectId,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{itemType,jdbcType=DECIMAL}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.OrderItem">
     <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into ORDER_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="name != null">
        NAME,
      </if>
      <if test="orderId != null">
        OBJECT_ID,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="itemType != null">
        ITEM_TYPE,
      </if>
      <if test="updateDate != null">
        UPDATE_DATE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null">
        #{itemType,jdbcType=DECIMAL},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.OrderItem">
    update ORDER_ITEM
    <set>
      <if test="name != null">
        NAME = #{name,jdbcType=VARCHAR},
      </if>
      <if test="objectId != null">
        OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="itemType != null">
        ITEM_TYPE = #{itemType,jdbcType=DECIMAL},
      </if>
      <if test="updateDate != null">
        UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.OrderItem">
    update ORDER_ITEM
    set NAME = #{name,jdbcType=VARCHAR},
      OBJECT_ID = #{objectId,jdbcType=VARCHAR},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      ITEM_TYPE = #{itemType,jdbcType=DECIMAL},
      UPDATE_DATE = #{updateDate,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>