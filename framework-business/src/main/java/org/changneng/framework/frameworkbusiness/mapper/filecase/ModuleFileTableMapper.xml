<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.ModuleFileTableMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.ModuleFileTable" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="MODULETYPE" property="moduletype" jdbcType="DECIMAL" />
    <result column="CODE" property="code" jdbcType="VARCHAR" />
    <result column="GENERAL_TYPE" property="generalType" jdbcType="DECIMAL" />
    <result column="UP_DOWN_STATE" property="upDownState" jdbcType="DECIMAL" />
    <result column="NAME_MAPPING" property="nameMapping" jdbcType="VARCHAR" />
    <result column="IS_DAY_PENALTY" jdbcType="DECIMAL" property="isDayPenalty" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, MODULETYPE, CODE, GENERAL_TYPE, UP_DOWN_STATE, NAME_MAPPING,IS_DAY_PENALTY
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from MODULE_FILE_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <select id="selectByModuleTypeAndUpDownState" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from MODULE_FILE_TABLE
    where  1=1
    AND MODULETYPE =  #{moduletype,jdbcType=DECIMAL}
    <if test="upDownState!=null and upDownState=='1'.toString()">
    	AND UP_DOWN_STATE = 1
    </if>
    <if test="upDownState!=null and upDownState=='2'.toString()">
    	AND UP_DOWN_STATE in(1,2)
    </if>
  </select>
  <select id="selectUpOrDownItemByModuleTypeAndUpDownState" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from MODULE_FILE_TABLE
    where MODULETYPE = #{moduletype,jdbcType=VARCHAR}
    <if test="upDownState!=null and upDownState=='1'.toString()">
    	AND UP_DOWN_STATE = 1
    </if>
    <if test="upDownState!=null and upDownState=='2'.toString()">
    	AND UP_DOWN_STATE = 2
    </if>
  </select>
  <select id="selectGeneralUpOrDownItemByModuleTypeAndUpDownState" resultMap="BaseResultMap" >
    select 
    <include refid="Base_Column_List" />
    from MODULE_FILE_TABLE
    where MODULETYPE = #{moduletype,jdbcType=VARCHAR}
    <if test="generalType!=null and generalType=='2'.toString()">
    	AND GENERAL_TYPE in (1,2)
    </if>
    <if test="generalType!=null and generalType=='3'.toString()">
    	AND GENERAL_TYPE = #{generalType,jdbcType=VARCHAR} or  GENERAL_TYPE=1
    </if>
    <if test="generalType!=null and generalType=='4'.toString()">
    	AND GENERAL_TYPE = #{generalType,jdbcType=VARCHAR}
    </if>
    
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from MODULE_FILE_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ModuleFileTable" >
    insert into MODULE_FILE_TABLE (ID, MODULETYPE, CODE, 
      GENERAL_TYPE, UP_DOWN_STATE, NAME_MAPPING
      )
    values (#{id,jdbcType=VARCHAR}, #{moduletype,jdbcType=DECIMAL}, #{code,jdbcType=VARCHAR}, 
      #{generalType,jdbcType=DECIMAL}, #{upDownState,jdbcType=DECIMAL}, #{nameMapping,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ModuleFileTable" >
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into MODULE_FILE_TABLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="moduletype != null" >
        MODULETYPE,
      </if>
      <if test="code != null" >
        CODE,
      </if>
      <if test="generalType != null" >
        GENERAL_TYPE,
      </if>
      <if test="upDownState != null" >
        UP_DOWN_STATE,
      </if>
      <if test="nameMapping != null" >
        NAME_MAPPING,
      </if>
      <if test="isDayPenalty != null">
        IS_DAY_PENALTY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="moduletype != null" >
        #{moduletype,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="generalType != null" >
        #{generalType,jdbcType=DECIMAL},
      </if>
      <if test="upDownState != null" >
        #{upDownState,jdbcType=DECIMAL},
      </if>
      <if test="nameMapping != null" >
        #{nameMapping,jdbcType=VARCHAR},
      </if>
      <if test="isDayPenalty != null">
        #{isDayPenalty,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ModuleFileTable" >
    update MODULE_FILE_TABLE
    <set >
      <if test="moduletype != null" >
        MODULETYPE = #{moduletype,jdbcType=DECIMAL},
      </if>
      <if test="code != null" >
        CODE = #{code,jdbcType=VARCHAR},
      </if>
      <if test="generalType != null" >
        GENERAL_TYPE = #{generalType,jdbcType=DECIMAL},
      </if>
      <if test="upDownState != null" >
        UP_DOWN_STATE = #{upDownState,jdbcType=DECIMAL},
      </if>
      <if test="nameMapping != null" >
        NAME_MAPPING = #{nameMapping,jdbcType=VARCHAR},
      </if>
      <if test="isDayPenalty != null">
        IS_DAY_PENALTY = #{isDayPenalty,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.ModuleFileTable" >
    update MODULE_FILE_TABLE
    set MODULETYPE = #{moduletype,jdbcType=DECIMAL},
      CODE = #{code,jdbcType=VARCHAR},
      GENERAL_TYPE = #{generalType,jdbcType=DECIMAL},
      UP_DOWN_STATE = #{upDownState,jdbcType=DECIMAL},
      NAME_MAPPING = #{nameMapping,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>