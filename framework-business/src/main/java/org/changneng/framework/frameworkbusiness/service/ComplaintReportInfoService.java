package org.changneng.framework.frameworkbusiness.service;

import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkcore.utils.PageBean;

import java.util.List;


public interface ComplaintReportInfoService {


    PageBean<ComplaintReportInfo> selectall(ComplaintReportInfoCurd complaintReportInfoCurd);


    //提供给app   信访执法列表
    // 查询信访投诉对象列表
    List<ComplaintObjectInfo>  getComplaintObjectInfoList(String tsdxbh,String kyblxcqk,String tsdxdz);
//    List<LawEnforceData>  getLawEnforceDataList(String tsdxbh,String kyblxcqk,String tsdxdz);
}
