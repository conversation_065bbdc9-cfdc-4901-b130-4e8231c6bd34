<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.PenaltyAttachmentDetailMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyAttachmentDetail">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId" />
    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
    <result column="FILE_TYPE" jdbcType="DECIMAL" property="fileType" />
    <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl" />
    <result column="STATE" jdbcType="DECIMAL" property="state" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize" />
    <result column="IS_GENERATE" jdbcType="DECIMAL" property="isGenerate" />
    <result column="IS_BELONG_RECORD" jdbcType="DECIMAL" property="isBelongRecord" />
    <result column="GENERATE_ID" jdbcType="VARCHAR" property="generateId" />
    <result column="LOCATION" jdbcType="DECIMAL" property="location" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, ITEM_ID, FILE_NAME, FILE_TYPE, FILE_URL, STATE, CREATE_DATE, FILE_SIZE, IS_GENERATE, 
    IS_BELONG_RECORD, GENERATE_ID,LOCATION
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from PENALTY_ATTACHMENT_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <update id="batchUpdateStateByGenerateIds">
  	update PENALTY_ATTACHMENT_DETAIL
	SET state = 2
  	where GENERATE_ID in 
  	<foreach collection="generateIdList" index="index" item="item"  open="(" separator="," close=")">    
        #{item}
    </foreach> 
  </update>
  <update id="batchUpdateStateByItemIds">
  	update PENALTY_ATTACHMENT_DETAIL
	SET state = 2
  	where item_id in 
  	<foreach collection="itemIdList" index="index" item="item"  open="(" separator="," close=")">    
        #{item}
    </foreach> 
  </update>
  <update id="updateStateByItemId">
  	update PENALTY_ATTACHMENT_DETAIL set state=1 where item_id=#{itemId}
  </update>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from PENALTY_ATTACHMENT_DETAIL
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyAttachmentDetail">
    insert into PENALTY_ATTACHMENT_DETAIL (ID, ITEM_ID, FILE_NAME, 
      FILE_TYPE, FILE_URL, STATE, 
      CREATE_DATE, FILE_SIZE, IS_GENERATE, 
      IS_BELONG_RECORD, GENERATE_ID)
    values (sys_guid(), #{itemId,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileType,jdbcType=DECIMAL}, #{fileUrl,jdbcType=VARCHAR}, #{state,jdbcType=DECIMAL}, 
      #{createDate,jdbcType=TIMESTAMP}, #{fileSize,jdbcType=VARCHAR}, #{isGenerate,jdbcType=DECIMAL}, 
      #{isBelongRecord,jdbcType=DECIMAL}, #{generateId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyAttachmentDetail">
    insert into PENALTY_ATTACHMENT_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        ID,
      <if test="itemId != null">
        ITEM_ID,
      </if>
      <if test="fileName != null">
        FILE_NAME,
      </if>
      <if test="fileType != null">
        FILE_TYPE,
      </if>
      <if test="fileUrl != null">
        FILE_URL,
      </if>
      <if test="state != null">
        STATE,
      </if>
      <if test="createDate != null">
        CREATE_DATE,
      </if>
      <if test="fileSize != null">
        FILE_SIZE,
      </if>
      <if test="isGenerate != null">
        IS_GENERATE,
      </if>
      <if test="isBelongRecord != null">
        IS_BELONG_RECORD,
      </if>
      <if test="generateId != null">
        GENERATE_ID,
      </if>
      <if test="location != null">
		LOCATION,
	  </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      sys_guid(),
      <if test="itemId != null">
        #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=DECIMAL},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        #{state,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=VARCHAR},
      </if>
      <if test="isGenerate != null">
        #{isGenerate,jdbcType=DECIMAL},
      </if>
      <if test="isBelongRecord != null">
        #{isBelongRecord,jdbcType=DECIMAL},
      </if>
      <if test="generateId != null">
        #{generateId,jdbcType=DECIMAL},
      </if>
      <if test="location != null">
		#{location,jdbcType=DECIMAL},
	 </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyAttachmentDetail">
    update PENALTY_ATTACHMENT_DETAIL
    <set>
      <if test="itemId != null">
        ITEM_ID = #{itemId,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        FILE_NAME = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        FILE_TYPE = #{fileType,jdbcType=DECIMAL},
      </if>
      <if test="fileUrl != null">
        FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="createDate != null">
        CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fileSize != null">
        FILE_SIZE = #{fileSize,jdbcType=VARCHAR},
      </if>
      <if test="isGenerate != null">
        IS_GENERATE = #{isGenerate,jdbcType=DECIMAL},
      </if>
      <if test="isBelongRecord != null">
        IS_BELONG_RECORD = #{isBelongRecord,jdbcType=DECIMAL},
      </if>
      <if test="generateId != null">
        GENERATE_ID = #{generateId,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        LOCATION = #{location,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyAttachmentDetail">
    update PENALTY_ATTACHMENT_DETAIL
    set ITEM_ID = #{itemId,jdbcType=VARCHAR},
      FILE_NAME = #{fileName,jdbcType=VARCHAR},
      FILE_TYPE = #{fileType,jdbcType=DECIMAL},
      FILE_URL = #{fileUrl,jdbcType=VARCHAR},
      STATE = #{state,jdbcType=DECIMAL},
      CREATE_DATE = #{createDate,jdbcType=TIMESTAMP},
      FILE_SIZE = #{fileSize,jdbcType=VARCHAR},
      IS_GENERATE = #{isGenerate,jdbcType=DECIMAL},
      IS_BELONG_RECORD = #{isBelongRecord,jdbcType=DECIMAL},
      GENERATE_ID = #{generateId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>