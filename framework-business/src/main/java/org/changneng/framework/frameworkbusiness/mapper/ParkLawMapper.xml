<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.ParkLawMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.ParkLaw" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="PARK_ID" property="parkId" jdbcType="VARCHAR" />
    <result column="LAW_ID" property="lawId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PARK_ID, LAW_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PARK_LAW
    where ID = #{id,jdbcType=VARCHAR}
   
  </select>
  <select id="selectByParkId" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select 
    count(*)
    from PARK_LAW
     where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PARK_LAW
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByParkId" parameterType="java.lang.String" >
    delete from PARK_LAW
    where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByLawId" parameterType="java.lang.String" >
    delete from PARK_LAW
    where LAW_ID = #{lawId,jdbcType=VARCHAR}
  </delete>
  <!-- 批量删除 -->
  <delete id="batchDeleteParkLawInfo">
    delete from PARK_LAW
    where park_id = #{parkId,jdbcType=VARCHAR} and 
    law_id in
    <foreach item="item" index="index" collection="ids.split(',')"  open="(" separator="," close=")">
           #{item}
    </foreach>
  </delete>
  <!-- 批量新增 -->
  <select  id="batchInsert" parameterType="java.util.List">
     insert ALL 
        <foreach item="item" index="index" collection="list"   separator="" >
        into  PARK_LAW (id, PARK_ID,LAW_ID)
        values(sys_guid(), #{item.parkId,jdbcType=VARCHAR},#{item.lawId,jdbcType=VARCHAR})
        </foreach>
         SELECT * FROM dual
  </select >
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkLaw" >
    insert into PARK_LAW (ID, PARK_ID, LAW_ID
      )
    values (#{id,jdbcType=VARCHAR}, #{parkId,jdbcType=VARCHAR}, #{lawId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkLaw" >
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  			select sys_guid() from dual
  		</selectKey>
    insert into PARK_LAW
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="parkId != null" >
        PARK_ID,
      </if>
      <if test="lawId != null" >
        LAW_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="parkId != null" >
        #{parkId,jdbcType=VARCHAR},
      </if>
      <if test="lawId != null" >
        #{lawId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkLaw" >
    update PARK_LAW
    <set >
      <if test="parkId != null" >
        PARK_ID = #{parkId,jdbcType=VARCHAR},
      </if>
      <if test="lawId != null" >
        LAW_ID = #{lawId,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkLaw" >
    update PARK_LAW
    set PARK_ID = #{parkId,jdbcType=VARCHAR},
      LAW_ID = #{lawId,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
    <!-- BaseResultMapAll -->
    <resultMap id="BaseResultMapAll" type="org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="OBJECT_NUMBER" jdbcType="VARCHAR" property="objectNumber" />
    <result column="OBJECT_NAME" jdbcType="VARCHAR" property="objectName" />
    <result column="BELONG_AREA_ID" jdbcType="VARCHAR" property="belongAreaId" />
    <result column="BELONG_AREA_NAME" jdbcType="VARCHAR" property="belongAreaName" />
    <result column="BELONG_AREA_CITY_ID" jdbcType="VARCHAR" property="belongAreaCityId" />
    <result column="BELONG_AREA_CITY_NAME" jdbcType="VARCHAR" property="belongAreaCityName" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="POWER_AREA_ID" jdbcType="VARCHAR" property="powerAreaId" />
    <result column="POWER_AREA_NAME" jdbcType="VARCHAR" property="powerAreaName" />
    <result column="GIS_COORDINATE_X" jdbcType="VARCHAR" property="gisCoordinateX" />
    <result column="GIS_COORDINATE_Y" jdbcType="VARCHAR" property="gisCoordinateY" />
    <result column="GIS_COORDINATE_X84" jdbcType="VARCHAR" property="gisCoordinateX84" />
    <result column="GIS_COORDINATE_Y84" jdbcType="VARCHAR" property="gisCoordinateY84" />
    <result column="LICENSE_NO" jdbcType="VARCHAR" property="licenseNo" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="SOCIAL_CREDIT_CODE" jdbcType="VARCHAR" property="socialCreditCode" />
    <result column="INDUSTRY_TYPE_CODE" jdbcType="VARCHAR" property="industryTypeCode" />
    <result column="INDUSTRY_TYPE_NAME" jdbcType="VARCHAR" property="industryTypeName" />
    <result column="LEVEL_CODE" jdbcType="VARCHAR" property="levelCode" />
    <result column="LEVEL_NAME" jdbcType="VARCHAR" property="levelName" />
    <result column="LEGAL_PERSON" jdbcType="VARCHAR" property="legalPerson" />
    <result column="LEGAL_PHONE" jdbcType="VARCHAR" property="legalPhone" />
    <result column="CHARGE_PERSON" jdbcType="VARCHAR" property="chargePerson" />
    <result column="CHARGE_PERSON_PHONE" jdbcType="VARCHAR" property="chargePersonPhone" />
    <result column="IS_KEY_SOURCE" jdbcType="DECIMAL" property="isKeySource" />
    <result column="IS_ONLINE_MONITOR" jdbcType="DECIMAL" property="isOnlineMonitor" />
    <result column="PRODUCT_STATE_CODE" jdbcType="VARCHAR" property="productStateCode" />
    <result column="PRODUCT_STATE_NAME" jdbcType="VARCHAR" property="productStateName" />
    <result column="IS_SOLIDWASTE_OPERUNIT" jdbcType="DECIMAL" property="isSolidwasteOperunit" />
    <result column="IS_SOLIDWASTE_CREATEUNIT" jdbcType="DECIMAL" property="isSolidwasteCreateunit" />
    <result column="IS_RISK_SOURCE" jdbcType="DECIMAL" property="isRiskSource" />
    <result column="IS_OUTFALL_STANDARD" jdbcType="DECIMAL" property="isOutfallStandard" />
    <result column="PERSON_NATURE_CODE" jdbcType="VARCHAR" property="personNatureCode" />
    <result column="PERSON_NATURE_NAME" jdbcType="VARCHAR" property="personNatureName" />
    <result column="BELONG_LEVEL_CODE" jdbcType="VARCHAR" property="belongLevelCode" />
    <result column="BELONG_LEVEL_NAME" jdbcType="VARCHAR" property="belongLevelName" />
    <result column="IS_LISTED_COMPANY" jdbcType="DECIMAL" property="isListedCompany" />
    <result column="STOCK_CODE" jdbcType="VARCHAR" property="stockCode" />
    <result column="IS_GROUP_COMPANY" jdbcType="DECIMAL" property="isGroupCompany" />
    <result column="GROUP_COMPANY_NAME" jdbcType="VARCHAR" property="groupCompanyName" />
    <result column="GROUP_COMPANY_ORGCODE" jdbcType="VARCHAR" property="groupCompanyOrgcode" />
    <result column="GROUP_COMPANY_STOCKCODE" jdbcType="VARCHAR" property="groupCompanyStockcode" />
    <result column="PERSONCARD_TYPE_CODE" jdbcType="VARCHAR" property="personcardTypeCode" />
    <result column="PERSONCARD_TYPE_NAME" jdbcType="VARCHAR" property="personcardTypeName" />
    <result column="CARD_NUMBER" jdbcType="VARCHAR" property="cardNumber" />
    <result column="CARD_TYPE_CODE" jdbcType="VARCHAR" property="cardTypeCode" />
    <result column="CARD_TYPE_NAME" jdbcType="VARCHAR" property="cardTypeName" />
    <result column="IS_SPECIAL_ORG" jdbcType="DECIMAL" property="isSpecialOrg" />
    <result column="MANAGE_ORG_NAME" jdbcType="VARCHAR" property="manageOrgName" />
    <result column="SEX" jdbcType="DECIMAL" property="sex" />
    <result column="OBJECT_DESC" jdbcType="VARCHAR" property="objectDesc" />
    <result column="PROTECTED_AREA_DESC" jdbcType="VARCHAR" property="protectedAreaDesc" />
    <result column="MAIN_PROTECT_OBJECT" jdbcType="VARCHAR" property="mainProtectObject" />
    <result column="CITY" jdbcType="VARCHAR" property="city" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime" />
    <result column="DANGER_COEFFICIENT" jdbcType="VARCHAR" property="dangerCoefficient" />
    <result column="RANDOM_STATE" jdbcType="VARCHAR" property="randomState" />
    <result column="IS_LAW_PASS" jdbcType="DECIMAL" property="isLawPass" />
    <result column="RANDOM_ATTR_NAME" jdbcType="VARCHAR" property="randomAttrName" />
    <result column="LASTUPDATEMAN" jdbcType="VARCHAR" property="lastupdateman" />
    <result column="LEGAL_MAN_ID_CARD" jdbcType="VARCHAR" property="legalManIdCard" />
    <result column="CHARGE_MAN_ID_CARD" jdbcType="VARCHAR" property="chargeManIdCard" />
    <result column="STANDENTERID" jdbcType="VARCHAR" property="standenterid" />
    
    <result column="isIntoPark" jdbcType="VARCHAR" property="isIntoPark" />
    
    <!-- 失信被执行人 -->
    <result column="SUPERVISE_DATE" jdbcType="TIMESTAMP" property="superviseDate" />
    <result column="IS_SUPERVISE" jdbcType="DECIMAL" property="isSupervise" />
  </resultMap>
  <select id="selectLawEnforceObject" resultMap="BaseResultMapAll" >
select leo.id,leo.object_name,leo.type_name,leo.card_type_code,leo.card_number,TA.PROVINCE || TA.CITY || TA.COUNTRY as BELONG_AREA_NAME,leo.legal_person,leo.charge_person,leo.charge_person_phone,leo.legal_phone,leo.org_code,leo.ADDRESS,leo.belong_area_id,leo.DELMARK,leo.license_no,leo.social_credit_code as social_credit_code,leo.TYPE_CODE,leo.personcard_type_name,leo.isIntoPark from (
select leo.id,leo.object_name,leo.type_name,leo.charge_person,leo.charge_person_phone,LEO.card_type_code,LEO.legal_person,LEO.ADDRESS,LEO.belong_area_id,LEO.card_number,LEO.DELMARK,LEO.legal_phone,LEO.license_no,LEO.org_code,LEO.social_credit_code,leo.TYPE_CODE,leo.personcard_type_name,'1' as isIntoPark from park_law pl LEFT JOIN LAW_ENFORCE_OBJECT leo on PL.LAW_ID=leo.id
where leo.delmark = 1 and PL.PARK_ID= #{seachBean.parkId,jdbcType = VARCHAR}
union all 
select leo.id,leo.object_name,leo.type_name,leo.charge_person,leo.charge_person_phone,LEO.card_type_code,LEO.legal_person,LEO.ADDRESS,LEO.belong_area_id,LEO.card_number,LEO.DELMARK,LEO.legal_phone,LEO.license_no,LEO.org_code,LEO.social_credit_code,leo.TYPE_CODE,leo.personcard_type_name,'0' as isIntoPark from LAW_ENFORCE_OBJECT leo LEFT JOIN park_law pl on PL.LAW_ID=leo.id
where leo.delmark = 1 AND leo.ID not in (select LAW_ID from PARK_LAW where PARK_ID =#{seachBean.parkId,jdbcType = VARCHAR})) leo left join T_AREA ta on LEO.BELONG_AREA_ID = TA.CODE where 1=1
	  <if test="seachBean.objectName != null and seachBean.objectName !='' ">
       AND  leo.OBJECT_NAME  like  CONCAT(CONCAT('%',#{seachBean.objectName}),'%')
      </if>
      <if test="seachBean.typeCode != null and seachBean.typeCode !='' ">
      	AND  leo.TYPE_CODE = #{seachBean.typeCode}
      </if>
      <if test="seachBean.licenseNo != null and seachBean.licenseNo !='' ">
       	AND  leo.license_no like  CONCAT(CONCAT('%',#{seachBean.licenseNo}),'%')
      </if>
      <if test="seachBean.address != null and seachBean.address !='' ">
       	AND  leo.ADDRESS like  CONCAT(CONCAT('%',#{seachBean.address}),'%')
      </if>
      <if test="seachBean.isIntoPark != null and seachBean.isIntoPark !='' ">
       	AND  leo.isIntoPark = #{seachBean.isIntoPark}
      </if>
      <!-- 所在行政区 -->
     <if test="seachBean.belongAreaId!=null and seachBean.belongAreaId!=''">
	    and belong_area_id like concat('%',concat(#{seachBean.belongAreaId},'%'))
     </if> 
     order by isIntoPark desc,BELONG_AREA_ID asc ,OBJECT_NAME asc 
  </select>
  <!-- 根据园区id查询监管对象总数 -->
  <select id="selectCountById" resultType="java.lang.Integer" parameterType="java.lang.String" >
    select count(*) from PARK_LAW where PARK_ID = #{id,jdbcType=VARCHAR}
  </select>
  <!-- 根据园区id查询监管对象总数 -->
  <select id="selectCountByParam" resultType="java.lang.Integer" parameterType="java.lang.String">
    select count(*) from PARK_LAW pl left join LAW_ENFORCE_OBJECT leo on pl.LAW_ID = leo.ID
     where PARK_ID = #{id,jdbcType=VARCHAR}
    <if test="startTime != null and startTime !='' ">
      AND  leo.CREATETIME >= TO_DATE(#{startTime}, 'YYYY-MM-DD')
    </if>
    <if test="endTime != null and endTime !='' ">
      AND  leo.CREATETIME &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD')
    </if>
  </select>
  <!--根据园区ID查询对象ID  -->
  <select id="selectDataByParkId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
      <include refid="Base_Column_List" /> 
      from PARK_LAW where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </select>
</mapper>