package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;

/**
 * 环境监管一件事-检查项配置 Mapper接口
 * 提供基础的平铺数据查询方法，树形结构在Service层构建
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigMapper {

    /**
     * 查询所有检查项配置记录
     * 返回平铺的数据列表，按排序字段和创建时间排序
     *
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectAll();

    /**
     * 根据父级ID查询直接子项列表
     *
     * @param parentId 父级ID
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectByParentId(@Param("parentId") String parentId);

    /**
     * 查询顶级检查项（父级ID为空或null）
     *
     * @return 检查项配置列表
     */
    List<CheckItemConfig> selectTopLevelItems();

    /**
     * 根据主键查询记录
     *
     * @param id 主键ID
     * @return 检查项配置对象
     */
    CheckItemConfig selectByPrimaryKey(String id);
}
