<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.CaseSubmitTableMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CASE_ID" jdbcType="VARCHAR" property="caseId" />
    <result column="MODELER_CASE_ID" jdbcType="VARCHAR" property="modelerCaseId" />
    <result column="MODELER_CASE_NAME" jdbcType="VARCHAR" property="modelerCaseName" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_NAME" jdbcType="VARCHAR" property="userName" />
    <result column="USER_AREA_CODE" jdbcType="VARCHAR" property="userAreaCode" />
    <result column="USER_AREA_NAME" jdbcType="VARCHAR" property="userAreaName" />
    <result column="USER_DEPT_NUMBER" jdbcType="VARCHAR" property="userDeptNumber" />
    <result column="USER_DEPT_NAME" jdbcType="VARCHAR" property="userDeptName" />
    <result column="FILES_NAME" jdbcType="VARCHAR" property="filesName" />
    <result column="FLIES_URL" jdbcType="VARCHAR" property="fliesUrl" />
    <result column="CREAT_DATE" jdbcType="TIMESTAMP" property="creatDate" />
    <result column="SUBMIT_TYPE" jdbcType="DECIMAL" property="submitType" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CASE_ID, MODELER_CASE_ID, MODELER_CASE_NAME, USER_ID, USER_NAME, USER_AREA_CODE, 
    USER_AREA_NAME, USER_DEPT_NUMBER, USER_DEPT_NAME, FILES_NAME, FLIES_URL, CREAT_DATE, 
    SUBMIT_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_SUBMIT_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CASE_SUBMIT_TABLE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable">
    insert into CASE_SUBMIT_TABLE (ID, CASE_ID, MODELER_CASE_ID, 
      MODELER_CASE_NAME, USER_ID, USER_NAME, 
      USER_AREA_CODE, USER_AREA_NAME, USER_DEPT_NUMBER, 
      USER_DEPT_NAME, FILES_NAME, FLIES_URL, 
      CREAT_DATE, SUBMIT_TYPE)
    values (#{id,jdbcType=VARCHAR}, #{caseId,jdbcType=VARCHAR}, #{modelerCaseId,jdbcType=VARCHAR}, 
      #{modelerCaseName,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR}, 
      #{userAreaCode,jdbcType=VARCHAR}, #{userAreaName,jdbcType=VARCHAR}, #{userDeptNumber,jdbcType=VARCHAR}, 
      #{userDeptName,jdbcType=VARCHAR}, #{filesName,jdbcType=VARCHAR}, #{fliesUrl,jdbcType=VARCHAR}, 
      #{creatDate,jdbcType=TIMESTAMP}, #{submitType,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable">
    insert into CASE_SUBMIT_TABLE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="caseId != null">
        CASE_ID,
      </if>
      <if test="modelerCaseId != null">
        MODELER_CASE_ID,
      </if>
      <if test="modelerCaseName != null">
        MODELER_CASE_NAME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="userName != null">
        USER_NAME,
      </if>
      <if test="userAreaCode != null">
        USER_AREA_CODE,
      </if>
      <if test="userAreaName != null">
        USER_AREA_NAME,
      </if>
      <if test="userDeptNumber != null">
        USER_DEPT_NUMBER,
      </if>
      <if test="userDeptName != null">
        USER_DEPT_NAME,
      </if>
      <if test="filesName != null">
        FILES_NAME,
      </if>
      <if test="fliesUrl != null">
        FLIES_URL,
      </if>
      <if test="creatDate != null">
        CREAT_DATE,
      </if>
      <if test="submitType != null">
        SUBMIT_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="modelerCaseId != null">
        #{modelerCaseId,jdbcType=VARCHAR},
      </if>
      <if test="modelerCaseName != null">
        #{modelerCaseName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAreaCode != null">
        #{userAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="userAreaName != null">
        #{userAreaName,jdbcType=VARCHAR},
      </if>
      <if test="userDeptNumber != null">
        #{userDeptNumber,jdbcType=VARCHAR},
      </if>
      <if test="userDeptName != null">
        #{userDeptName,jdbcType=VARCHAR},
      </if>
      <if test="filesName != null">
        #{filesName,jdbcType=VARCHAR},
      </if>
      <if test="fliesUrl != null">
        #{fliesUrl,jdbcType=VARCHAR},
      </if>
      <if test="creatDate != null">
        #{creatDate,jdbcType=TIMESTAMP},
      </if>
      <if test="submitType != null">
        #{submitType,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable">
    update CASE_SUBMIT_TABLE
    <set>
      <if test="caseId != null">
        CASE_ID = #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="modelerCaseId != null">
        MODELER_CASE_ID = #{modelerCaseId,jdbcType=VARCHAR},
      </if>
      <if test="modelerCaseName != null">
        MODELER_CASE_NAME = #{modelerCaseName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        USER_NAME = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userAreaCode != null">
        USER_AREA_CODE = #{userAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="userAreaName != null">
        USER_AREA_NAME = #{userAreaName,jdbcType=VARCHAR},
      </if>
      <if test="userDeptNumber != null">
        USER_DEPT_NUMBER = #{userDeptNumber,jdbcType=VARCHAR},
      </if>
      <if test="userDeptName != null">
        USER_DEPT_NAME = #{userDeptName,jdbcType=VARCHAR},
      </if>
      <if test="filesName != null">
        FILES_NAME = #{filesName,jdbcType=VARCHAR},
      </if>
      <if test="fliesUrl != null">
        FLIES_URL = #{fliesUrl,jdbcType=VARCHAR},
      </if>
      <if test="creatDate != null">
        CREAT_DATE = #{creatDate,jdbcType=TIMESTAMP},
      </if>
      <if test="submitType != null">
        SUBMIT_TYPE = #{submitType,jdbcType=DECIMAL},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseSubmitTable">
    update CASE_SUBMIT_TABLE
    set CASE_ID = #{caseId,jdbcType=VARCHAR},
      MODELER_CASE_ID = #{modelerCaseId,jdbcType=VARCHAR},
      MODELER_CASE_NAME = #{modelerCaseName,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=VARCHAR},
      USER_NAME = #{userName,jdbcType=VARCHAR},
      USER_AREA_CODE = #{userAreaCode,jdbcType=VARCHAR},
      USER_AREA_NAME = #{userAreaName,jdbcType=VARCHAR},
      USER_DEPT_NUMBER = #{userDeptNumber,jdbcType=VARCHAR},
      USER_DEPT_NAME = #{userDeptName,jdbcType=VARCHAR},
      FILES_NAME = #{filesName,jdbcType=VARCHAR},
      FLIES_URL = #{fliesUrl,jdbcType=VARCHAR},
      CREAT_DATE = #{creatDate,jdbcType=TIMESTAMP},
      SUBMIT_TYPE = #{submitType,jdbcType=DECIMAL}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>