package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.TaskMapper;
import org.changneng.framework.frameworkbusiness.dao.notice.NewInfoTableMapper;
import org.changneng.framework.frameworkbusiness.dao.notice.NewInfoUserMapper;
import org.changneng.framework.frameworkbusiness.entity.Task;
import org.changneng.framework.frameworkbusiness.entity.filecase.QuestionCaseTable;
import org.changneng.framework.frameworkbusiness.entity.notice.NewInfoTable;
import org.changneng.framework.frameworkbusiness.entity.notice.NewInfoUser;
import org.changneng.framework.frameworkbusiness.entity.notice.PushMessEntity;
import org.changneng.framework.frameworkbusiness.mongodb.bean.NewInfoCase;
import org.changneng.framework.frameworkbusiness.mongodb.bean.NewInfoTask;
import org.changneng.framework.frameworkbusiness.mongodb.bean.filecase.DocPublicBean;
import org.changneng.framework.frameworkbusiness.mongodb.dao.impl.ExecuDocMongoPubDaoImpl;
import org.changneng.framework.frameworkbusiness.service.NewInfoTableService;
import org.changneng.framework.frameworkbusiness.service.filecase.MessToRedisService;
import org.changneng.framework.frameworkbusiness.service.filecase.impl.MessToRedisUtilImpl;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.JavaMail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@Service
public class NewInfoTableServiceImpl implements NewInfoTableService {

	@Autowired
	private NewInfoTableMapper newInfoTableMapper;

	@Autowired
	private NewInfoUserMapper newInfoUserMapper;

	@Autowired
	private TaskMapper taskMapper;

	@Autowired
	private ExecuDocMongoPubDaoImpl execuDocMongoPubDaoImpl;

	@Autowired
	private MessToRedisService messtoredisservice;

	@Override
	public void creatSendTaskInsertNewInfoTable(Task task) throws Exception {
		List<PushMessEntity> objList = new ArrayList<>();
		try {
			Task tmpTask = taskMapper.selectByNewInfo(task.getId());
			// 先写入mongo 得到ID回写主体
			DocPublicBean bean = saveDocPublicBeanTypeTask(tmpTask);
			// 录入主体，得到ID回写子信息类
			NewInfoTable infoTable = new NewInfoTable();
		    infoTable  = insertNewInfoTable(infoTable,bean, 0);
		    // 录入子信息表
		    infoTable.setSender(task.getCreatUserName());
		    String handlingPersonIdList = task.getChecUserIds();
		    if(handlingPersonIdList!=null){
		    	String[] userIdList  = handlingPersonIdList.split(",");
		    	for (int i = 0; i < userIdList.length; i++) {
		    		NewInfoUser infoUser = new NewInfoUser();
					String str= userIdList[i];
					insertNewInfoUser(infoTable, infoUser, tmpTask, 0, str);
					// 极光推送
					PushMessEntity tmp = new PushMessEntity();
					tmp.setId(infoUser.getId());
					tmp.setUserId(infoUser.getUserId());
					tmp.setInfoType(infoUser.getInfoType());
					tmp.setMessageType(1);
					tmp.setTitle(infoUser.getTitle());
					objList.add(tmp);
				}
		    }
		    // 获得当前轮盘格数
			int index = MessToRedisUtilImpl.getIndexMinute();
			//存储到推送消息的Redis中
			messtoredisservice.chengeRedisPushList3(objList, index);
		} catch (Exception e) {
			e.printStackTrace();
			//todo 0730暂时注释掉
//			JavaMail.sendEmail("定时异常通知", "财主，这次是大哥的分配推送消息出问题了，赶紧给我排查一下。", e.getMessage());
		}


	}

	@Override
	public void creatTaskNewInfoTables(List<NewInfoTable> list) {
		if (!list.isEmpty()) {
			List<PushMessEntity> objList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				try {
					if (list.get(i).getTempId() != null) {
						NewInfoTable infoTable  = list.get(i);
						Task task = taskMapper.selectByNewInfo(list.get(i).getTempId());
						// 先写入mongo 得到ID回写主体
						DocPublicBean bean = saveDocPublicBeanTypeTask(task);
						// 录入主体，得到ID回写子信息类
					    infoTable  = insertNewInfoTable(infoTable,bean, 1);
						// 写入子信息类
					    NewInfoUser infoUser = new  NewInfoUser();
						insertNewInfoUser(infoTable,infoUser, task, 1,infoTable.getHandlingPersonId());
						// 极光推送
						PushMessEntity tmp = new PushMessEntity();
						tmp.setId(infoUser.getId());
						tmp.setUserId(infoUser.getUserId());
						tmp.setInfoType(infoUser.getInfoType());
						tmp.setMessageType(1);
						tmp.setTitle(infoUser.getTitle());
						objList.add(tmp);
					}
				} catch (Exception ex) {
					ex.printStackTrace();
					//JavaMail.sendEmail("定时异常通知", "财主，大哥的定时发送预警倒计时任务出问题了，赶紧给我排查一下。", ex.getMessage());
				}
			}
			//存储到推送消息的Redis中
			messtoredisservice.chengeRedisPushList24(objList, null);
		}
	}


	@Override
	public void insertCaseNewInfoTypeCase(List<QuestionCaseTable> list,int day,int type) {
		if(!list.isEmpty()){
			List<PushMessEntity> objList = new ArrayList<>();
			for (int i = 0; i < list.size(); i++) {
				QuestionCaseTable table = list.get(i);
				try {
					DocPublicBean bean  = saveDocPublicBeanTypeCase(table,type);
					// 录入主体，得到ID回写子信息类
					NewInfoTable infoTable = new NewInfoTable();
					infoTable.setDay(day+"");
				    infoTable  = insertNewInfoTable(infoTable,bean, type);
					// 写入子信息类
				    NewInfoUser infoUser = new  NewInfoUser();
				    insertNewInfoUserByCase(infoTable,infoUser, table, type);
				    // 极光推送
					PushMessEntity tmp = new PushMessEntity();
					tmp.setId(infoUser.getId());
					tmp.setUserId(infoUser.getUserId());
					tmp.setInfoType(infoUser.getInfoType());
					tmp.setMessageType(1);
					tmp.setTitle(infoUser.getTitle());
					objList.add(tmp);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			//存储到推送消息的Redis中
			messtoredisservice.chengeRedisPushList24(objList, null);
		}
	}
	/**
	 * mongo 任务相关信息保存
	 * @param task
	 * @return
	 * <AUTHOR>
	 * @date 2017年12月26日-下午6:44:50
	 */
	private DocPublicBean saveDocPublicBeanTypeTask(Task task){
		NewInfoTask mongoTask = new  NewInfoTask();
		// 先写入mongo 得到ID回写主体
		mongoTask.setTaskId(task.getTaskId() != null ? task.getTaskId() : "");
		mongoTask.setLimitTime(task.getLimitTime() != null ? task.getLimitTime() : null);
		mongoTask.setLawObjectName(task.getLawObjectName());
		mongoTask.setCity(task.getCity());
		mongoTask.setCountry(task.getCountry());
		DocPublicBean bean = new DocPublicBean();
		bean.setInfoJson((JSONObject) JSON.toJSON(mongoTask));
		execuDocMongoPubDaoImpl.save(bean);
		return bean;
	}

	/**
	 * 消息推送案件相关的消息推送
	 * @param caseTable
	 * @param type
	 * @return
	 * <AUTHOR>
	 * @date 2018年1月2日-下午6:25:12
	 */
	private DocPublicBean saveDocPublicBeanTypeCase(QuestionCaseTable caseTable,int type){
		NewInfoCase mongoCase = new  NewInfoCase();
		// 先写入mongo 得到ID回写主体
		mongoCase.setCaseId(caseTable.getCaseId()!=null?caseTable.getCaseId():"");
		mongoCase.setCaseName(caseTable.getCaseName()!=null?caseTable.getCaseName():"");
		mongoCase.setInformantId(caseTable.getInformantId()!=null?caseTable.getInformantId():"");
		mongoCase.setLawObjectName(caseTable.getLawObjectName()!=null?caseTable.getLawObjectName():"");
		mongoCase.setCaseReasonName(caseTable.getCase1()!=null?caseTable.getCase1():"");
		mongoCase.setCreateDate(caseTable.getCreateDate()!=null?caseTable.getCreateDate():null);
		mongoCase.setDecisionDate(caseTable.getDecisionDate()!=null?caseTable.getDecisionDate():null);
		mongoCase.setCity(caseTable.getCase2()!=null?caseTable.getCase2():"");
		mongoCase.setCaseNumber(caseTable.getCaseNumber()!=null?caseTable.getCaseNumber():"");
		mongoCase.setCountry(caseTable.getCase3()!=null?caseTable.getCase3():"");
		if(2==type){
			//消息类型3 行政处罚下达期限（标红加粗）：“案件建立时间”向后算4个月。
			if(caseTable.getCreateDate()!=null){
				mongoCase.setLimitTime(DateUtil.addMonth(caseTable.getCreateDate(),4));
			}
		}else{
			//消息类型4 结案或申请法院强制执行期限（标红加粗）：“行政处罚下达时间”向后算6个月。
			if(caseTable.getDecisionDate()!=null){
				mongoCase.setLimitTime(DateUtil.addMonth(caseTable.getDecisionDate(),6));
			}
		}
		DocPublicBean bean = new DocPublicBean();
		bean.setInfoJson((JSONObject) JSON.toJSON(mongoCase));
		execuDocMongoPubDaoImpl.save(bean);
		return bean;
	}



	/**
	 * 主干消息保存
	 * @param bean
	 * @param infoType 消息类型： 0 用户被分配 1 执法距限办 2 建立案件未处罚 3 案件程序不完整
	 * @return
	 * <AUTHOR>
	 * @date 2017年12月26日-下午6:44:17
	 */
	private NewInfoTable insertNewInfoTable(NewInfoTable infoTable,DocPublicBean bean,Integer infoType){
		// 录入主体，得到ID回写子信息类
		infoTable.setCreateType(0); // 目前只有  创建类型： 0：系统推送（初始化）  1：业务发送      后期可扩展
		infoTable.setInfoType(infoType);
		infoTable.setCreateDate(new Date());
		infoTable.setMongoId(bean.getMongoId());
		newInfoTableMapper.insertSelective(infoTable);
		return infoTable;
	}

	/**
	 * 消息子信息表录入，基于主干保存过，并且知道任务信息
	 * @param baseInfo 主干消息表
	 * @param task 任务相关信息
	 * @param infoType 消息类型： 0 用户被分配 1 执法距限办 2 建立案件未处罚 3 案件程序不完整
	 * @return
	 * <AUTHOR>
	 * @date 2017年12月27日-下午2:09:53
	 */
	private NewInfoUser insertNewInfoUser(NewInfoTable baseInfo,NewInfoUser infoUser,Task task,Integer infoType,String handlingPersonId){
		infoUser.setUserId(handlingPersonId != null ? handlingPersonId : "");
		if(0==infoType){
			// XXX给您分配了一个执法任务。
			infoUser.setTitle(baseInfo.getSender()+"给您分配了一个执法任务");
		}else if(1==infoType){
			//您的待办执法XXXXXX，距离限办日期还有5天。
			infoUser.setTitle("您的待办执法" + task.getTaskId() + "，距离限办日期还有" + baseInfo.getDay() + "天。");
		}else{
			// 错误或者未开始建设类型
			infoUser.setTitle("标题也去执法了。。。");
		}
		infoUser.setInfoType(infoType);
		infoUser.setNewInfoId(baseInfo.getId());
		infoUser.setCreateDate(new Date());
		newInfoUserMapper.insertSelective(infoUser);
		return infoUser;
	}

	/**
	 * 消息子信息表录入，基于主干保存过，并且知道任务信息
	 * @param baseInfo 主干消息表
	 * @param task 任务相关信息
	 * @param infoType 消息类型：2 建立案件未处罚 3 案件程序不完整
	 * @return
	 * <AUTHOR>
	 * @date 2017年12月27日-下午2:09:53
	 */
	private NewInfoUser insertNewInfoUserByCase(NewInfoTable baseInfo,NewInfoUser infoUser,QuestionCaseTable table,Integer infoType){
		infoUser.setUserId(table.getInformantId() != null ? table.getInformantId() : "");
		if(2 == infoType){
			// 您的案件XXXXXX，需要在5天内下达处罚决定。
			infoUser.setTitle("您的案件" + table.getCaseNumber()+ "，需要在" + baseInfo.getDay() + "天内下达处罚决定。");
		}else if(3 == infoType){
			//您的案件XXXXXX，需要在3天内结案或申请法院强制执行。
			infoUser.setTitle("您的案件" + table.getCaseNumber()+ "，需要在" + baseInfo.getDay() + "天内结案或申请法院强制执行。");
		}else{
			// 错误或者未开始建设类型
			infoUser.setTitle("标题也去执法了。。。");
		}
		infoUser.setInfoType(infoType);
		infoUser.setNewInfoId(baseInfo.getId());
		infoUser.setCreateDate(new Date());
		newInfoUserMapper.insertSelective(infoUser);
		return infoUser;
	}
}
