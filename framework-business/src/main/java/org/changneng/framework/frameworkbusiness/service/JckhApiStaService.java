package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import java.util.Map;

import org.changneng.framework.frameworkbusiness.entity.CaseStatisSearchBean;
import org.changneng.framework.frameworkbusiness.entity.JckhSearchListBean;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;

/**
 * App端统计分析业务处理（整合web端统计数据）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018年1月31日 下午4:44:03
 */
public interface JckhApiStaService {

	/**
	 * App端点击统计分析获取首页所有统计信息
	 * @param sysUser
	 * @return
	 * 
	 * <AUTHOR>
	 */
	Map<String, Object> staGeneInfo(SysUsers sysUser) throws Exception;

	/**
	 * App端点击统计分析获取执法情况所有统计信息
	 * @param type 选择的类型：1执法任务办结数，2人均执法天数，3人均执法记录数，4日均执法任务办结数，5执法检查人天总数，6日均执法检查人数
	 * @param sysUser
	 * @return
	 * 
	 * <AUTHOR>
	 */
	Map<String,Object> staTaskInfo(JckhSearchListBean searchListBean, SysUsers sysUser);

	/**
	 * App端点击统计分析获取执法情况所有统计信息
	 * @param type 选择的类型：1大案件数，2处罚总金额，3行政处罚案件数，4行政处罚金额，5按日连续处罚案件数，6按日连续处罚总金额
	 * 				<br/> 7.查封扣押案件数，8限产停产案件数，9移送拘留案件数，10涉嫌污染犯罪移送公安机关，11申请法院强制执行，12行政命令，13其他移送
	 * @return
	 * 
	 * <AUTHOR>
	 */
	Map<String,Object> staCaseInfo(CaseStatisSearchBean searchBean, SysUsers sysUser);

}
