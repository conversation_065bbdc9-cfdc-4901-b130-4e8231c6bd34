package org.changneng.framework.frameworkbusiness.entity;

public class SceneUsuallyModel {
    private String id;

    private String userid;

    private String customDatabaseId;

    private String templateObjectType;

    
    public SceneUsuallyModel(String id, String userid, String customDatabaseId,
			String templateObjectType) {
		super();
		this.id = id;
		this.userid = userid;
		this.customDatabaseId = customDatabaseId;
		this.templateObjectType = templateObjectType;
	}
    
    
	public SceneUsuallyModel() {
		super();
		// TODO Auto-generated constructor stub
	}


	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid == null ? null : userid.trim();
    }

    public String getCustomDatabaseId() {
        return customDatabaseId;
    }

    public void setCustomDatabaseId(String customDatabaseId) {
        this.customDatabaseId = customDatabaseId == null ? null : customDatabaseId.trim();
    }

    public String getTemplateObjectType() {
        return templateObjectType;
    }

    public void setTemplateObjectType(String templateObjectType) {
        this.templateObjectType = templateObjectType == null ? null : templateObjectType.trim();
    }
}