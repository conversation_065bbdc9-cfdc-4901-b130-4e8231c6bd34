package org.changneng.framework.frameworkbusiness.service;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;


public interface ISurveyRecordService {
	
	/**
	 * 保存勘察笔录信息
	 * @param surveyRecord
	 * @return
	 * @throws Exception
	 */
	public int saveSurveyRecord(SurveyRecord surveyRecord) throws Exception;
	public int saveSurveyRecords(SurveyRecord surveyRecord, List<SysFiles> filesList) throws Exception;
	/**
	 * 重新生成勘察笔录信息
	 * @param surveyRecord
	 * @return
	 * @throws Exception
	 */
	public int generateSurveyRecord(SurveyRecord surveyRecord) throws Exception;
	
	/**
	 * 删除勘察记录
	 * @param recordId
	 * @return
	 */
	public int deleteSurveyRecord(String recordId);
	
	/**
	 * 查询勘查笔录名称是否有重复的
	 * @param taskId
	 * @param recordName
	 * @return
	 * @throws Exception
	 */
	public int queryIsRepeat(String taskId,String recordName) throws Exception;
	
	/**
	 * 根据任务id查询勘察笔录信息，若无ID，默认查询最新的
	 * @param taskId
	 * @param recordId
	 * @return
	 * @throws Exception
	 */
	public SurveyRecord getReordInfoByID(String taskId,String recordId) throws Exception;
	
	/**
	 * 导入离线模版
	 * @param is
	 * @return
	 */
	public Map<String, Object> readContentFroExcel(InputStream is);
	
	/**
	 * 根据用户areacode查询环保局（并非部门）名称
	 * @param areacode
	 * @return
	 * @throws Exception
	 */
	public String queryHBJNameByAreacode(String areacode)throws Exception;
	
	/**
	 * 勘验记录保存勘验附图
	 * @param taskId
	 * @param recorID
	 * @param list
	 * @throws BusinessException
	 */
	void saveKyblPic(String taskId,String recorID,String fileCode,List<SysFiles> list) throws BusinessException;

	/**
	 * 根据recordId从survey_pics表中获取附件数据
	 * @param taskId
	 * @param surveyId
	 * @return
	 */
	public List<SurveyPics> getAppendFile(String surveyId);
	
	/**
	 * 根据SURVEY_PICS表的ID删除其中的数据并将SYS_FILES表中的数据状态置为2
	 * @param surveyPicId
	 */
	public void fileinfoDelete(String surveyPicId) throws BusinessException;

	/**
	 * 根据SURVEY_PICS表ID获取对象
	 * @param id
	 * @return
	 */
	public SurveyPics getPicById(String id);
	
	
	public SurveyRecord initRecord(SurveyRecord surveyRecord,String flag,LawObjectTypeBean lawObj) throws Exception;
	/**
	 * 模板库查询模板现场情况查询
	 * @param surveyRecordBean
	 * @return
	 */
	public SurveyItemDatabase localDescList(SurveyRecordBean surveyRecordBean);
	/**
	 * 根据任务的id查询法律对象，行业类型和类型
	 * @param taskId
	 * @return
	 */
	public LawEnforceObject selectLawObjectByTaskId(String taskId);
	/**
	 *   保存自定义模板
	 * @param surveyCustomtModel
	 * @return
	 */
	public ResponseJson saveTemplate(SurveyCustomtModel surveyCustomtModel)throws Exception;
	/**
	 * 勘查笔录自定义模版列表（根据用户，和执法对象类型区分）
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @return
	 */
	public PageBean<AskingCustomModel> inquestRecCustomModeList(
			AskingCustomBean customBean);
	/**
	 * 修改默认和常用项和删除
	 * @param customBean
	 * @param request
	 * @param response
	 */
	public JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean)throws Exception;
	
	/**
	 * 勘查笔录现有记录模版列表（根据用户，和执法对象类型区分）
	 * @param model
	 * @param lawObj
	 * @param request
	 * @param response
	 * @return
	 */
	public PageBean<InquestRecExistBean> getInquestRecExistModelByLawObject(
			AskingCustomBean customBean);

	/**
	 * 现场情况自动保存
	 * @param textVal
	 * @param taskId
	 * @param recordId
	 */
	public void autoSave(String textVal, String taskId, String recordId) throws Exception;

    List<SysFiles> uploadFiless(HttpServletRequest request, HttpServletResponse response, SysUsers sysUsers) throws BusinessException;

	void updateAttachmentFileName(String id, String attachmentFileName) throws BusinessException;


	SurveyRecord getsurveyRecordById(String id);
}
