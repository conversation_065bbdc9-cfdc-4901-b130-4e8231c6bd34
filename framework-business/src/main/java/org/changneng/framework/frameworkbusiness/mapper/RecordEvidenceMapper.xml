<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.RecordEvidenceMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.RecordEvidence" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="TASK_ID" property="taskId" jdbcType="VARCHAR" />
    <result column="EVIDENCE_OBJECT" property="evidenceObject" jdbcType="VARCHAR" />
    <result column="RECORD_MAN" property="recordMan" jdbcType="VARCHAR" />
    <result column="RECORD_ADDRESS" property="recordAddress" jdbcType="VARCHAR" />
    <result column="RECORD_TIME" property="recordTime" jdbcType="TIMESTAMP" />
    <result column="LOCATION_INFO" property="locationInfo" jdbcType="VARCHAR" />
    <result column="RECORD_URL" property="recordUrl" jdbcType="VARCHAR" />
    <result column="FILE_ID" property="fileId" jdbcType="VARCHAR" />
    <result column="PARTY" property="party" jdbcType="VARCHAR" />
    <result column="LAW_ENFORC_PERSON" property="lawEnforcPerson" jdbcType="VARCHAR" />
    <result column="LAW_ENFORC_ID" property="lawEnforcId" jdbcType="VARCHAR" />
    <result column="RECORD_UPDATE_TIME" property="recordUpdateTime" jdbcType="TIMESTAMP" />
    <result column="CREATE_USER_ID" property="createUserId" jdbcType="VARCHAR" />
    <result column="CREATE_USER_NAME" property="createUserName" jdbcType="VARCHAR" />
    <result column="RECORD_CREATE_TIME" property="recordCreateTime" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, TASK_ID, EVIDENCE_OBJECT, RECORD_MAN, RECORD_ADDRESS, RECORD_TIME, LOCATION_INFO, 
    RECORD_URL, FILE_ID, PARTY, LAW_ENFORC_PERSON, LAW_ENFORC_ID, RECORD_UPDATE_TIME, 
    CREATE_USER_ID, CREATE_USER_NAME, RECORD_CREATE_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from RECORD_EVIDENCE
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from RECORD_EVIDENCE
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.RecordEvidence" >
    insert into RECORD_EVIDENCE (ID, TASK_ID, EVIDENCE_OBJECT, 
      RECORD_MAN, RECORD_ADDRESS, RECORD_TIME, 
      LOCATION_INFO, RECORD_URL, FILE_ID, 
      PARTY, LAW_ENFORC_PERSON, LAW_ENFORC_ID, 
      RECORD_UPDATE_TIME, CREATE_USER_ID, CREATE_USER_NAME, 
      RECORD_CREATE_TIME)
    values (#{id,jdbcType=VARCHAR}, #{taskId,jdbcType=VARCHAR}, #{evidenceObject,jdbcType=VARCHAR}, 
      #{recordMan,jdbcType=VARCHAR}, #{recordAddress,jdbcType=VARCHAR}, #{recordTime,jdbcType=TIMESTAMP}, 
      #{locationInfo,jdbcType=VARCHAR}, #{recordUrl,jdbcType=VARCHAR}, #{fileId,jdbcType=VARCHAR}, 
      #{party,jdbcType=VARCHAR}, #{lawEnforcPerson,jdbcType=VARCHAR}, #{lawEnforcId,jdbcType=VARCHAR}, 
      #{recordUpdateTime,jdbcType=TIMESTAMP}, #{createUserId,jdbcType=VARCHAR}, #{createUserName,jdbcType=VARCHAR}, 
      #{recordCreateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RecordEvidence" >
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into RECORD_EVIDENCE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="taskId != null" >
        TASK_ID,
      </if>
      <if test="evidenceObject != null" >
        EVIDENCE_OBJECT,
      </if>
      <if test="recordMan != null" >
        RECORD_MAN,
      </if>
      <if test="recordAddress != null" >
        RECORD_ADDRESS,
      </if>
      <if test="recordTime != null" >
        RECORD_TIME,
      </if>
      <if test="locationInfo != null" >
        LOCATION_INFO,
      </if>
      <if test="recordUrl != null" >
        RECORD_URL,
      </if>
      <if test="fileId != null" >
        FILE_ID,
      </if>
      <if test="party != null" >
        PARTY,
      </if>
      <if test="lawEnforcPerson != null" >
        LAW_ENFORC_PERSON,
      </if>
      <if test="lawEnforcId != null" >
        LAW_ENFORC_ID,
      </if>
      <if test="recordUpdateTime != null" >
        RECORD_UPDATE_TIME,
      </if>
      <if test="createUserId != null" >
        CREATE_USER_ID,
      </if>
      <if test="createUserName != null" >
        CREATE_USER_NAME,
      </if>
      <if test="recordCreateTime != null" >
        RECORD_CREATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null" >
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="evidenceObject != null" >
        #{evidenceObject,jdbcType=VARCHAR},
      </if>
      <if test="recordMan != null" >
        #{recordMan,jdbcType=VARCHAR},
      </if>
      <if test="recordAddress != null" >
        #{recordAddress,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null" >
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="locationInfo != null" >
        #{locationInfo,jdbcType=VARCHAR},
      </if>
      <if test="recordUrl != null" >
        #{recordUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="party != null" >
        #{party,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcPerson != null" >
        #{lawEnforcPerson,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null" >
        #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="recordUpdateTime != null" >
        #{recordUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null" >
        #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        #{recordCreateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.RecordEvidence" >
    update RECORD_EVIDENCE
    <set >
      <if test="taskId != null" >
        TASK_ID = #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="evidenceObject != null" >
        EVIDENCE_OBJECT = #{evidenceObject,jdbcType=VARCHAR},
      </if>
      <if test="recordMan != null" >
        RECORD_MAN = #{recordMan,jdbcType=VARCHAR},
      </if>
      <if test="recordAddress != null" >
        RECORD_ADDRESS = #{recordAddress,jdbcType=VARCHAR},
      </if>
      <if test="recordTime != null" >
        RECORD_TIME = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="locationInfo != null" >
        LOCATION_INFO = #{locationInfo,jdbcType=VARCHAR},
      </if>
      <if test="recordUrl != null" >
        RECORD_URL = #{recordUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileId != null" >
        FILE_ID = #{fileId,jdbcType=VARCHAR},
      </if>
      <if test="party != null" >
        PARTY = #{party,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcPerson != null" >
        LAW_ENFORC_PERSON = #{lawEnforcPerson,jdbcType=VARCHAR},
      </if>
      <if test="lawEnforcId != null" >
        LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      </if>
      <if test="recordUpdateTime != null" >
        RECORD_UPDATE_TIME = #{recordUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createUserId != null" >
        CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      </if>
      <if test="createUserName != null" >
        CREATE_USER_NAME = #{createUserName,jdbcType=VARCHAR},
      </if>
      <if test="recordCreateTime != null" >
        RECORD_CREATE_TIME = #{recordCreateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.RecordEvidence" >
    update RECORD_EVIDENCE
    set TASK_ID = #{taskId,jdbcType=VARCHAR},
      EVIDENCE_OBJECT = #{evidenceObject,jdbcType=VARCHAR},
      RECORD_MAN = #{recordMan,jdbcType=VARCHAR},
      RECORD_ADDRESS = #{recordAddress,jdbcType=VARCHAR},
      RECORD_TIME = #{recordTime,jdbcType=TIMESTAMP},
      LOCATION_INFO = #{locationInfo,jdbcType=VARCHAR},
      RECORD_URL = #{recordUrl,jdbcType=VARCHAR},
      FILE_ID = #{fileId,jdbcType=VARCHAR},
      PARTY = #{party,jdbcType=VARCHAR},
      LAW_ENFORC_PERSON = #{lawEnforcPerson,jdbcType=VARCHAR},
      LAW_ENFORC_ID = #{lawEnforcId,jdbcType=VARCHAR},
      RECORD_UPDATE_TIME = #{recordUpdateTime,jdbcType=TIMESTAMP},
      CREATE_USER_ID = #{createUserId,jdbcType=VARCHAR},
      CREATE_USER_NAME = #{createUserName,jdbcType=VARCHAR},
      RECORD_CREATE_TIME = #{recordCreateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="queryRecordEvidence" parameterType="java.lang.String" resultMap="BaseResultMap">
  	 select 
    <include refid="Base_Column_List" />
    from RECORD_EVIDENCE
    where ID=#{id,jdbcType=VARCHAR}
  </select>
  <select id="queryRecordEcidenceList" parameterType="java.lang.String" resultMap="BaseResultMap">
  	 select 
    r.ID, r.TASK_ID, r.EVIDENCE_OBJECT, r.RECORD_MAN, r.RECORD_ADDRESS, r.RECORD_TIME, r.LOCATION_INFO, 
    r.RECORD_URL, r.FILE_ID, r.PARTY, r.LAW_ENFORC_PERSON, r.LAW_ENFORC_ID, r.RECORD_UPDATE_TIME, 
    r.CREATE_USER_ID, r.CREATE_USER_NAME, r.RECORD_CREATE_TIME,f.FILE_SIZE as fileSize
    from RECORD_EVIDENCE r 
     LEFT JOIN SYS_FILES f on r.FILE_ID = f.ID
    where r.TASK_ID=#{taskId,jdbcType=VARCHAR}
    order by r.RECORD_CREATE_TIME desc
  </select>
</mapper>