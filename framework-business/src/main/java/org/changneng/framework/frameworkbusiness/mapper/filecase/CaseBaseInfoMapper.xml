<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.CaseBaseInfoMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CASE_NUMBER" jdbcType="VARCHAR" property="caseNumber" />
    <result column="LAW_OBJECT_ID" jdbcType="VARCHAR" property="lawObjectId" />
    <result column="LAW_OBJECT_NAME" jdbcType="VARCHAR" property="lawObjectName" />
    <result column="OBJECT_TYPE_NAME" jdbcType="VARCHAR" property="objectTypeName" />
    <result column="OBJECT_TYPE_CODE" jdbcType="VARCHAR" property="objectTypeCode" />
    <result column="YEAR" jdbcType="VARCHAR" property="year" />
    <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
    <result column="INFORMANT_ID" jdbcType="VARCHAR" property="informantId" />
    <result column="INFORMANT_NAME" jdbcType="VARCHAR" property="informantName" />
    <result column="PUNISH_SUBJECT" jdbcType="VARCHAR" property="punishSubject" />
    <result column="PUNISH_SUBJECT_ID" jdbcType="VARCHAR" property="punishSubjectId" />
    <result column="PUNISH_SUBJECT_LEVEL_NAME" jdbcType="VARCHAR" property="punishSubjectLevelName" />
    <result column="PUNISH_SUBJECT_LEVEL_CODE" jdbcType="VARCHAR" property="punishSubjectLevelCode" />
    <result column="RESEARCH_ORG_NAME" jdbcType="VARCHAR" property="researchOrgName" />
    <result column="LINKMAN" jdbcType="VARCHAR" property="linkman" />
    <result column="LINKMAN_ID" jdbcType="VARCHAR" property="linkmanId" />
    <result column="LINKMAN_PHONE" jdbcType="VARCHAR" property="linkmanPhone" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="CASE_STATUS" jdbcType="DECIMAL" property="caseStatus" />
    <result column="IS_DELETE" jdbcType="DECIMAL" property="isDelete" />
    <result column="DEPARTMENT_NUMBER" jdbcType="VARCHAR" property="departmentNumber" />
    <result column="USER_BELONG_AREA_NAME" jdbcType="VARCHAR" property="userBelongAreaName" />
    <result column="USER_BELONG_AREA_CODE" jdbcType="VARCHAR" property="userBelongAreaCode" />
    <result column="DEPARTMENT_NAME" jdbcType="VARCHAR" property="departmentName" />
    <result column="DEPARTMENT_ID" jdbcType="VARCHAR" property="departmentId" />
    <result column="SYNCHRONIZATION_STATUS" jdbcType="DECIMAL" property="synchronizationStatus" />
 	<result column="RESEARCH_ORG_ID" jdbcType="VARCHAR" property="researchOrgId" />
 	<result column="CASE_REASON_ID" jdbcType="VARCHAR" property="caseReasonId" />
    <result column="CASE_REASON_NAME" jdbcType="VARCHAR" property="caseReasonName" />
    <result column="CASE_END_DATE" jdbcType="TIMESTAMP" property="caseEndDate" />
    <result column="PENALTY_AMOUNT" jdbcType="VARCHAR" property="penaltyAmount" />
    <!-- 20180104新增查处分离字段 -->
    <result column="CHECK_SEPARATION_TYPE" jdbcType="VARCHAR" property="checkSeparationType" />
    <result column="CHECK_SEPARATION_NUMBER" jdbcType="VARCHAR" property="checkSeparationNumber" />

    <!-- case_state表中判断是否可以下放权限的字段 -->
    <result column="IS_POWER_EDIT" jdbcType="DECIMAL" property="isPowerEdit" />
    <result column="POWER_DATE_END" jdbcType="TIMESTAMP" property="powerDateEnd"/>
    <!-- 对接需要的字段 -->
    <result column="DOCKING_STATE" jdbcType="DECIMAL" property="dockingState" />
    <result column="DOCK_SUS_STATE" jdbcType="DECIMAL" property="dockSusState" />
    <result column="SUCCESS_DATE" jdbcType="TIMESTAMP" property="successDate" />
    <result column="SEND_DOCK_DATE" jdbcType="TIMESTAMP" property="sendDockDate" />
    <result column="DOCKING_TYPE" jdbcType="DECIMAL" property="dockingType" />
    <result column="LAST_DOCK_STATE" jdbcType="DECIMAL" property="lastDockState" />
    <!-- 第一次对接环保部成功时间 -->
    <result column="FIRST_DOCKING_DATE" jdbcType="TIMESTAMP" property="firstDockingDate" />
    <!-- 违法行为发生日期 -->
    <result column="ILLEGAL_DATE" jdbcType="TIMESTAMP" property="illegalDate" />
    <result column="SPECIAL_ACTION_NAME" jdbcType="VARCHAR" property="specialActionName" />
    <result column="CODE_NUMBER" jdbcType="VARCHAR" property="codeNumber" />
      <result column="GIS_COORDINATE_X" jdbcType="VARCHAR" property="gisCoordinateX" />
      <result column="GIS_COORDINATE_Y" jdbcType="VARCHAR" property="gisCoordinateY" />
      <result column="GIS_COORDINATE_X84" jdbcType="VARCHAR" property="gisCoordinateX84" />
      <result column="GIS_COORDINATE_Y84" jdbcType="VARCHAR" property="gisCoordinateY84" />

  </resultMap>
  <resultMap id="customResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.CaseInfoBean">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CASE_TYPE" jdbcType="VARCHAR" property="caseType" />
    <result column="CASE_TYPE_NAME" jdbcType="VARCHAR" property="caseTypeName" />
    <result column="case_id" jdbcType="VARCHAR" property="caseId" />
    <result column="CASE_NUMBER" jdbcType="VARCHAR" property="caseNumber" />
    <result column="CASE_STATE" jdbcType="VARCHAR" property="caseState" />
    <result column="CASE_NAME" jdbcType="VARCHAR" property="caseName" />
    <result column="PUNISH_SUBJECT" jdbcType="VARCHAR" property="punishSubject" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="CASE_END_DATE" jdbcType="TIMESTAMP" property="caseEndDate" />
  </resultMap>

  <!-- 案件台账查询结果映射 -->
  <resultMap id="AjtzSearchResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResultBean">
  	<result column="id" jdbcType="VARCHAR" property="id" />
  	<result column="caseNumber" jdbcType="VARCHAR" property="caseNumber" />
  	<result column="caseName" jdbcType="VARCHAR" property="caseName" />
  	<result column="punishSubject" jdbcType="VARCHAR" property="punishSubject" />
  	<result column="lawObjectName" jdbcType="VARCHAR" property="lawObjectName" />
  	<result column="createTime" jdbcType="VARCHAR" property="createTime" />
  	<result column="caseReason" jdbcType="VARCHAR" property="caseReason" />
  	<result column="researchOrgName" jdbcType="VARCHAR" property="researchOrgName" />
  	<result column="caseStatus" jdbcType="VARCHAR" property="caseStatus" />
  	<result column="specialActions" jdbcType="VARCHAR" property="specialActions" />
    <result column="illegalTypes" jdbcType="VARCHAR" property="illegalTypes" />
    <result column="informant_name" jdbcType="VARCHAR" property="informantName" />
  	<result column="smrProgramState" jdbcType="VARCHAR" property="smrProgramState" />
  	<result column="cmlPunishState" jdbcType="VARCHAR" property="cmlPunishState" />
  	<result column="astnDecState" jdbcType="VARCHAR" property="astnDecState" />
  	<result column="attachmentState" jdbcType="VARCHAR" property="attachmentState" />
  	<result column="lmtProductionState" jdbcType="VARCHAR" property="lmtProductionState" />
  	<result column="detentionState" jdbcType="VARCHAR" property="detentionState" />

  	<result column="evmtPollutionState" jdbcType="VARCHAR" property="evmtPollutionState" />
  	<result column="pdcount" jdbcType="VARCHAR" property="pdcount" />
  	<result column="belongCity" jdbcType="VARCHAR" property="belongCity" />
  	<result column="dockSusState" jdbcType="VARCHAR" property="dockSusState" />
  	<result column="firstDockingDate" jdbcType="VARCHAR" property="firstDockingDate" />
  	<!-- 1.5.3新增字段 -->
  	<result column="easyAmount" jdbcType="VARCHAR" property="easyAmount" />
  	<result column="generalAmount" jdbcType="VARCHAR" property="generalAmount" />
  	<result column="penaltyAmount" jdbcType="VARCHAR" property="penaltyAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <sql id="Base_Column_List">
    ID, CASE_NUMBER, LAW_OBJECT_ID, LAW_OBJECT_NAME, OBJECT_TYPE_NAME, OBJECT_TYPE_CODE,
    YEAR, CASE_NAME, INFORMANT_ID, INFORMANT_NAME, PUNISH_SUBJECT, PUNISH_SUBJECT_ID,
    PUNISH_SUBJECT_LEVEL_NAME, PUNISH_SUBJECT_LEVEL_CODE, RESEARCH_ORG_NAME, LINKMAN,
    LINKMAN_ID, LINKMAN_PHONE, CREATE_TIME, UPDATE_TIME, CASE_STATUS, IS_DELETE,LAST_DOCK_STATE,
    DEPARTMENT_NUMBER, USER_BELONG_AREA_NAME, USER_BELONG_AREA_CODE, DEPARTMENT_NAME,
    DEPARTMENT_ID,SYNCHRONIZATION_STATUS,RESEARCH_ORG_ID,CASE_REASON_ID,CASE_REASON_NAME,CASE_END_DATE,DOCKING_STATE,
    DOCK_SUS_STATE,SUCCESS_DATE,SEND_DOCK_DATE,DOCKING_TYPE,CHECK_SEPARATION_TYPE,CHECK_SEPARATION_NUMBER,FIRST_DOCKING_DATE,ILLEGAL_DATE,SPECIAL_ACTION_NAME,
    CODE_NUMBER
  </sql>

  <!-- 大案件台账sql -->
  <select id="getMainCaseList" resultMap="AjtzSearchResultMap">
       select a.id as id,
        a.CASE_NUMBER as caseNumber,
        a.LAW_OBJECT_NAME as lawObjectName,
        a.CASE_NAME as caseName,
        a.PUNISH_SUBJECT as punishSubject,
        a.RESEARCH_ORG_NAME as researchOrgName,
        a.CREATE_TIME as createTime,
        a.CASE_STATUS as caseStatus,
        a.DEPARTMENT_NAME as departmentName,
        a.special_action_name as specialActions,
        a.illegal_type_name as illegalTypes,
        a.CASE_REASON_NAME as caseReason,
        a.user_belong_area_code as belongAreaCode,
        a.FIRST_DOCKING_DATE as firstDockingDate,
        a.DOCK_SUS_STATE as dockSusState,
        cs.SMR_PROGRAM_STATE as smrProgramState,
        cs.CML_PUNISH_STATE as cmlPunishState,
        cs.ASTN_DEC_STATE as astnDecState,
        cs.ATTACHMENT_STATE as attachmentState,
        cs.LMT_PRODUCTION_STATE as lmtProductionState,
        cs.DETENTION_STATE as detentionState,
        cs.EVMT_POLLUTION_STATE as evmtPollutionState,
        LEO.GIS_COORDINATE_X as  gisCoordinateX,
        LEO.GIS_COORDINATE_Y as  gisCoordinateY,
        LEO.GIS_COORDINATE_X84 as  gisCoordinateX84,
        LEO.GIS_COORDINATE_Y84 as  gisCoordinateY84,
		pds.pdcount,
		case
				 when (atv.CASE_TYPE = 1) then  atv.PENALTY_AMOUNT
		end as easyAmount ,
		case
				 when (atv.CASE_TYPE = 2) then  atv.PENALTY_AMOUNT
		end as generalAmount ,
		pds.penaltyAmount
        from (select id,CASE_NUMBER, LAW_OBJECT_ID,LAW_OBJECT_NAME,CASE_NAME,PUNISH_SUBJECT,RESEARCH_ORG_NAME,PUNISH_SUBJECT_ID,
            user_belong_area_code,CREATE_TIME,CASE_STATUS,DEPARTMENT_NAME,special_action_name,illegal_type_name,CASE_REASON_NAME,DOCK_SUS_STATE,FIRST_DOCKING_DATE
             from case_base_info cb where cb.is_delete=1
              <if test="searchBean.createTimeStart !=null and searchBean.createTimeStart!=''">
                  and cb.create_time  &gt;= to_date(CONCAT(#{searchBean.createTimeStart},' 00:00:00'),'yyyy-mm-dd HH24:mi:ss')
              </if>
              <if test="searchBean.createTimeEnd !=null and searchBean.createTimeEnd!=''">
                  and cb.create_time  &lt; to_date(CONCAT(#{searchBean.createTimeEnd},' 23:59:59'),'yyyy-mm-dd HH24:mi:ss')
              </if>
             ) a
            LEFT JOIN (select  pd.case_id , count(*) as pdcount ,SUM(pd.PENALTY_ACCOUNT) as penaltyAmount
					from penalty_day pd  where pd.is_del = 0 GROUP BY  pd.case_id ) pds
			on  pds.case_id = a.id
			LEFT JOIN ATV_SANCTION_CASE atv on atv.CASE_ID = a.id  and atv.IS_DELETE = 0
            left join case_state cs on a.id = cs.case_id
            left join LAW_ENFORCE_OBJECT LEO ON  a.LAW_OBJECT_ID = leo.ID
            left join t_area t on t.code=a.user_belong_area_code
             where 1 =1
			  ORDER BY  t.location_atv ASC, id DESC
  </select>

    <!-- 大案件台账sql -->
    <select id="getAjtzResultList" resultMap="AjtzSearchResultMap">
        select a.id as id,
        a.CASE_NUMBER as caseNumber,
        a.LAW_OBJECT_NAME as lawObjectName,
        a.CASE_NAME as caseName,
        a.PUNISH_SUBJECT as punishSubject,
        a.RESEARCH_ORG_NAME as researchOrgName,
        a.CREATE_TIME as createTime,
        a.CASE_STATUS as caseStatus,
        a.DEPARTMENT_NAME as departmentName,
        a.special_action_name as specialActions,
        a.illegal_type_name as illegalTypes,
        a.CASE_REASON_NAME as caseReason,
        a.user_belong_area_code as belongAreaCode,
        a.FIRST_DOCKING_DATE as firstDockingDate,
        a.DOCK_SUS_STATE as dockSusState,
        cs.SMR_PROGRAM_STATE as smrProgramState,
        cs.CML_PUNISH_STATE as cmlPunishState,
        cs.ASTN_DEC_STATE as astnDecState,
        cs.ATTACHMENT_STATE as attachmentState,
        cs.LMT_PRODUCTION_STATE as lmtProductionState,
        cs.DETENTION_STATE as detentionState,
        cs.EVMT_POLLUTION_STATE as evmtPollutionState,
        pds.pdcount,
        case
        when (atv.CASE_TYPE = 1) then  atv.PENALTY_AMOUNT
        end as easyAmount ,
        case
        when (atv.CASE_TYPE = 2) then  atv.PENALTY_AMOUNT
        end as generalAmount ,
        pds.penaltyAmount
        from (	SELECT
        cb.id,
        cb.CASE_NUMBER,
        cb.LAW_OBJECT_NAME,
        cb.CASE_NAME,
        cb.PUNISH_SUBJECT,
        cb.RESEARCH_ORG_NAME,
        cb.PUNISH_SUBJECT_ID,
        cb.user_belong_area_code,
        cb.CREATE_TIME,
        cb.CASE_STATUS,
        cb.DEPARTMENT_NAME,
        cb.special_action_name,
        cb.illegal_type_name,
        cb.CASE_REASON_NAME,
        cb.DOCK_SUS_STATE,
        cb.FIRST_DOCKING_DATE
        FROM
        case_base_info cb ,LAW_ENFORCE_OBJECT lb
        WHERE
        cb.LAW_OBJECT_ID = lb.id
        and	cb.is_delete = 1
        <if test=" searchBean.specialSuperviseIndustry != null and searchBean.specialSuperviseIndustry !='' ">
            and lb.SPECIAL_SUPERVISE_INDUSTRY =#{searchBean.specialSuperviseIndustry}
        </if>
        <if test=" searchBean.industryTypeCode != null and searchBean.industryTypeCode !='' ">
            AND  lb.industry_type_code =#{searchBean.industryTypeCode}
        </if>
        <if test="searchBean.isLevelCity == '0'.toString()">
            <!-- 未勾选只查看本级 -->
            <if test="searchBean.belongCity == null or searchBean.belongCity == ''.toString()">
                and cb.user_belong_area_code like '35%'
            </if>
            <if test="searchBean.belongCity != null and searchBean.belongCity != ''.toString()
		  				and (searchBean.belongCountry == null or searchBean.belongCountry == ''.toString())">
                and cb.user_belong_area_code like  replace(#{searchBean.belongCity},'00','%')
            </if>
            <if test="searchBean.belongCountry != null and searchBean.belongCountry != ''.toString()">
                and cb.user_belong_area_code = #{searchBean.belongCountry}
            </if>
        </if>
        <if test="searchBean.isLevelCity == '1'.toString()">
            <!-- 勾选了只查看本级 -->
            <if test="searchBean.belongCity == null or searchBean.belongCity == ''.toString()">
                and cb.user_belong_area_code = '35000000'
            </if>
            <if test="searchBean.belongCity != null and searchBean.belongCity != ''.toString()
		  				and (searchBean.belongCountry == null or searchBean.belongCountry == ''.toString())">
                and cb.user_belong_area_code = #{searchBean.belongCity}
            </if>
            <if test="searchBean.belongCountry != null and searchBean.belongCountry != ''.toString()">
                and cb.user_belong_area_code = #{searchBean.belongCountry}
            </if>
        </if>
        <!-- 關聯專項行動 -->
        <if test="searchBean.specialAction !=null and searchBean.specialAction!=''">
            <!-- and #{searchBean.specialAction} in (select cs.special_action_id
                     from case_special cs
                    where cs.case_id = cb.id) -->
            and ${searchBean.specialAction}
        </if>
        <!-- 違法類型 -->
        <if test="searchBean.illegalType !=null and searchBean.illegalType!=''">
            and #{searchBean.illegalType} in (select ict.ILLEGA_NAME_ID
            from illegal_case_table ict
            where ict.case_id = cb.id)
        </if>
        <!-- 案由 -->
        <if test="searchBean.caseReason != null and searchBean.caseReason != ''">
            and cb.CASE_REASON_ID = #{searchBean.caseReason}
        </if>
        <if test="searchBean.createTimeStart !=null and searchBean.createTimeStart!=''">
            and cb.create_time  &gt;= to_date(CONCAT(#{searchBean.createTimeStart},' 00:00:00'),'yyyy-mm-dd HH24:mi:ss')
        </if>
        <if test="searchBean.createTimeEnd !=null and searchBean.createTimeEnd!=''">
            and cb.create_time  &lt; to_date(CONCAT(#{searchBean.createTimeEnd},' 23:59:59'),'yyyy-mm-dd HH24:mi:ss')
        </if>
        <!-- 执法对象名称 -->
        <if test="searchBean.lawObjName != null and searchBean.lawObjName != ''">
            and cb.law_object_name like concat(concat('%',#{searchBean.lawObjName}),'%')
        </if>
        <!-- 调查机构 -->
        <if test="searchBean.researchOrgName != null and searchBean.researchOrgName != ''">
            and cb.research_org_name like concat(concat('%',#{searchBean.researchOrgName}),'%')
        </if>
        <!-- 案件名称 -->
        <if test="searchBean.caseName != null and searchBean.caseName != ''">
            and cb.case_name like concat(concat('%',#{searchBean.caseName}),'%')
        </if>
        <!-- 大案件编号 -->
        <if test="searchBean.mainCaseNumber != null and searchBean.mainCaseNumber != ''">
            and cb.case_number like concat(concat('%',#{searchBean.mainCaseNumber}),'%')
        </if>
        <!-- 案件状态 -->
        <if test="searchBean.caseStatus != null and searchBean.caseStatus != ''">
            and cb.case_status = #{searchBean.caseStatus}
        </if>
        <!-- 查询条件处罚主体 移送单位 -->
        <if test="searchBean.handlingUnitId != null and searchBean.handlingUnitId != ''">
            and cb.punish_subject_id=#{searchBean.handlingUnitId}
        </if>
        ) a
        LEFT JOIN (select  pd.case_id , count(*) as pdcount ,SUM(pd.PENALTY_ACCOUNT) as penaltyAmount
        from penalty_day pd  where pd.is_del = 0 GROUP BY  pd.case_id ) pds
        on  pds.case_id = a.id
        LEFT JOIN ATV_SANCTION_CASE atv on atv.CASE_ID = a.id  and atv.IS_DELETE = 0
        left join case_state cs on a.id = cs.case_id
        left join t_area t on t.code=a.user_belong_area_code
        where 1 =1
        <!-- 案件统计  lhl -->
        <if test="searchBean.begDate !=null and searchBean.begDate!=''">
            and cs.update_date  &gt;= to_timestamp(#{searchBean.begDate},'yyyy-MM-dd HH24:mi:ss.ff9')
        </if>
        <if test="searchBean.endDate !=null and searchBean.endDate!=''">
            and cs.update_date  &lt; to_timestamp(#{searchBean.endDate},'yyyy-MM-dd HH24:mi:ss.ff9')
        </if>
        <!-- 有无简易行政处罚 -->
        <if test="searchBean.isSmr != null and searchBean.isSmr != ''">
            <if test="searchBean.isSmr ==1">
                and cs.smr_program_state != 0 and cs.smr_program_state !=3
            </if>
            <if test="searchBean.isSmr ==0">
                and (cs.smr_program_state = 0 or cs.smr_program_state = 3)
            </if>
        </if>

        <!-- 有无一般行政处罚 -->
        <if test="searchBean.isCml != null and searchBean.isCml != ''">
            <if test="searchBean.isCml ==1">
                and cs.cml_punish_state != 0 and cs.cml_punish_state != 3
            </if>
            <if test="searchBean.isCml ==0">
                and (cs.cml_punish_state = 0 or cs.cml_punish_state = 3)
            </if>
        </if>
        <!-- 有无行政命令 -->
        <if test="searchBean.isAstn != null and searchBean.isAstn != ''">
            <if test="searchBean.isAstn ==1">
                and cs.astn_dec_state != 0
            </if>
            <if test="searchBean.isAstn ==0">
                and cs.astn_dec_state = 0
            </if>
        </if>
        <!-- 有无限产停产令 -->
        <if test="searchBean.isLmt != null and searchBean.isLmt != ''">
            <if test="searchBean.isLmt ==1">
                and cs.lmt_production_state != 0
            </if>
            <if test="searchBean.isLmt ==0">
                and cs.lmt_production_state = 0
            </if>
        </if>
        <!-- 有无查封扣押 -->
        <if test="searchBean.isAtt != null and searchBean.isAtt != ''">
            <if test="searchBean.isAtt ==1">
                and cs.attachment_state != 0
            </if>
            <if test="searchBean.isAtt ==0">
                and cs.attachment_state = 0
            </if>
        </if>
        <!-- 有无移送行政拘留 -->
        <if test="searchBean.isDet != null and searchBean.isDet != ''">
            <if test="searchBean.isDet ==1">
                and cs.detention_state != 0
            </if>
            <if test="searchBean.isDet ==0">
                and cs.detention_state = 0
            </if>
        </if>
        <!-- 有无移送涉嫌犯罪 -->
        <if test="searchBean.isEvmt != null and searchBean.isEvmt != ''">
            <if test="searchBean.isEvmt ==1">
                and cs.evmt_pollution_state != 0
            </if>
            <if test="searchBean.isEvmt ==0">
                and cs.evmt_pollution_state = 0
            </if>
        </if>
        <!--有无按日连续处罚-->
        <if test="searchBean.isDay != null and searchBean.isDay != ''">
            <if test="searchBean.isDay ==1">
                and cs.day_punish_state != 0
            </if>
            <if test="searchBean.isDay ==0">
                and cs.day_punish_state = 0
            </if>
        </if>
        <if test="searchBean.isSubmit == '0'.toString()">
            and a.DOCK_SUS_STATE = 0
        </if>
        <if test="searchBean.isSubmit == '1'.toString()">
            and a.DOCK_SUS_STATE = 1
        </if>
        <!-- 首次入库时间 -->
        <if test="searchBean.searchDateStart3 !=null and searchBean.searchDateStart3 !=''">
            and a.first_docking_date &gt;= to_date(concat(#{searchBean.searchDateStart3},' 00:00:00'),'yyyy-MM-dd HH24:mi:ss')
        </if>
        <if test="searchBean.searchDateEnd3 !=null and searchBean.searchDateEnd3 !=''">
            and a.first_docking_date &lt; to_date(concat(#{searchBean.searchDateEnd3},'23:59:59'),'yyyy-MM-dd HH24:mi:ss')
        </if>
        ORDER BY a.CASE_STATUS DESC,  t.location_atv ASC, id DESC
    </select>


    <!-- 根据执法对象id查询list集合 -->
  <select id="getCaseInfoByLawObjId" resultMap="customResultMap">
    select
    ID, CASE_NUMBER, CASE_NAME,CREATE_TIME,PUNISH_SUBJECT
    from CASE_BASE_INFO
    where LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR} and IS_DELETE = 1
    order by create_time desc
  </select>

   <select id="getCaseBaseInfoByCaseNumber" parameterType="java.lang.String" resultMap="BaseResultMap">
    select   ID, CASE_NUMBER   from CASE_BASE_INFO where CASE_NUMBER = #{caseNumber,jdbcType=VARCHAR}
  </select>

  <!-- 查询案件删除状态 -->
  <select id="selectCaseState" parameterType="java.lang.String" resultType="java.lang.Integer">
    select
    IS_DELETE
    from CASE_BASE_INFO
    where id=#{id,jdbcType=VARCHAR}
  </select>

  <!-- 根据执法对象id查询行政处罚案件集合 -->
  <select id="selectAtvCaseListByObjId" resultMap="customResultMap">
  	select a.id, <if test="caseType == '1'.toString()">'简易行政处罚' as case_type_name, '0' as case_type,</if>
  				<if test="caseType == '2'.toString()">'一般行政处罚' as case_type_name, '1' as case_type,</if>
  			a.easy_sanction_number case_number, b.case_name case_name, b.punish_subject punish_subject,
  			b.id as case_id,
  			a.up_creator_date create_time
  			, (case when (c.${submitField} is not null and c.${submitField} = 2) then '已完成'
  					when (c.${submitField} is null or c.${submitField} != 2) then '进行中' end) as case_state
  		from atv_sanction_case a, case_base_info b , case_state c
  		where a.case_id=b.id and a.case_id=#{caseId, jdbcType=VARCHAR} and a.is_delete=0 and b.is_delete=1
  				and b.law_object_id=#{lawObjectId, jdbcType=VARCHAR} and c.case_id=a.case_id
  			and a.case_type = #{caseType}
  		<if test="caseStartTimes != null and caseStartTimes != ''">
	        AND a.up_creator_date &gt;= to_date(#{caseStartTimes},'yyyy-MM-dd')
	    </if>
	    <if test="caseEndTimes != null and caseEndTimes !=''">
	        AND a.up_creator_date &lt;= to_date(#{caseEndTimes},'yyyy-MM-dd')+1
	    </if>
	    <if test="caseState != null and caseState == '1'.toString()">
	    	and (c.${submitField} is not null and c.${submitField} = 2)
	    </if>
	    <if test="caseState != null and caseState == '0'.toString()">
	    	and (c.${submitField} is null or c.${submitField} != 2)
	    </if>
	  order by a.up_creator_date desc
  </select>
  <!-- 根据执法对象id查询配套措施案件集合 -->
  <select id="selectCaseListByObjId" resultMap="customResultMap">
  	select a.id, '${caseTypeName}' as case_type_name, '${caseType}' as case_type,
  			a.${caseNumberField} case_number, b.case_name case_name, b.punish_subject punish_subject,
  			b.id as case_id,
  			a.${startField} create_time
  			, (case when (c.${submitField} is not null and c.${submitField} = 2) then '已完成'
  					when (c.${submitField} is null or c.${submitField} != 2) then '进行中' end) as case_state
  		from ${tableName} a, case_base_info b , case_state c
  		where a.case_id=b.id and a.case_id=#{caseId, jdbcType=VARCHAR} and a.is_del=0 and b.is_delete=1
  			and b.law_object_id=#{lawObjectId, jdbcType=VARCHAR} and c.case_id=a.case_id
  		<if test="caseStartTimes != null and caseStartTimes != ''">
	        AND a.${startField} &gt;= to_date(#{caseStartTimes},'yyyy-MM-dd')
	    </if>
	    <if test="caseEndTimes != null and caseEndTimes !=''">
	        AND a.${startField} &lt;= to_date(#{caseEndTimes},'yyyy-MM-dd')+1
	    </if>
	    <if test="caseState != null and caseState == '1'.toString()">
	    	and (c.${submitField} is not null and c.${submitField} = 2)
	    </if>
	    <if test="caseState != null and caseState == '0'.toString()">
	    	and (c.${submitField} is null or c.${submitField} != 2)
	    </if>
	  order by a.${startField} desc
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CASE_BASE_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </select>

   <select id="selectByPrimaryKeyIsNotDelete" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CASE_BASE_INFO
    where ID = #{id,jdbcType=VARCHAR} and is_delete = 1
  </select>

   <select id="selectCaseBaseInfoCaseName" parameterType="java.lang.String" resultType="java.lang.String">
    select  case_name  from CASE_BASE_INFO where ID = #{caseId,jdbcType=VARCHAR} and is_delete = 1
  </select>

  <select id="getSendList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from CASE_BASE_INFO
    where docking_state=1
      <!--and CASE_NUMBER in (select id from A_TEST where TYPE_CODE = '0' )-->
      order by INFORMANT_ID ASC
  </select>
  <!-- 关联案件管理表查询 -->
  <select id="selectDockFailInfo" resultMap="BaseResultMap">
    select
    cb.ID, cb.CASE_NUMBER, cb.LAW_OBJECT_ID, cb.LAW_OBJECT_NAME, cb.OBJECT_TYPE_NAME, cb.OBJECT_TYPE_CODE,
    cb.YEAR, cb.CASE_NAME, cb.INFORMANT_ID, cb.INFORMANT_NAME, cb.PUNISH_SUBJECT, cb.PUNISH_SUBJECT_ID,
    cb.PUNISH_SUBJECT_LEVEL_NAME, cb.PUNISH_SUBJECT_LEVEL_CODE, cb.RESEARCH_ORG_NAME, cb.LINKMAN,
    cb.LINKMAN_ID, cb.LINKMAN_PHONE, cb.CREATE_TIME, cb.UPDATE_TIME, cb.CASE_STATUS, cb.IS_DELETE,cb.LAST_DOCK_STATE,
    cb.DEPARTMENT_NUMBER, cb.USER_BELONG_AREA_NAME, cb.USER_BELONG_AREA_CODE, cb.DEPARTMENT_NAME,
    cb.DEPARTMENT_ID,cb.SYNCHRONIZATION_STATUS,cb.RESEARCH_ORG_ID,cb.CASE_REASON_ID,cb.CASE_REASON_NAME,cb.CASE_END_DATE,cb.DOCKING_STATE,
    cb.DOCK_SUS_STATE,cb.SUCCESS_DATE,cb.SEND_DOCK_DATE,cb.DOCKING_TYPE
    from (select DISTINCT case_id as caseId from case_manage_table where docking_result=1 and info_type = 0
    and send_date &gt; to_date(concat(#{yesterday},' 00:00:00'),'yyyy-MM-dd hh24:mi:ss') and send_date &lt; to_date(concat(#{yesterday},' 23:59:59'),'yyyy-MM-dd hh24:mi:ss')
     ) a left join CASE_BASE_INFO cb on a.caseId = cb.id where cb.docking_state = 1
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CASE_BASE_INFO
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
    insert into CASE_BASE_INFO (ID, CASE_NUMBER, LAW_OBJECT_ID,
      LAW_OBJECT_NAME, OBJECT_TYPE_NAME, OBJECT_TYPE_CODE,
      YEAR, CASE_NAME, INFORMANT_ID,
      INFORMANT_NAME, PUNISH_SUBJECT, PUNISH_SUBJECT_ID,
      PUNISH_SUBJECT_LEVEL_NAME, PUNISH_SUBJECT_LEVEL_CODE,
      RESEARCH_ORG_NAME, LINKMAN, LINKMAN_ID,
      LINKMAN_PHONE, CREATE_TIME, UPDATE_TIME,
      CASE_STATUS, IS_DELETE, DEPARTMENT_NUMBER,
      USER_BELONG_AREA_NAME, USER_BELONG_AREA_CODE,
      DEPARTMENT_NAME, DEPARTMENT_ID,SYNCHRONIZATION_STATUS,RESEARCH_ORG_ID,CASE_REASON_ID,CASE_REASON_NAME,
      DOCKING_STATE,DOCK_SUS_STATE,SUCCESS_DATE,SEND_DOCK_DATE,DOCKING_TYPE)
    values (#{id,jdbcType=VARCHAR}, #{caseNumber,jdbcType=VARCHAR}, #{lawObjectId,jdbcType=VARCHAR},
      #{lawObjectName,jdbcType=VARCHAR}, #{objectTypeName,jdbcType=VARCHAR}, #{objectTypeCode,jdbcType=VARCHAR},
      #{year,jdbcType=VARCHAR}, #{caseName,jdbcType=VARCHAR}, #{informantId,jdbcType=VARCHAR},
      #{informantName,jdbcType=VARCHAR}, #{punishSubject,jdbcType=VARCHAR}, #{punishSubjectId,jdbcType=VARCHAR},
      #{punishSubjectLevelName,jdbcType=VARCHAR}, #{punishSubjectLevelCode,jdbcType=VARCHAR},
      #{researchOrgName,jdbcType=VARCHAR}, #{linkman,jdbcType=VARCHAR}, #{linkmanId,jdbcType=VARCHAR},
      #{linkmanPhone,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{caseStatus,jdbcType=DECIMAL}, #{isDelete,jdbcType=DECIMAL}, #{departmentNumber,jdbcType=VARCHAR},
      #{userBelongAreaName,jdbcType=VARCHAR}, #{userBelongAreaCode,jdbcType=VARCHAR},
      #{departmentName,jdbcType=VARCHAR}, #{departmentId,jdbcType=VARCHAR},#{synchronizationStatus,jdbcType=DECIMAL},
      #{researchOrgId,jdbcType=VARCHAR},#{caseReasonId,jdbcType=VARCHAR},#{caseReasonName,jdbcType=VARCHAR},#{dockingState,jdbcType=DECIMAL}
      ,#{dockSusState,jdbcType=DECIMAL},#{successDate,jdbcType=TIMESTAMP},#{sendDockDate,jdbcType=TIMESTAMP},#{dockingType,jdbcType=DECIMAL})
  </insert>

  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
     <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into CASE_BASE_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="caseNumber != null">
        CASE_NUMBER,
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID,
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME,
      </if>
      <if test="objectTypeName != null">
        OBJECT_TYPE_NAME,
      </if>
      <if test="objectTypeCode != null">
        OBJECT_TYPE_CODE,
      </if>
      <if test="year != null">
        YEAR,
      </if>
      <if test="caseName != null">
        CASE_NAME,
      </if>
      <if test="informantId != null">
        INFORMANT_ID,
      </if>
      <if test="informantName != null">
        INFORMANT_NAME,
      </if>
      <if test="punishSubject != null">
        PUNISH_SUBJECT,
      </if>
      <if test="punishSubjectId != null">
        PUNISH_SUBJECT_ID,
      </if>
      <if test="punishSubjectLevelName != null">
        PUNISH_SUBJECT_LEVEL_NAME,
      </if>
      <if test="punishSubjectLevelCode != null">
        PUNISH_SUBJECT_LEVEL_CODE,
      </if>
      <if test="researchOrgName != null">
        RESEARCH_ORG_NAME,
      </if>
      <if test="linkman != null">
        LINKMAN,
      </if>
      <if test="linkmanId != null">
        LINKMAN_ID,
      </if>
      <if test="linkmanPhone != null">
        LINKMAN_PHONE,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="caseStatus != null">
        CASE_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="departmentNumber != null">
        DEPARTMENT_NUMBER,
      </if>
      <if test="userBelongAreaName != null">
        USER_BELONG_AREA_NAME,
      </if>
      <if test="userBelongAreaCode != null">
        USER_BELONG_AREA_CODE,
      </if>
      <if test="departmentName != null">
        DEPARTMENT_NAME,
      </if>
      <if test="departmentId != null">
        DEPARTMENT_ID,
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS,
      </if>
      <if test="researchOrgId != null">
        RESEARCH_ORG_ID,
      </if>
      <if test="caseReasonId != null">
        CASE_REASON_ID,
      </if>
      <if test="caseReasonName != null">
        CASE_REASON_NAME,
      </if>
      <if test="dockingState != null">
        DOCKING_STATE,
      </if>
      <if test="dockSusState != null">
        DOCK_SUS_STATE,
      </if>
      <if test="successDate != null">
        SUCCESS_DATE,
      </if>
      <if test="sendDockDate != null">
        SEND_DOCK_DATE,
      </if>
      <if test="dockingType != null">
        DOCKING_TYPE,
      </if>
      <if test="lastDockState != null" >
       LAST_DOCK_STATE,
      </if>
      <if test="checkSeparationType != null" >
       CHECK_SEPARATION_TYPE,
      </if>
      <if test="checkSeparationNumber != null" >
       CHECK_SEPARATION_NUMBER,
      </if>
       <if test="firstDockingDate != null" >
        FIRST_DOCKING_DATE,
      </if>
       <if test="illegalDate != null" >
        ILLEGAL_DATE,
      </if>
      <if test="codeNumber != null" >
        CODE_NUMBER,
      </if>
      </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="caseNumber != null">
        #{caseNumber,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeName != null">
        #{objectTypeName,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeCode != null">
        #{objectTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        #{year,jdbcType=VARCHAR},
      </if>
      <if test="caseName != null">
        #{caseName,jdbcType=VARCHAR},
      </if>
      <if test="informantId != null">
        #{informantId,jdbcType=VARCHAR},
      </if>
      <if test="informantName != null">
        #{informantName,jdbcType=VARCHAR},
      </if>
      <if test="punishSubject != null">
        #{punishSubject,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectId != null">
        #{punishSubjectId,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectLevelName != null">
        #{punishSubjectLevelName,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectLevelCode != null">
        #{punishSubjectLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="researchOrgName != null">
        #{researchOrgName,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="linkmanId != null">
        #{linkmanId,jdbcType=VARCHAR},
      </if>
      <if test="linkmanPhone != null">
        #{linkmanPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseStatus != null">
        #{caseStatus,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=DECIMAL},
      </if>
      <if test="departmentNumber != null">
        #{departmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="userBelongAreaName != null">
        #{userBelongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="userBelongAreaCode != null">
        #{userBelongAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=VARCHAR},
      </if>
       <if test="synchronizationStatus != null">
        #{synchronizationStatus,jdbcType=DECIMAL},
      </if>
       <if test="researchOrgId != null">
        #{researchOrgId,jdbcType=VARCHAR},
      </if>
      <if test="caseReasonId != null">
        #{caseReasonId,jdbcType=VARCHAR},
      </if>
      <if test="caseReasonName != null">
        #{caseReasonName,jdbcType=VARCHAR},
      </if>
      <if test="dockingState != null">
        #{dockingState,jdbcType=DECIMAL},
      </if>
       <if test="dockSusState != null">
        #{dockSusState,jdbcType=DECIMAL},
      </if>
       <if test="successDate != null">
        #{successDate,jdbcType=TIMESTAMP},
      </if>
      <if test="sendDockDate != null">
        #{sendDockDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dockingType != null">
        #{dockingType,jdbcType=DECIMAL},
      </if>
      <if test="lastDockState != null" >
        #{lastDockState,jdbcType=DECIMAL},
      </if>
      <if test="checkSeparationType != null">
        #{checkSeparationType,jdbcType=VARCHAR},
      </if>
      <if test="checkSeparationNumber != null" >
        #{checkSeparationNumber,jdbcType=VARCHAR},
      </if>
       <if test="firstDockingDate != null" >
        #{firstDockingDate,jdbcType=TIMESTAMP},
      </if>
       <if test="illegalDate != null" >
        #{illegalDate,jdbcType=TIMESTAMP},
      </if>
      <if test="codeNumber != null" >
         #{codeNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
   <update id="updateByPrimaryKeySelectiveCaseEnd">
   		update CASE_BASE_INFO set CASE_STATUS = 1,CASE_END_DATE = sysdate where ID = #{id}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
    update CASE_BASE_INFO
    <set>
      <if test="caseNumber != null">
        CASE_NUMBER = #{caseNumber,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeName != null">
        OBJECT_TYPE_NAME = #{objectTypeName,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeCode != null">
        OBJECT_TYPE_CODE = #{objectTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="year != null">
        YEAR = #{year,jdbcType=VARCHAR},
      </if>
      <if test="caseName != null">
        CASE_NAME = #{caseName,jdbcType=VARCHAR},
      </if>
      <if test="informantId != null">
        INFORMANT_ID = #{informantId,jdbcType=VARCHAR},
      </if>
      <if test="informantName != null">
        INFORMANT_NAME = #{informantName,jdbcType=VARCHAR},
      </if>
      <if test="punishSubject != null">
        PUNISH_SUBJECT = #{punishSubject,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectId != null">
        PUNISH_SUBJECT_ID = #{punishSubjectId,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectLevelName != null">
        PUNISH_SUBJECT_LEVEL_NAME = #{punishSubjectLevelName,jdbcType=VARCHAR},
      </if>
      <if test="punishSubjectLevelCode != null">
        PUNISH_SUBJECT_LEVEL_CODE = #{punishSubjectLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="researchOrgName != null">
        RESEARCH_ORG_NAME = #{researchOrgName,jdbcType=VARCHAR},
      </if>
      <if test="linkman != null">
        LINKMAN = #{linkman,jdbcType=VARCHAR},
      </if>
      <if test="linkmanId != null">
        LINKMAN_ID = #{linkmanId,jdbcType=VARCHAR},
      </if>
      <if test="linkmanPhone != null">
        LINKMAN_PHONE = #{linkmanPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseStatus != null">
        CASE_STATUS = #{caseStatus,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=DECIMAL},
      </if>
      <if test="departmentNumber != null">
        DEPARTMENT_NUMBER = #{departmentNumber,jdbcType=VARCHAR},
      </if>
      <if test="userBelongAreaName != null">
        USER_BELONG_AREA_NAME = #{userBelongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="userBelongAreaCode != null">
        USER_BELONG_AREA_CODE = #{userBelongAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=DECIMAL},
      </if>
      <if test="researchOrgId != null">
        RESEARCH_ORG_ID = #{researchOrgId,jdbcType=VARCHAR},
      </if>
      <if test="synchronizationStatus != null">
        CASE_REASON_ID = #{caseReasonId,jdbcType=DECIMAL},
      </if>
      <if test="researchOrgId != null">
        CASE_REASON_NAME = #{caseReasonName,jdbcType=VARCHAR},
      </if>
      <if test="dockingState != null">
        DOCKING_STATE = #{dockingState,jdbcType=DECIMAL},
      </if>
      <if test="dockSusState != null">
        DOCK_SUS_STATE = #{dockSusState,jdbcType=DECIMAL},
      </if>
      <if test="successDate != null">
        SUCCESS_DATE = #{successDate,jdbcType=TIMESTAMP},
      </if>
      <if test="successDate != null">
        SEND_DOCK_DATE = #{successDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dockingType != null">
        DOCKING_TYPE = #{dockingType,jdbcType=DECIMAL},
      </if>
      <if test="lastDockState != null" >
        LAST_DOCK_STATE = #{lastDockState,jdbcType=DECIMAL},
      </if>
      <if test="checkSeparationType != null" >
        CHECK_SEPARATION_TYPE = #{checkSeparationType,jdbcType=VARCHAR},
      </if>
      <if test="checkSeparationNumber != null" >
        CHECK_SEPARATION_NUMBER = #{checkSeparationNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstDockingDate != null" >
        FIRST_DOCKING_DATE =  #{firstDockingDate,jdbcType=TIMESTAMP},
      </if>
      <if test="illegalDate != null ">
        ILLEGAL_DATE =  #{illegalDate,jdbcType=TIMESTAMP},
      </if>
      <!-- 违法发生日期 为空 tempIllegalDate（历史的违法发生日期）不为空 -->
      <if test="illegalDate == null and tempIllegalDate!=null ">
        ILLEGAL_DATE = null,
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>

  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
    update CASE_BASE_INFO
    set CASE_NUMBER = #{caseNumber,jdbcType=VARCHAR},
      LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      OBJECT_TYPE_NAME = #{objectTypeName,jdbcType=VARCHAR},
      OBJECT_TYPE_CODE = #{objectTypeCode,jdbcType=VARCHAR},
      YEAR = #{year,jdbcType=VARCHAR},
      CASE_NAME = #{caseName,jdbcType=VARCHAR},
      INFORMANT_ID = #{informantId,jdbcType=VARCHAR},
      INFORMANT_NAME = #{informantName,jdbcType=VARCHAR},
      PUNISH_SUBJECT = #{punishSubject,jdbcType=VARCHAR},
      PUNISH_SUBJECT_ID = #{punishSubjectId,jdbcType=VARCHAR},
      PUNISH_SUBJECT_LEVEL_NAME = #{punishSubjectLevelName,jdbcType=VARCHAR},
      PUNISH_SUBJECT_LEVEL_CODE = #{punishSubjectLevelCode,jdbcType=VARCHAR},
      RESEARCH_ORG_NAME = #{researchOrgName,jdbcType=VARCHAR},
      LINKMAN = #{linkman,jdbcType=VARCHAR},
      LINKMAN_ID = #{linkmanId,jdbcType=VARCHAR},
      LINKMAN_PHONE = #{linkmanPhone,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CASE_STATUS = #{caseStatus,jdbcType=DECIMAL},
      IS_DELETE = #{isDelete,jdbcType=DECIMAL},
      DEPARTMENT_NUMBER = #{departmentNumber,jdbcType=VARCHAR},
      USER_BELONG_AREA_NAME = #{userBelongAreaName,jdbcType=VARCHAR},
      USER_BELONG_AREA_CODE = #{userBelongAreaCode,jdbcType=VARCHAR},
      DEPARTMENT_NAME = #{departmentName,jdbcType=VARCHAR},
      DEPARTMENT_ID = #{departmentId,jdbcType=VARCHAR},
      SYNCHRONIZATION_STATUS = #{synchronizationStatus,jdbcType=DECIMAL},
      RESEARCH_ORG_ID = #{researchOrgId,jdbcType=VARCHAR},
      CASE_REASON_ID = #{caseReasonId,jdbcType=DECIMAL},
      CASE_REASON_NAME = #{caseReasonName,jdbcType=VARCHAR},
      DOCKING_STATE = #{dockingState,jdbcType=DECIMAL},
      DOCK_SUS_STATE = #{dockSusState,jdbcType=DECIMAL},
      SUCCESS_DATE = #{successDate,jdbcType=TIMESTAMP},
      SEND_DOCK_DATE = #{sendDockDate,jdbcType=TIMESTAMP},
      DOCKING_TYPE = #{dockingType,jdbcType=DECIMAL},
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!--getcaseRansactionList 查询区划下的案件列表  -->
    <select id="getcaseRansactionList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    ID, CASE_NUMBER, LAW_OBJECT_ID, LAW_OBJECT_NAME, OBJECT_TYPE_NAME,
    YEAR, CASE_NAME
    from CASE_BASE_INFO
  	where case_status = 0 and IS_DELETE =1
  	and INFORMANT_ID =#{sysUserId}
   	<if test="caseName !=null and caseName !=''">
   	AND CASE_NAME  like  CONCAT(CONCAT('%',#{caseName}),'%')
   	</if>
   	<if test="lawObjectName !=null and lawObjectName !=''">
   	AND LAW_OBJECT_NAME  like  CONCAT(CONCAT('%',#{lawObjectName}),'%')
   	</if>
   	<if test="caseNumber !=null and caseNumber !=''">
   	AND CASE_NUMBER  like  CONCAT(CONCAT('%',#{caseNumber}),'%')
   	</if>
   	<if test ="arealevel == 1">
    <!-- 省级 -->
    </if>
    <if test ="arealevel == 2 ">
    <!-- 市查询本级-->
      and USER_BELONG_AREA_CODE like  '${code}%'
    </if>
      <if test ="arealevel == 3">
    	<!-- xian 查询本级-->
   	 and USER_BELONG_AREA_CODE = #{belongAreaId}
    </if>
    order by UPDATE_TIME desc
  </select>
  <select id="selectCaseInfo" resultMap="BaseResultMap">
  	select
     ID, CASE_NUMBER, LAW_OBJECT_NAME, DEPARTMENT_ID, USER_BELONG_AREA_CODE,
    CASE_NAME,PUNISH_SUBJECT,RESEARCH_ORG_NAME,CREATE_TIME
    from CASE_BASE_INFO
  	where IS_DELETE =1 and (case_status is null or case_status=0)
   	<if test="caseName !=null and caseName !=''.toString()">
   	and CASE_NAME like CONCAT(CONCAT('%',#{caseName}),'%')
   	</if>
   	<if test="lawObjectName !=null and lawObjectName !=''.toString()">
   	and LAW_OBJECT_NAME like CONCAT(CONCAT('%',#{lawObjectName}),'%')
   	</if>
   	<if test="punishSubject !=null and punishSubject !=''.toString()">
   	and PUNISH_SUBJECT like CONCAT(CONCAT('%',#{punishSubject}),'%')
   	</if>
   	<if test="researchOrgName !=null and researchOrgName !=''.toString()">
   	and RESEARCH_ORG_NAME like CONCAT(CONCAT('%',#{researchOrgName}),'%')
   	</if>
   	<if test="caseNumber !=null and caseNumber !=''.toString()">
   	and CASE_NUMBER like CONCAT(CONCAT('%',#{caseNumber}),'%')
   	</if>
    <if test="userBelongAreaCode != null and userBelongAreaCode !=''.toString()">
        AND USER_BELONG_AREA_CODE = #{userBelongAreaCode,jdbcType=VARCHAR}
    </if>
    <if test="createStartTime != null and createStartTime != ''">
        AND CREATE_TIME&gt;= to_date(#{createStartTime},'yyyy-MM-dd')
    </if>
    <if test="createEndTime != null and createEndTime !=''">
        AND CREATE_TIME&lt;= to_date(#{createEndTime},'yyyy-MM-dd')
    </if>
    order by CREATE_TIME desc
  </select>
  <!-- 查询本级及本级以下的所有案件 -->
   <select id="getCaseList" resultMap="BaseResultMap">
    select
     a.ID, a.CASE_NUMBER, a.LAW_OBJECT_NAME, a.DEPARTMENT_ID, a.USER_BELONG_AREA_CODE,
    a.CASE_NAME,a.PUNISH_SUBJECT,a.RESEARCH_ORG_NAME,a.CREATE_TIME,b.is_power_edit
    from CASE_BASE_INFO a
    left join case_state b on b.case_id=a.id
  	where a.IS_DELETE =1
   	<if test="caseName !=null and caseName !=''">
   	and a.CASE_NAME like CONCAT(CONCAT('%',#{caseName}),'%')
   	</if>
   	<if test="lawObjectName !=null and lawObjectName !=''">
   	and a.LAW_OBJECT_NAME like CONCAT(CONCAT('%',#{lawObjectName}),'%')
   	</if>
   	<if test="punishSubject !=null and punishSubject !=''">
   	and a.PUNISH_SUBJECT like CONCAT(CONCAT('%',#{punishSubject}),'%')
   	</if>
   	<if test="researchOrgName !=null and researchOrgName !=''">
   	and a.RESEARCH_ORG_NAME like CONCAT(CONCAT('%',#{researchOrgName}),'%')
   	</if>
   	<if test="caseNumber !=null and caseNumber !=''">
   	and a.CASE_NUMBER like CONCAT(CONCAT('%',#{caseNumber}),'%')
   	</if>
    <if test="userBelongAreaCode != null and userBelongAreaCode !=''">
        AND a.USER_BELONG_AREA_CODE LIKE #{userBelongAreaCode,jdbcType=VARCHAR}||'%'
    </if>
    <if test="createStartTime != null and createStartTime != ''">
        AND a.CREATE_TIME&gt;= to_date(#{createStartTime},'yyyy-MM-dd')
    </if>
    <if test="createEndTime != null and createEndTime !=''">
        AND a.CREATE_TIME&lt;= to_date(#{createEndTime},'yyyy-MM-dd')
    </if>
    order by a.DEPARTMENT_ID asc,a.CREATE_TIME desc
  </select>



   <select id="getCaseListToOne" resultType ="java.lang.Integer" >
    select  count (*) from CASE_BASE_INFO a  where a.IS_DELETE = 1
    <if test="userBelongAreaCode != null and userBelongAreaCode !=''">
        AND a.USER_BELONG_AREA_CODE LIKE #{userBelongAreaCode,jdbcType=VARCHAR}||'%'
    </if>
    <if test="caseId !=null and  caseId !='' ">
    	AND a.id = #{caseId }
    </if>
  </select>





  <!--checkCaseName 查重根据区划和案件名称  -->
   <select id="checkCaseName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
     ID
    from CASE_BASE_INFO
  	where  IS_DELETE =1 and USER_BELONG_AREA_CODE = #{belongAreaId}
   	<if test="caseName !=null and caseName !=''">
   	and CASE_NAME  = #{caseName}
   	</if>
   	</select>
   	<!--getcaseTransferList 查询区划下的案件列表(已办结的案件列表)  -->
    <select id="getcaseTransferList" parameterType="java.lang.String" resultMap="BaseResultMap">

    select
    a.ID, a.CASE_NUMBER, a.LAW_OBJECT_ID, a.LAW_OBJECT_NAME, a.OBJECT_TYPE_NAME,
    a.YEAR, a.CASE_NAME, b.is_power_edit, b.power_date_end
    from CASE_BASE_INFO a
    left join case_state b on b.case_id=a.id
  	where a.case_status = 1 and a.IS_DELETE =1
  	and a.INFORMANT_ID =#{sysUserId}
   	<if test="caseName !=null and caseName !=''">
   	and a.CASE_NAME like CONCAT(CONCAT('%',#{caseName}),'%')
   	</if>
   	<if test="lawObjectName !=null and lawObjectName !=''">
   	and a.LAW_OBJECT_NAME like CONCAT(CONCAT('%',#{lawObjectName}),'%')
   	</if>
   	<if test="caseNumber !=null and caseNumber !=''">
 	and a.CASE_NUMBER like CONCAT(CONCAT('%',#{caseNumber}),'%')
   	</if>
   	<if test ="arealevel == 1">
    <!-- 省级 -->
    </if>
    <if test ="arealevel == 2 ">
    <!-- 市查询本级-->
      and a.USER_BELONG_AREA_CODE like  '${code}%'
    </if>
      <if test ="arealevel == 3">
    	<!-- xian 查询本级-->
   	 and a.USER_BELONG_AREA_CODE = #{belongAreaId}
    </if>
    order by a.UPDATE_TIME desc
  </select>

  <!-- selectCaseInfoByDeptId 根据部门的id查询主案件的id 局级 -->
   <select id="selectCaseInfoByDeptId" resultMap="BaseResultMap">
   	select
    ID, CASE_NUMBER, punish_subject, punish_subject_id, research_org_id,
    research_org_name
    from CASE_BASE_INFO
  	where  punish_subject_id=#{deptId}
   </select>
    <!-- selectCaseInfoByDeptId 根据部门的id查询主案件的id 大队级-->
  <select id="selectCaseInfoByDownDeptId" resultMap="BaseResultMap">
   	select
    ID, CASE_NUMBER, punish_subject, punish_subject_id, research_org_id,
    research_org_name
    from CASE_BASE_INFO
  	where  research_org_id=#{deptId}
   </select>
   <!--  -->
    <update id="updateByPrimaryKeyUpdateCheckSeparation" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo">
   		update CASE_BASE_INFO set update_time = sysdate
        	,CHECK_SEPARATION_TYPE = #{checkSeparationType,jdbcType=VARCHAR}
        	,CHECK_SEPARATION_NUMBER = #{checkSeparationNumber,jdbcType=VARCHAR}
   		 where ID = #{id}
  </update>
  <!-- 思路对接 -->
  <resultMap id="siLuCaseBean" type="org.changneng.framework.frameworkbusiness.entity.SiLuCaseBean" >
  	 <result column="CASE_NUMBER" jdbcType="VARCHAR" property="caseId" />
  	 <result column="LAW_OBJECT_NAME" jdbcType="VARCHAR" property="lawObjectName" />
  	  <result column="CASE_REASON_NAME" jdbcType="VARCHAR" property="caseReasonName" />
  	  <result column="PUNISH_SUBJECT" jdbcType="VARCHAR" property="punishSubject" />
  	  <result column="RESEARCH_ORG_NAME" jdbcType="VARCHAR" property="researchOrgName" />
  	   <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
  	    <result column="LINKMAN" jdbcType="VARCHAR" property="linkman" />
    <result column="LINKMAN_PHONE" jdbcType="VARCHAR" property="linkmanPhone" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    	<result column="SOCIAL_CREDIT_CODE" jdbcType="VARCHAR" property="socialCode" />
    	<result column="GIS_COORDINATE_X" jdbcType="VARCHAR" property="gisX" />
    	<result column="GIS_COORDINATE_Y" jdbcType="VARCHAR" property="gisY" />
  </resultMap>
  <select id="selectDataByCaseId" resultMap="siLuCaseBean" parameterType="java.util.Map">
SELECT
  cbi.CASE_NUMBER,
  cbi.law_object_name,
  cbi.CASE_REASON_NAME,
  cbi.PUNISH_SUBJECT,
  cbi.research_org_name,
  cbi.CREATE_TIME,
  cbi.LINKMAN,
  cbi.LINKMAN_PHONE,
  leo.ORG_CODE,
  leo.SOCIAL_CREDIT_CODE,
  leo.GIS_COORDINATE_X,
  leo.GIS_COORDINATE_Y
FROM CASE_BASE_INFO cbi LEFT JOIN LAW_ENFORCE_OBJECT leo ON  cbi.LAW_OBJECT_ID = leo.ID
WHERE  cbi.CASE_NUMBER = #{caseId} AND cbi.IS_DELETE = 1 and leo.DELMARK  = 1
  </select>

    <select id="selectQrCaseInfo" resultMap="BaseResultMap">
	select cbi.informant_id ,cbi.is_delete,cbi.case_end_date,cbi.case_status,
	CS.IS_POWER_EDIT as docking_type
	from case_base_info cbi
	LEFT JOIN case_state cs on CS.case_id = cbi.id
	where cbi.id = #{caseId,jdbcType=VARCHAR}
   </select>


   <select id="selectsysModulesPowerMenu"  resultType ="java.lang.Integer">
	 	select count(DISTINCT sm.id)
		from  SYS_USERS su
		LEFT JOIN SYS_USERS_ROLES  sur on sur.userId = su.id
		LEFT JOIN SYS_ROLES sr on sr.role_id = sur.roleId
		LEFT JOIN SYS_ROLES_MOUDLES srm on srm.roleId = sr.role_id
		LEFT JOIN SYS_MODULES sm on sm.id = srm.modleId
		where  su.id=  #{userId ,jdbcType=VARCHAR}  and  sr.role_is_enabled = '1' and sm.id = '202'
   </select>
    <select id="selectByPrimaryKeyQr" parameterType="java.lang.String" resultMap="BaseResultMap">
	    select
	    	cbi.ID, cbi.CASE_NUMBER, cbi.LAW_OBJECT_ID, cbi.LAW_OBJECT_NAME, cbi.OBJECT_TYPE_NAME, cbi.OBJECT_TYPE_CODE,
		    cbi.CASE_NAME, cbi.INFORMANT_ID, cbi.INFORMANT_NAME, cbi.PUNISH_SUBJECT,
		    cbi.PUNISH_SUBJECT_LEVEL_CODE, cbi.RESEARCH_ORG_NAME, cbi.CREATE_TIME, cbi.UPDATE_TIME, cbi.CASE_STATUS, cbi.IS_DELETE,
		    CS.IS_POWER_EDIT as docking_type
	    from CASE_BASE_INFO  cbi
	    LEFT JOIN case_state cs on CS.case_id = cbi.id
	    where cbi.ID = #{id,jdbcType=VARCHAR}
 	 </select>
 	 <!--  初始化唯一案件编号  -->
 	 <select id="selectAllCaseBaseInfoForCaseNumber" parameterType="java.lang.String" resultMap="BaseResultMap">
		select id, user_belong_area_code,create_time
		from CASE_BASE_INFO
		where user_belong_area_code like CONCAT(#{code},'%') ORDER BY create_time
 	 </select>
 	 <!-- 获取批量案件关联列表 -->
 	  <select id="getCaseBatchAssociationList" resultMap="BaseResultMap">
    select
     a.ID, a.CASE_NUMBER, a.LAW_OBJECT_NAME, a.DEPARTMENT_ID, a.USER_BELONG_AREA_CODE,
    a.CASE_NAME,a.PUNISH_SUBJECT,a.RESEARCH_ORG_NAME,a.CREATE_TIME,b.is_power_edit
    from CASE_BASE_INFO a
    left join case_state b on b.case_id=a.id
    left join t_area ta on ta.code=a.user_belong_area_code
  	where a.IS_DELETE =1
   	<if test="caseName !=null and caseName !=''">
   	and a.CASE_NAME like CONCAT(CONCAT('%',#{caseName}),'%')
   	</if>
   	<if test="lawObjectName !=null and lawObjectName !=''">
   	and a.LAW_OBJECT_NAME like CONCAT(CONCAT('%',#{lawObjectName}),'%')
   	</if>
   	<if test="punishSubject !=null and punishSubject !=''">
   	and a.PUNISH_SUBJECT like CONCAT(CONCAT('%',#{punishSubject}),'%')
   	</if>
   	<if test="caseNumber !=null and caseNumber !=''">
   	and a.CASE_NUMBER like CONCAT(CONCAT('%',#{caseNumber}),'%')
   	</if>
    <if test="userBelongAreaCode != null and userBelongAreaCode !=''">
        AND a.USER_BELONG_AREA_CODE LIKE #{userBelongAreaCode,jdbcType=VARCHAR}||'%'
    </if>
    <if test="createStartTime != null and createStartTime != ''">
        AND a.CREATE_TIME&gt;= to_date(concat(#{createStartTime},' 00:00:00'),'yyyy-MM-dd HH24:mi:ss')
    </if>
    <if test="createEndTime != null and createEndTime !=''">
        AND a.CREATE_TIME&lt;= to_date(concat(#{createEndTime},'  23:59:59'),'yyyy-MM-dd HH24:mi:ss')
    </if>
    order by ta.location asc,a.CREATE_TIME desc
  </select>
  <update id="updateBaseInfoSpecialActionName" parameterType="java.lang.String">
    update case_base_info
    <set>
      <if test="specialActionName != null">
        special_action_name = #{specialActionName,jdbcType=VARCHAR}
      </if>
    </set>
    where ID = #{caseId,jdbcType=VARCHAR}
  </update>
   <select id="getCaseInfoWg" parameterType="java.lang.String" resultMap="AjtzSearchResultMap">
   select  a.id as id,
  	    cho.id as illegalTypes,
        a.CASE_NUMBER as caseNumber,
        a.LAW_OBJECT_NAME as lawObjectName,
        a.CASE_NAME as caseName,
        a.PUNISH_SUBJECT as punishSubject,
		t.province||' '||t.city||' '||t.country  as  remark ,
		a.informant_name,
        a.CREATE_TIME as createTime,
		a.CASE_REASON_NAME as caseReason,
        a.CASE_STATUS as caseStatus,
        cs.SMR_PROGRAM_STATE as smrProgramState,
        cs.CML_PUNISH_STATE as cmlPunishState,
        cs.ASTN_DEC_STATE as astnDecState,
        cs.ATTACHMENT_STATE as attachmentState,
        cs.LMT_PRODUCTION_STATE as lmtProductionState,
        cs.DETENTION_STATE as detentionState,
        cs.EVMT_POLLUTION_STATE as evmtPollutionState,
				pds.pdcount
	    from (
					select id,CASE_NUMBER, LAW_OBJECT_NAME,CASE_NAME,PUNISH_SUBJECT,RESEARCH_ORG_NAME,PUNISH_SUBJECT_ID,
									user_belong_area_code,CREATE_TIME,CASE_STATUS,DEPARTMENT_NAME,
									special_action_name,illegal_type_name,CASE_REASON_NAME,DOCK_SUS_STATE,FIRST_DOCKING_DATE,informant_name
	             from case_base_info cb where cb.is_delete=1
			) a
			LEFT JOIN (select  pd.case_id , count(*) as pdcount ,SUM(pd.PENALTY_ACCOUNT) as penaltyAmount  from penalty_day pd  where pd.is_del = 0 GROUP BY  pd.case_id ) pds  on  pds.case_id = a.id
			LEFT JOIN case_his_law_object cho on cho.case_id  = a.id
	    left join case_state cs on a.id = cs.case_id
	    left join t_area t on t.code = cho.belong_area_id
	    <where>
	    	<if test="caseId != null">
		        a.id = #{caseId,jdbcType=VARCHAR}
		      </if>
	    </where>

   </select>

    <select id="getInitBaseInfoCaseId" resultMap="BaseResultMap">
    	select id from CASE_BASE_INFO  where is_delete = 1
    </select>

     <resultMap id="AtvFileMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.RecordsCaseFile">
    	<id column="ID" jdbcType="VARCHAR" property="id" />
    	<result column="GENERATE_DATE" jdbcType="TIMESTAMP" property="generateDate" />
    	<result column="RECORDS_ID" jdbcType="VARCHAR" property="recordsId" />
    	<result column="OBJECT_ID" jdbcType="VARCHAR" property="objectId" />
    	<result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
	    <result column="ITEM_ID" jdbcType="VARCHAR" property="itemId" />
	    <result column="FILE_NAME" jdbcType="VARCHAR" property="fileName" />
	    <result column="FILE_TYPE" jdbcType="DECIMAL" property="fileType" />
	    <result column="FILE_URL" jdbcType="VARCHAR" property="fileUrl" />
	    <result column="STATE" jdbcType="DECIMAL" property="state" />
	    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
	    <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize" />
	    <result column="IS_GENERATE" jdbcType="DECIMAL" property="isGenerate" />
	    <result column="IS_BELONG_RECORD" jdbcType="DECIMAL" property="isBelongRecord" />
	    <result column="GENERATE_ID" jdbcType="VARCHAR" property="generateId" />
	    <result column="LOCATION" jdbcType="DECIMAL" property="location" />
  	</resultMap>
    <select id="getAtvRecordsCaseFile" resultMap="AtvFileMap">
    	select arc.id as records_id, atv.generate_time_up as generate_date , arc.type_code, arc.object_id , afi.*
		from atv_sanction_case atv
		LEFT JOIN atv_records_content arc ON arc.object_id = atv.id and IS_DEL = 0
		LEFT JOIN atv_files_info afi on afi.GENERATE_ID = arc.id and STATE = 1
		where  atv.is_delete = 0 and arc.object_id is not null and afi.id is not null
		<if test="caseType != null">
		  and atv.case_type =  #{caseType,jdbcType=VARCHAR}
		</if>
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
    </select>

    <select id="getPenaltyDay" resultMap="AtvFileMap">
     	select arc.id as records_id, atv.ONEKEY_UPDATE_DATE as generate_date , arc.type_code, arc.object_id , afi.*
		from PENALTY_DAY atv
		LEFT JOIN PENALTY_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN PENALTY_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0 and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
    </select>

    <select id="getExecutiveOrder" resultMap="AtvFileMap">
	  	select arc.id as records_id, atv.generate_time as generate_date , arc.type_code, arc.object_id , afi.*
		from executive_order atv
		LEFT JOIN ORDER_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN ORDER_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
    </select>


     <select id="getSequestrationInfo" resultMap="AtvFileMap">
        select arc.id as records_id, atv.generate_time_up as generate_date , arc.type_code, arc.object_id , afi.*
		from sequestration_info atv
		LEFT JOIN sequestration_records_content arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN sequestration_files_info afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
     </select>

 	 <select id="getLimitStopProduct" resultMap="AtvFileMap">
 	  	select arc.id as records_id, atv.generate_time_up as generate_date , arc.type_code, arc.object_id , afi.*
		from limit_stop_product atv
		LEFT JOIN limit_stop_records_content arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN limit_stop_files_info afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
 	 </select>

 	<select id="getApplyForce" resultMap="AtvFileMap">
 	 	select arc.id as records_id, atv.ONEKEY_UPDATE_DATE as generate_date , arc.type_code, arc.object_id , afi.*
		from APPLY_FORCE atv
		LEFT JOIN APPLY_FORCE_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN APPLY_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
 	</select>

	<select id="getAdministrativeDetention" resultMap="AtvFileMap">
	 	select arc.id as records_id, atv.generate_time_up as generate_date , arc.type_code, arc.object_id , afi.*
		from administrative_detention atv
		LEFT JOIN detention_records_content arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN detention_files_info afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
	</select>

	<select id="getPollutionCrime" resultMap="AtvFileMap">
	   	select arc.id as records_id, atv.ONEKEY_UPDATE_DATE as generate_date , arc.type_code, arc.object_id , afi.*
		from POLLUTION_CRIME atv
		LEFT JOIN POLLUTION_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN POLLUTION_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
	</select>


  	<select id="getOtherTransfer" resultMap="AtvFileMap">
  	   	select arc.id as records_id, atv.generate_time as generate_date , arc.type_code, arc.object_id , afi.*
		from other_transfer atv
		LEFT JOIN OTHER_TRANSFER_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN OTHER_TRANSF_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0  and arc.object_id is not null and afi.id is not null
		<if test="caseId != null">
		  and atv.case_id = #{caseId,jdbcType=VARCHAR}
		</if>
  	</select>


  	<select id="getInitPenaltyDayCaseId" resultMap="BaseResultMap">
    	select id  , CASE_ID as CASE_NUMBER , PENALTY_CASE_NUMBER as CASE_NAME  from PENALTY_DAY  where IS_DEL = 0
    </select>


    <select id="getPenaltyDayById" resultMap="AtvFileMap">
     	select arc.id as records_id, atv.ONEKEY_UPDATE_DATE as generate_date , arc.type_code, arc.object_id , afi.*
		from PENALTY_DAY atv
		LEFT JOIN PENALTY_ONEKEY_ITEM arc ON arc.object_id = atv.id and arc.IS_DEL = 0
		LEFT JOIN PENALTY_ATTACHMENT_DETAIL afi on afi.GENERATE_ID = arc.id and afi.STATE = 1
		where  atv.IS_DEL = 0 and arc.object_id is not null and afi.id is not null
		<if test="penaltyDayId != null">
		  and atv.ID = #{penaltyDayId,jdbcType=VARCHAR}
		</if>
    </select>
    <!-- 2019-5-22 一园一档  案件查询 -->
    <select id="getCaseListBean" resultMap="BaseResultMap">
	  		select
				C.ID,
				C.CASE_NUMBER,
				C.CASE_STATUS,
				C.CASE_NAME,
				C.PUNISH_SUBJECT,
				C.RESEARCH_ORG_NAME,
				C.CREATE_TIME
			from
			     CASE_BASE_INFO C
			LEFT JOIN  PARK_LAW P ON P.LAW_ID = C.LAW_OBJECT_ID
			WHERE P.PARK_ID = #{bean.parkId} AND C.IS_DELETE = 1
				<if test="bean.lawObjName!= null and bean.lawObjName !='' ">
				AND  C.CASE_NAME LIKE concat('%',concat(#{bean.lawObjName},'%'))
				</if>
				<if test="bean.status!= null and bean.status !='' ">
				AND  C.CASE_STATUS = #{bean.status}
				</if>
				<if test="bean.startTime!= null and bean.startTime !='' ">
				AND  C.CREATE_TIME &gt;=TO_DATE(#{bean.startTime}, 'YYYY-MM-DD')
				</if>
				<if test="bean.endTime!= null and bean.endTime !='' ">
				AND  C.CREATE_TIME &lt; TO_DATE(#{bean.endTime}, 'YYYY-MM-DD')+1
				</if>
				<if test="bean.caseNumber!= null and bean.caseNumber !='' ">
				AND  C.case_number LIKE concat('%',concat(#{bean.caseNumber},'%'))
				</if>
				ORDER BY  C.CREATE_TIME DESC
    </select>

    <!-- 2019-5-22 一园一档  案件查询 -->
    <select id="getEasyCaseListBean" resultMap="BaseResultMap">
        select
        C.ID,
        C.CASE_NUMBER,
        C.CASE_STATUS,
        C.LAW_OBJECT_ID,
        C.LAW_OBJECT_NAME,
        C.CASE_NAME,
        C.PUNISH_SUBJECT,
        C.RESEARCH_ORG_NAME,
        C.CREATE_TIME,
        atv.PENALTY_AMOUNT
        from
        CASE_BASE_INFO C
        LEFT JOIN  PARK_LAW P ON P.LAW_ID = C.LAW_OBJECT_ID
        left join ATV_SANCTION_CASE atv on atv.CASE_ID = C.ID
        WHERE P.PARK_ID = #{bean.parkId}   AND C.IS_DELETE = 1
        and (atv.CASE_TYPE = '1' or atv.CASE_TYPE = '2'  )
        <if test="bean.lawObjName!= null and bean.lawObjName !='' ">
            AND  C.CASE_NAME LIKE concat('%',concat(#{bean.lawObjName},'%'))
        </if>
        <if test="bean.status!= null and bean.status !='' ">
            AND  C.CASE_STATUS = #{bean.status}
        </if>
        <if test="bean.startTime!= null and bean.startTime !='' ">
            AND  C.CREATE_TIME &gt;=TO_DATE(#{bean.startTime}, 'YYYY-MM-DD')
        </if>
        <if test="bean.endTime!= null and bean.endTime !='' ">
            AND  C.CREATE_TIME &lt; TO_DATE(#{bean.endTime}, 'YYYY-MM-DD')+1
        </if>
        <if test="bean.caseNumber!= null and bean.caseNumber !='' ">
            AND  C.case_number LIKE concat('%',concat(#{bean.caseNumber},'%'))
        </if>
        ORDER BY  C.CREATE_TIME DESC
    </select>

    <!-- 2019-5-22 一园一档  案件查询 -->
    <select id="getEasyCaseList" resultMap="BaseResultMap">
        select
        C.ID,
        C.CASE_NUMBER,
        C.CASE_STATUS,
        C.LAW_OBJECT_ID,
        C.LAW_OBJECT_NAME,
        C.CASE_NAME,
        C.PUNISH_SUBJECT,
        C.RESEARCH_ORG_NAME,
        C.CREATE_TIME,
        atv.PENALTY_AMOUNT,
        leo.GIS_COORDINATE_X,
        leo.GIS_COORDINATE_X84,
        leo.GIS_COORDINATE_Y,
        leo.GIS_COORDINATE_Y84
        from
        CASE_BASE_INFO C
        LEFT JOIN  PARK_LAW P ON P.LAW_ID = C.LAW_OBJECT_ID
        left join ATV_SANCTION_CASE atv on atv.CASE_ID = C.ID
        LEFT JOIN LAW_ENFORCE_OBJECT leo ON leo. ID = C.LAW_OBJECT_ID
        WHERE P.PARK_ID = #{bean.parkId}   AND C.IS_DELETE = 1
        and (atv.CASE_TYPE = '1' or atv.CASE_TYPE = '2'  )
        <if test="bean.lawObjName!= null and bean.lawObjName !='' ">
            AND  C.CASE_NAME LIKE concat('%',concat(#{bean.lawObjName},'%'))
        </if>
        <if test="bean.status!= null and bean.status !='' ">
            AND  C.CASE_STATUS = #{bean.status}
        </if>
        <if test="bean.startTime!= null and bean.startTime !='' ">
            AND  C.CREATE_TIME &gt;=TO_DATE(#{bean.startTime}, 'YYYY-MM-DD')
        </if>
        <if test="bean.endTime!= null and bean.endTime !='' ">
            AND  C.CREATE_TIME &lt; TO_DATE(#{bean.endTime}, 'YYYY-MM-DD')+1
        </if>
        <if test="bean.caseNumber!= null and bean.caseNumber !='' ">
            AND  C.case_number LIKE concat('%',concat(#{bean.caseNumber},'%'))
        </if>
        ORDER BY  C.CREATE_TIME DESC
    </select>
    <!-- 2019-7-2 专项台账 案件查询 -->
    <select id="getCaseListBeanFromtz" resultMap="BaseResultMap">
	  		select
				C.ID,
				C.CASE_NUMBER,
				C.CASE_STATUS,
				C.CASE_NAME,
				C.PUNISH_SUBJECT,
				C.RESEARCH_ORG_NAME,
				C.CREATE_TIME,
				C.INFORMANT_NAME
			from
			     CASE_BASE_INFO C
			LEFT JOIN  CASE_SPECIAL P ON P.CASE_ID = C.ID
			WHERE P.SPECIAL_ACTION_ID = #{bean.parkId}   AND C.IS_DELETE = 1
				<if test="bean.lawObjName!= null and bean.lawObjName !='' ">
				AND  C.CASE_NAME LIKE concat('%',concat(#{bean.lawObjName},'%'))
				</if>
				<if test="bean.status!= null and bean.status !='' ">
				AND  C.CASE_STATUS = #{bean.status}
				</if>
				<if test="bean.startTime!= null and bean.startTime !='' ">
				AND  C.CREATE_TIME &gt;=TO_DATE(#{bean.startTime}, 'YYYY-MM-DD')
				</if>
				<if test="bean.endTime!= null and bean.endTime !='' ">
				AND  C.CREATE_TIME &lt; TO_DATE(#{bean.endTime}, 'YYYY-MM-DD')+1
				</if>
				<if test="bean.caseNumber!= null and bean.caseNumber !='' ">
				AND  C.case_number LIKE concat('%',concat(#{bean.caseNumber},'%'))
				</if>
				ORDER BY  C.CREATE_TIME DESC
    </select>

    <select id="getCaseBaseInfoByCaseId" parameterType="java.lang.String" resultType="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
        select b.case_number as caseCode,TO_CHAR( b.create_time, 'yyyy-mm-dd HH24:mi:ss') formatDate, b.law_object_name caseParty, b.punish_subject punishMain, b.id as caseId, a.id as  id
         from E_DBD_CASE a INNER JOIN CASE_BASE_INFO b
          on a.CASE_NUMBER = b.CASE_NUMBER AND  is_delete = '1' and a.CASE_ID = b.id
          AND a.DBDBH in (#{caseId, jdbcType=VARCHAR})
    </select>

    <select id="selectSupervisionCases" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchBean" resultType="org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResult">
        select a.CASE_NUMBER as caseNumber, a.case_name as caseName, a.law_object_name as lawObjectName,
        a.punish_subject as punishSubject, a.informant_name as informantName, TO_CHAR( a.create_time , 'yyyy-mm-dd HH24:mi:ss')  as createTime, a.id
        from CASE_BASE_INFO  a INNER JOIN SYS_DEPARTMENT b  ON a.DEPARTMENT_NUMBER = b.DEPARTMENT_NUMBER
         <if test="caseNumber != null and caseNumber != ''">
            AND a.CASE_NUMBER = #{caseNumber, jdbcType=VARCHAR}
        </if>
        <if test="caseName != null and caseName != ''">
            and a.case_name = #{caseName, jdbcType=VARCHAR}
        </if>
        <if test="punishSubject != null and punishSubject != ''">
            and a.punish_subject = #{punishSubject, jdbcType=VARCHAR}
        </if>
        <if test="closeCaseDateBegin != null and closeCaseDateBegin != ''">
            and a.create_time &gt;= TO_DATE(#{closeCaseDateBegin}, 'YYYY-MM-DD')
        </if>
        <if test="closeCaseDateEnd != null and closeCaseDateEnd != ''">
            and a.create_time &lt; TO_DATE(#{closeCaseDateEnd}, 'YYYY-MM-DD')+1
        </if>
        <if test="belongCity != null and belongCity != ''">
            AND b.BELONG_AREACODE like concat('%', CONCAT(#{belongCity, jdbcType=VARCHAR} ,'%'))
        </if>
        <if test="lawObjName != null and lawObjName != ''">
            AND a.LAW_OBJECT_NAME like concat('%', CONCAT(#{lawObjName, jdbcType=VARCHAR} ,'%'))
        </if>
        <if test="dbdbh != null and dbdbh != ''">
            AND   a.CASE_NUMBER not in (select CASE_NUMBER from  E_DBD_CASE   where DBDBH in ( #{dbdbh, jdbcType=VARCHAR} ))
        </if>
        AND is_delete = '1'
        order by  a.create_time desc
    </select>

    <select id="getSupervise" parameterType="java.lang.String" resultType="java.lang.String">
        select listagg (CASE_NUMBER, ',') WITHIN GROUP (ORDER BY CREATE_TIME) as caseNumber from E_DBD_CASE
        where DBDBH in ( #{dbdbh, jdbcType=VARCHAR} )
    </select>
</mapper>
