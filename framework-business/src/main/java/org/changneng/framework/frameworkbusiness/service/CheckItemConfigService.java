package org.changneng.framework.frameworkbusiness.service;

import java.util.List;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;

/**
 * 环境监管一件事-检查项配置 Service接口
 * 简单的两级树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
public interface CheckItemConfigService {

    /**
     * 获取完整的两级树形结构数据
     * 一级节点的PARENT_ID为"0"，二级节点的PARENT_ID为对应一级节点的ID
     *
     * @return 两级树形结构的检查项配置VO列表
     */
    List<CheckItemConfigTreeVO> getTreeStructure();
}
