package org.changneng.framework.frameworkbusiness.entity;

import org.springframework.format.annotation.DateTimeFormat;

/**
 * 系统日志对应查询条件基类
 * 
 */
public class SysLogSearchCondition {

	private Integer pageSize;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String beginDate;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private String endDate;

	private String ip;

	private Integer pageNum;

	public Integer getPageSize() {
		return pageSize;
	}

	public void setPageSize(Integer pageSize) {
		this.pageSize = pageSize;
	}

	public String getBeginDate() {
		return beginDate;
	}

	public void setBeginDate(String beginDate) {
		this.beginDate = beginDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getIp() {
		return ip;
	}

	public void setIp(String ip) {
		this.ip = ip;
	}

	public Integer getPageNum() {
		return pageNum;
	}

	public void setPageNum(Integer pageNum) {
		this.pageNum = pageNum;
	}

}
