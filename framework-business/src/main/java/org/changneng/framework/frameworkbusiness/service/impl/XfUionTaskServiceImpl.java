package org.changneng.framework.frameworkbusiness.service.impl;

import org.changneng.framework.frameworkbusiness.dao.XfUionTaskMapper;
import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.service.XfUionTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
public class XfUionTaskServiceImpl implements XfUionTaskService {

    @Autowired
    private XfUionTaskMapper xfUionTaskMapper;


    @Override
    public void inserXfUionTask(String ajid,List<String> list,String isrelation) {

        List<XfUionTask> xfUionTasklist = new ArrayList<>();
        System.out.println(xfUionTasklist);
        // 关联操作
        if(list.size()>0){
            for (int i = 0;i < list.size();i++){
                // 执行插入之前先查询
                XfUionTask xfUionTask1 = xfUionTaskMapper.selectXfUionTask(ajid, list.get(i));
                if(xfUionTask1 == null){
                    XfUionTask xfUionTask = new XfUionTask();
                    // 32 + 13 位
                    String uuid = UUID.randomUUID().toString().replace("-","")+System.currentTimeMillis();
                    xfUionTask.setId(uuid);
                    xfUionTask.setTaskid((String) list.get(i));
                    xfUionTask.setXfajid(ajid);
                    xfUionTask.setIsrelation(1);
                    xfUionTask.setIsreal(Integer.valueOf(isrelation));
                    xfUionTask.setListtype(0);  // 关联列表
                    xfUionTasklist.add(xfUionTask);
                }
            }
        }

        System.out.println(xfUionTasklist);
        if(xfUionTasklist.size() > 0){
            xfUionTaskMapper.xfUionTaskintsert(xfUionTasklist);
        }

    }

    // 取消关联
    @Override
    public void delXfUionTask(String ajid, String task_id) {
        xfUionTaskMapper.xfUionTaskdel(ajid,task_id);
    }

    // 分配任务保存
    @Override
    public void inserXfUionTaskid(String xfajid,String taskid,String isrelation,String isreal) {

        // 关联表中保存之前先查询
        XfUionTask xfUionTask1 = xfUionTaskMapper.selectXfUionTask(xfajid, taskid);
        if(xfUionTask1 == null){
            // 32 + 13 位
            String uuid = UUID.randomUUID().toString().replace("-","")+System.currentTimeMillis();
            xfUionTaskMapper.xfUionTaskidintsert(uuid,xfajid,taskid,isrelation,isreal,"1");
        }
    }

    @Override
    public List<XfzfList> selectzflistbyajid(String ajid) {
        // 包含 关联 is 和 分配
        List<XfzfList> selectzflistbyajid = xfUionTaskMapper.selectzflistbyajid(ajid);
        return selectzflistbyajid;
    }



    @Override
    public void delpwUionTask(String ajid, String taskid) {
         xfUionTaskMapper.delpwUionTask(ajid,taskid);
    }

    @Override
    public int deleteTask(String id) {
        return xfUionTaskMapper.deleteTask(id);
    }

    @Override
    public List<TbPwTaskList> queryWgtaskList(String ajid) {
        List<TbPwTaskList> pwzfList = xfUionTaskMapper.queryWgtaskList(ajid);
        return pwzfList;
    }

    /**
     *  排污 TASK
     * <AUTHOR>
     * @date 2021/4/1 18:56
     * @param ajid
     * @return java.util.List<org.changneng.framework.frameworkbusiness.entity.TbPwTaskList>
     */
    @Override
    public List<TbPwTaskList> querypwtaskList(String ajid) {
        List<TbPwTaskList> pwzfList = xfUionTaskMapper.querypwtaskList(ajid);
        return pwzfList;
    }


    @Override
    public void addpwUionTask(String ajid, List<String> list, String isrelation) {
        List<TbPwzfTask> pwUionTasklist = new ArrayList<>();
        // 关联操作
        if(list.size()>0){
            for (int i = 0;i < list.size();i++){
                String[] split = list.get(i).split(":");
                String taskcode=split[0];
                String taskids=split[1];

                // 执行插入之前先查询
                /* XfUionTask xfUionTask1 = xfUionTaskMapper.selectXfUionTask(ajid, list.get(i));*/
                TbPwzfTask pwzfTask = xfUionTaskMapper.selectPwUionTask(ajid, taskcode);
                if(pwzfTask == null){
                    TbPwzfTask pwzfTask2 = new TbPwzfTask();
                    // 32 + 13 位
                    String uuid = UUID.randomUUID().toString().replace("-","")+System.currentTimeMillis();
                    pwzfTask2.setId(uuid);
                    pwzfTask2.setTaskid(taskids);
                    pwzfTask2.setTaskcode(taskcode);
                    pwzfTask2.setPwzfid(ajid);
                    pwzfTask2.setIsrelation("1");
                    pwzfTask2.setIsreal(isrelation);
                    pwzfTask2.setListtype("0");  // 关联列表
                    pwUionTasklist.add(pwzfTask2);
                }
            }
        }

        System.out.println(pwUionTasklist);
        if(pwUionTasklist.size() > 0){

            /*xfUionTaskMapper.xfUionTaskintsert(xfUionTasklist);*/
            xfUionTaskMapper.pwUionTaskintsert(pwUionTasklist);

        }
    }


    @Override
    public void addWgTask(String ajid, List<String> list, String isrelation) {
        List<WgsjTask> wgUionTasklist = new ArrayList<>();
        System.out.println(wgUionTasklist);
        // 关联操作
        if(list.size()>0){
            for (int i = 0;i < list.size();i++){
                String[] split = list.get(i).split(":");
                String taskcode=split[0];
                String taskids=split[1];

                // 执行插入之前先查询
                WgsjTask wgzfTask = xfUionTaskMapper.selectWgUionTask(ajid, taskcode);
                if(wgzfTask == null){
                    WgsjTask wgzfTask2 = new WgsjTask();
                    // 32 + 13 位
                    String uuid = UUID.randomUUID().toString().replace("-","")+System.currentTimeMillis();
                    wgzfTask2.setId(uuid);
                    wgzfTask2.setTaskid(taskids);
                    wgzfTask2.setTaskcode(taskcode);
                    wgzfTask2.setWgsjzfid(ajid);
                    wgUionTasklist.add(wgzfTask2);
                }
            }
        }

        System.out.println(wgUionTasklist);
        if(wgUionTasklist.size() > 0){

            xfUionTaskMapper.wgUionTaskintsert(wgUionTasklist);

        }
    }

    @Override
    public int deleteWgTask(String id) {
        return xfUionTaskMapper.deleteWgTask(id);
    }
}
