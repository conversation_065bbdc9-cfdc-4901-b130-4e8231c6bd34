package org.changneng.framework.frameworkbusiness.service.filecase;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDayWithBLOBs;

/**
 * 按日计罚业务处理接口
 * @ClassName: PenaltyDayService 
 * @Description: 
 * <AUTHOR>
 * @date 2017年8月2日 上午9:51:21 
 *
 */
public interface PenaltyDayService {
	
	/**
	 * 根据案件id获取一条数据
	 * @param caseId
	 * @return
	 */
	List<PenaltyDayWithBLOBs> selectByCaseId(String caseId);

	/**
	 * 检查号码重复
	 * @param checkType
	 * @param checkId
	 * @param informantBelongArea
	 * @return
	 */
	List<PenaltyDayWithBLOBs> checkRepeat(String checkType, String belongArea, String decisionNumber, String caseNumber);

	/**
	 * 上半部分保存
	 * @param penalty
	 * @param itemIds
	 * @return
	 */
	Integer penaltySave(PenaltyDayWithBLOBs penalty, String itemIds) throws Exception;

	/**
	 * 提交操作：type：1上半部分，2下半部分
	 * @param id
	 * @param type
	 * @throws Exception
	 */
	void submit(String id, String type) throws Exception;

	/**
	 * 根据id查询
	 * @param penaltyId
	 * @return
	 */
	PenaltyDayWithBLOBs selectById(String penaltyId);

	/**
	 * 删除一条按日计罚信息
	 * @param id
	 * @param deleteSource 0：表示基本信息进来的删除  1：表示自己本模块的删除
	 * @throws Exception
	 */
	void delPenaltyInfo(String id,Integer deleteSource) throws Exception;

	/**
	 * 根据caseId查出多条数据的id集合
	 * @param caseId
	 * @return
	 */
	List<String> selectIdsByCaseId(String caseId);
}
