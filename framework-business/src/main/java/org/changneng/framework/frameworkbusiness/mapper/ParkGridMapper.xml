<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.ParkGridMapper" >
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.ParkGrid" >
    <id column="ID" property="id" jdbcType="VARCHAR" />
    <result column="PARK_ID" property="parkId" jdbcType="VARCHAR" />
    <result column="GRID_CODE" property="gridCode" jdbcType="VARCHAR" />
    <result column="GRID_NAME" property="gridName" jdbcType="VARCHAR" />
    <result column="GRID_LEVEL" property="gridLevel" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ID, PARK_ID, GRID_CODE,GRID_LEVEL,GRID_NAME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PARK_GRID
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <!-- 通过园区id查询涉及网格信息 -->
  <select id="selectByParkId" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from PARK_GRID
    where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </select>
  <!-- 获取园区下涉及网格code -->
  <select id="getGridCodesByParkId" resultType="java.lang.String" parameterType="java.lang.String" >
  	<!-- 这个sql只适合数据组合后少于4000个字符的情况 -->
    <!-- select listagg(grid_code,',') within group (order by park_id)  from PARK_GRID  -->
    <!-- 该sql可以支持clob长度 -->
    select xmlagg(xmlparse(content grid_code ||',' wellformed) order by park_id).getclobval() attributes  from PARK_GRID 
    where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </select>
  <!-- 获取园区下涉及网格name -->
  <select id="getGridNamesByParkId" resultType="java.lang.String" parameterType="java.lang.String" >
    <!-- select listagg(grid_name,',') within group (order by park_id)  from PARK_GRID  -->
    select xmlagg(xmlparse(content grid_name ||',' wellformed) order by park_id).getclobval() attributes  from PARK_GRID 
    where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from PARK_GRID
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByParkId" parameterType="java.lang.String" >
    delete from PARK_GRID
    where PARK_ID = #{parkId,jdbcType=VARCHAR}
  </delete>
    <!-- 批量新增 -->
  <select  id="batchInsert" parameterType="java.util.List">
     insert ALL 
        <foreach item="item" index="index" collection="list"   separator="" >
        into  PARK_GRID (ID, PARK_ID, GRID_CODE,GRID_LEVEL,GRID_NAME)
        values(sys_guid(), #{item.parkId,jdbcType=VARCHAR},#{item.gridCode,jdbcType=VARCHAR},#{item.gridLevel,jdbcType=VARCHAR},#{item.gridName,jdbcType=VARCHAR})
        </foreach>
         SELECT * FROM dual
  </select >
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkGrid" >
    insert into PARK_GRID (ID, PARK_ID, GRID_CODE
      )
    values (#{id,jdbcType=VARCHAR}, #{parkId,jdbcType=VARCHAR}, #{gridCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkGrid" >
  <selectKey keyProperty="id" order="BEFORE" resultType="String">
  			select sys_guid() from dual
  		</selectKey>
    insert into PARK_GRID
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        ID,
      </if>
      <if test="parkId != null" >
        PARK_ID,
      </if>
      <if test="gridCode != null" >
        GRID_CODE,
      </if>
      <if test="gridLevel != null" >
        GRID_LEVEL,
      </if>
      <if test="gridName != null" >
        GRID_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="parkId != null" >
        #{parkId,jdbcType=VARCHAR},
      </if>
      <if test="gridCode != null" >
        #{gridCode,jdbcType=VARCHAR},
      </if>
      <if test="gridLevel != null" >
        #{gridLevel,jdbcType=VARCHAR},
      </if>
      <if test="gridName != null" >
        #{gridName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkGrid" >
    update PARK_GRID
    <set >
      <if test="parkId != null" >
        PARK_ID = #{parkId,jdbcType=VARCHAR},
      </if>
      <if test="gridCode != null" >
        GRID_CODE = #{gridCode,jdbcType=VARCHAR},
      </if>
      <if test="gridLevel != null" >
        GRID_LEVEL = #{gridLevel,jdbcType=VARCHAR},
      </if>
      <if test="gridName != null" >
        GRID_NAME = #{gridName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.ParkGrid" >
    update PARK_GRID
    set PARK_ID = #{parkId,jdbcType=VARCHAR},
      GRID_CODE = #{gridCode,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>