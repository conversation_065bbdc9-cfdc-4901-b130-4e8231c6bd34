<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.AskingSysModelMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.AskingSysModel">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="TEMPLATE_NUMBER" jdbcType="VARCHAR" property="templateNumber" />
    <result column="TEMPLATE_NAME" jdbcType="VARCHAR" property="templateName" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="IS_ISSUE" jdbcType="DECIMAL" property="isIssue" />
    <result column="TEMPLATE_OBJECT_TYPE" jdbcType="VARCHAR" property="templateObjectType" />
    <result column="TEMPLATE_AREA" jdbcType="VARCHAR" property="templateArea" />
    <result column="TEMPLATE_AREANAME" jdbcType="VARCHAR" property="templateAreaname" />
    <result column="TEMPLATE_INDUSTRY" jdbcType="VARCHAR" property="templateIndustry" />
    <result column="TEMPLATE_INDUSTRY_NAME" jdbcType="VARCHAR" property="templateIndustryName" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="IS_DEFAULT" jdbcType="DECIMAL" property="isDefault" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, TEMPLATE_NUMBER, TEMPLATE_NAME, CREATE_TIME, UPDATE_TIME, IS_ISSUE, TEMPLATE_OBJECT_TYPE, 
    TEMPLATE_AREA, TEMPLATE_AREANAME, TEMPLATE_INDUSTRY, TEMPLATE_INDUSTRY_NAME, USER_ID, 
    IS_DEFAULT
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ASKING_SYS_MODEL
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from ASKING_SYS_MODEL
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.AskingSysModel">
    insert into ASKING_SYS_MODEL (ID, TEMPLATE_NUMBER, TEMPLATE_NAME, 
      CREATE_TIME, UPDATE_TIME, IS_ISSUE, 
      TEMPLATE_OBJECT_TYPE, TEMPLATE_AREA, TEMPLATE_AREANAME, 
      TEMPLATE_INDUSTRY, TEMPLATE_INDUSTRY_NAME, 
      USER_ID, IS_DEFAULT)
    values (#{id,jdbcType=VARCHAR}, #{templateNumber,jdbcType=VARCHAR}, #{templateName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{isIssue,jdbcType=DECIMAL}, 
      #{templateObjectType,jdbcType=VARCHAR}, #{templateArea,jdbcType=VARCHAR}, #{templateAreaname,jdbcType=VARCHAR}, 
      #{templateIndustry,jdbcType=VARCHAR}, #{templateIndustryName,jdbcType=VARCHAR}, 
      #{userId,jdbcType=VARCHAR}, #{isDefault,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.AskingSysModel">
       <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into ASKING_SYS_MODEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="templateNumber != null">
        TEMPLATE_NUMBER,
      </if>
      <if test="templateName != null">
        TEMPLATE_NAME,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="isIssue != null">
        IS_ISSUE,
      </if>
      <if test="templateObjectType != null">
        TEMPLATE_OBJECT_TYPE,
      </if>
      <if test="templateArea != null">
        TEMPLATE_AREA,
      </if>
      <if test="templateAreaname != null">
        TEMPLATE_AREANAME,
      </if>
      <if test="templateIndustry != null">
        TEMPLATE_INDUSTRY,
      </if>
      <if test="templateIndustryName != null">
        TEMPLATE_INDUSTRY_NAME,
      </if>
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="templateNumber != null">
        #{templateNumber,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isIssue != null">
        #{isIssue,jdbcType=DECIMAL},
      </if>
      <if test="templateObjectType != null">
        #{templateObjectType,jdbcType=VARCHAR},
      </if>
      <if test="templateArea != null">
        #{templateArea,jdbcType=VARCHAR},
      </if>
      <if test="templateAreaname != null">
        #{templateAreaname,jdbcType=VARCHAR},
      </if>
      <if test="templateIndustry != null">
        #{templateIndustry,jdbcType=VARCHAR},
      </if>
      <if test="templateIndustryName != null">
        #{templateIndustryName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.AskingSysModel">
    update ASKING_SYS_MODEL
    <set>
      <if test="templateNumber != null">
        TEMPLATE_NUMBER = #{templateNumber,jdbcType=VARCHAR},
      </if>
      <if test="templateName != null">
        TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.AskingSysModel">
    update ASKING_SYS_MODEL
    set TEMPLATE_NUMBER = #{templateNumber,jdbcType=VARCHAR},
      TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
      <resultMap id="selectAskingSysModelBySeachInfoMap" type="org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelResult">
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="templateNumber" jdbcType="VARCHAR" property="templateNumber" />
    <result column="templateName" jdbcType="VARCHAR" property="templateName" />
    <result column="templateArea" jdbcType="VARCHAR" property="templateArea" />
    <result column="templateObjectType" jdbcType="VARCHAR" property="templateObjectType" />
    <result column="templateAreaname" jdbcType="VARCHAR" property="templateAreaname" />
    <result column="templateIndustry" jdbcType="VARCHAR" property="templateIndustry" />
    <result column="templateIndustryName" jdbcType="VARCHAR" property="templateIndustryName" />
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="userName" jdbcType="VARCHAR" property="userName" />
    <result column="userId" jdbcType="VARCHAR" property="userId" />
    <result column="contributionName" jdbcType="VARCHAR" property="contributionName" />
    <result column="contributionId" jdbcType="VARCHAR" property="contributionId" />
    <result column="admUserId" jdbcType="VARCHAR" property="admUserId" />
    <result column="isDefault" jdbcType="DECIMAL" property="isDefault" />
  	</resultMap>
  
  <select id="selectAskingSysModelBySeachInfo" parameterType="org.changneng.framework.frameworkbusiness.entity.refinetemplate.SceneSysModelSeach" resultMap="selectAskingSysModelBySeachInfoMap">
    select ASM.id as id, ASM.TEMPLATE_NUMBER as templateNumber, ASM.TEMPLATE_NAME as templateName, ASM.CREATE_TIME as createTime, ASM.UPDATE_TIME as updateTime, ASM.IS_ISSUE as isIssue, ASM.TEMPLATE_OBJECT_TYPE as templateObjectType, 
    ASM.TEMPLATE_AREA as templateArea, ASM.TEMPLATE_AREANAME as templateAreaname, ASM.TEMPLATE_INDUSTRY as templateIndustry, ASM.TEMPLATE_INDUSTRY_NAME as templateIndustryName, ASM.USER_ID as userId, 
    ASM.IS_DEFAULT,adm.userid as admUserId,ASM.is_default as isDefault
	from ASKING_SYS_MODEL  ASM
	LEFT JOIN (select * from ASKING_DEFAULT_MODEL where ASKING_DEFAULT_MODEL.userId = #{askingSysModelSeach.userId} and ASKING_DEFAULT_MODEL.template_type='0') adm ON ASM. ID = adm.CUSTOM_DATABASE_ID
	where 1=1
    <if test="askingSysModelSeach.createStartTime != null and askingSysModelSeach.createStartTime != ''	">
		<!-- 创建时间 -->
		<!-- 由于创建时间是根据用户创建时  的时分秒生成的 所以先转成字符串再转日期 -->
  		AND to_date(to_char(ASM.CREATE_TIME,'yyyy-mm-dd'),'yyyy-mm-dd') &gt;= to_date(#{askingSysModelSeach.createStartTime},'yyyy-mm-dd')
  	</if>
    <if test="askingSysModelSeach.createEndTime != null and askingSysModelSeach.createEndTime != ''">
		<!-- 创建时间 -->
		<!-- 由于创建时间是根据用户创建时  的时分秒生成的 所以先转成字符串再转日期 -->
  		AND to_date(to_char(ASM.CREATE_TIME,'yyyy-mm-dd'),'yyyy-mm-dd') &lt;= to_date(#{askingSysModelSeach.createEndTime},'yyyy-mm-dd')
  	</if>
  	
  	<if test="askingSysModelSeach.templateName != null and askingSysModelSeach.templateName != ''">
  	<!-- 模版名称 -->	
  		and ASM.TEMPLATE_NAME like concat(concat('%',#{askingSysModelSeach.templateName}),'%')
  	</if>
  	
  	<if test="askingSysModelSeach.templateNumber != null and askingSysModelSeach.templateNumber != ''">
  	<!-- 模版编号 -->	
  		and ASM.TEMPLATE_NUMBER like concat(concat('%',#{askingSysModelSeach.templateNumber}),'%')
  	</if>
  	
  	<if test="askingSysModelSeach.templateObjectType != null and askingSysModelSeach.templateObjectType != ''">
  	<!-- 适用对象类型 -->
  		and ASM.TEMPLATE_OBJECT_TYPE=#{askingSysModelSeach.templateObjectType}
  	</if>
  	<if test="askingSysModelSeach.templateIndustry != null and askingSysModelSeach.templateIndustry != ''">
  	<!-- 适用行业code -->
  		and ASM.TEMPLATE_INDUSTRY=#{askingSysModelSeach.templateIndustry}
  	</if>
  	order by 
  		adm.id ASC,
		asm.CREATE_TIME DESC
  </select>
  
  <!-- 同类型下，模版名称不能重复 -->
   <select id="chickTemplateName" parameterType="java.lang.String" resultMap="BaseResultMap">
     select  ID from ASKING_SYS_MODEL
     where 1=1 and TEMPLATE_NAME = #{templateName,jdbcType=VARCHAR}
     	<if test="templateObjectType==null or templateObjectType==''">
    		and TEMPLATE_OBJECT_TYPE is null
    	</if>
    	<if test="templateObjectType!=null and templateObjectType!=''">
    		AND TEMPLATE_OBJECT_TYPE = #{templateObjectType}
    	</if>
  </select>
</mapper>