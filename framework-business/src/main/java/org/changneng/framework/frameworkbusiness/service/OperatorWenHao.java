package org.changneng.framework.frameworkbusiness.service;

public interface OperatorWenHao {
	
	public void initWenHao();
	
	/**
	 * 总的调用文号
	 */
	public String getWenHao(String areaCode,String type,String year);
	public String getWenHaoRedis(String areaCode,String type);
	/**
	 * 选取不是当前年的时候，获取文号
	 */
	public String getOldYearWenHao(String areaCode,String type,String year);
	
	public boolean deleteKey(String areaCode,String type,String code);
	public boolean deleteOldYear(String areaCode,String type,String code,String year);
	public boolean deleteWenHao(String areaCode,String type,String code,String year);
	
	public boolean isValidKeyRedis(String areaCode,String type,String code);
	public boolean isValidOldYear(String areaCode,String type,String code,String year);
	public boolean isValidWenHao(String areaCode,String type,String code,String year);
	
	/**
	 * 删除后，把文号回收。redis
	 */
	public boolean addCode(String areaCode,String type,String code);
	
	/**
	 * 每年12月31号，把（在线申请文号类型的key）全部删掉
	 */
	
	public void deleteAllKey();
	
	/**
	 * 初始化redis中集合最大值
	 */
	public void initRedisMax();
	
	/**
	 * 取消文号
	 */
	public void clanceWenHao(String areaCode,String type,String code);

	/**
	 * 获取往年文号并存入 CASE_MANAGE_MAX_NUMBER中
	 * @param areaCode   区划数组
	 * @param type   案件类型数组
	 */
	public void getTheUsualNumber(String[] areaCode,String[] type);
}

