package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.CDictionaryEntry;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileCheck;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileDictionary;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface CaseFileEntryService {

	/**
	 * 条件查询案卷管理配置
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	List<CFileEntry> getCaseFileEntryList(SysUsers sysUser);
	
	/**
	 * 根据主键删除该条配置信息
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 */
	JsonResult deleteCaseFileEntryListById(String id,SysUsers sysUser);
	
	/**
	 * cFileEntryList
	 * 查询所有附件条目信息
	 */
	List<CFileDictionary> getCFileDictionaryList(SysUsers sysUser,String ids);
	
	/**
	 * 根据主键查询
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	List<CFileDictionary> getCaseFileEntryById(SysUsers sysUser,String id);
	
	/**
	 * 根据外键查询
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	List<CFileDictionary> getCaseFileEntryByEntryId(SysUsers sysUser,String id);
	
	/**
	 * 根据主键和名称查询
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	void getCaseFileEntryByIdAndName(String entryId,String entryName)throws Exception;
	
	/**
	 * 新插入一条文书类别到附件条目表
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	void addCFileEntry(CFileEntry cFileEntry)throws Exception;
	
	/**
	 * 更新附件条目表
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	void updateById(CFileEntry cFileEntry)throws Exception;
	
	
	/**
	 * 新插入一条文书类别到附件条目表
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	void addCDictionaryEntry(CDictionaryEntry cDictionaryEntry)throws Exception;
	
	
	/**
	 * 查询附件条目表location最大值
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	Integer getMaxLocation();
	
	/**
	 *查询附件条目表和字典中间表location最大值
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	Integer getCDictionaryEntryMaxLocation();
	
	/**
	 *查询附件条目表和字典中间表location最大值
	 * @param modeleNum
	 * @param sysUser 
	 * @return
	 */
	void deleteAllByEntryId(String entryId)throws Exception;
	
	/**
	 * 新增或修改配置信息
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 */
	void saveOrUpdate(String ids,String entryName)throws Exception;
	
	/**
	 * caseFileEntryList
	 * 保存
	 */
	ResponseJson saveCDictionaryEnty(String ids,String entryId,String entryName)throws Exception;
	
	/**
	 * cFileEntryList
	 * 查询所有附件条目下的文书类型信息
	 *//*
	List<CFileDictionary> getCFileDictionary(String id);
	
	*//**
	 * 新增配置信息
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 *//*
	JsonResult insertIntoCaseCheck(CFileCheck cFileCheck);
	
	
	*//**
	 * 根据主键查找该条配置信息
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 *//*
	CFileCheck getCaseCheckByid(String id);
	
	*//**
	 * 根据主键更新该条配置信息
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 *//*
	JsonResult updateFileCheckById(CFileCheck cFileCheck);
	
	*//**
	 * 根据modeleNum,dictionaryCode查找该条配置信息，判断是否重复添加
	 * @param specialTask
	 * @param sysUser 
	 * @return
	 *//*
	List<CFileCheck> getCaseCheckByCode(Integer dictionaryCode,String modeleNum);*/
}
