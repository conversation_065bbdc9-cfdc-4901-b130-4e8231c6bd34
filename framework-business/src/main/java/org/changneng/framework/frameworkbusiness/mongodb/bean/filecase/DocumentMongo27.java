package org.changneng.framework.frameworkbusiness.mongodb.bean.filecase;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文书制作-4-案件处理内部审批表（通用）
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年11月22日 下午8:13:19
 */
@Document(collection = "ExecuDocMongo")
public class DocumentMongo27 {
	@Id
	private String id;

	/**
	 * 责令限制生产决定书编号第一部分
	 */
	private String approvalNum1;
	
	/**
	 * 责令限制生产决定书编号第二部分
	 */
	private String approvalNum2;
	
	/**
	 * 当事人身份证号或营业执照号
	 */
	private String licenseNo;
	
	/**
	 * 组织机构代码
	 */
	private String orgCode;
	
	/**
	 * 社会信用代码
	 */
	private String socialCreditCode;
	
	/**
	 * 地址
	 */
	private String address;
	
	/**
	 * 负责人/法定代表
	 */
	private String legalPerson;
	
	/**
	 * 调查时间年
	 */
	private String researchYear;
	
	/**
	 * 调查时间月
	 */
	private String researchMonth;
	
	/**
	 * 调查时间日
	 */
	private String researchDate;
	
	/**
	 * 违法事实
	 */
	private String illegalFact;
	
	/**
	 * 违法依据
	 */
	private String illegalBasis;
	
	/**
	 * 改正措施
	 */
	private String correctFunc;
	
	/**
	 * 企业名称或其他经营者
	 */
	private String lawObjName;
	
	private String userDept;
	
    private String someGov;
	
	private String someDept;
	
	private String someCourt;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	
	public String getApprovalNum1() {
		return approvalNum1;
	}

	public void setApprovalNum1(String approvalNum1) {
		this.approvalNum1 = approvalNum1;
	}

	public String getApprovalNum2() {
		return approvalNum2;
	}

	public void setApprovalNum2(String approvalNum2) {
		this.approvalNum2 = approvalNum2;
	}

	public String getLawObjName() {
		return lawObjName;
	}

	public void setLawObjName(String lawObjName) {
		this.lawObjName = lawObjName;
	}
	

	public String getLicenseNo() {
		return licenseNo;
	}

	public void setLicenseNo(String licenseNo) {
		this.licenseNo = licenseNo;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
	public String getResearchYear() {
		return researchYear;
	}

	public void setResearchYear(String researchYear) {
		this.researchYear = researchYear;
	}

	public String getResearchMonth() {
		return researchMonth;
	}

	public void setResearchMonth(String researchMonth) {
		this.researchMonth = researchMonth;
	}

	public String getResearchDate() {
		return researchDate;
	}

	public void setResearchDate(String researchDate) {
		this.researchDate = researchDate;
	}

	public String getIllegalFact() {
		return illegalFact;
	}

	public void setIllegalFact(String illegalFact) {
		this.illegalFact = illegalFact;
	}

	public String getIllegalBasis() {
		return illegalBasis;
	}

	public void setIllegalBasis(String illegalBasis) {
		this.illegalBasis = illegalBasis;
	}

	public String getCorrectFunc() {
		return correctFunc;
	}

	public void setCorrectFunc(String correctFunc) {
		this.correctFunc = correctFunc;
	}

	public String getUserDept() {
		return userDept;
	}

	public void setUserDept(String userDept) {
		this.userDept = userDept;
	}

	public String getOrgCode() {
		return orgCode;
	}

	public void setOrgCode(String orgCode) {
		this.orgCode = orgCode;
	}

	public String getSocialCreditCode() {
		return socialCreditCode;
	}

	public void setSocialCreditCode(String socialCreditCode) {
		this.socialCreditCode = socialCreditCode;
	}

	public String getLegalPerson() {
		return legalPerson;
	}

	public void setLegalPerson(String legalPerson) {
		this.legalPerson = legalPerson;
	}

	public String getSomeGov() {
		return someGov;
	}

	public void setSomeGov(String someGov) {
		this.someGov = someGov;
	}

	public String getSomeDept() {
		return someDept;
	}

	public void setSomeDept(String someDept) {
		this.someDept = someDept;
	}

	public String getSomeCourt() {
		return someCourt;
	}

	public void setSomeCourt(String someCourt) {
		this.someCourt = someCourt;
	}
	
}
