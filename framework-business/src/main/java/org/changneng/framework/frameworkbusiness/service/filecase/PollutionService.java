package org.changneng.framework.frameworkbusiness.service.filecase;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.filecase.PollutionCrimeWithBLOBs;

/**
 * 环境污染犯罪业务处理接口
 * @ClassName: PollutionService 
 * @Description: 
 * <AUTHOR>
 * @date 2017年7月24日 上午9:32:24 
 *
 */
public interface PollutionService {

	/**
	 * 根据案件id获取一条数据
	 * @param caseId
	 * @return
	 */
	PollutionCrimeWithBLOBs selectByCaseId(String caseId);

	/**
	 * 上半部分保存操作
	 * @param pollution 数据实体
	 * @return 
	 */
	Integer pollutionSave(PollutionCrimeWithBLOBs pollution, String itemIds) throws Exception;

	/**
	 * 检查移送案卷编号是否重复
	 * @param transferCaseNumber 移送案卷编号
	 * @param informantBelongArea 所属区划
	 * @return
	 */
	List<PollutionCrimeWithBLOBs> checkRepeat(String transferCaseNumber, String informantBelongArea);

	/**
	 * 提交操作：type：1上半部分，2下半部分
	 * @param id
	 * @param type
	 * @throws Exception
	 */
	void submit(String id, String type) throws Exception;

	/**
	 * 删除一条数据
	 * @param id
	 * @param deleteSource 0：表示基本信息进来的删除  1：表示自己本模块的删除
	 * @throws Exception
	 */
	void delPollution(String id,Integer deleteSource) throws Exception;

	/**
	 * 根据caseId查询可用的一条数据的id
	 * @param caseId
	 * @return
	 */
	String selectIdByCaseId(String caseId);

}
