package org.changneng.framework.frameworkbusiness.service.impl;


import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.RSAUtils;
import org.changneng.framework.frameworkbusiness.dao.AppSessionRecordMapper;
import org.changneng.framework.frameworkbusiness.dao.RandomUserCountMapper;
import org.changneng.framework.frameworkbusiness.dao.RandomUserManagerMapper;
import org.changneng.framework.frameworkbusiness.dao.RandomUserTableMapper;
import org.changneng.framework.frameworkbusiness.dao.SysDepartmentMapper;
import org.changneng.framework.frameworkbusiness.dao.SysFilesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysHelpDocMapper;
import org.changneng.framework.frameworkbusiness.dao.SysRolesMapper;
import org.changneng.framework.frameworkbusiness.dao.SysUsersMapper;
import org.changneng.framework.frameworkbusiness.dao.SysUsersRolesMapper;
import org.changneng.framework.frameworkbusiness.dao.UpdateRecordMapper;
import org.changneng.framework.frameworkbusiness.dao.UserUpdateLogMapper;
import org.changneng.framework.frameworkbusiness.entity.AppSessionRecord;
import org.changneng.framework.frameworkbusiness.entity.AppSysUsers;
import org.changneng.framework.frameworkbusiness.entity.FjAvatarUploadJson;
import org.changneng.framework.frameworkbusiness.entity.RandomUserTable;
import org.changneng.framework.frameworkbusiness.entity.ResponseSyncUser;
import org.changneng.framework.frameworkbusiness.entity.SysDepartment;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysHelpDoc;
import org.changneng.framework.frameworkbusiness.entity.SysRoles;
import org.changneng.framework.frameworkbusiness.entity.SysUserSearchCondition;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.SysUsersRoles;
import org.changneng.framework.frameworkbusiness.entity.UpdateRecord;
import org.changneng.framework.frameworkbusiness.entity.UserImag;
import org.changneng.framework.frameworkbusiness.entity.UserUpdateLog;
import org.changneng.framework.frameworkbusiness.entity.WyhbJson;
import org.changneng.framework.frameworkbusiness.service.ISysUserService;
import org.changneng.framework.frameworkbusiness.service.SystemCodingService;
import org.changneng.framework.frameworkbusiness.utils.AesEncryptUtils;
import org.changneng.framework.frameworkbusiness.utils.RSAEncryptUtils;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.FileAndBytes;
import org.changneng.framework.frameworkcore.utils.FileUtil;
import org.changneng.framework.frameworkcore.utils.HttpClientUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.csource.fastdfs.StorageClient1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.session.SessionInformation;
import org.springframework.security.core.session.SessionRegistry;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

/**  
* 
* @ClassName: SysUserAndDeptServiceImpl
* @Description: TODO
* <AUTHOR>
* @date 2017年3月20日 下午3:10:03
*
*/
@Service("sysUserService")
public class SysUserServiceImpl implements ISysUserService{
    private static Logger logger = LogManager.getLogger(SysUserServiceImpl.class.getName());
	private static String publicKey = PropertiesHandlerUtil.getValue("publicKey", "jiguang");

	@Autowired
	private SysUsersMapper sysUsersMapper;
	
	@Autowired
	private PasswordEncoder passwordEncoder;
	
	@Autowired
	private SysUsersRolesMapper sysUsersRolesMapper;
	
	@Autowired
	private SysRolesMapper sysRolesMapper;
	
	@Autowired
	private SysDepartmentMapper sysDepartmentMapper;
	
	@Autowired
	private SystemCodingService  systemCodingService;
	
	@Autowired
	private SysHelpDocMapper sysHelpDocMapper;
	
	@Autowired
	private SysFilesMapper sysFilesMapper;
	
	@Autowired
	private UpdateRecordMapper updateRecordMapper;
	
 
	@Autowired
	private UserUpdateLogMapper userUpdateLogMapper;
	
 
	@Autowired
	private RandomUserCountMapper randomUserCountMapper;
	@Autowired
	private RandomUserTableMapper randomUserTableMapper;

	@Autowired
	private RandomUserManagerMapper randomUserManagerMapper;
	
	@Autowired
	private AppSessionRecordMapper appSessionRecordMapper;
	
	@Autowired
	private RedisTemplate<String,SysUsers> redisTemplate;
	
	@Autowired
	private  SessionRegistry sessionRegistry;
	
	private static String  snsServer ="";
	
	static{
		snsServer=PropertiesHandlerUtil.getValue("snsServer","sns_server");
	}
	
	@Override
	public PageInfo<SysUsers> querySystemUsers(SysUserSearchCondition search) {

		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();

		if(sysUsers.getIsSysadmin()==1){
			search.setIsAdmin("1");
		}else {
			search.setIsAdmin("0");
		}

		if(search.getPageNum() == null || search.getPageNum() == 0 ){
			search.setPageNum(1);
		}
		if(search.getPageSize() == null || search.getPageSize() == 0){
			search.setPageSize(10);
		}

		PageHelper.startPage(search.getPageNum(),search.getPageSize());
		List<SysUsers> list = sysUsersMapper.querySystemUsers(search);
		for (SysUsers su : list) {
		    SysDepartment sd = sysDepartmentMapper.selectByPrimaryKey(su.getOrgBelongDepartId());
		    su.setOrgBelongDepartName(sd.getDepartmentName());
			String roles = "";
			List<SysRoles>  roleList = sysRolesMapper.selectRoleNameById(su.getId());
			for (int i = 0; i < roleList.size(); i++) {
				roles += roleList.get(i).getRoleRealName();
				if((i+1) < roleList.size()){
					roles += "/";
				}
			}
			// 加密用户身份证号 / 手机号 / 用户编号 采用公钥加密 私钥解密
			if (StringUtils.isNotEmpty(su.getCardid())) {
				su.setCardid(RSAUtils.publicEncrypt(su.getCardid(), publicKey));
			}
			if (StringUtils.isNotEmpty(su.getPhone())) {
				su.setPhone(RSAUtils.publicEncrypt(su.getPhone(), publicKey));
			}
			if (StringUtils.isNotEmpty(su.getSysUserNumber())) {
				su.setSysUserNumber(RSAUtils.publicEncrypt(su.getSysUserNumber(), publicKey));
			}
			su.setRoles(roles);
		}
		return new PageInfo<SysUsers>(list);                                                                     
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void saveUser(SysUsers sysUser,String roles) throws Exception {
		
		try {
			if(sysUser.getPassword()!=null&&!"".equals(sysUser.getPassword())){
				System.out.println("加密前的密码"+sysUser.getPassword());
				sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
				System.out.println("加密后的密码"+sysUser.getPassword());
			}else{
				throw new BusinessException("密码不能为空");
			}
			if(sysUser.getBelongDepartmentId()!=null&&!"".equals(sysUser.getBelongDepartmentId())){
				//根据部门ID查询部门区划的信息
				SysDepartment sysd=sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
				sysUser.setBelongAreaId(sysd.getBelongAreacode());
				sysUser.setBelongAreaName(sysd.getBelongAreaname());
				sysUser.setDepartmentNumber(sysd.getDepartmentNumber());
				//生成用户编码
				String sysNum=systemCodingService.getSysUserCoding(sysd.getBelongAreacode());
				sysUser.setSysUserNumber(sysNum);
			}else{
				throw new BusinessException("部门信息不能为空");
			}
			//维护双随机信息
			sysUsersMapper.insertSelective(sysUser);
			synSysuerToRandomTask(null, sysUser, "2");
			
			
			if(!"".equals(roles)&&roles!=null){
				String roleArr []=roles.split(",");
				for (int i = 0; i < roleArr.length; i++) {
					SysUsersRoles sur=new SysUsersRoles();
					sur.setUserid(sysUser.getId());
					sur.setRoleid(roleArr[i]);
					sysUsersRolesMapper.insertSelective(sur);
				}
			}else{
				throw new BusinessException("角色不能为空");
			}
		} catch (Exception e) {
			throw e;
		}
		
	}

	
	@Override
	public SysUsers queryUser(String id) {
		SysUsers su = sysUsersMapper.selectByPrimaryKey(id);
		SysDepartment sd=null;
		SysDepartment sd2=null;
		if(su!=null&&!"".equals(su)){
			sd= sysDepartmentMapper.selectByPrimaryKey(su.getBelongDepartmentId());
			sd2=sysDepartmentMapper.selectByPrimaryKey(su.getOrgBelongDepartId());
		}
		if(sd!=null&&!"".equals(sd)){
			su.setBelongDepartmentName(sd.getDepartmentName());
		}

		if(sd2!=null&&!"".equals(sd2)){
			su.setOrgBelongDepartName(sd2.getDepartmentName());
		}
		return su;
	}

	
	@Override
	@Transactional(rollbackFor=Exception.class)
	public void updateUser(SysUsers sysUser,String roles) throws BusinessException {
		
		String pwd=sysUser.getPassword();
		sysUser.setLastUpdateDate(new Date());
		try {
			if(sysUser.getPassword()!=null&&!"".equals(sysUser.getPassword())){
				System.out.println("加密前的密码"+sysUser.getPassword());
				sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
				System.out.println("加密后的密码"+sysUser.getPassword());
			}
			if(sysUser.getBelongDepartmentId()!=null&&!"".equals(sysUser.getBelongDepartmentId())){
				//根据部门ID查询部门区划的信息
				SysDepartment sysd=sysDepartmentMapper.selectByPrimaryKey(sysUser.getBelongDepartmentId());
				sysUser.setBelongAreaId(sysd.getBelongAreacode());
				sysUser.setBelongAreaName(sysd.getBelongAreaname());
				sysUser.setDepartmentNumber(sysd.getDepartmentNumber());
				if(ChangnengUtil.isNull(sysUser.getSysUserNumber())){
					//生成用户编码
					String sysNum=systemCodingService.getSysUserCoding(sysd.getBelongAreacode());
					sysUser.setSysUserNumber(sysNum);
				}
				
			}else{
				throw new BusinessException("部门信息不能为空");
			}
			
			//清除app端session
			List<AppSessionRecord> userList=appSessionRecordMapper.queryUsernameList(sysUser.getId());
			if(userList!=null&&userList.size()>0){
				//删除redis中的记录
				for (int i = 0; i <userList.size(); i++) {
					redisTemplate.delete(userList.get(i).getLoginToken());
				}
				//删除数据库中的记录
				appSessionRecordMapper.deleteByUserid(sysUser.getId());
			}
			//清除web端session
		  	List<Object> webUser=sessionRegistry.getAllPrincipals();  
		  	if(webUser!=null&&webUser.size()>0){
		  		 for(int i=0; i<webUser.size(); i++){  
			    	SysUsers userTemp=(SysUsers) webUser.get(i);      
			        if(userTemp.getUsername().equals(sysUser.getUsername())){          
			            List<SessionInformation> sessionInformationList = sessionRegistry.getAllSessions(userTemp, false);  
			            if (sessionInformationList!=null) {   
			                for (int j=0; j<sessionInformationList.size(); j++) {  
			                   sessionInformationList.get(j).expireNow();  
			                }  
			            }  
			        }  
				 }  
		  	}
			
		  	
			SysUsers oldSysUsers = sysUsersMapper.selectByPrimaryKey(sysUser.getId());
			// 修改增加日志 - 李宏利 2016-06-16
			SysUsers sysUserLogin = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			updateSysUserLog(oldSysUsers, sysUser, sysUserLogin);
			//维护双随机人员
			synSysuerToRandomTask(oldSysUsers, sysUser, "1");
			
			sysUsersMapper.updateByPrimaryKeySelective(sysUser);
			if(!"".equals(roles)&&roles!=null){
				sysUsersRolesMapper.deleteByUserid(sysUser.getId());
				String roleArr []=roles.split(",");
				for (int i = 0; i < roleArr.length; i++) {
					SysUsersRoles sur=new SysUsersRoles();
					sur.setUserid(sysUser.getId());
					sur.setRoleid(roleArr[i]);
					sysUsersRolesMapper.insertSelective(sur);
				}
			}else{
				throw new BusinessException("角色不能为空");
			}
			
			/*//如果已在我要环保社区注册，修改社区用户信息
			if(oldSysUsers.getIsSns()==1){
				//修改我要环保用户信息接口
				Map<String,String> httpMap=new HashedMap<String,String>();
				httpMap.put("uid",sysUser.getId());
				String temp = sysUser.getUsername();
				httpMap.put("user_name",temp);
				httpMap.put("real_name",sysUser.getLoginname());
				httpMap.put("email", "");
				httpMap.put("mobile",sysUser.getPhone());
				httpMap.put("sex", null);
				httpMap.put("password",pwd);
				httpMap.put("province", "");
				httpMap.put("city", "");
				httpMap.put("avatar_file", "");
				String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fjxm_modify_process/","utf-8",httpMap);
				WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
				if(wj.getErrno()!=1){
					//throw new BusinessException("同步我要环保用户信息失败");
				}
			}*/
			
		} catch (Exception e) {
			e.printStackTrace();
			throw new BusinessException();
		}
		
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void deleteUser(String id) throws BusinessException {
		try {
			
			String idArr []=id.split(",");
			for (int i = 0; i < idArr.length; i++) {
				SysUsers oldSysUsers = sysUsersMapper.selectByPrimaryKey(idArr[i]);
				synSysuerToRandomTask(oldSysUsers, null, "3");
				sysUsersMapper.updateEnableById(idArr[i]);
				
				//清除app端session
				List<AppSessionRecord> userList=appSessionRecordMapper.queryUsernameList(idArr[i]);
				if(userList!=null&&userList.size()>0){
					//删除redis中的记录
					for (int j = 0; j <userList.size(); j++) {
						redisTemplate.delete(userList.get(j).getLoginToken());
					}
					
					//删除数据库中的记录
					appSessionRecordMapper.deleteByUserid(idArr[i]);
				}
				//清除web端session
			  	List<Object> webUser=sessionRegistry.getAllPrincipals();  
			  	if(webUser!=null&&webUser.size()>0){
			  		 for(int j=0; j<webUser.size(); j++){  
				    	SysUsers userTemp=(SysUsers) webUser.get(j);      
				        if(userTemp.getUsername().equals(oldSysUsers.getUsername())){          
				            List<SessionInformation> sessionInformationList = sessionRegistry.getAllSessions(userTemp, false);  
				            if (sessionInformationList!=null) {   
				                for (int k=0; k<sessionInformationList.size(); k++) {  
				                   sessionInformationList.get(k).expireNow();  
				                }  
				            }  
				        }  
					 }  
			  	}
				
			  	if(oldSysUsers.getIsSns()==1){
			  		//调用我要环保删除用户接口
				  	Map<String,String> httpMap=new HashedMap<String,String>();
					httpMap.put("uid",oldSysUsers.getId());
					httpMap.put("remove_user","1");
					httpMap.put("_post_type", "ajax");
					String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fj_remove_users/","utf-8",httpMap);
					WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
					if(wj.getErrno()!=1){
						throw new BusinessException("同步我要环保删除用户信息失败");
					}
			  	}
			}
		} catch (Exception e) {
			throw new BusinessException(e);
		}
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void enabledAndDisabledUser(String id, Integer type) throws Exception {
		try {
			SysUsers sysUsers = sysUsersMapper.selectByPrimaryKey(id);
			
			Map<String,String> httpMap=new HashedMap<String,String>();
			httpMap.put("uid",id);
			//httpMap.put("_post_type", "ajax");
			if(type ==1){
				//禁用
				synSysuerToRandomTask(sysUsers, null, "4");
				httpMap.put("status","1");
			}else{
				//启用
				synSysuerToRandomTask(sysUsers, null, "5");	
				httpMap.put("status","0");
			}
			sysUsersMapper.enabledAndDisabledUser(id, type);
			
//			if(sysUsers.getIsSns()==1){
//				String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fj_forbidden_user/","utf-8",httpMap);
//				WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
//				if(wj.getErrno()!=1){
//					throw new BusinessException("同步我要环保启动禁用用户信息失败");
//				}
//			}
			
			//清除app端session
//			List<AppSessionRecord> userList=appSessionRecordMapper.queryUsernameList(id);
//			if(userList!=null&&userList.size()>0){
//				//删除redis中的记录
//				for (int j = 0; j <userList.size(); j++) {
//					redisTemplate.delete(userList.get(j).getLoginToken());
//				}
//
//				//删除数据库中的记录
//				appSessionRecordMapper.deleteByUserid(id);
//			}
			
			//清除web端session
		  	List<Object> webUser=sessionRegistry.getAllPrincipals();  
		  	if(webUser!=null&&webUser.size()>0){
		  		 for(int i=0; i<webUser.size(); i++){  
			    	SysUsers userTemp=(SysUsers) webUser.get(i);      
			        if(userTemp.getUsername().equals(sysUsers.getUsername())){          
			            List<SessionInformation> sessionInformationList = sessionRegistry.getAllSessions(userTemp, false);  
			            if (sessionInformationList!=null) {   
			                for (int j=0; j<sessionInformationList.size(); j++) {  
			                   sessionInformationList.get(j).expireNow();  
			                }  
			            }  
			        }  
				 }  
		  	}
			
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}
	
	@Override
	public void updateSnsStateById(String id) throws BusinessException {
		try {
			sysUsersMapper.updateSnsStateById(id);
			//同步用户头像
			SysUsers sus=sysUsersMapper.selectByPrimaryKey(id);
			if(sus.getAvatarUrl()!=null&&!"".equals(sus.getAvatarUrl())){
			  SysFiles	sf=sysFilesMapper.selectByPrimaryKey(sus.getFileId());
			  if(sf.getState().equals("2")){
					sysFilesMapper.updateSysFile(sus.getFileId());
			  }
			  byte[] bye=FileUtil.getFileBytesByFDFSUtils(sf.getFileUrl(),sf.getFileName());
				Map<String, String> map = new HashMap<String, String>();  
				map.put("uid",sus.getId());  
				String res=HttpClientUtil.getInstance().httpPostUpload(snsServer+"/?/account/ajax/fj_avatar_upload/","aws_upload_file",sf.getFileName(),bye, map);
				FjAvatarUploadJson fau=JacksonUtils.toObject(res,FjAvatarUploadJson.class);
				if(!fau.getSuccess()){
					throw new BusinessException("同步我要环保头像失败");
				}
			}
		} catch (Exception e) {
			throw new BusinessException("修改状态失败！");
		}
	}

	
	@Override
	@Transactional(rollbackFor=Exception.class)
	public UserImag updatePersonalInfo(SysUsers user) throws Exception {
		try {
			SysFiles sf=null;
			if(user.getFileId()!=null&&!"".equals(user.getFileId())){
				sf=sysFilesMapper.selectByPrimaryKey(user.getFileId());
				if(sf.getState().equals("2")){
					sysFilesMapper.updateSysFile(user.getFileId());
				}
			}else{
				SysUsers suf=sysUsersMapper.selectByPrimaryKey(user.getId());
				if(suf.getFileId()!=null&&!"".equals(suf.getFileId())){
					sysFilesMapper.updateSysFileDidable(suf.getFileId());
				}
			}
			if(ChangnengUtil.isNull(user.getDefaultAvatarUrl())){
			//后续要改	
			user.setIsDefaultAvatar(0);
			user.setDefaultAvatarUrl("");
			}else{
				user.setIsDefaultAvatar(1);
			}
			SysUsers oldUser=sysUsersMapper.selectByPrimaryKey(user.getId());
			sysUsersMapper.updatePersonalInfo(user);
			
			//维护执法证件状态
			if(user.getLawEnforceCredentialId()!=null && !"".equals(user.getLawEnforceCredentialId())){
				SysFiles sysFile = sysFilesMapper.selectByPrimaryKey(user.getLawEnforceCredentialId());
				if("2".equals(sysFile.getState())){
					sysFilesMapper.updateSysFile(user.getLawEnforceCredentialId());
				}
				if(oldUser.getLawEnforceCredentialId() !=null && !user.getLawEnforceCredentialId().equals(oldUser.getLawEnforceCredentialId())){
					//更新时，原来图片置成脏数据
					sysFilesMapper.updateSysFileDidable(oldUser.getLawEnforceCredentialId());
				}
				
				
			}
			if(oldUser.getIsSns()==1){
				//同步修改我要环保头像
				if(sf!=null){
					byte[] bye=FileUtil.getFileBytesByFDFSUtils(sf.getFileUrl(),sf.getFileName());
					Map<String, String> map = new HashMap<String, String>();  
					map.put("uid",user.getId());  
					String res=HttpClientUtil.getInstance().httpPostUpload(snsServer+"/?/account/ajax/fj_avatar_upload/","aws_upload_file",sf.getFileName(),bye, map);
					FjAvatarUploadJson fau=JacksonUtils.toObject(res,FjAvatarUploadJson.class);
					if(!fau.getSuccess()){
						throw new BusinessException("同步我要环保头像失败");
					}
				}
				//同步修改我要环保手机号码
				if(user.getPhone()!=null&&!"".equals(user.getPhone())){
					Map<String, String> map = new HashMap<String, String>();  
					map.put("uid",user.getId());  
					map.put("mobile",user.getPhone());
					String res=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fjxm_modify_process/","utf-8",map);
					WyhbJson wj=JacksonUtils.toObject(res,WyhbJson.class);
					if(wj.getErrno()!=1){
						throw new BusinessException("同步我要环保手机号失败");
					}
				}
			}
			if(user.getDefaultAvatarUrl()!=null && !"".equals(user.getDefaultAvatarUrl())){
				//return  user.getDefaultAvatarUrl();
				return new UserImag(1, null, user.getDefaultAvatarUrl());
			}else{
				//return sf.getFileUrl();
				return new UserImag(0,sf.getFileUrl() , null);
			}
		} catch (Exception e) {
			throw e;
		}
	}

	@Override
	public boolean checkOldPass(SysUsers sysUser,String password) {
		try {
			SysUsers sys=sysUsersMapper.selectByPrimaryKey(sysUser.getId());
			boolean bool=passwordEncoder.matches(password,sys.getPassword());
			return bool;
		} catch (Exception e) {
			return false;
		}
	}

	@Override
	public boolean checkLoginnameIsExist(String loginname) {
		boolean bool=false;
		try {
			SysUsers user=sysUsersMapper.getByUsername(loginname);
			
			ResponseSyncUser responseSyncUser = new ResponseSyncUser();
    		String url =  PropertiesHandlerUtil.getValue("sync_sysuser_url", "resources");
    		Map map = new HashMap();
	    	map.put("userName", loginname);
			String result = HttpClientUtil.getInstance().httpPost(url, "utf-8", map);
			responseSyncUser = JSON.parseObject(result, ResponseSyncUser.class);
			
			if(responseSyncUser.getData().isSuccess()&&responseSyncUser.getData().isUsed()){//网格系统查询成功，用户名已经被使用
				return bool;
			}else if(responseSyncUser.getData().isSuccess()&&!(responseSyncUser.getData().isUsed())){//网格系统查询成功，用户名未被使用
				if(user==null||"".equals(user.getId())){
					bool=true;
					return bool;
				}else{
					return bool;
				}
			}else{
				return bool;
			}
			/*SysUsers user=sysUsersMapper.getByUsername(loginname);
			if(user==null||"".equals(user)){
				bool=true;
				return bool;
			}else{
				return bool;
			}*/
			
		} catch (Exception e) {
			return bool;
		}
	}

	@Override
	public void resetPassword(SysUsers sysUser) throws BusinessException {
		try {
			String oldPass=sysUser.getPassword();
			
			if(!"".equals(sysUser.getPassword())&&sysUser.getPassword()!=null){
				sysUser.setPassword(passwordEncoder.encode(sysUser.getPassword()));
			}else{
				throw new BusinessException();
			}
			sysUsersMapper.resetPassword(sysUser);
			
		    SysUsers oldUser=sysUsersMapper.selectByPrimaryKey(sysUser.getId());
			
//		    if(oldUser.getIsSns()==1){
//		    	Map<String,String> httpMap=new HashedMap<String,String>();
//				httpMap.put("uid",sysUser.getId());
//				httpMap.put("password",oldPass);
//				String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fjxm_modify_process/","utf-8",httpMap);
//				WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
//				if(wj.getErrno()!=1){
//					throw new BusinessException("同步我要环保密码失败");
//				}
//		    }
			
			//清除app端session
			List<AppSessionRecord> userList=appSessionRecordMapper.queryUsernameList(sysUser.getId());
			if(userList!=null&&userList.size()>0){
				//删除redis中的记录
				for (int i = 0; i <userList.size(); i++) {
					redisTemplate.delete(userList.get(i).getLoginToken());
				}
				//删除数据库中的记录
				appSessionRecordMapper.deleteByUserid(sysUser.getId());
			}
			
			//清除web端session
		  	List<Object> webUser=sessionRegistry.getAllPrincipals();  
		  	if(webUser!=null&&webUser.size()>0){
		  		 for(int i=0; i<webUser.size(); i++){  
			    	SysUsers userTemp=(SysUsers) webUser.get(i);      
			        if(userTemp.getUsername().equals(sysUser.getUsername())){          
			            List<SessionInformation> sessionInformationList = sessionRegistry.getAllSessions(userTemp, false);  
			            if (sessionInformationList!=null) {   
			                for (int j=0; j<sessionInformationList.size(); j++) {  
			                   sessionInformationList.get(j).expireNow();  
			                }  
			            }  
			        }  
				 }  
		  	}
			
		} catch (Exception e) {
			throw new BusinessException();
		}
		
	}

	@Override
	public int LoginResetPassword(String username,String oldPassword,String password) throws BusinessException {

		SysUsers sysUser = sysUsersMapper.getByUsername(username);
		boolean bool=passwordEncoder.matches(oldPassword,sysUser.getPassword());
		if(bool){
			try {
				if(!"".equals(password)&&password!=null){
					sysUser.setPassword(passwordEncoder.encode(password));
				}else{
					throw new BusinessException();
				}
				sysUsersMapper.resetPassword(sysUser);

				SysUsers oldUser=sysUsersMapper.selectByPrimaryKey(sysUser.getId());

				if(oldUser.getIsSns()==1){
					Map<String,String> httpMap=new HashedMap<String,String>();
					httpMap.put("uid",sysUser.getId());
					httpMap.put("password",password);
					String result=HttpClientUtil.getInstance().httpPost(snsServer+"/?/account/ajax/fjxm_modify_process/","utf-8",httpMap);
					WyhbJson wj=JacksonUtils.toObject(result,WyhbJson.class);
					if(wj.getErrno()!=1){
						throw new BusinessException("同步我要环保密码失败");
					}
				}


				//清除app端session
				List<AppSessionRecord> userList=appSessionRecordMapper.queryUsernameList(sysUser.getId());
				if(userList!=null&&userList.size()>0){
					//删除redis中的记录
					for (int i = 0; i <userList.size(); i++) {
						redisTemplate.delete(userList.get(i).getLoginToken());
					}
					//删除数据库中的记录
					appSessionRecordMapper.deleteByUserid(sysUser.getId());
				}

				//清除web端session
				List<Object> webUser=sessionRegistry.getAllPrincipals();
				if(webUser!=null&&webUser.size()>0){
					for(int i=0; i<webUser.size(); i++){
						SysUsers userTemp=(SysUsers) webUser.get(i);
						if(userTemp.getUsername().equals(sysUser.getUsername())){
							List<SessionInformation> sessionInformationList = sessionRegistry.getAllSessions(userTemp, false);
							if (sessionInformationList!=null) {
								for (int j=0; j<sessionInformationList.size(); j++) {
									sessionInformationList.get(j).expireNow();
								}
							}
						}
					}
				}

			} catch (Exception e) {
				throw new BusinessException();
			}
		}else {
			return 100;
		}

		return 200;
	}

	@Override
	public boolean checkLoginnameIsExistForUpdate(String loginname,String updateid) {
		boolean bool=false;
		try {
			SysUsers user=sysUsersMapper.checkUsernameForUpdate(loginname,updateid);
			if(user==null||"".equals(user)){
				bool=true;
				return bool;
			}else{
				return bool;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return bool;
		}
	}

	@Override
	public List<SysRoles> queryUserSysroles(String depid) {
		SysDepartment sd=sysDepartmentMapper.selectByPrimaryKey(depid);
		SysRoles sr=new SysRoles();
		sr.setRoleAreacode(sd.getBelongAreacode());
		List<SysRoles> sysList=sysRolesMapper.selectRolesList(sr);
		return sysList;
	}

	@Override
	public List<SysUsers> querySystemUsersForExcel(SysUserSearchCondition search) {
		//1.先查出所有用户权限 并放入map中<String,String>
		List<SysRoles>  roleList=sysRolesMapper.selectRoleNameAll();
		//2.组装成map  key 是 用户id
		Map<String,String> roleMap = new HashMap();//key 用户id  value 该用户所有权限名称
		Iterator<SysRoles> iterator = roleList.iterator();
		while(iterator.hasNext()) {
			SysRoles next = iterator.next();
			if(roleMap. containsKey(next.getSuId())) {//如果这个key在map中存在则在原有的基础上追加
				String roleRealName = roleMap.get(next.getSuId());
				roleRealName+=next.getRoleRealName()+"/";
				//因为使用HashMap所以采用的直接覆盖
				roleMap.put(next.getSuId(), roleRealName);
				
			}else {//不存在则直接增加
				String roleRealName = next.getRoleRealName()+"/";
				roleMap.put(next.getSuId(), roleRealName);				
			}
		}
		//3.获取要导出用户信息，之后进行转换
		List<SysUsers> list=sysUsersMapper.querySystemUsers(search);
		for (SysUsers su:list) {
		    /*SysDepartment sd=sysDepartmentMapper.selectByPrimaryKey(su.getOrgBelongDepartId());
		    su.setOrgBelongDepartName(sd.getDepartmentName());*/
			/**
			 * V2.0.4修改 （在SysUser Bean中新增了一个departmentName属性，所以删除了上面的查询）
			 */
			su.setOrgBelongDepartName(su.getDepartmentName());
		    
		    /**
		     * 一下为转换 - 李宏利
		     */
		    if(su.getAccountNonTeam()!=null){su.setAccountNonTeamStr(su.getAccountNonTeam()==1?"是":"否");}
		    if(su.getAccountNonLawofficer()!=null){su.setAccountNonLawofficerStr(su.getAccountNonLawofficer()==1?"是":"否");}
		    if(su.getAccountNonStation()!=null){su.setAccountNonStationStr(su.getAccountNonStation()==1?"是":"否");}
		    /**
		     * 2017-07-10新增查询字段(在岗人员)开始
		     */
		    if(su.getAccountNonPost()!=null){su.setAccountNonPostStr(su.getAccountNonPost()==1?"是":"否");}
		    /**
		     * 2017-07-10新增查询字段(在岗人员)结束
		     */
		    if(su.getLawCertificateEnabled()!=null){su.setLawCertificateEnabledStr(su.getLawCertificateEnabled()==1?"是":"否");}
		    if(su.getSupervisioCertificateEnabled()!=null){su.setSupervisioCertificateEnabledStr(su.getSupervisioCertificateEnabled()==1?"是":"否");}
		    
		    try {
				if(su.getSupervisionEffectiveDate()!=null){su.setSupervisionEffectiveDateStr(DateUtil.getdefaulSimpleFormate(su.getSupervisionEffectiveDate()));}
				if(su.getSupervisionInvalidDate()!=null){su.setSupervisionInvalidDateStr(DateUtil.getdefaulSimpleFormate(su.getSupervisionInvalidDate()));}
				if(su.getLawEffectiveDate()!=null){su.setLawEffectiveDateStr(DateUtil.getdefaulSimpleFormate(su.getLawEffectiveDate()));}
				if(su.getLawInvalidDate()!=null){su.setLawInvalidDateStr(DateUtil.getdefaulSimpleFormate(su.getLawInvalidDate()));}
			} catch (ParseException e) {
				System.out.println(e.getMessage());
			}
		    //这部分的代码特别慢 所以进行了优化
			/*String roles="";
			List<SysRoles>  roleList=sysRolesMapper.selectRoleNameById(su.getId());
			for (int i = 0; i < roleList.size(); i++) {
				roles+=roleList.get(i).getRoleRealName();
				if((i+1)<roleList.size()){
					roles+="/";
				}
			}
			su.setRoles(roles);*/
		    if(roleMap.containsKey(su.getId())) {
		    	String roleRealName = roleMap.get(su.getId());
		    	roleRealName = roleRealName.substring(0, roleRealName.length()-1);
		    	su.setRoles(roleRealName);
		    }
		}
		return list;           
	}
	
	@Override
	public PageInfo<SysHelpDoc> querySysHelpDoc(Integer pageSize, Integer pageNum) {
		if(pageSize==null || pageSize==0 ){
			pageSize=1;
		}
		if(pageNum==null || pageNum==0){
			pageNum=10;
		}
		PageHelper.startPage(pageNum,pageSize);
		List<SysHelpDoc> list=sysHelpDocMapper.querySysHelpDocList();
		
		return new PageInfo<SysHelpDoc>(list);                     
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void saveSysHelpDoc(List<SysFiles> sf,SysHelpDoc shd) throws BusinessException {
		try {
			shd.setFileId(sf.get(0).getId());
			shd.setDownloadTimes(0);
			shd.setFileSize(sf.get(0).getFileSize());
			shd.setFileType(sf.get(0).getFileType());
			shd.setFileUrl(sf.get(0).getFileUrl());
			sysHelpDocMapper.insertSelective(shd);
			sysFilesMapper.updateSysFile(sf.get(0).getId());
		} catch (Exception e) {
			logger.error("使用必备保存失败："+e.getMessage());
			System.out.println(e.getMessage());
			e.printStackTrace();
			throw new BusinessException("保存失败");
		}
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void deleteSysHelpDoc(String id) throws BusinessException {
		try {
			SysHelpDoc shd= sysHelpDocMapper.selectByPrimaryKey(id);
			sysHelpDocMapper.deleteByPrimaryKey(id);
			sysFilesMapper.updateSysFileDidable(shd.getFileId());
		} catch (Exception e) {
			throw new BusinessException("删除失败");
		}
		
	}

	@Override
	public SysHelpDoc querySysHelpDoc(String id) {
		return sysHelpDocMapper.selectByPrimaryKey(id);
	}

	@Override
	public void sysHelpDocCount(String id) {
		sysHelpDocMapper.sysHelpDocCount(id);
	}

	@Override
	public PageInfo<UpdateRecord> queryUpdateRecordList(Integer pageSize, Integer pageNum) {
		if(pageSize==null || pageSize==0 ){
			pageSize=1;
		}
		if(pageNum==null || pageNum==0){
			pageNum=10;
		}
		PageHelper.startPage(pageNum,pageSize);
		return new PageInfo<UpdateRecord>(updateRecordMapper.queryUpdateRecordList());
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void updateRecordSave(UpdateRecord ur) throws BusinessException {
		try {
			updateRecordMapper.insertSelective(ur);
		} catch (Exception e) {
			throw new BusinessException();
		}
		
	}

	@Override
	@Transactional(rollbackFor=Exception.class)
	public void deleteSysHelpRecord(String id) throws BusinessException {
		try {
			updateRecordMapper.deleteByPrimaryKey(id);
		} catch (Exception e) {
			throw new BusinessException();
		}
	}

	@Override
	public List<AppSysUsers> getUserByDepartmentNumber(String departmentNumber,SysUsers sysUsers,String searchState) {
		String userId = null;
		if("0".equals(searchState)){
			userId = sysUsers.getId();  
		}
		
		return sysUsersMapper.getUserByDepartmentNumber(departmentNumber,userId);
	}
	
	/**
	 * 用户修改日志记录，这里只对比一个字段: accountNonLawofficer
	 * @param oldSysUser 被修改的用户（老）
	 * @param newSysUser 被修改的用户（新）
	 * @param loginSysUser 当前登录操作用户
	 * @throws BusinessException
	 */
	@Transactional(rollbackFor=Exception.class)
	public void updateSysUserLog(SysUsers oldSysUser, SysUsers newSysUser, SysUsers loginSysUser)throws BusinessException{
		try {
			String oldAnl  = "" , newAnl="";
			oldAnl = oldSysUser.getAccountNonLawofficer()!=null?oldSysUser.getAccountNonLawofficer().toString():"";
			newAnl = newSysUser.getAccountNonLawofficer()!=null?newSysUser.getAccountNonLawofficer().toString():"";
			if(!oldAnl.equals(newAnl)){
				UserUpdateLog userLog = new UserUpdateLog();
				userLog.setCurrentUserId(oldSysUser.getId());;
				userLog.setCurrentUserName(oldSysUser.getLoginname());
				userLog.setCurrentAreaCode(oldSysUser.getBelongAreaId());
				userLog.setCurrentAreaName(oldSysUser.getBelongAreaName());
				userLog.setCurrentDepartmentId(oldSysUser.getBelongDepartmentId());
				userLog.setCurrentDepartmentName(sysDepartmentMapper.selectByPrimaryKey(oldSysUser.getBelongDepartmentId()).getDepartmentName());
				userLog.setUpdateColumn("ACCOUNT_NON_LAWOFFICER");
				userLog.setUpdateColumnName("是否双随机执法人员");
				userLog.setBeforValue(oldAnl);
				userLog.setAfterValue(newAnl);
				userLog.setUpdateTime(new Date());
				userLog.setUpdateUserId(loginSysUser.getId());
				userLog.setUpdateUserName(loginSysUser.getLoginname());
				userLog.setUpdateDepartmentId(loginSysUser.getBelongDepartmentId());
				userLog.setUpdateDepartmentName(sysDepartmentMapper.selectByPrimaryKey(loginSysUser.getBelongDepartmentId()).getDepartmentName());
				userLog.setUpdateAreaCode(loginSysUser.getBelongAreaId());
				userLog.setUpdateAreaName(loginSysUser.getBelongAreaName());
				userUpdateLogMapper.insertSelective(userLog);
			}
		} catch (Exception e) {
			throw new BusinessException();
		}
	}
 
	
	/**
	 * 同步用户信息，到双随机用户，部门，统计表
	 * @param sysUser
	 * @param type 类型 （1修改，2新增，3删除）
	 * @param updateRandomUserAddCount 0统计表总人数加1，1统计表总人数和执法人数加1，2只增加执法人数加1
	 * @return
	 */
	public String   synSysuerToRandomTask(SysUsers oldSysUser,SysUsers newSysUser,String type){
		//randomUserCountMapper   randomUserTableMapper  randomUserManagerMapper
		//是否为双随机执法人员
		String flag = "";
		if("1".equals(type)){
			//修改操作
			//根据用户的id和部门id查询双随机库人员信息
			if(!oldSysUser.getBelongDepartmentId().equals( newSysUser.getBelongDepartmentId())){
				
				//D00350121-02执法部门2    D00350121-02-01   D00350121-01-01非执法部门   D00350121-01执法部门 
				//1.执法父部门向所属子部门切
				String oldDepartmentNumber = oldSysUser.getDepartmentNumber();
				String[] oldSplit = oldDepartmentNumber.split("-");
				String newDepartmentNumber = newSysUser.getDepartmentNumber();
				String[] newSplit = newDepartmentNumber.split("-");
//				RandomUserTable randomUserTable1 = randomUserTableMapper.seleteRandomUserByDepAndUserId(oldSysUser.getBelongDepartmentId(), oldSysUser.getId());
//				if(randomUserTable1 != null){ 
					//双随机人员信息不为空 accountNonLawofficer account_non_locked
					if(oldSysUser.getAccountNonLawofficer()==1 && oldSysUser.getUserNonLocked() ==1 && 
							((oldSysUser.getLawCertificateEnabled() ==1 
							&& !ChangnengUtil.isNull(oldSysUser.getLawEnforcId()))
							||  
							(oldSysUser.getSupervisioCertificateEnabled()==1&& 
							!ChangnengUtil.isNull(oldSysUser.getSupervisionCertificateId()) ))){
						if(oldSplit.length==1 && newSplit.length==2){//局级非执法部门 -->执法父部门
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==1 && newSplit.length>=3){//局级非执法部门 -->非执法子部门
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==2 && newSplit.length==1){//执法父部门-->局级非执法部门
							randomUserCountMapper.updateRandomUserCount(oldSysUser.getBelongDepartmentId(),"2");	
							randomUserManagerMapper.updateRandomUserTable(oldSysUser.getBelongDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length>=3 && newSplit.length==1){//非执法子部门-->局级非执法部门
							String rootDepartmentNumber = oldSplit[0]+"-"+oldSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTable(rootDepartment.getDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),rootDepartment.getDepartmentId());
						}else if(oldSplit.length==1 && newSplit.length==1){//局级非执法部门  -->局级非执法部门 
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==2 && newSplit.length>=3 && newDepartmentNumber.contains(oldDepartmentNumber)){//执法父部门--》非执法子孙部门
							//总人数、执法人数 均不变  维护randomUserTable
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==2 && newSplit.length==2 ){//执法a-->执法b
							randomUserCountMapper.updateRandomUserCount(oldSysUser.getBelongDepartmentId(),"2");	
							randomUserManagerMapper.updateRandomUserTable(oldSysUser.getBelongDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==2 && newSplit.length>=3 && !newDepartmentNumber.contains(oldDepartmentNumber)){//执法a-->b的子部门
							randomUserCountMapper.updateRandomUserCount(oldSysUser.getBelongDepartmentId(),"2");	
							randomUserManagerMapper.updateRandomUserTable(oldSysUser.getBelongDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
						}else if(oldSplit.length>=3 && newSplit.length==2 && oldDepartmentNumber.contains(newDepartmentNumber)){//非执法子部门--》父部门
							String rootDepartmentNumber = oldSplit[0]+"-"+oldSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),rootDepartment.getDepartmentId());
						}else if(oldSplit.length>=3 && newSplit.length==2 && !oldDepartmentNumber.contains(newDepartmentNumber)){//非执法子部门--》非父执法部门
							String rootDepartmentNumber = oldSplit[0]+"-"+oldSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTable(rootDepartment.getDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),rootDepartment.getDepartmentId());
						}else{//非执法子部门--》非父执法部门的子部门
							String rootDepartmentNumber = oldSplit[0]+"-"+oldSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTable(rootDepartment.getDepartmentId());
							randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),rootDepartment.getDepartmentId());
						}
					}
//				}else{
//					randomUserCountMapper.updateRandomUserCount(oldSysUser.getBelongDepartmentId(),"0");	
//				}
				if(newSysUser.getAccountNonLawofficer()==1 
						&& ((newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawEnforcId()))||  
						(newSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId()) ))){
					 RandomUserTable randomUser = new RandomUserTable();
					    if(oldSplit.length==1 && newSplit.length==2){//局级非执法部门 -->执法父部门
						 	randomUserCountMapper.updateRandomUserAddCount(newSysUser.getBelongDepartmentId(),"2");
						    //部门表人数加1
						    randomUserManagerMapper.updateRandomUserTableAddCount(newSysUser.getBelongDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(newSysUser.getBelongDepartmentId());
						}else if(oldSplit.length==1 && newSplit.length>=3){//局级非执法部门 -->非执法子部门
						 	String rootDepartmentNumber = newSplit[0]+"-"+newSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserAddCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTableAddCount(rootDepartment.getDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(rootDepartment.getDepartmentId());
						}else if(oldSplit.length==2 && newSplit.length==1){//执法父部门-->局级非执法部门
							//do nothing
						}else if(oldSplit.length>=3 && newSplit.length==1){//非执法子部门-->局级非执法部门
							//do nothing
						}else if(oldSplit.length==1 && newSplit.length==1){//局级非执法部门  -->局级非执法部门 
							//do nothing
						}else if(oldSplit.length==2 && newSplit.length>=3 && newDepartmentNumber.contains(oldDepartmentNumber)){//执法父部门--》非执法子孙部门
						//总人数、执法人数 均不变  维护randomUserTable
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							String rootDepartmentNumber = newSplit[0]+"-"+newSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUser.setDepartmentId(rootDepartment.getDepartmentId());
							
					 }else if(oldSplit.length==2 && newSplit.length==2 ){//执法a-->执法b
						 	randomUserCountMapper.updateRandomUserAddCount(newSysUser.getBelongDepartmentId(),"2");
						    //部门表人数加1
						    randomUserManagerMapper.updateRandomUserTableAddCount(newSysUser.getBelongDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(newSysUser.getBelongDepartmentId());
							
					 }else if(oldSplit.length==2 && newSplit.length>=3 && !newDepartmentNumber.contains(oldDepartmentNumber)){//执法a-->b的子部门
						    String rootDepartmentNumber = newSplit[0]+"-"+newSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserAddCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTableAddCount(rootDepartment.getDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(rootDepartment.getDepartmentId());
					 }else if(oldSplit.length>=3 && newSplit.length==2 && oldDepartmentNumber.contains(newDepartmentNumber)){//非执法子部门--》父部门
						    randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(newSysUser.getBelongDepartmentId());
					 }else if(oldSplit.length>=3 && newSplit.length==2 && !oldDepartmentNumber.contains(newDepartmentNumber)){//非执法子部门--》非父执法部门
						 	randomUserCountMapper.updateRandomUserAddCount(newSysUser.getBelongDepartmentId(),"2");
						    //部门表人数加1
						    randomUserManagerMapper.updateRandomUserTableAddCount(newSysUser.getBelongDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(newSysUser.getBelongDepartmentId());
					 }else{//非执法子部门--》非父执法部门的子部门
						    String rootDepartmentNumber = newSplit[0]+"-"+newSplit[1];
							SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
							randomUserCountMapper.updateRandomUserAddCount(rootDepartment.getDepartmentId(),"2");
							randomUserManagerMapper.updateRandomUserTableAddCount(rootDepartment.getDepartmentId());
							randomUser.setAreaCode(newSysUser.getBelongAreaId());
							randomUser.setCardid(newSysUser.getCardid());
							randomUser.setDepartmentId(rootDepartment.getDepartmentId());
					 }
					 if(newSplit.length==1){//-->局级非执法部门 不维护人员表
						 //do nothing
					 }else{
						 //公用代码  维护人员库表
						 if(!ChangnengUtil.isNull(newSysUser.getBelongDepartmentId())){
							 SysDepartment sysdepartment = sysDepartmentMapper.selectByPrimaryKey(newSysUser.getBelongDepartmentId());
							 randomUser.setDepartmentName(sysdepartment.getDepartmentName());
						 }
						 randomUser.setLawDeviceId(newSysUser.getLawDeviceId());
						 if(newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawCertificateEnabled()) ){
							 //执法证启用
							 randomUser.setLawEnforcId(newSysUser.getLawEnforcId());
						 }else if(newSysUser.getLawCertificateEnabled() ==0 && newSysUser.getSupervisioCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId())){
							 //检查证启用
							 randomUser.setLawEnforcId(newSysUser.getSupervisionCertificateId());
						 }
						 randomUser.setSupervisionCertificateId(newSysUser.getSupervisionCertificateId());
						 randomUser.setUserId(newSysUser.getId());
						 randomUser.setUserName(newSysUser.getLoginname());
						 randomUserTableMapper.insertSelective(randomUser);
					 }
					
				}
				
			}else{
				//部门信息没有修改
				String departmentNumber = oldSysUser.getDepartmentNumber();
				if(!ChangnengUtil.isNull(departmentNumber)) {
					String[] departmentArr = departmentNumber.split("-");
					if(departmentArr.length>=3){
						String rootDepartmentNumber = departmentArr[0]+"-"+departmentArr[1];
						SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
						oldSysUser.setBelongDepartmentId(rootDepartment.getDepartmentId());
					}
				}
			RandomUserTable randomUserTable = randomUserTableMapper.seleteRandomUserByDepAndUserId(oldSysUser.getBelongDepartmentId(), oldSysUser.getId());
			if(randomUserTable != null){ 
				//双随机人员信息不为空 accountNonLawofficer account_non_locked
				if(newSysUser.getAccountNonLawofficer()==1  && 
						((newSysUser.getLawCertificateEnabled() ==1 
						&& !ChangnengUtil.isNull(newSysUser.getLawEnforcId()))
						||  
						(newSysUser.getSupervisioCertificateEnabled()==1&& 
						!ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId()) ))){
					//统计表和 部门表的人数都减1
					//添加人员表人员
					RandomUserTable randomUserTable1 = new RandomUserTable();
					randomUserTable1.setAreaCode(newSysUser.getBelongAreaId());
					randomUserTable1.setCardid(newSysUser.getCardid());
					randomUserTable1.setDepartmentId(oldSysUser.getBelongDepartmentId());
					if(!ChangnengUtil.isNull(oldSysUser.getBelongDepartmentId())){
						SysDepartment sysdepartment = sysDepartmentMapper.selectByPrimaryKey(oldSysUser.getBelongDepartmentId());
					
						randomUserTable1.setDepartmentName(sysdepartment.getDepartmentName());
					}
					// randomUserTable1.setDepartmentName(newSysUser.getBelongDepartmentName());
					randomUserTable1.setLawDeviceId(newSysUser.getLawDeviceId());
					if(newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawCertificateEnabled()) ){
						//执法证启用
						randomUserTable1.setLawEnforcId(newSysUser.getLawEnforcId());
					}else if(newSysUser.getLawCertificateEnabled() ==0 && newSysUser.getSupervisioCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId())){
						//检查证启用
						randomUserTable1.setLawEnforcId(newSysUser.getSupervisionCertificateId());
					}
					randomUserTable1.setSupervisionCertificateId(newSysUser.getSupervisionCertificateId());
					randomUserTable1.setUserId(newSysUser.getId());
					randomUserTable1.setUserName(newSysUser.getLoginname());
					randomUserTable1.setId(randomUserTable.getId());
					randomUserTableMapper.updateByPrimaryKeySelective(randomUserTable1);
				  //	randomUserTableMapper.deleteRandomUserManager(userId,departmentId);
					//randomUserManagerMapper.deleteRandomUserManager(userId);
				}else{
					//删除双随机人员信息
					//统计表执法人数人数都减1  (1执法人数减1)
					randomUserCountMapper.updateRandomUserCount(oldSysUser.getBelongDepartmentId(),"2");
					//部门表人数减1
					randomUserManagerMapper.updateRandomUserTable(oldSysUser.getBelongDepartmentId());
					//删除人员表人员
					randomUserTableMapper.deleteRandomUserManager(oldSysUser.getId(),oldSysUser.getBelongDepartmentId());
				}
			}else{
				//双随机人员信息为空
				String departmentId = newSysUser.getBelongDepartmentId();
				String newDepartmentNumber = newSysUser.getDepartmentNumber();
				String[] newDepartmentArr = newDepartmentNumber.split("-");
				if(newDepartmentArr.length>=3){
					String newRootDepartmentNumber = newDepartmentArr[0]+"-"+newDepartmentArr[1];
					SysDepartment newRootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(newRootDepartmentNumber);
					oldSysUser.setBelongDepartmentId(newRootDepartment.getDepartmentId());
				}
				if(newSysUser.getAccountNonLawofficer()==1 
						&& ((newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawEnforcId()))||  
						(newSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId()) ))){
					//统计表和 部门表的人数都减1
					//统计表执法人数加1
					randomUserCountMapper.updateRandomUserAddCount(departmentId,"2");
					//部门表人数加1
					randomUserManagerMapper.updateRandomUserTableAddCount(departmentId);
					//添加人员表人员
					RandomUserTable randomUser = new RandomUserTable();
					randomUser.setAreaCode(newSysUser.getBelongAreaId());
					randomUser.setCardid(newSysUser.getCardid());
					randomUser.setDepartmentId(newSysUser.getBelongDepartmentId());
					if(!ChangnengUtil.isNull(newSysUser.getBelongDepartmentId())){
						SysDepartment sysdepartment = sysDepartmentMapper.selectByPrimaryKey(newSysUser.getBelongDepartmentId());
						randomUser.setDepartmentName(sysdepartment.getDepartmentName());
					}
					//randomUser.setDepartmentName(newSysUser.getBelongDepartmentName());
					randomUser.setLawDeviceId(newSysUser.getLawDeviceId());
					if(newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawCertificateEnabled()) ){
						//执法证启用
						randomUser.setLawEnforcId(newSysUser.getLawEnforcId());
					}else if(newSysUser.getLawCertificateEnabled() ==0 && newSysUser.getSupervisioCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId())){
						//检查证启用
						randomUser.setLawEnforcId(newSysUser.getSupervisionCertificateId());
					}
					randomUser.setSupervisionCertificateId(newSysUser.getSupervisionCertificateId());
					randomUser.setUserId(newSysUser.getId());
					randomUser.setUserName(newSysUser.getLoginname());
					randomUserTableMapper.insertSelective(randomUser );
				  //	randomUserTableMapper.deleteRandomUserManager(userId,departmentId);
					//randomUserManagerMapper.deleteRandomUserManager(userId);
				}
			}
			}
		}else if("2".equals(type)){
			//新增
			String departmentId = newSysUser.getBelongDepartmentId();
			String departmentNumber = newSysUser.getDepartmentNumber();
			String[] departmentArr = departmentNumber.split("-");
			if(newSysUser.getAccountNonLawofficer()==1 &&
					((newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawEnforcId()))|| 
					(newSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId()) ))){
				
				if(departmentArr.length>=3){
					String rootDepartmentNumber = departmentArr[0]+"-"+departmentArr[1];
					SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
					departmentId = rootDepartment.getDepartmentId();
				}
				//统计表和 部门表的人数都减1
				//统计表执法人数，和总人数都加1
				randomUserCountMapper.updateRandomUserAddCount(departmentId,"2");
				//部门表人数加1
				randomUserManagerMapper.updateRandomUserTableAddCount(departmentId);
				//添加人员表人员
				RandomUserTable randomUserTable = new RandomUserTable();
				randomUserTable.setAreaCode(newSysUser.getBelongAreaId());
				randomUserTable.setCardid(newSysUser.getCardid());
				randomUserTable.setDepartmentId(newSysUser.getBelongDepartmentId());
				if(!ChangnengUtil.isNull(newSysUser.getBelongDepartmentId())){
					SysDepartment sysdepartment = sysDepartmentMapper.selectByPrimaryKey(newSysUser.getBelongDepartmentId());
					
					randomUserTable.setDepartmentName(sysdepartment.getDepartmentName());
				}
				// randomUserTable.setDepartmentName(newSysUser.getBelongDepartmentName());
				randomUserTable.setLawDeviceId(newSysUser.getLawDeviceId());
				if(newSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getLawCertificateEnabled()) ){
					//执法证启用
					randomUserTable.setLawEnforcId(newSysUser.getLawEnforcId());
				}else if(newSysUser.getLawCertificateEnabled() ==0 && newSysUser.getSupervisioCertificateEnabled() ==1 && !ChangnengUtil.isNull(newSysUser.getSupervisionCertificateId())){
					//检查证启用
					randomUserTable.setLawEnforcId(newSysUser.getSupervisionCertificateId());
				}
				randomUserTable.setSupervisionCertificateId(newSysUser.getSupervisionCertificateId());
				randomUserTable.setUserId(newSysUser.getId());
				randomUserTable.setUserName(newSysUser.getLoginname());
				randomUserTableMapper.insertSelective(randomUserTable );
			}else{
				//统计表总人数加1
				randomUserCountMapper.updateRandomUserAddCount(departmentId,"0");
			}
			
		}else if("3".equals(type)){
			//删除
			String departmentId = oldSysUser.getBelongDepartmentId();
			String userId = oldSysUser.getId();
			//修改之前是否为双随机人员 law_certificate_enabled
			String departmentNumber = oldSysUser.getDepartmentNumber();
			if(!ChangnengUtil.isNull(departmentNumber)) {
				
				String[] departmentArr = departmentNumber.split("-");
				if(departmentArr.length>=3){
					String rootDepartmentNumber = departmentArr[0]+"-"+departmentArr[1];
					SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
					departmentId = rootDepartment.getDepartmentId();
					oldSysUser.setBelongDepartmentId(departmentId);
				}
			}
			RandomUserTable randomUserTable = randomUserTableMapper.seleteRandomUserByDepAndUserId(oldSysUser.getBelongDepartmentId(), oldSysUser.getId());
			if(randomUserTable != null){
				if(oldSysUser.getAccountNonLawofficer()==1  && oldSysUser.getUserNonLocked() ==1
						&& ((oldSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(oldSysUser.getLawEnforcId()))||
						(oldSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(oldSysUser.getSupervisionCertificateId()) ))){
					//统计表和 部门表的人数都减1
					//统计表执法人数，和总人数都减1
					randomUserCountMapper.updateRandomUserCount(departmentId,"2");
					//部门表人数减1
					randomUserManagerMapper.updateRandomUserTable(departmentId);
					//删除人员表人员
					randomUserTableMapper.deleteRandomUserManager(userId,departmentId);
					//randomUserManagerMapper.deleteRandomUserManager(userId);
				}
			}else{
				//统计表总人数减1
				randomUserCountMapper.updateRandomUserCount(departmentId,"0");
			}
			flag="删除成功";
		}else if("4".equals(type)){
			//禁用
			String departmentId = oldSysUser.getBelongDepartmentId();
			String userId = oldSysUser.getId();
			String departmentNumber = oldSysUser.getDepartmentNumber();
			if(!ChangnengUtil.isNull(departmentNumber)) {
				
				String[] departmentArr = departmentNumber.split("-");
				if(departmentArr.length>=3){
					String rootDepartmentNumber = departmentArr[0]+"-"+departmentArr[1];
					SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
					departmentId = rootDepartment.getDepartmentId();
					oldSysUser.setBelongDepartmentId(departmentId);
				}
			}
			RandomUserTable randomUserTable = randomUserTableMapper.seleteRandomUserByDepAndUserId(oldSysUser.getBelongDepartmentId(), oldSysUser.getId());
			if(randomUserTable != null){
			if(oldSysUser.getAccountNonLawofficer()==1 
					&& ((oldSysUser.getLawCertificateEnabled() ==1 &&!ChangnengUtil.isNull(oldSysUser.getLawEnforcId()))||
					(oldSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(oldSysUser.getSupervisionCertificateId()) ))){
				//统计表和 部门表的人数都减1
				//统计表执法人减1
				randomUserCountMapper.updateRandomUserCount(departmentId,"2");
				//部门表人数减1
				randomUserManagerMapper.updateRandomUserTable(departmentId);
				//删除人员表人员
				randomUserTableMapper.deleteRandomUserManager(userId,departmentId);
				//randomUserManagerMapper.deleteRandomUserManager(userId);
			}}
		
		}else if ("5".equals(type)){
			//启用
			String departmentId = oldSysUser.getBelongDepartmentId();
			String departmentNumber = oldSysUser.getDepartmentNumber();
			if(!ChangnengUtil.isNull(departmentNumber)) {
			String[] departmentArr = departmentNumber.split("-");
				if(departmentArr.length>=3){
					String rootDepartmentNumber = departmentArr[0]+"-"+departmentArr[1];
					SysDepartment rootDepartment = sysDepartmentMapper.selectAvailableDepartmentByDeptNumber(rootDepartmentNumber);
					departmentId = rootDepartment.getDepartmentId();
					oldSysUser.setBelongDepartmentId(departmentId);
				}
			}
			if(oldSysUser.getAccountNonLawofficer()==1  
					&& ((oldSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(oldSysUser.getLawEnforcId()))||   
					(oldSysUser.getSupervisioCertificateEnabled()==1&& !ChangnengUtil.isNull(oldSysUser.getSupervisionCertificateId()) ))){
				//统计表和 部门表的人数都减1
				//统计表执法人减1
				randomUserCountMapper.updateRandomUserAddCount(departmentId,"2");
				//部门表人数加1
				randomUserManagerMapper.updateRandomUserTableAddCount(departmentId);
				RandomUserTable randomUserTable = new RandomUserTable();
				randomUserTable.setAreaCode(oldSysUser.getBelongAreaId());
				randomUserTable.setCardid(oldSysUser.getCardid());
				randomUserTable.setDepartmentId(oldSysUser.getBelongDepartmentId());
				if(!ChangnengUtil.isNull(oldSysUser.getBelongDepartmentId())){
					SysDepartment sysdepartment = sysDepartmentMapper.selectByPrimaryKey(oldSysUser.getBelongDepartmentId());
					randomUserTable.setDepartmentName(sysdepartment.getDepartmentName());
				}
				randomUserTable.setLawDeviceId(oldSysUser.getLawDeviceId());
				if(oldSysUser.getLawCertificateEnabled() ==1 && !ChangnengUtil.isNull(oldSysUser.getLawCertificateEnabled()) ){
					//执法证启用
					randomUserTable.setLawEnforcId(oldSysUser.getLawEnforcId());
				}else if(oldSysUser.getLawCertificateEnabled() ==0 && oldSysUser.getSupervisioCertificateEnabled() ==1 && !ChangnengUtil.isNull(oldSysUser.getSupervisionCertificateId())){
					//检查证启用
					randomUserTable.setLawEnforcId(oldSysUser.getSupervisionCertificateId());
				}
				randomUserTable.setSupervisionCertificateId(oldSysUser.getSupervisionCertificateId());
				randomUserTable.setUserId(oldSysUser.getId());
				randomUserTable.setUserName(oldSysUser.getLoginname());
				randomUserTableMapper.insertSelective(randomUserTable );
			}
		}
		return flag;
	}

	/**
	 * 查出来用户所属部门（厅局级）
	 */
	@Override
	public String queryUserDeptName() {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		
		return sysUsersMapper.queryUserDeptName(sysUsers.getId());
	}

}
