package org.changneng.framework.frameworkbusiness.service.filecase.impl;

 
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.changneng.framework.frameworkbusiness.dao.filecase.CCaseFileMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CFilesInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.CaseBaseInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.PenaltyOnekeyItemMapper;
import org.changneng.framework.frameworkbusiness.entity.filecase.CCaseFile;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFilesInfo;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyOnekeyItem;
import org.changneng.framework.frameworkbusiness.entity.filecase.RecordsCaseFile;
import org.changneng.framework.frameworkbusiness.service.filecase.InitCaseFileService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

 

@Service
public class InitCaseFileServiceImpl implements InitCaseFileService {

	@Autowired
	private CaseBaseInfoMapper caseBaseInfoMapper;
	
	@Autowired
	private CCaseFileMapper cMapper;
	
	@Autowired
	private CFilesInfoMapper cFilesInfoMapper;
	
	@Autowired
	private PenaltyOnekeyItemMapper penaltyOnekeyItemMapper;
	
	public static  Map<String,String> newCodeMap  = new FileCaseInitUtil().newCodeMap;
	@Override
	public void initCaseFiles() {
		
		//FileCaseInitUtil caseInitUtil  = new FileCaseInitUtil();
		
		// 1.查出有多少案件需要初始化
	 	List<CaseBaseInfo> baseCase = caseBaseInfoMapper.getInitBaseInfoCaseId();
		for (int i = 0; i < baseCase.size(); i++) {
			FileCaseInitUtil caseInitUtil  = new FileCaseInitUtil();
			String caseId = baseCase.get(i).getId();
			// 2.所有文书默认按一般行政处罚》简易行政处罚》按日计罚》行政命令》查封扣押》限产停产》申请法院强制执行》行政拘留》环境违法犯罪》  其他移送的顺序
			// 依次尝试取各小案卷对应文书条目的内容，取到第一个不为空的即可。标注了【特殊第一优先级】的条目先取对应小案件的该条目内容，如果为空再按默认顺序尝试取其他小案件内容。
			
			Map<String,String> defaultRuleSet = caseInitUtil.defaultRuleSet;
			// 一般 类型 2（默认优先级） （简易程序：1，一般行政处罚：2） 模块号 2
			List<RecordsCaseFile> priority1  =  caseBaseInfoMapper.getAtvRecordsCaseFile(caseId,2+"");
			Map<String,List<RecordsCaseFile>> priorityMap1 = packageMap(priority1);
			System.out.println("一般 类型 2（默认优先级） （简易程序：1，一般行政处罚：2） 模块号 2");
			insertNewCodeFile(priorityMap1, defaultRuleSet, caseId,2+"");
			
			// 简易  1（默认优先级）模块号 1
			List<RecordsCaseFile> priority2  =  caseBaseInfoMapper.getAtvRecordsCaseFile(caseId,1+"");
			Map<String,List<RecordsCaseFile>> priorityMap2 = packageMap(priority2);
			System.out.println("简易  1（默认优先级）模块号 1");
			insertNewCodeFile(priorityMap2, defaultRuleSet, caseId,1+"");
			
			// 按日计罚 3（默认优先级）  按日：模块号 11
			List<RecordsCaseFile> priority3  =   caseBaseInfoMapper.getPenaltyDay(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap3 = packageMap(priority3);
			System.out.println("按日计罚 3（默认优先级）  按日：模块号 11");
			insertNewCodeFile(priorityMap3, defaultRuleSet, caseId,10+"");
			
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			List<RecordsCaseFile> priority4  =   caseBaseInfoMapper.getExecutiveOrder(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap4 = packageMap(priority4);
			System.out.println("行政命令4 （默认优先级）  行政命令：模块号 4，");
			insertNewCodeFile(priorityMap4, defaultRuleSet, caseId,3+"");
			
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			List<RecordsCaseFile> priority5  =   caseBaseInfoMapper.getSequestrationInfo(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap5 = packageMap(priority5);
			System.out.println("查封扣押 5（默认优先级）   查封扣押：模块号 5，");
			insertNewCodeFile(priorityMap5, defaultRuleSet, caseId,4+"");
			
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			List<RecordsCaseFile> priority6  =   caseBaseInfoMapper.getLimitStopProduct(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap6 = packageMap(priority6);
			System.out.println("限产停产6  （默认优先级） 限产停产：模块号 6，");
			insertNewCodeFile(priorityMap6, defaultRuleSet, caseId,5+"");
			
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			List<RecordsCaseFile> priority7  =   caseBaseInfoMapper.getApplyForce(caseId);
			if("66CFA9558F305668E055000000000001".equals(caseId)){
				System.out.println("停下检查");
			}
			Map<String,List<RecordsCaseFile>> priorityMap7 = packageMap(priority7);
			System.out.println("申请法院强制执行 7 （默认优先级）  申请：模块号 9，");
			insertNewCodeFile(priorityMap7, defaultRuleSet, caseId,8+"");
			
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			List<RecordsCaseFile> priority8  =   caseBaseInfoMapper.getAdministrativeDetention(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap8 = packageMap(priority8);
			System.out.println("行政拘留 8 （默认优先级）  刑拘：模块号  7");
			insertNewCodeFile(priorityMap8, defaultRuleSet, caseId,6+"");
			
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			List<RecordsCaseFile> priority9  =   caseBaseInfoMapper.getPollutionCrime(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap9 = packageMap(priority9);
			System.out.println("环境违法犯罪 9 （默认优先级） 犯罪：模块号 8");
			insertNewCodeFile(priorityMap9, defaultRuleSet, caseId,7+"");
			
			// 其他移送 10  其他：模块号 10
			List<RecordsCaseFile> priority10  =   caseBaseInfoMapper.getOtherTransfer(caseId);
			Map<String,List<RecordsCaseFile>> priorityMap10 = packageMap(priority10);
			System.out.println("其他移送 10  其他：模块号 10");
			insertNewCodeFile(priorityMap10, defaultRuleSet, caseId,9+"");
			
			//****************别的优先级初始化******* 查封扣押*************//*
			//  查封扣押
			Map<String,String> detainRuleMap = caseInitUtil.detainRuleMap;
			// 这里不会在处理其他 ，并且这些有可以已经插入过了
			lastFilter(priorityMap5, detainRuleMap, caseId, 4+"");
			// 一般 
			lastFilter(priorityMap1, detainRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, detainRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, detainRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, detainRuleMap, caseId,3+"");
 
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, detainRuleMap, caseId,5+"");
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, detainRuleMap, caseId,8+"");
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, detainRuleMap, caseId,6+"");
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, detainRuleMap, caseId,7+"");
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, detainRuleMap, caseId,9+"");
		
			//****************别的优先级初始化******* 行政拘留*************//*
			Map<String,String> detentionRuleMap = caseInitUtil.detentionRuleMap;
			
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, detentionRuleMap, caseId,6+"");
			// 一般 
			lastFilter(priorityMap1, detentionRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, detentionRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, detentionRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, detentionRuleMap, caseId,3+"");
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			lastFilter(priorityMap5, detentionRuleMap, caseId, 4+"");
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, detentionRuleMap, caseId,5+"");
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, detentionRuleMap, caseId,8+"");
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, detentionRuleMap, caseId,7+"");
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, detentionRuleMap, caseId,9+"");
			
			//****************别的优先级初始化******* 环境污染犯罪*************//*
			Map<String,String> pollutionRuleMap = caseInitUtil.pollutionRuleMap;
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, pollutionRuleMap, caseId,7+"");
			// 一般 
			lastFilter(priorityMap1, pollutionRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, pollutionRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, pollutionRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, pollutionRuleMap, caseId,3+"");
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			lastFilter(priorityMap5, pollutionRuleMap, caseId, 4+"");
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, pollutionRuleMap, caseId,5+"");
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, pollutionRuleMap, caseId,8+"");
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, pollutionRuleMap, caseId,6+"");
		
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, pollutionRuleMap, caseId,9+"");
			
			//****************别的优先级初始化******* 限产停产*************//*
			Map<String,String> limtStopRuleMap = caseInitUtil.limtStopRuleMap;
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, limtStopRuleMap, caseId,5+"");
			// 一般 
			lastFilter(priorityMap1, limtStopRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, limtStopRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, limtStopRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, limtStopRuleMap, caseId,3+"");
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			lastFilter(priorityMap5, limtStopRuleMap, caseId, 4+"");
			
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, limtStopRuleMap, caseId,8+"");
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, limtStopRuleMap, caseId,6+"");
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, limtStopRuleMap, caseId,7+"");
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, limtStopRuleMap, caseId,9+"");
			
			//****************别的优先级初始化******* 其他移送*************//*
			Map<String,String> otherRuleMap = caseInitUtil.otherRuleMap;
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, otherRuleMap, caseId,9+"");
			// 一般 
			lastFilter(priorityMap1, otherRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, otherRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, otherRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, otherRuleMap, caseId,3+"");
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			lastFilter(priorityMap5, otherRuleMap, caseId, 4+"");
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, otherRuleMap, caseId,5+"");
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, otherRuleMap, caseId,8+"");
			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, otherRuleMap, caseId,6+"");
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, otherRuleMap, caseId,7+"");
			
			//****************别的优先级初始化******* 申请法院强制执行*************//*
			Map<String,String> applyRuleMap = caseInitUtil.applyRuleMap;
			// 申请法院强制执行 7 （默认优先级）  申请：模块号 9，
			lastFilter(priorityMap7, applyRuleMap, caseId,8+"");
			// 一般 
			lastFilter(priorityMap1, applyRuleMap, caseId,2+"");
			// 简易  1（默认优先级）模块号 1
			lastFilter(priorityMap2, applyRuleMap, caseId,1+"");
			// 按日计罚 3（默认优先级）  按日：模块号 11
			lastFilter(priorityMap3, applyRuleMap, caseId,10+"");
			// 行政命令4 （默认优先级）  行政命令：模块号 4，
			lastFilter(priorityMap4, applyRuleMap, caseId,3+"");
			// 查封扣押 5（默认优先级）   查封扣押：模块号 5，
			lastFilter(priorityMap5, applyRuleMap, caseId, 4+"");
			// 限产停产6  （默认优先级） 限产停产：模块号 6，
			lastFilter(priorityMap6, applyRuleMap, caseId,5+"");

			// 行政拘留 8 （默认优先级）  刑拘：模块号  7
			lastFilter(priorityMap8, applyRuleMap, caseId,6+"");
			// 环境违法犯罪 9 （默认优先级） 犯罪：模块号 8
			lastFilter(priorityMap9, applyRuleMap, caseId,7+"");
			// 其他移送 10  其他：模块号 10
			lastFilter(priorityMap10, applyRuleMap, caseId,9+"");
		}
		
		
		
		/****************别的优先级初始化******* 申请法院强制执行**********这个特别注意，这些都是私有的按日计罚***************/
		List<CaseBaseInfo> penaltyDayBaseInfo = caseBaseInfoMapper.getInitPenaltyDayCaseId();
		if(!penaltyDayBaseInfo.isEmpty() && penaltyDayBaseInfo.size() >0){
			for (int i = 0; i < penaltyDayBaseInfo.size(); i++) {
				FileCaseInitUtil caseInitUtil  = new FileCaseInitUtil();
				
				Map<String,String> penaltyRuleMap = caseInitUtil.penaltyRuleMap;
				
				String caseId = penaltyDayBaseInfo.get(i).getCaseNumber();
				
				String penaltyDayId = penaltyDayBaseInfo.get(i).getId();
				
				String penaltyCaseNumber = penaltyDayBaseInfo.get(i).getCaseName();
				 
				if(ChangnengUtil.isNull(caseId)) continue;
				
				// 按日计罚 3（默认优先级）  按日：模块号 11
				List<RecordsCaseFile> priority3  =   caseBaseInfoMapper.getPenaltyDayById(penaltyDayId);
				Map<String,List<RecordsCaseFile>> priorityMap3 = packageMap(priority3);
				
				System.out.println("按日计罚 3（默认优先级）  按日：模块号 11");
		 
				inserPenaltyDayFile(priorityMap3, penaltyRuleMap, caseId, penaltyDayId,penaltyCaseNumber);
			}
		}
	
		System.out.println("****************************************************************************************");
		System.out.println("****************************************************************************************");
		System.out.println("**********************************初始化附件完成******************************************");
		System.out.println("****************************************************************************************");
		System.out.println("****************************************************************************************");
		
	}

	/**
	 * 各个模块附件封装Map
	 * @param fileList
	 * @return
	 * <AUTHOR>
	 * @date 2018年9月13日-下午6:16:13
	 */
	private Map<String,List<RecordsCaseFile>>  packageMap(List<RecordsCaseFile> fileList){
		Map<String,List<RecordsCaseFile>> fileMap =  new HashMap<>(); 
		if(!fileList.isEmpty() && fileList.size()>0){
			for (int i = 0; i < fileList.size(); i ++) {
				RecordsCaseFile tmp = fileList.get(i);
				if(fileMap.containsKey(tmp.getTypeCode())){
					fileMap.get(tmp.getTypeCode()).add(tmp);
				}else{
					List<RecordsCaseFile> tmpList =  new ArrayList<>(); 
					tmpList.add(tmp);
					if(ChangnengUtil.isNull(tmp.getTypeCode())){
						System.out.println("不能存在为空的现象，否则就是sql写错了！--->问题找到了，现在硬是有空的，估计是程序错误，现在把空归结到56code");
						tmp.setTypeCode("56");
						if(fileMap.containsKey(tmp.getTypeCode())){
							fileMap.get(tmp.getTypeCode()).add(tmp);
						}else{
							fileMap.put(tmp.getTypeCode(),tmpList);
						}
					}else{
						fileMap.put(tmp.getTypeCode(),tmpList);
					}
				
				}
			}
		}
		return fileMap;
	} 
	
	
	/**
	 * 处理默认优先级取附件Map
	 * @param sourceMap
	 * @param defaultRuleSet
	 * @param oldTaskMap
	 * @param caseId
	 * @param modelNumber
	 * <AUTHOR>
	 * @date 2018年9月14日-上午11:12:32
	 */
	private void insertNewCodeFile(Map<String,List<RecordsCaseFile>> sourceMap, Map<String,String> defaultRuleSet,String caseId,String modelNumber){
		if (!sourceMap.isEmpty()) {
			for (String key : sourceMap.keySet()) {
				System.out.println(key+"------------------------------");
				if (key==null) {
					System.out.println("停下来监察");
				}
				if((!defaultRuleSet.isEmpty() && defaultRuleSet.containsKey(key)) || key.equals(56+"")){ // 是不是在这默认取值集合中  或者是其他
					List<RecordsCaseFile> tmpCaseFile = sourceMap.get(key); // 获取老数据当前code应该有那些附件集合
					
					if(!tmpCaseFile.isEmpty() && tmpCaseFile.size()>0){
						String itemId = "";//  具体的某一个条目ID
						// defaultRuleSet.put("12","12,384,386"); 	defaultRuleSet.put("99","99");  有的是多个code，有的是单个的
						String newCode ="";
						if(key.equals(56+"")){// 其他需要特殊处理，以后条目中没有其他，但是老数据需要维护
							newCode = "56";
						}else{
							newCode = defaultRuleSet.get(key);
						}
						String[] newCodeList = newCode.split(",");
						
						for (int q = 0; q < newCodeList.length; q++) {
							String newCodeTmp = newCodeList[q];
							CCaseFile cc  = null;
							if(key.equals("6")|| key.equals("57")|| key.equals("7")||key.equals("77")||key.equals("64")||key.equals("75")||key.equals("76")||key.equals("10") ){
								 cc = cMapper.selectByCaseIdAndCode(caseId, Integer.parseInt(newCodeTmp), null);
							}
							if(cc==null){
								cc =  new CCaseFile();
							}
							cc.setCaseId(caseId);
							// 放入新code中
							cc.setCode(Integer.parseInt(newCodeTmp));
							cc.setLastGenerateDate(tmpCaseFile.get(0).getCreateDate());
	 
							if("56".equals(newCodeTmp)){ //  如果是其他，我需要维护模块标号
								cc.setModuleNumber(Integer.parseInt(modelNumber));
								cc.setItemId(tmpCaseFile.get(0).getRecordsId());
								itemId  = tmpCaseFile.get(0).getRecordsId();
							}
							String fileName  = "";
							List<CFilesInfo> cfInfos = new ArrayList<>();
							for (int j = 0; j < tmpCaseFile.size(); j++) {
								if(!"56".equals(newCodeTmp)){
									itemId  = tmpCaseFile.get(j).getRecordsId();
								}
								CFilesInfo cFilesInfo = new CFilesInfo(); 
								cFilesInfo.setFileName(tmpCaseFile.get(j).getFileName());
								fileName = fileName + tmpCaseFile.get(j).getFileName()+",";
								if(tmpCaseFile.get(j).getFileType()!=null && tmpCaseFile.get(j).getFileType()==1){
									cFilesInfo.setFileType(1);
								}else{
									cFilesInfo.setFileType(2);
								}
								cFilesInfo.setFileUrl(tmpCaseFile.get(j).getFileUrl());
								cFilesInfo.setState(tmpCaseFile.get(j).getState());
								// 注意  这里可能是  M 或者 Kb 这里转换为字节
								if(tmpCaseFile.get(j).getFileSize()!=null){
									Integer bSize = 0;
								    if(tmpCaseFile.get(j).getFileSize().trim().endsWith("Kb")){ // 如果结尾是 Kb
								    	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("K"));
								    	bSize = Integer.parseInt(size) * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else if (tmpCaseFile.get(j).getFileSize().trim().endsWith("M")){ // 结尾是M，因为之前 算法取了整数，所以我这里直接 +1
									 	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("M"));
								    	bSize = (Integer.parseInt(size)+1) * 1024 * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else{
										// 不处理
									}
								}
								cFilesInfo.setCreateDate(tmpCaseFile.get(j).getCreateDate());
								cFilesInfo.setLocation(tmpCaseFile.get(j).getLocation());
								cfInfos.add(cFilesInfo);
							}
							if(!ChangnengUtil.isNull(fileName)){
								cc.setFileName(fileName.substring(0, fileName.length()-1));
							}
							// 写入案件公共条目表
							if(cc.getId()==null){
								cMapper.insertSelective(cc);
							}
							for (int k = 0; k < cfInfos.size(); k++) {
								cfInfos.get(k).setId(UUID.randomUUID().toString());
								cfInfos.get(k).setCaseFileId(cc.getId());
							}
							// 写入该条目下涉及到的附件
							cFilesInfoMapper.insertBatch(cfInfos);
							
							// 回写具体的条目为最新对应code
							if(!ChangnengUtil.isNull(itemId)){
								String tableName = "";
								if("1".equals(modelNumber) || "2".equals(modelNumber) ){
									tableName = " atv_records_content "; 
								}else if ("3".equals(modelNumber) ){
									tableName = " ORDER_ONEKEY_ITEM "; 
								}else if ("4".equals(modelNumber) ){
									tableName = " sequestration_records_content "; 
								}else if ("5".equals(modelNumber) ){
									tableName = " limit_stop_records_content "; 
								}else if ("6".equals(modelNumber) ){
									tableName = " detention_records_content "; 
								}else if ("7".equals(modelNumber) ){
									tableName = " POLLUTION_ONEKEY_ITEM "; 
								}else if ("8".equals(modelNumber) ){
									tableName = " APPLY_FORCE_ONEKEY_ITEM "; 
								}else if ("9".equals(modelNumber) ){
									tableName = " OTHER_TRANSFER_ONEKEY_ITEM "; 
								}else if ("10".equals(modelNumber) ){
									tableName = " PENALTY_ONEKEY_ITEM "; 
								}
								String  newName  = newCodeMap.get(newCodeTmp);
								
								String sql  =" UPDATE "+tableName+" set TYPE_CODE ="+Integer.parseInt(newCodeTmp)+" ,TYPE_NAME = '"+newName+"'   where  id ='"+itemId+"' ";
								cFilesInfoMapper.updateNewItemCode(sql);
							}
						}
					}
					// 移除
					defaultRuleSet.remove(key);
				}
			}
		}
	}
	
	/**
	 * 按照指定优先级别取附件
	 * @param sourceMap
	 * @param currentRuleSet
	 * @param caseId
	 * @param modelNumber
	 * <AUTHOR>
	 * @date 2018年9月14日-下午6:50:59
	 */
	private void lastFilter(Map<String,List<RecordsCaseFile>> sourceMap, Map<String,String> currentRuleSet,String caseId,String modelNumber){
		if (!sourceMap.isEmpty()) {
			for (String key : sourceMap.keySet()) {
				if((!currentRuleSet.isEmpty() && currentRuleSet.containsKey(key))){ // 是不是在这默认取值集合中  或者是其他
					List<RecordsCaseFile> tmpCaseFile = sourceMap.get(key); // 获取老数据当前code应该有那些附件集合
					if(!tmpCaseFile.isEmpty() && tmpCaseFile.size()>0){
						
						String itemId = "";//  具体的某一个条目ID
						// defaultRuleSet.put("12","12,384,386"); 	defaultRuleSet.put("99","99");  有的是多个code，有的是单个的
						String newCode ="";
						newCode = currentRuleSet.get(key);
						String[] newCodeList = newCode.split(",");
						for (int q = 0; q < newCodeList.length; q++) {
							String newCodeTmp = newCodeList[q];
							CCaseFile cc = cMapper.selectByCaseIdAndCode(caseId, Integer.parseInt(newCodeTmp), null);
							if(cc==null){
								cc =  new CCaseFile();
							}
							cc.setCaseId(caseId);
							// 放入新code中
							cc.setCode(Integer.parseInt(newCodeTmp));
							cc.setLastGenerateDate(tmpCaseFile.get(0).getCreateDate());
							String fileName  = "";
							List<CFilesInfo> cfInfos = new ArrayList<>();
							for (int j = 0; j < tmpCaseFile.size(); j++) {
								itemId  = tmpCaseFile.get(j).getRecordsId();
								CFilesInfo cFilesInfo = new CFilesInfo(); 
								cFilesInfo.setFileName(tmpCaseFile.get(j).getFileName());
								fileName = fileName + tmpCaseFile.get(j).getFileName()+",";
								if(tmpCaseFile.get(j).getFileType()!=null && tmpCaseFile.get(j).getFileType()==1){
									cFilesInfo.setFileType(1);
								}else{
									cFilesInfo.setFileType(2);
								}
								cFilesInfo.setFileUrl(tmpCaseFile.get(j).getFileUrl());
								cFilesInfo.setState(tmpCaseFile.get(j).getState());
								// 注意  这里可能是  M 或者 Kb 这里转换为字节
								if(tmpCaseFile.get(j).getFileSize()!=null){
									Integer bSize = 0;
								    if(tmpCaseFile.get(j).getFileSize().trim().endsWith("Kb")){ // 如果结尾是 Kb
								    	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("K"));
								    	bSize = Integer.parseInt(size) * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else if (tmpCaseFile.get(j).getFileSize().trim().endsWith("M")){ // 结尾是M，因为之前 算法取了整数，所以我这里直接 +1
									 	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("M"));
								    	bSize = (Integer.parseInt(size)+1) * 1024 * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else{
										// 不处理
									}
								}
								cFilesInfo.setCreateDate(tmpCaseFile.get(j).getCreateDate());
								cFilesInfo.setLocation(tmpCaseFile.get(j).getLocation());
								cfInfos.add(cFilesInfo);
							}
							if(!ChangnengUtil.isNull(fileName)){
								cc.setFileName(fileName.substring(0, fileName.length()-1));
							}
							// 写入案件公共条目表
							if(cc.getId()==null){
								cMapper.insertSelective(cc);
							}
							for (int k = 0; k < cfInfos.size(); k++) {
								cfInfos.get(k).setId(UUID.randomUUID().toString());
								cfInfos.get(k).setCaseFileId(cc.getId());
							}
							// 写入该条目下涉及到的附件
							cFilesInfoMapper.insertBatch(cfInfos);
							
							// 回写具体的条目为最新对应code
							if(!ChangnengUtil.isNull(itemId)){
								String tableName = "";
								if("1".equals(modelNumber) || "2".equals(modelNumber) ){
									tableName = " atv_records_content "; 
								}else if ("3".equals(modelNumber) ){
									tableName = " ORDER_ONEKEY_ITEM "; 
								}else if ("4".equals(modelNumber) ){
									tableName = " sequestration_records_content "; 
								}else if ("5".equals(modelNumber) ){
									tableName = " limit_stop_records_content "; 
								}else if ("6".equals(modelNumber) ){
									tableName = " detention_records_content "; 
								}else if ("7".equals(modelNumber) ){
									tableName = " POLLUTION_ONEKEY_ITEM "; 
								}else if ("8".equals(modelNumber) ){
									tableName = " APPLY_FORCE_ONEKEY_ITEM "; 
								}else if ("9".equals(modelNumber) ){
									tableName = " OTHER_TRANSFER_ONEKEY_ITEM "; 
								}else if ("10".equals(modelNumber) ){
									tableName = " PENALTY_ONEKEY_ITEM "; 
								}
								String  newName  = newCodeMap.get(newCodeTmp);
								
								String sql  =" UPDATE "+tableName+" set TYPE_CODE ="+Integer.parseInt(newCodeTmp)+" ,TYPE_NAME = '"+newName+"'   where  id ='"+itemId+"' ";
								cFilesInfoMapper.updateNewItemCode(sql);
							}
							
						}
					}
					// 移除
					currentRuleSet.remove(key);
				}
			}
		}
	}
	
	
	/**
	 * 初始化按日计罚私有的文书code
	 * @param sourceMap 当前按日计罚输出的集合
	 * @param defaultRuleSet
	 * @param caseId 案件ID
	 * @param penaltyDayId 按日计罚ID
	 * @param penaltyCaseNumber 按日计罚编号
	 * <AUTHOR>
	 * @date 2018年9月19日-下午5:40:21
	 */
	private void inserPenaltyDayFile(Map<String,List<RecordsCaseFile>> sourceMap, Map<String,String> defaultRuleSet,String caseId,String penaltyDayId,String penaltyCaseNumber){
		if (!sourceMap.isEmpty()) {
			for (String key : sourceMap.keySet()) {
				
				if((!defaultRuleSet.isEmpty() && defaultRuleSet.containsKey(key))){ // 是不是在这默认取值集合中  或者是其他
					List<RecordsCaseFile> tmpCaseFile = sourceMap.get(key); // 获取老数据当前code应该有那些附件集合
					if(!tmpCaseFile.isEmpty() && tmpCaseFile.size()>0){
						String itemId = "";// 按日计罚条目具体ID
						String newCode ="";
						newCode = defaultRuleSet.get(key);
						String[] newCodeList = newCode.split(",");
						for (int q = 0; q < newCodeList.length; q++) {
							String newCodeTmp = newCodeList[q];
							CCaseFile cc = cMapper.selectByCaseIdAndCode(caseId, Integer.parseInt(newCodeTmp), penaltyDayId);
							if(cc==null){
								cc =  new CCaseFile();
							}
							cc.setCaseId(caseId); // 案件ID
							cc.setEntryId(penaltyDayId);// 按日计罚ID
							// 放入新code中
							cc.setCode(Integer.parseInt(newCodeTmp));
							cc.setLastGenerateDate(tmpCaseFile.get(0).getCreateDate());
							String fileName  = "";
							List<CFilesInfo> cfInfos = new ArrayList<>();
							for (int j = 0; j < tmpCaseFile.size(); j++) {
								itemId  = tmpCaseFile.get(j).getRecordsId();
								CFilesInfo cFilesInfo = new CFilesInfo(); 
								cFilesInfo.setFileName(tmpCaseFile.get(j).getFileName());
								fileName = fileName + tmpCaseFile.get(j).getFileName()+",";
								if(tmpCaseFile.get(j).getFileType()!=null && tmpCaseFile.get(j).getFileType()==1){
									cFilesInfo.setFileType(1);
								}else{
									cFilesInfo.setFileType(2);
								}
								cFilesInfo.setFileUrl(tmpCaseFile.get(j).getFileUrl());
								cFilesInfo.setState(tmpCaseFile.get(j).getState());
								// 注意  这里可能是  M 或者 Kb 这里转换为字节
								if(tmpCaseFile.get(j).getFileSize()!=null){
									Integer bSize = 0;
								    if(tmpCaseFile.get(j).getFileSize().trim().endsWith("Kb")){ // 如果结尾是 Kb
								    	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("K"));
								    	bSize = Integer.parseInt(size) * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else if (tmpCaseFile.get(j).getFileSize().trim().endsWith("M")){ // 结尾是M，因为之前 算法取了整数，所以我这里直接 +1
									 	String fileSize = tmpCaseFile.get(j).getFileSize();
								    	String size = fileSize.substring(0, fileSize.indexOf("M"));
								    	bSize = (Integer.parseInt(size)+1) * 1024 * 1024;
								    	cFilesInfo.setFileSize(bSize+"");
									}else{
										// 不处理
									}
								}
								cFilesInfo.setCreateDate(tmpCaseFile.get(j).getCreateDate());
								cFilesInfo.setLocation(tmpCaseFile.get(j).getLocation());
								cfInfos.add(cFilesInfo);
							}
							if(!ChangnengUtil.isNull(fileName)){
								cc.setFileName(fileName.substring(0, fileName.length()-1));
							}
							// 写入案件公共条目表
							if(cc.getId()==null){
								cMapper.insertSelective(cc);
							}
							
							for (int k = 0; k < cfInfos.size(); k++) {
								cfInfos.get(k).setId(UUID.randomUUID().toString());
								cfInfos.get(k).setCaseFileId(cc.getId());
							}
							// 写入该条目下涉及到的附件
							cFilesInfoMapper.insertBatch(cfInfos);
							
							// 回写具体的按日计罚信息，或者添加条目信息
							if(!ChangnengUtil.isNull(itemId)){
								PenaltyOnekeyItem pDayItem = new PenaltyOnekeyItem();
								pDayItem.setId(itemId);
								pDayItem.setIsDayPenalty(1);
								pDayItem.setPenaltyCaseNumber(penaltyCaseNumber);
								penaltyOnekeyItemMapper.updateByPrimaryKeySelective(pDayItem);
							}
					 
						}
					}
					// 移除
					defaultRuleSet.remove(key);
				}
			}
		}
	}
	
	
	public static void main(String[] args) {
		String size = "456Kb";
		System.out.println(size.substring(0, size.indexOf("K")));
		System.out.println(228*1024*1024);
		
		Map<String,String> defaultRuleSet = new HashMap<>(); 
		defaultRuleSet.put("-3","-3");
		defaultRuleSet.put("-2","-2");
		defaultRuleSet.put("86","86");
		defaultRuleSet.remove("56");
		System.out.println("-----is ok ");
		String newCode ="";
		//if(!defaultRuleSet.get("132132"))
		newCode = defaultRuleSet.get("132132");
		String[] newCodeList = newCode.split(",");
		for (int q = 0; q < newCodeList.length; q++) {
			String  newCodeTmp= newCodeList[q];
			System.out.println(newCodeTmp+"-----------");
		}
		
	}
}
