package org.changneng.framework.frameworkbusiness.repeatCommit;

import java.lang.reflect.Method;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

public class PutSessionValueIntercor implements HandlerInterceptor {

	@Autowired
	private StringRedisTemplate StringRedisTemplate;
	
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
			throws Exception {
		try {
			if(handler instanceof HandlerMethod){
				HandlerMethod handlerMethod = (HandlerMethod)handler;
				Method method = handlerMethod.getMethod();
				PutSessionValue putSessionValue = method.getAnnotation(PutSessionValue.class);
				if(putSessionValue!=null){
					String uuId = UUID.randomUUID().toString();
					request.getSession().setAttribute("tokenReport", uuId);
					
					request.getSession().setMaxInactiveInterval(60*60*12);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		return true;
	}

	@Override
	public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
			ModelAndView modelAndView) throws Exception {
		
	}

	@Override
	public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
			throws Exception {
		
	}
	

}
