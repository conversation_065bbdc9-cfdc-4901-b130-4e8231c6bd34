package org.changneng.framework.frameworkbusiness.mongodb.dao.impl;

import org.changneng.framework.frameworkbusiness.mongodb.bean.filecase.DocumentMongo18;
import org.changneng.framework.frameworkbusiness.mongodb.dao.ExecuDocMongoDao18;
import org.changneng.framework.frameworkbusiness.mongodb.dao.MongodbBaseDao;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

@Component
public class ExecuDocMongoDao18Impl extends MongodbBaseDao<DocumentMongo18> implements ExecuDocMongoDao18 {

	/**
	 * 实现钩子方法,返回反射的类型
	 * 
	 * <AUTHOR>
	 * 
	 * @return 反射类型
	 */
	@Override
	protected Class getEntityClass() {
		return DocumentMongo18.class;
	}

	/**
	 * 初始化 mongo数据库手柄
	 */
	@Autowired
	@Qualifier("mongoTemplate")
	@Override
	protected void setMongoTemplate(MongoTemplate mongoTemplate) {
		super.mongoTemplate = mongoTemplate;
	}

	@Override
	public DocumentMongo18 findOneDocumentMongo18(String id) {
		Query query = new Query();
		return this.findOne(Query.query(Criteria.where("_id").is(id)));
	}

	@Override
	public DocumentMongo18 saveDocumentMongo18(DocumentMongo18 bean) {
		this.mongoTemplate.save(bean);
		return bean;
	}

	@Override
	public void delDocumentMongo18(DocumentMongo18 bean) {
		this.mongoTemplate.remove(bean);
	}

	@Override
	public void updateDocumentMongo18(String id) throws Exception {
		Update update = new Update();
		if (null == id || "".equals(id.trim())) {
			// 如果主键为空,则不进行修改
			throw new Exception("Update data Id is Null");
		}
		this.updateFirst(Query.query(Criteria.where("_id").is(id)), update);
	}
}
