package org.changneng.framework.frameworkbusiness.service.filecase;

import java.util.Map;

import org.changneng.framework.frameworkbusiness.entity.JsonResult;

public interface FtpService {

	/**
	 * 上传文件方法
	 * @param uploadFile
	 * @return
	 */
	public void uploadPicture(String fileUrl) throws Exception;
	
	/**
	 * 发送附件方法
	 * @param postUrl
	 * @param accessToken
	 * @param areaCode
	 * @param fileName
	 * @param caseId
	 * @param fileId
	 * @return
	 */
	public String postSendFront(String postUrl, String accessToken, String areaCode, String fileName, String caseId,String fileId) throws Exception;
	
	/**
	 * 删除附件 接口
	 * @param delUrl
	 * @param accessToken
	 * @param areaCode
	 * @param businessId
	 * @param enterpriseId
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2018年5月16日-下午3:59:41
	 */
	public JsonResult deleteFront(String delUrl, String accessToken, String areaCode, String businessId,String enterpriseId) throws Exception;
	
	
}
