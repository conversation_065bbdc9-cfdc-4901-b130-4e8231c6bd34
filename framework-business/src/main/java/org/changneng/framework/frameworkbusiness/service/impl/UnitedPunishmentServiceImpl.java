package org.changneng.framework.frameworkbusiness.service.impl;



import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;

import javax.crypto.Cipher;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.dao.LawEnforceObjectMapper;
import org.changneng.framework.frameworkbusiness.dao.PPunishmentHistoryMapper;
import org.changneng.framework.frameworkbusiness.dao.PPunishmentResultMapper;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObject;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.PPunishmentHistory;
import org.changneng.framework.frameworkbusiness.entity.PPunishmentResult;
import org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entities;
import org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity;
import org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Record;
import org.changneng.framework.frameworkbusiness.service.UnitedPunishmentService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.DateUtil;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.JaxbXmlUtil;
import org.changneng.framework.frameworkcore.utils.PageModel;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.WebserviceClientUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service(value="unitedPunishmentService")
public class UnitedPunishmentServiceImpl implements UnitedPunishmentService {
	
	@Autowired
	private LawEnforceObjectMapper lawEnforceObjectMapper;
	@Autowired
	private PPunishmentHistoryMapper pPunishmentHistoryMapper;
	@Autowired
	private PPunishmentResultMapper pPunishmentResultMapper;
	
	private static Logger logger = LogManager.getLogger(UnitedPunishmentServiceImpl.class.getName());
	private static final String enterpriseCertTypeCode ="28";//统一社会信用代码
	private static final String individalCertTypeCode ="1";//身份证
	private static final Integer enterpriseBatchLimit =100;//企业批量查询限制
	private static final Integer individalThreeBatchLimit =100;//个体、三无批量查询限制
	private static final Integer individalBatchLimit =100;//个人批量查询限制
	private static final String[] beginLoginParamName = {"userName"};
	private static final String beginLoginMethodName = "BeginLogin";
	private static final String endLoginMethodName = "EndLogin";
	private static final String ValidateOrganizationByCertMethodName = "ValidateOrganizationByCert";//单独查询机构失信情况接口名称
	private static final String ValidateOrganizationsByCertMethodName = "ValidateOrganizationsByCert";//批量查询机构失信情况接口名称
	private static final String ValidateIndividualsMethodName = "ValidateIndividuals";//批量查询自然人失信情况接口名称
	private static final String FindOrganizationByCertMethodName = "FindOrganizationByCert";//查询机构失信记录接口名称
	private static final String FindIndividualMethodName = "FindIndividual";//查询自然失信记录接口名称
	private static final String[] ValidateOrganizationByCertParamName = {"token","certNo","certTypeCode"};//单独查询机构失信情况接口参数名
	private static final String[] ValidateOrganizationsByCertParamName = {"token","certNoXml","certTypeCode"};//批量查询机构失信情况接口参数名
	private static final String[] ValidateIndividualsParamName = {"token","requestXml","certTypeCode"};//批量查询自然人失信情况接口参数名
	private static final String[] FindOrganizationByCertParamName = {"token","certNo","certTypeCode"};//查询机构失信记录接口参数名
	private static final String[] FindIndividualParamName = {"token","certNo","name","certTypeCode"};//查询自然人失信记录接口参数名
	private static final String[] endLoginParamName = {"userName","salt","pwdCipher"};
	private static final String PUNISHMENTSVC_WEBSERVICE_URL=PropertiesHandlerUtil.getValue("PUNISHMENTSVC_WEBSERVICE_URL","supervise_monitor_overproof");
	private static final String PUNISHMENTSVC_QNAME=PropertiesHandlerUtil.getValue("PUNISHMENTSVC_QNAME","supervise_monitor_overproof");
	private static final String PUNISHMENTSVC_USERNAME=PropertiesHandlerUtil.getValue("PUNISHMENTSVC_USERNAME","supervise_monitor_overproof");
	private static final String PUNISHMENTSVC_PWD=PropertiesHandlerUtil.getValue("PUNISHMENTSVC_PWD","supervise_monitor_overproof");
	private static final String PUNISHMENTSVC_PUBLIC_KEY=PropertiesHandlerUtil.getValue("PUNISHMENTSVC_PUBLIC_KEY","supervise_monitor_overproof");
	@Override
	/**
	 * 批量查询
	 */
	public void batchSearchOrganizationAndIndividualCredits() {
		try {
			//1.获取盐
			String salt = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, beginLoginMethodName, beginLoginParamName, new String[]{PUNISHMENTSVC_USERNAME});
			//2.获取token
			String pwdCipher = "";
				pwdCipher = getRSAEncryptPwdHexStr(salt,PUNISHMENTSVC_PUBLIC_KEY,PUNISHMENTSVC_PWD);
			String[] endLoginParamValue = {PUNISHMENTSVC_USERNAME,salt,pwdCipher};
			String token = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, endLoginMethodName, endLoginParamName, endLoginParamValue);
			
			//3.获取企事业、个体三无、个人待查询数据
			//企事业
			List<LawEnforceObject> enterpriseList = lawEnforceObjectMapper.selectEnterpriseObjList();
	        enterprise(token, enterpriseList);
			//个体、三无
			List<LawEnforceObject> individualThreeList = lawEnforceObjectMapper.selectIndividualThreeList();
	        individualThree(token, individualThreeList);
			//个人
			List<LawEnforceObject> individualList = lawEnforceObjectMapper.selectIndividualList();
	        individual(token, individualList);
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	public void batchSearchOrganizationAndIndividualCredits2() {
		try {
			//1.获取盐
			String salt = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, beginLoginMethodName, beginLoginParamName, new String[]{PUNISHMENTSVC_USERNAME});
			//2.获取token
			String pwdCipher = "";
			pwdCipher = getRSAEncryptPwdHexStr(salt,PUNISHMENTSVC_PUBLIC_KEY,PUNISHMENTSVC_PWD);
			String[] endLoginParamValue = {PUNISHMENTSVC_USERNAME,salt,pwdCipher};
			String token = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, endLoginMethodName, endLoginParamName, endLoginParamValue);
			
			//3.获取企事业、个体三无、个人待查询数据
			//企事业
			List<LawEnforceObject> enterpriseList = lawEnforceObjectMapper.selectEnterpriseObjList();
			if(enterpriseList!=null && enterpriseList.size()>0){
				for(LawEnforceObject lawEnforceObject:enterpriseList){
					singleSearchOrganizationAndIndividualCredits(lawEnforceObject,token);
				}
			}
			//个体、三无
			List<LawEnforceObject> individualThreeList = lawEnforceObjectMapper.selectIndividualThreeList();
			if(individualThreeList!=null && individualThreeList.size()>0){
				for(LawEnforceObject lawEnforceObject:individualThreeList){
					singleSearchOrganizationAndIndividualCredits(lawEnforceObject,token);
				}
			}
			//个人
			List<LawEnforceObject> individualList = lawEnforceObjectMapper.selectIndividualList();
			if(individualList!=null && individualList.size()>0){
				for(LawEnforceObject lawEnforceObject:individualList){
					singleSearchOrganizationAndIndividualCredits(lawEnforceObject,token);
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	/**
	 * 单独查询 调用的批量接口 参数传单个
	 */
	public void singleSearchOrganizationAndIndividualCredits(LawEnforceObject lawEnforceObject,String token) {
		try {
			if("1".equals(lawEnforceObject.getTypeCode())){//企业
				List<LawEnforceObject> enterpriseList = new ArrayList<LawEnforceObject>();
				enterpriseList.add(lawEnforceObject);
				enterprise(token, enterpriseList);
			}else if("3".equals(lawEnforceObject.getTypeCode())){//个体三无
				List<LawEnforceObject> individualThreeList = new ArrayList<LawEnforceObject>();
				individualThreeList.add(lawEnforceObject);
				individualThree(token, individualThreeList);
			}else if("2".equals(lawEnforceObject.getTypeCode())){//个人
				List<LawEnforceObject> individualList = new ArrayList<LawEnforceObject>();
				individualList.add(lawEnforceObject);
				individual(token, individualList);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	/**
	 * 单独查询 调用单独查询接口
	 */
	public void enterprise1(LawEnforceObject lawEnforceObject,String token) {
		try {
			String result = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, ValidateOrganizationByCertMethodName, ValidateOrganizationByCertParamName, new String[]{token,lawEnforceObject.getSocialCreditCode(),enterpriseCertTypeCode});
			String enterpriseRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindOrganizationByCertMethodName, FindOrganizationByCertParamName, new String[]{token,lawEnforceObject.getSocialCreditCode(),enterpriseCertTypeCode});
			Entity enterpriseRecordsEntity = JaxbXmlUtil.convertToJavaBean(enterpriseRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
			List<Record> records = enterpriseRecordsEntity.getRecords();
			String objId = lawEnforceObject.getId();
			List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
			if(lawEnforceObject.getIsSupervise()!=null && lawEnforceObject.getIsSupervise()==1){//失信
				if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records!=null && records.size()>0)){//返回01、02
					for(PPunishmentHistory pHistory:oldPunishmentList){
						for(Record record:records){
							if(pHistory.getInfoid().equals(record.getInfoID())){
								pHistory.setExist(true);
								record.setNew(false);
								break;
							}
						}
					}
					for(PPunishmentHistory pHistory:oldPunishmentList){
						if(pHistory.isExist()){//
							for(Record record:records){
								PPunishmentHistory temp = new PPunishmentHistory();
								temp.setInfoid(record.getInfoID().getValue());
								temp.setEntityname(record.getEntityName().getValue());
								temp.setEntitycerttype(record.getEntityCertType().getValue());
								temp.setEntitycertno(record.getEntityCertNo().getValue());
								temp.setReason(record.getReason().getValue());
//								temp.setLegalbasis(record.getLegalBasis().getValue());
								temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
								SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
								if(!record.getPublishDate().getValue().equals("")){
									temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
								}
								temp.setOrganization(record.getOrganization().getValue());
								temp.setCaseid(record.getCaseID().getValue());
								temp.setCaseno(record.getCaseNo().getValue());
								temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
								temp.setAppstatus(record.getAppStatus().getValue());
								temp.setCourt(record.getCourt().getValue());
								temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
								temp.setProgress(record.getProgress().getValue());
								temp.setFulfilled(record.getFulfilled().getValue());
								temp.setUnfulfilled(record.getUnfulfilled().getValue());
								temp.setLegalperson(record.getLegalPerson().getValue());
								temp.setEntityage(record.getEntityAge().getValue());
								temp.setEntitygender(record.getEntityGender().getValue());
								temp.setReasonforblocking(record.getReasonForBlocking().getValue());
								temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
								SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
								temp.setInsertdate(new Date());
								temp.setLawObjectId(objId);
								if(record.isNew()){//新增
									pPunishmentHistoryMapper.insertSelective(temp);
								}else{//更新
									Date oldDate = pHistory.getUpdatedate();
									SimpleDateFormat sdDateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									Date newDate = sdDateFormat.parse(record.getUpdatedTime().getValue());
									//>0:d1晚于d2; =0:d1=d2;<0:d1早于d2
									if(DateUtil.compareDate(newDate, oldDate)>0){//查询到数据更新时间晚于本地数据更新时间的，要更新
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(temp);
									}
								}
							}
							
						}else{//置为无效
							pHistory.setAppstatus("0");
							pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
						}
						
					}
				}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records==null || records.size()==0)){//相当于返回03
					//全置为无效
					for(PPunishmentHistory pHistory:oldPunishmentList){
						pHistory.setAppstatus("0");
						pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
					}
				}
				//01 不维护执法对象 惩戒信息部分与 02、03时公用
				//更新惩戒信息表
				Record record = records.get(0);
				PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
				pPunishmentResult.setEntityname(record.getEntityName().getValue());
				pPunishmentResult.setEntitycerttype(record.getEntityCertType().getValue());
				pPunishmentResult.setEntitycertno(record.getEntityCertNo().getValue());
				pPunishmentResult.setEntitytype("2");//企业
				pPunishmentResult.setUpdatedate(new Date());
				if("02".equals(result)||"03".equals(result)){
					//执法对象维护为不失信
					LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
					objectWithBLOBs.setIsSupervise(0);//修改为不失信
					objectWithBLOBs.setSuperviseDate(null);
					lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
					//维护惩戒信息
					pPunishmentResult.setIsvalid("2");
				}
				pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
			}else{
				
				if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records!=null && records.size()>0)){//原来02  返回01、02
					for(PPunishmentHistory pHistory:oldPunishmentList){
						for(Record record:records){
							if(pHistory.getInfoid().equals(record.getInfoID())){
								pHistory.setExist(true);
								System.out.println("===================infoId="+pHistory.getInfoid());
								record.setNew(false);
								break;
							}
						}
					}
					for(PPunishmentHistory pHistory:oldPunishmentList){
						if(pHistory.isExist()){//
							for(Record record:records){
								PPunishmentHistory temp = new PPunishmentHistory();
								temp.setInfoid(record.getInfoID().getValue());
								temp.setEntityname(record.getEntityName().getValue());
								temp.setEntitycerttype(record.getEntityCertType().getValue());
								temp.setEntitycertno(record.getEntityCertNo().getValue());
								temp.setReason(record.getReason().getValue());
								temp.setLegalbasis(record.getLegalBasis().getValue());
								temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
								SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
								if(!record.getPublishDate().getValue().equals("")){
									temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
								}
								temp.setOrganization(record.getOrganization().getValue());
								temp.setCaseid(record.getCaseID().getValue());
								temp.setCaseno(record.getCaseNo().getValue());
								temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
								temp.setAppstatus(record.getAppStatus().getValue());
								temp.setCourt(record.getCourt().getValue());
								temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
								temp.setProgress(record.getProgress().getValue());
								temp.setFulfilled(record.getFulfilled().getValue());
								temp.setUnfulfilled(record.getUnfulfilled().getValue());
								temp.setLegalperson(record.getLegalPerson().getValue());
								temp.setEntityage(record.getEntityAge().getValue());
								temp.setEntitygender(record.getEntityGender().getValue());
								temp.setReasonforblocking(record.getReasonForBlocking().getValue());
								temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
								SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
								temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
								temp.setInsertdate(new Date());
								temp.setLawObjectId(objId);
								if(record.isNew()){//新增
									pPunishmentHistoryMapper.insertSelective(temp);
								}else{//更新
									Date oldDate = pHistory.getUpdatedate();
									SimpleDateFormat sdDateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									Date newDate = sdDateFormat.parse(record.getUpdatedTime().getValue());
									//>0:d1晚于d2; =0:d1=d2;<0:d1早于d2
									if(DateUtil.compareDate(newDate, oldDate)>0){//查询到数据更新时间晚于本地数据更新时间的，要更新
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(temp);
									}
								}
							}
							
						}else{//置为无效
							pHistory.setAppstatus("0");
							pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
						}
						
					}
					//维护惩戒信息表
					Record record = records.get(0);
					PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
					pPunishmentResult.setEntityname(record.getEntityName().getValue());
					pPunishmentResult.setEntitycerttype(record.getEntityCertType().getValue());
					pPunishmentResult.setEntitycertno(record.getEntityCertNo().getValue());
					pPunishmentResult.setEntitytype("2");//企业
					pPunishmentResult.setUpdatedate(new Date());
					if("01".equals(result)){//失信
						//维护执法对象
						LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
						objectWithBLOBs.setIsSupervise(1);//修改失信
						objectWithBLOBs.setSuperviseDate(new Date());
						lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
						pPunishmentResult.setIsvalid("1");
					}else if("02".equals(result)){//没有有效失信
						//更新惩戒信息表
						pPunishmentResult.setIsvalid("2");
					}
					pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
					
				}else if((records!=null && records.size()>0) && (oldPunishmentList==null || oldPunishmentList.size()==0)){//原来03，返回01
					//全部新增
					for(Record record:records){
						PPunishmentHistory temp = new PPunishmentHistory();
						temp.setInfoid(record.getInfoID().getValue());
						temp.setEntityname(record.getEntityName().getValue());
						temp.setEntitycerttype(record.getEntityCertType().getValue());
						temp.setEntitycertno(record.getEntityCertNo().getValue());
						temp.setReason(record.getReason().getValue());
//						temp.setLegalbasis(record.getLegalBasis().getValue());
						temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
						SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
						if(!record.getPublishDate().getValue().equals("")){
							temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
						}
						temp.setOrganization(record.getOrganization().getValue());
						temp.setCaseid(record.getCaseID().getValue());
						temp.setCaseno(record.getCaseNo().getValue());
						temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
						temp.setAppstatus(record.getAppStatus().getValue());
						temp.setCourt(record.getCourt().getValue());
						temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
						temp.setProgress(record.getProgress().getValue());
						temp.setFulfilled(record.getFulfilled().getValue());
						temp.setUnfulfilled(record.getUnfulfilled().getValue());
						temp.setLegalperson(record.getLegalPerson().getValue());
						temp.setEntityage(record.getEntityAge().getValue());
						temp.setEntitygender(record.getEntityGender().getValue());
						temp.setReasonforblocking(record.getReasonForBlocking().getValue());
						temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
						SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
						temp.setInsertdate(new Date());
						temp.setLawObjectId(objId);
						pPunishmentHistoryMapper.insertSelective(temp);
					}
					
					//维护执法对象
					LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
					objectWithBLOBs.setIsSupervise(1);//修改失信
					objectWithBLOBs.setSuperviseDate(new Date());
					lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
					//新添加一条数据到惩戒信息表
					Record record = records.get(0);
					PPunishmentResult pPunishmentResult = new PPunishmentResult();
					pPunishmentResult.setId(objId);
					pPunishmentResult.setBusiness("生产经营");
					pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
					pPunishmentResult.setDecideddate(new Date());
					pPunishmentResult.setOrganization("福建省环境保护厅");
					pPunishmentResult.setIsvalid("1");
					pPunishmentResult.setEntityname(record.getEntityName().getValue());
					pPunishmentResult.setEntitycerttype(record.getEntityCertType().getValue());
					pPunishmentResult.setEntitycertno(record.getEntityCertNo().getValue());
					pPunishmentResult.setEntitytype("2");//企业
					pPunishmentResult.setUpdatedate(new Date());
					pPunishmentResultMapper.insertSelective(pPunishmentResult);
					
				}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records==null || records.size()==0)){//原来02  返回03
					//全置为无效
					for(PPunishmentHistory pHistory:oldPunishmentList){
						pHistory.setAppstatus("0");
						pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
					}
					//执法对象、惩戒信息均不动
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}


	public void enterprise(String token, List<LawEnforceObject> enterpriseList) throws Exception, ParseException {
		//分批查询，每次100条,否则数据量大时会报错
		int totalCount = 0;
		int failedCount = 0;
		if(enterpriseList !=null){
			totalCount = enterpriseList.size();
		}
		PageModel<LawEnforceObject> batchInsertEnterprise=new PageModel<LawEnforceObject>(enterpriseList,enterpriseBatchLimit); 
		for (int i = 0; i <batchInsertEnterprise.getTotalPages(); i++) {
			//企业基本信息入库操作,插入或更新
			List<LawEnforceObject> blockList   = batchInsertEnterprise.getObjects(i+1);
			Entities enterpriseEntities = new Entities();
			List<Entity> enterpriseEntityList = new ArrayList<Entity>();
			if(blockList!=null && blockList.size()>0){
				for(LawEnforceObject enterpriseObj:blockList){
					Entity enterpriseEntitie = new Entity();
					enterpriseEntitie.setCertNo(enterpriseObj.getSocialCreditCode());
					enterpriseEntityList.add(enterpriseEntitie);
				}
			}
			enterpriseEntities.setEntities(enterpriseEntityList);
			String enterpriseXml = JaxbXmlUtil.convertToXml(enterpriseEntities);
			System.out.println(enterpriseXml);
			//4.批量查询失信情况
			String[] ValidateOrganizationsByCertParamValue = {token,enterpriseXml,enterpriseCertTypeCode};
			String reponseEnterpriseXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, ValidateOrganizationsByCertMethodName, ValidateOrganizationsByCertParamName, ValidateOrganizationsByCertParamValue);
			if(!reponseEnterpriseXml.startsWith("<?")){
				logger.error(reponseEnterpriseXml);
				if(blockList==null){
					//do nothing
				}else if(blockList.size()<enterpriseBatchLimit){
					failedCount += blockList.size();
				}else{
					failedCount += enterpriseBatchLimit;
				}
				continue;
			}
			Entities resultBean = JaxbXmlUtil.convertToJavaBean(reponseEnterpriseXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entities.class);
			List<Entity> entities = resultBean.getEntities();
			for(LawEnforceObject enterpriseObj:blockList){
				try {
					for(Entity entity:entities){
						if(enterpriseObj.getSocialCreditCode().equals(entity.getCertNo())){
							if(enterpriseObj.getIsSupervise() !=null && enterpriseObj.getIsSupervise()==1){//失信
								//查询详情更新失信记录表
								String socialCreditCode = enterpriseObj.getSocialCreditCode();
								String enterpriseRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindOrganizationByCertMethodName, FindOrganizationByCertParamName, new String[]{token,socialCreditCode,enterpriseCertTypeCode});
								if(!enterpriseRecordsXml.startsWith("<?")&& !enterpriseRecordsXml.startsWith("03") && !enterpriseRecordsXml.startsWith("02")){
									logger.error(enterpriseRecordsXml);
									continue;
								}
								List<Record> records = null;
								if(!enterpriseRecordsXml.startsWith("03") &&  !enterpriseRecordsXml.startsWith("02")){
									Entity enterpriseRecordsEntity = JaxbXmlUtil.convertToJavaBean(enterpriseRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									records = enterpriseRecordsEntity.getRecords();
								}
								String objId = enterpriseObj.getId();
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records!=null && records.size()>0)){//返回01
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:records){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									//置为失效或插入或更新
									commonInsertOrUpdate(objId, records, oldPunishmentList);
									
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records==null || records.size()==0)){//相当于返回 02 、03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
								}
								//01 不维护执法对象 惩戒信息部分与 02、03时公用
								//更新惩戒信息表
								if("01".equals(entity.getResult())){
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}else{
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(objId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(objId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}
									
								}else if("02".equals(entity.getResult())||"03".equals(entity.getResult())){
									//执法对象维护为不失信
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
									objectWithBLOBs.setIsSupervise(0);//修改为不失信
									objectWithBLOBs.setSuperviseDate(null);
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//维护惩戒信息
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setIsvalid("2");
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
								}
								
							}else{
								//查询详情更新失信记录表
								String objId = enterpriseObj.getId();
								String socialCreditCode = enterpriseObj.getSocialCreditCode();
								String enterpriseRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindOrganizationByCertMethodName, FindOrganizationByCertParamName, new String[]{token,socialCreditCode,enterpriseCertTypeCode});
								if(!enterpriseRecordsXml.startsWith("<?") && !enterpriseRecordsXml.startsWith("03") && !enterpriseRecordsXml.startsWith("02")){
									logger.error(enterpriseRecordsXml);
									continue;
								}
								List<Record> records = null;
								if(!enterpriseRecordsXml.startsWith("03") && !enterpriseRecordsXml.startsWith("02")){
									Entity enterpriseRecordsEntity = JaxbXmlUtil.convertToJavaBean(enterpriseRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									records = enterpriseRecordsEntity.getRecords();
								}
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records!=null && records.size()>0)){//原来02  返回01
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:records){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									commonInsertOrUpdate(objId, records, oldPunishmentList);
									//维护惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
									pPunishmentResult.setEntityname(record.getEntityname());
									pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
									pPunishmentResult.setEntitycertno(record.getEntitycertno());
									pPunishmentResult.setEntitytype("2");//企业
									pPunishmentResult.setUpdatedate(new Date());
									if("01".equals(entity.getResult())){//失信
										//维护执法对象
										LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
										objectWithBLOBs.setIsSupervise(1);//修改失信
										objectWithBLOBs.setSuperviseDate(new Date());
										lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
										pPunishmentResult.setIsvalid("1");
									}else if("02".equals(entity.getResult())){//没有有效失信
										//更新惩戒信息表
										pPunishmentResult.setIsvalid("2");
									}
									pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									
								}else if((records!=null && records.size()>0) && (oldPunishmentList==null || oldPunishmentList.size()==0)){//原来03，返回01
									//全部新增
									for(Record record:records){
										PPunishmentHistory temp = new PPunishmentHistory();
										temp.setInfoid(record.getInfoID().getValue());
										temp.setEntityname(record.getEntityName().getValue());
										temp.setEntitycerttype(record.getEntityCertType().getValue());
										temp.setEntitycertno(record.getEntityCertNo().getValue());
										temp.setReason(record.getReason().getValue());
//										temp.setLegalbasis(record.getLegalBasis().getValue());
										temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
										SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
										if(!record.getPublishDate().getValue().equals("")){
											temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
										}
										temp.setOrganization(record.getOrganization().getValue());
										temp.setCaseid(record.getCaseID().getValue());
										temp.setCaseno(record.getCaseNo().getValue());
										temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
										temp.setAppstatus(record.getAppStatus().getValue());
										temp.setCourt(record.getCourt().getValue());
										temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
										temp.setProgress(record.getProgress().getValue());
										temp.setFulfilled(record.getFulfilled().getValue());
										temp.setUnfulfilled(record.getUnfulfilled().getValue());
										temp.setLegalperson(record.getLegalPerson().getValue());
										temp.setEntityage(record.getEntityAge().getValue());
										temp.setEntitygender(record.getEntityGender().getValue());
										temp.setReasonforblocking(record.getReasonForBlocking().getValue());
										temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
										SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
										temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
										temp.setInsertdate(new Date());
										temp.setLawObjectId(objId);
										pPunishmentHistoryMapper.insertSelective(temp);
									}
									
									//维护执法对象
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(objId);
									objectWithBLOBs.setIsSupervise(1);//修改失信
									objectWithBLOBs.setSuperviseDate(new Date());
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//新添加一条数据到惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(objId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(objId);
									if(pPunishmentResult ==null){
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(objId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(objId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}else{
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
								
									
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (records==null || records.size()==0)){//原来02  返回02、03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
									//执法对象、惩戒信息均不动
								}
							}
							
						}
					}
				} catch (Exception e) {
					logger.error(e.getMessage());
					failedCount +=1;
				}
				
			}
		}
		logger.info("企事业执法对象对接总数为："+totalCount);
		logger.info("企事业执法对象对接失败数为："+failedCount);
	}


	public void individualThree(String token, List<LawEnforceObject> individualThreeList)
			throws Exception, ParseException {
		//分批查询，每次100条,否则数据量大时会报错
		int totalCount = 0;
		int failedCount = 0;
		if(individualThreeList !=null){
			totalCount = individualThreeList.size();
		}
		PageModel<LawEnforceObject> batchInsertindividualThree=new PageModel<LawEnforceObject>(individualThreeList,individalThreeBatchLimit); 
		for (int i = 0; i <batchInsertindividualThree.getTotalPages(); i++) {
			List<LawEnforceObject> blockList   = batchInsertindividualThree.getObjects(i+1);
			Entities individualThreeEntities = new Entities();
			List<Entity> individualThreeEntityList = new ArrayList<Entity>();
			if(blockList!=null && blockList.size()>0){
				for(LawEnforceObject individualThreeObj:blockList){
					Entity individualThreeEntitie = new Entity();
					individualThreeEntitie.setCertNo(individualThreeObj.getCardNumber());
					individualThreeEntityList.add(individualThreeEntitie);
				}
			}
			individualThreeEntities.setEntities(individualThreeEntityList);
			String individualThreeXml = JaxbXmlUtil.convertToXml(individualThreeEntities);
			//批量查询失信情况
			String[] individualThreeParamValue = {token,individualThreeXml,enterpriseCertTypeCode};
			String reponseIndividualThreeXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, ValidateOrganizationsByCertMethodName, ValidateOrganizationsByCertParamName, individualThreeParamValue);
			if(!reponseIndividualThreeXml.startsWith("<?")){
				logger.error(reponseIndividualThreeXml);
				if(blockList==null){
					//do nothing
				}else if(blockList.size()<individalThreeBatchLimit){
					failedCount += blockList.size();
				}else{
					failedCount += individalThreeBatchLimit;
				}
				continue;
			}
			Entities individualThreeResultBean = JaxbXmlUtil.convertToJavaBean(reponseIndividualThreeXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entities.class);
			List<Entity> individualThreeResponse = individualThreeResultBean.getEntities();
			for(LawEnforceObject individualThreeObj:blockList){
				
				try {

					for(Entity individualThreeEntity:individualThreeResponse){
						if(individualThreeObj.getCardNumber().equals(individualThreeEntity.getCertNo())){
							if(individualThreeObj.getIsSupervise()!=null && individualThreeObj.getIsSupervise()==1){//失信
								//查询详情更新失信记录表
								String cardNumber = individualThreeObj.getCardNumber();
								String individualThreeRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindOrganizationByCertMethodName, FindOrganizationByCertParamName, new String[]{token,cardNumber,enterpriseCertTypeCode});
								if(!individualThreeRecordsXml.startsWith("<?") && !individualThreeRecordsXml.startsWith("03") && !individualThreeRecordsXml.startsWith("02")){
									logger.error(individualThreeRecordsXml);
									failedCount +=1;
									continue;
								}
								List<Record> individualThreeRecords = null;
								if(!individualThreeRecordsXml.startsWith("03") && !individualThreeRecordsXml.startsWith("02")){
									Entity individualThreeRecordsEntity = JaxbXmlUtil.convertToJavaBean(individualThreeRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									individualThreeRecords = individualThreeRecordsEntity.getRecords();
								}
								String individualThreeObjId = individualThreeObj.getId();
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualThreeObjId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualThreeRecords!=null && individualThreeRecords.size()>0)){//返回01
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:individualThreeRecords){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									commonInsertOrUpdate(individualThreeObjId, individualThreeRecords,
											oldPunishmentList);
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualThreeRecords==null || individualThreeRecords.size()==0)){//相当于返回02、03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
								}
								//01 不维护执法对象 惩戒信息部分与 02、03时公用
								//更新惩戒信息表
								if("01".equals(individualThreeEntity.getResult())){
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualThreeObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualThreeObjId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}else{
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(individualThreeObjId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(individualThreeObjId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}
									
								}else if("02".equals(individualThreeEntity.getResult())||"03".equals(individualThreeEntity.getResult())){
									//执法对象维护为不失信
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualThreeObjId);
									objectWithBLOBs.setIsSupervise(0);//修改为不失信
									objectWithBLOBs.setSuperviseDate(null);
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//维护惩戒信息
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualThreeObjId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setIsvalid("2");
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
								}
								
							}else{
								//查询详情更新失信记录表
								String individualThreeObjId = individualThreeObj.getId();
								String cardNumber = individualThreeObj.getCardNumber();
								String individualThreeRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindOrganizationByCertMethodName, FindOrganizationByCertParamName, new String[]{token,cardNumber,enterpriseCertTypeCode});
								if(!individualThreeRecordsXml.startsWith("<?") && !individualThreeRecordsXml.startsWith("03") && !individualThreeRecordsXml.startsWith("02")){
									logger.error(individualThreeRecordsXml);
									failedCount +=1;
									continue;
								}
								List<Record> individualThreeRecords = null;
								if(!individualThreeRecordsXml.startsWith("03") && !individualThreeRecordsXml.startsWith("02")){
									Entity individualThreeRecordsEntity = JaxbXmlUtil.convertToJavaBean(individualThreeRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									individualThreeRecords = individualThreeRecordsEntity.getRecords();
								}
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualThreeObjId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualThreeRecords!=null && individualThreeRecords.size()>0)){//原来02  返回01
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:individualThreeRecords){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									commonInsertOrUpdate(individualThreeObjId, individualThreeRecords,
											oldPunishmentList);
									//维护惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualThreeObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualThreeObjId);
									pPunishmentResult.setEntityname(record.getEntityname());
									pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
									pPunishmentResult.setEntitycertno(record.getEntitycertno());
									pPunishmentResult.setEntitytype("2");//企业
									pPunishmentResult.setUpdatedate(new Date());
									if("01".equals(individualThreeEntity.getResult())){//失信
										//维护执法对象
										LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualThreeObjId);
										objectWithBLOBs.setIsSupervise(1);//修改失信
										objectWithBLOBs.setSuperviseDate(new Date());
										lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
										pPunishmentResult.setIsvalid("1");
									}else if("02".equals(individualThreeEntity.getResult())){//没有有效失信
										//更新惩戒信息表
										pPunishmentResult.setIsvalid("2");
									}
									pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									
								}else if((individualThreeRecords!=null && individualThreeRecords.size()>0) && (oldPunishmentList==null || oldPunishmentList.size()==0)){//原来03，返回01
									//全部新增
									for(Record record:individualThreeRecords){
										PPunishmentHistory temp = new PPunishmentHistory();
										temp.setInfoid(record.getInfoID().getValue());
										temp.setEntityname(record.getEntityName().getValue());
										temp.setEntitycerttype(record.getEntityCertType().getValue());
										temp.setEntitycertno(record.getEntityCertNo().getValue());
										temp.setReason(record.getReason().getValue());
//										temp.setLegalbasis(record.getLegalBasis().getValue());
										temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
										SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
										if(!record.getPublishDate().getValue().equals("")){
											temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
										}
										temp.setOrganization(record.getOrganization().getValue());
										temp.setCaseid(record.getCaseID().getValue());
										temp.setCaseno(record.getCaseNo().getValue());
										temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
										temp.setAppstatus(record.getAppStatus().getValue());
										temp.setCourt(record.getCourt().getValue());
										temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
										temp.setProgress(record.getProgress().getValue());
										temp.setFulfilled(record.getFulfilled().getValue());
										temp.setUnfulfilled(record.getUnfulfilled().getValue());
										temp.setLegalperson(record.getLegalPerson().getValue());
										temp.setEntityage(record.getEntityAge().getValue());
										temp.setEntitygender(record.getEntityGender().getValue());
										temp.setReasonforblocking(record.getReasonForBlocking().getValue());
										temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
										SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
										temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
										temp.setInsertdate(new Date());
										temp.setLawObjectId(individualThreeObjId);
										pPunishmentHistoryMapper.insertSelective(temp);
									}
									
									//维护执法对象
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualThreeObjId);
									objectWithBLOBs.setIsSupervise(1);//修改失信
									objectWithBLOBs.setSuperviseDate(new Date());
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//新添加一条数据到惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualThreeObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualThreeObjId);
									if(pPunishmentResult ==null){
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(individualThreeObjId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(individualThreeObjId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}else{
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("2");//企业
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
								
									
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualThreeRecords==null || individualThreeRecords.size()==0)){//原来02  返回02、03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
									//执法对象、惩戒信息均不动
								}
							}
							
						}
					}
				
				} catch (Exception e) {
					logger.error(e.getMessage());
					failedCount +=1;
				}
			}
		}
		logger.info("个体、三无、小三产执法对象对接总数为："+totalCount);
		logger.info("个体、三无、小三产执法对象对接失败数为："+failedCount);
	}
	/**
	 * 置为失效或插入或更新
	 * @param lawObjectId
	 * @param searchedRecords
	 * @param oldPunishmentList
	 */
	public void commonInsertOrUpdate(String lawObjectId, List<Record> searchedRecords,
			List<PPunishmentHistory> oldPunishmentList) throws ParseException {
		//先把不存在的置为无效
		for(PPunishmentHistory pHistory:oldPunishmentList){
			if(!pHistory.isExist()){
				pHistory.setAppstatus("0");
				pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
			}
		}
		//到这里的是新增的和待更新的
		for(Record record:searchedRecords){
			PPunishmentHistory temp = new PPunishmentHistory();
			temp.setInfoid(record.getInfoID().getValue());
			temp.setEntityname(record.getEntityName().getValue());
			temp.setEntitycerttype(record.getEntityCertType().getValue());
			temp.setEntitycertno(record.getEntityCertNo().getValue());
			temp.setReason(record.getReason().getValue());
//										temp.setLegalbasis(record.getLegalBasis().getValue());
			temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
			SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
			if(!record.getPublishDate().getValue().equals("")){
				temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
			}
			temp.setOrganization(record.getOrganization().getValue());
			temp.setCaseid(record.getCaseID().getValue());
			temp.setCaseno(record.getCaseNo().getValue());
			temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
			temp.setAppstatus(record.getAppStatus().getValue());
			temp.setCourt(record.getCourt().getValue());
			temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
			temp.setProgress(record.getProgress().getValue());
			temp.setFulfilled(record.getFulfilled().getValue());
			temp.setUnfulfilled(record.getUnfulfilled().getValue());
			temp.setLegalperson(record.getLegalPerson().getValue());
			temp.setEntityage(record.getEntityAge().getValue());
			temp.setEntitygender(record.getEntityGender().getValue());
			temp.setReasonforblocking(record.getReasonForBlocking().getValue());
			temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
			SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
			temp.setInsertdate(new Date());
			temp.setLawObjectId(lawObjectId);
			if(record.isNew()){//新增
				pPunishmentHistoryMapper.insertSelective(temp);
			}else{//更新 
				for(PPunishmentHistory pHistory:oldPunishmentList){
					if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
						/*Date oldDate = pHistory.getUpdatedate();
						SimpleDateFormat sdDateFormat= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						Date newDate = sdDateFormat.parse(record.getUpdatedTime().getValue());
						//>0:d1晚于d2; =0:d1=d2;<0:d1早于d2
						if(DateUtil.compareDate(newDate, oldDate)>0){//查询到数据更新时间晚于本地数据更新时间的，要更新
							pPunishmentHistoryMapper.updateByPrimaryKeySelective(temp);
							break;
						}*/
						pPunishmentHistoryMapper.updateByPrimaryKeySelective(temp);
						break;
					}
				}
			}
		}
	}


	public void individual(String token, List<LawEnforceObject> individualList) throws Exception, ParseException {
		//分批查询，每次500条,否则数据量大时会报错
		int totalCount = 0;
		int failedCount = 0;
		if(individualList !=null){
			totalCount = individualList.size();
		}
		PageModel<LawEnforceObject> batchInsertindividual=new PageModel<LawEnforceObject>(individualList,individalBatchLimit); 
		for (int i = 0; i <batchInsertindividual.getTotalPages(); i++) {
			List<LawEnforceObject> blockList   = batchInsertindividual.getObjects(i+1);
			Entities individualEntities = new Entities();
			List<Entity> individualEntityList = new ArrayList<Entity>();
			if(blockList!=null && blockList.size()>0){
				for(LawEnforceObject individualObj:blockList){
					Entity individualEntitie = new Entity();
					individualEntitie.setCertNo(individualObj.getCardNumber());
					individualEntitie.setName(individualObj.getObjectName());
					individualEntityList.add(individualEntitie);
				}
			}
			individualEntities.setEntities(individualEntityList);
			String individualXml = JaxbXmlUtil.convertToXml(individualEntities);
			//批量查询失信情况
			String[] individualParamValue = {token,individualXml,individalCertTypeCode};
			String reponseIndividualXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, ValidateIndividualsMethodName, ValidateIndividualsParamName, individualParamValue);
			if(!reponseIndividualXml.startsWith("<?")){
				logger.error(reponseIndividualXml);
				if(blockList==null){
					//do nothing
				}else if(blockList.size()<individalBatchLimit){
					failedCount += blockList.size();
				}else{
					failedCount += individalBatchLimit;
				}
				continue;
			}
			Entities individualResultBean = JaxbXmlUtil.convertToJavaBean(reponseIndividualXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entities.class);
			List<Entity> individualResponse = individualResultBean.getEntities();
			for(LawEnforceObject individualObj:blockList){
				try {
					for(Entity individualEntity:individualResponse){
						if(individualObj.getCardNumber().equals(individualEntity.getCertNo())){
							if(individualObj.getIsSupervise()!=null && individualObj.getIsSupervise()==1){//失信
								//查询详情更新失信记录表
								String cardNumber = individualObj.getCardNumber();
								String objectName = individualObj.getObjectName();
								String individualRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindIndividualMethodName, FindIndividualParamName, new String[]{token,cardNumber,objectName,individalCertTypeCode});
								if(!individualRecordsXml.startsWith("<?") && !individualRecordsXml.startsWith("03") && !individualRecordsXml.startsWith("02")){
									logger.error(individualRecordsXml);
									failedCount +=1;
									continue;
								}
								List<Record> individualRecords = null;
								if(!individualRecordsXml.startsWith("03") && !individualRecordsXml.startsWith("02")){
									Entity individualRecordsEntity = JaxbXmlUtil.convertToJavaBean(individualRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									individualRecords = individualRecordsEntity.getRecords();
								}
								String individualObjId = individualObj.getId();
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualObjId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualRecords!=null && individualRecords.size()>0)){//返回01、02
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:individualRecords){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									commonInsertOrUpdate(individualObjId,individualRecords,oldPunishmentList);
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualRecords==null || individualRecords.size()==0)){//相当于返回03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
								}
								//01 不维护执法对象 惩戒信息部分与 02、03时公用
								//更新惩戒信息表
								if("01".equals(individualEntity.getResult())){
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualObjId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntitytype("1");//自然人
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}else{
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(individualObjId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("1");//自然人
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(individualObjId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}
									
								}else if("02".equals(individualEntity.getResult())||"03".equals(individualEntity.getResult())){
									//执法对象维护为不失信
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualObjId);
									objectWithBLOBs.setIsSupervise(0);//修改为不失信
									objectWithBLOBs.setSuperviseDate(null);
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//维护惩戒信息
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualObjId);
									if(pPunishmentResult !=null){
										pPunishmentResult.setIsvalid("2");
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
								}
							}else{
								//查询详情更新失信记录表
								String individualObjId = individualObj.getId();
								String cardNumber = individualObj.getCardNumber();
								String objectName = individualObj.getObjectName();
								String individualRecordsXml = WebserviceClientUtil.axis2WebService(PUNISHMENTSVC_WEBSERVICE_URL, PUNISHMENTSVC_QNAME, FindIndividualMethodName, FindIndividualParamName, new String[]{token,cardNumber,objectName,individalCertTypeCode});
								if(!individualRecordsXml.startsWith("<?") && !individualRecordsXml.startsWith("03") && !individualRecordsXml.startsWith("02")){
									logger.error(individualRecordsXml);
									failedCount +=1;
									continue;
								}
								List<Record> individualRecords = null;
								if(!individualRecordsXml.startsWith("03") && !individualRecordsXml.startsWith("02")){
									Entity individualRecordsEntity = JaxbXmlUtil.convertToJavaBean(individualRecordsXml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
									individualRecords = individualRecordsEntity.getRecords();
								}
								List<PPunishmentHistory> oldPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualObjId);
								
								if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualRecords!=null && individualRecords.size()>0)){//原来02  返回01、02
									for(PPunishmentHistory pHistory:oldPunishmentList){
										for(Record record:individualRecords){
											if(pHistory.getInfoid().equals(record.getInfoID().getValue())){
												pHistory.setExist(true);
												record.setNew(false);
												break;
											}
										}
									}
									commonInsertOrUpdate(individualObjId,individualRecords,oldPunishmentList);
									//维护惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualObjId);
									pPunishmentResult.setEntityname(record.getEntityname());
									pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
									pPunishmentResult.setEntitycertno(record.getEntitycertno());
									pPunishmentResult.setEntitytype("1");//自然人
									pPunishmentResult.setUpdatedate(new Date());
									if("01".equals(individualEntity.getResult())){//失信
										//维护执法对象
										LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualObjId);
										objectWithBLOBs.setIsSupervise(1);//修改失信
										objectWithBLOBs.setSuperviseDate(new Date());
										lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
										pPunishmentResult.setIsvalid("1");
									}else if("02".equals(individualEntity.getResult())){//没有有效失信
										//更新惩戒信息表
										pPunishmentResult.setIsvalid("2");
									}
									pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									
								}else if((individualRecords!=null && individualRecords.size()>0) && (oldPunishmentList==null || oldPunishmentList.size()==0)){//原来03，返回01
									//全部新增
									for(Record record:individualRecords){
										PPunishmentHistory temp = new PPunishmentHistory();
										temp.setInfoid(record.getInfoID().getValue());
										temp.setEntityname(record.getEntityName().getValue());
										temp.setEntitycerttype(record.getEntityCertType().getValue());
										temp.setEntitycertno(record.getEntityCertNo().getValue());
										temp.setReason(record.getReason().getValue());
//									temp.setLegalbasis(record.getLegalBasis().getValue());
										temp.setDecisioninstrumentno(record.getDecisionInstrumentNo().getValue());
										SimpleDateFormat sdf= new SimpleDateFormat("yyyy-MM-dd");
										if(!record.getPublishDate().getValue().equals("")){
											temp.setPublishdate(sdf.parse(record.getPublishDate().getValue()));
										}
										temp.setOrganization(record.getOrganization().getValue());
										temp.setCaseid(record.getCaseID().getValue());
										temp.setCaseno(record.getCaseNo().getValue());
										temp.setCreatedate(sdf.parse(record.getCreateDate().getValue()));
										temp.setAppstatus(record.getAppStatus().getValue());
										temp.setCourt(record.getCourt().getValue());
										temp.setCourtgradingcode(record.getCourtGradingCode().getValue());
										temp.setProgress(record.getProgress().getValue());
										temp.setFulfilled(record.getFulfilled().getValue());
										temp.setUnfulfilled(record.getUnfulfilled().getValue());
										temp.setLegalperson(record.getLegalPerson().getValue());
										temp.setEntityage(record.getEntityAge().getValue());
										temp.setEntitygender(record.getEntityGender().getValue());
										temp.setReasonforblocking(record.getReasonForBlocking().getValue());
										temp.setReasonforrevoking(record.getReasonForRevoking().getValue());
										SimpleDateFormat sdf2= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
										temp.setUpdatedate(sdf2.parse(record.getUpdatedTime().getValue()));
										temp.setInsertdate(new Date());
										temp.setLawObjectId(individualObjId);
										pPunishmentHistoryMapper.insertSelective(temp);
									}
									
									//维护执法对象
									LawEnforceObjectWithBLOBs objectWithBLOBs = lawEnforceObjectMapper.selectByPrimaryKey(individualObjId);
									objectWithBLOBs.setIsSupervise(1);//修改失信
									objectWithBLOBs.setSuperviseDate(new Date());
									lawEnforceObjectMapper.updateByPrimaryKeySelective(objectWithBLOBs);
									//新添加一条数据到惩戒信息表
									List<PPunishmentHistory> newPunishmentList = pPunishmentHistoryMapper.selectPunishmentHistoryListByLawObjId(individualObjId);
									PPunishmentHistory record = newPunishmentList.get(0);
									PPunishmentResult pPunishmentResult = pPunishmentResultMapper.selectByPrimaryKey(individualObjId);
									if(pPunishmentResult ==null){
										pPunishmentResult = new PPunishmentResult();
										pPunishmentResult.setId(individualObjId);
										pPunishmentResult.setBusiness("生产经营");
										pPunishmentResult.setMeasure("打上“失信被执行人”标签，列入重点监管企业");
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setOrganization("福建省环境保护厅");
										pPunishmentResult.setIsvalid("1");
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("1");//自然人
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResult.setLawObjectId(individualObjId);
										pPunishmentResultMapper.insertSelective(pPunishmentResult);
									}else{
										pPunishmentResult.setDecideddate(new Date());
										pPunishmentResult.setEntityname(record.getEntityname());
										pPunishmentResult.setEntitycerttype(record.getEntitycerttype());
										pPunishmentResult.setEntitycertno(record.getEntitycertno());
										pPunishmentResult.setEntitytype("1");//自然人
										pPunishmentResult.setUpdatedate(new Date());
										pPunishmentResult.setInsertdate(new Date());
										pPunishmentResultMapper.updateByPrimaryKeySelective(pPunishmentResult);
									}
									
								}else if((oldPunishmentList!=null && oldPunishmentList.size()>0) && (individualRecords==null || individualRecords.size()==0)){//原来02  返回03
									//全置为无效
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pHistory.setAppstatus("0");
										pPunishmentHistoryMapper.updateByPrimaryKeySelective(pHistory);
									}
									/*//删掉旧的记录
									for(PPunishmentHistory pHistory:oldPunishmentList){
										pPunishmentHistoryMapper.deleteByPrimaryKey(pHistory.getInfoid());
									}*/
									//执法对象、惩戒信息均不动
								}
							}
							
						}
					}
				} catch (Exception e) {
					logger.error(e.getMessage());
					failedCount +=1;
				}
				
			}
		}
		logger.info("个人执法对象对接总数为："+totalCount);
		logger.info("个人执法对象对接失败数为："+failedCount);
	}
	
	
	/**
	 * 返回加密后的密码
	 * @param salt
	 * @param publicKeyStr
	 * @param pwd
	 * @return
	 * @throws Exception
	 */
	public String getRSAEncryptPwdHexStr(String salt,String publicKeyStr,String pwd) throws Exception{
				 //加载公钥
		        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
		        byte[] keyData = Base64.getDecoder().decode(publicKeyStr);
		        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyData);
		        RSAPublicKey publicKey = (RSAPublicKey) keyFactory.generatePublic(keySpec);

		        //初始化RSA加密程序
		        Cipher encryptor = Cipher.getInstance("RSA");
		        encryptor.init(Cipher.ENCRYPT_MODE, publicKey);

		        //加密
		        String plainText = pwd + "{" + salt + "}";
		        byte[] plainData = plainText.getBytes(StandardCharsets.UTF_8);
		        encryptor.update(plainData);
		        byte[] cipherData = encryptor.doFinal();

		        //将加密结果编码为字符串
		        ByteBuffer cipherBuffer = ByteBuffer.wrap(cipherData);
		        cipherBuffer = cipherBuffer.order(ByteOrder.LITTLE_ENDIAN);
		        StringBuilder cipherTextBuilder = new StringBuilder();
		        while (cipherBuffer.hasRemaining())
		        {
		            String hex = Integer.toHexString(cipherBuffer.get() & 0xFF);
		            if (hex.length() < 2)
		            {
		                cipherTextBuilder.append(0);
		            }
		            cipherTextBuilder.append(hex.toUpperCase());
		        }
		        String cipherText = cipherTextBuilder.toString();
		        return cipherText;
			
	}
	public static void main(String[] args) {
//		String xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?><Entities><Entity certNo=\"913505006692997883\" result=\"03\" /><Entity certNo=\"913505006692997883\" result=\"03\" /></Entities>";
		String xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?><Entity><Record><InfoID value=\"2622c437-51b4-4c2e-8f94-992367540a7d\" /><EntityName value=\"尤溪县亿森木业有限公司\" /><EntityType value=\"2\" description=\"法人\" /><EntityCertType value=\"28\" description=\"统一社会信用代码\" /><EntityCertNo value=\"91350426793777783F\" /><Reason value=\"其他有履行能力而拒不履行生效法律文书确定义务的\" /><LegalBasis value=\"一、尤溪县亿森林业有限公司应向陈小琴、周治光偿还借款本金520,000元并支付该款自2017年4月1日起至实际还款之日止按月利率1.5%计算的利息； 二、尤溪县亿森林业有限公司应向陈小琴、周治光支付截止2017年3月31日尚欠的借款利息93,600元； 三、尤溪县亿森林业有限公司应向陈小琴、周治光偿付为财产保全申请支出的财产保全申请费3588元。 上述金钱给付义务限尤溪县亿森木业有限公司于本判决生效之日十日内付清。 如尤溪县亿森林业有限公司未按指定的期间履行给付金钱义务，应当按照《中华人民共和国民事诉讼法》第二百五十三条之规定，加倍支付迟延履行期间的债务利息。 案件受理费9936元，减半收取计4968元，由尤溪县亿森林业有限公司负担。\" /><DecisionInstrumentNo value=\"(2018)闽0426民初361号\" /><PublishDate value=\"\" /><Organization value=\"尤溪县人民法院\" /><UpdatedTime value=\"2018-08-21 06:24:21\" /><CaseID value=\"163620181002000896\" /><CaseNo value=\"(2018)闽0426执874号\" /><CreateDate value=\"2018-04-20\" /><AppStatus value=\"4\" description=\"已发布\" /><Court value=\"尤溪县人民法院\" /><CourtGradingCode value=\"D48\" /><Progress value=\"1\" description=\"全部未履行\" /><Fulfilled value=\"\" /><Unfulfilled value=\"\" /><LegalPerson value=\"吴孝春\" /><EntityAge value=\"\" /><EntityGender value=\"\" description=\"\" /><ReasonForBlocking value=\"\" description=\"\" /><ReasonForRevoking value=\"\" description=\"\" /></Record><Record><InfoID value=\"9dfa7a0f-129c-4774-b867-bcb1b9d942c5\" /><EntityName value=\"尤溪县亿森木业有限公司\" /><EntityType value=\"2\" description=\"法人\" /><EntityCertType value=\"28\" description=\"统一社会信用代码\" /><EntityCertNo value=\"91350426793777783F\" /><Reason value=\"其他有履行能力而拒不履行生效法律文书确定义务的\" /><LegalBasis value=\"一、尤溪县亿森林业有限公司应于本判决生效之日起十日内向陈巧珠偿还借款本金440,000元及利息（其中借款本金100,000元自2017年4月17日开始计息，借款本金200,000元自2017年8月28日开始计息，借款本金140,000元2017年12月18日开始计息，均按月利率1.5%计息至实际还款之日止）； 二、尤溪县亿森林业有限公司应于本判决生效之日起十日内向陈巧珠支付双方已结算未付的利息款121,500元。 如尤溪县亿森林业有限公司未按指定的期间履行给付金钱义务，应当按照《中华人民共和国民事诉讼法》第二百五十三条之规定，加倍支付迟延履行期间的债务利息。 案件受理费9415元，减半收取计4707.50元，由尤溪县亿森林业有限公司负担。\" /><DecisionInstrumentNo value=\"(2018)闽0426民初362号\" /><PublishDate value=\"2018-07-04\" /><Organization value=\"尤溪县人民法院\" /><UpdatedTime value=\"2018-08-21 06:24:21\" /><CaseID value=\"163620181002000896\" /><CaseNo value=\"(2018)闽0426执876号\" /><CreateDate value=\"2018-04-20\" /><AppStatus value=\"4\" description=\"已发布\" /><Court value=\"尤溪县人民法院\" /><CourtGradingCode value=\"D48\" /><Progress value=\"1\" description=\"全部未履行\" /><Fulfilled value=\"\" /><Unfulfilled value=\"\" /><LegalPerson value=\"吴孝春\" /><EntityAge value=\"\" /><EntityGender value=\"\" description=\"\" /><ReasonForBlocking value=\"\" description=\"\" /><ReasonForRevoking value=\"\" description=\"\" /></Record></Entity>";
		try {
			Entity convertToJavaBean = JaxbXmlUtil.convertToJavaBean(xml, org.changneng.framework.frameworkbusiness.entity.unitedPunishment.Entity.class);
			System.out.println(JacksonUtils.toJsonString(convertToJavaBean));
			
			Entities entities = new Entities();
			List<Entity> list = new ArrayList<Entity>();
			for(int i=0;i<100;i++){
				
				Entity entity1 = new Entity();
				entity1.setCertNo(i+"");
				entity1.setName("李玉春");
				list.add(entity1);
			}
			entities.setEntities(list);
			String convertToXml = JaxbXmlUtil.convertToXml(entities);
			System.out.println(convertToXml);
			
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	@Override
	public void modifyRelatedFieldSituationHandle() {
		List<PPunishmentResult> results = pPunishmentResultMapper.selectAllResultLawEnforceIds();
		List<String> list = new ArrayList<String>();
		if(!ChangnengUtil.isNull(results)){
			for(PPunishmentResult pResult:results){
				list.add(pResult.getLawObjectId());
			}
		}

		// 这个给改了  直接把list写到sql里面了。  避免超过1000条数据报错的问题
		List<LawEnforceObjectWithBLOBs> lawEnforceObjects = lawEnforceObjectMapper.batchQueryLawEnforceObjectByIds(list);
		if(!ChangnengUtil.isNull(lawEnforceObjects)){
			for(LawEnforceObjectWithBLOBs obj:lawEnforceObjects){
				for(PPunishmentResult pResult:results){
					if(obj.getId().equals(pResult.getLawObjectId())){
						if(obj.getTypeCode().equals("1")){//企业
							if(ChangnengUtil.isNull(obj.getSocialCreditCode())){
								obj.setIsSupervise(0);
								obj.setSuperviseDate(null);
								lawEnforceObjectMapper.updateByPrimaryKeySelective(obj);
								pPunishmentHistoryMapper.deleteByLawObjectId(obj.getId());
								pResult.setIsvalid("2");
								pResult.setUpdatedate(new Date());
								pPunishmentResultMapper.updateByPrimaryKeySelective(pResult);
							}
							
						}else if(obj.getTypeCode().equals("2")||obj.getTypeCode().equals("3")){//个人
							if(ChangnengUtil.isNull(obj.getCardNumber())){
								obj.setIsSupervise(0);
								obj.setSuperviseDate(null);
								lawEnforceObjectMapper.updateByPrimaryKeySelective(obj);
								pPunishmentHistoryMapper.deleteByLawObjectId(obj.getId());
								pResult.setIsvalid("2");
								pResult.setUpdatedate(new Date());
								pPunishmentResultMapper.updateByPrimaryKeySelective(pResult);
							}
						}else{
							obj.setIsSupervise(0);
							obj.setSuperviseDate(null);
							lawEnforceObjectMapper.updateByPrimaryKeySelective(obj);
							pPunishmentHistoryMapper.deleteByLawObjectId(obj.getId());
							pResult.setIsvalid("2");
							pResult.setUpdatedate(new Date());
							pPunishmentResultMapper.updateByPrimaryKeySelective(pResult);
						}
						break;
						
					}
				}
			}
		}
	}

}
