package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.TaskNode;

public interface TaskNodeMapper {
    int deleteByPrimaryKey(String id);

    int insert(TaskNode record);

    int insertSelective(TaskNode record);

    TaskNode selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(TaskNode record);

    int updateByPrimaryKey(TaskNode record);
    /**
     * 根据任务的信息查询最后一条环节信息
     * @param taskId
     * @return
     */
	TaskNode selectByTaskId(@Param("taskId") String taskId);
	
	/**
	 * 该方法只提供给删除该主干任务
	 * 但且仅当是该发起人操作，并处于任务任务分配环节
	 * @param taskId
	 * @return
	 */
	TaskNode selectLaunchTaskNodeByTaskId(@Param("taskId") String taskId,@Param("nodeCode") String nodeCode);
	/**
	 * 查询主干任务环节集合
	 * @param taskId
	 * @return
	 */
	List<TaskNode> getTaskNodeListByTaskId(@Param("taskId") String taskId);
	
	/**
	 * 删除环节信息
	 * @param taskId 任务id
	 * @param nodeCode 环节名称
	 * @return
	 */
	int deleteTaskNodeByTaskIdAndCode(@Param("taskId") String taskId,@Param("nodeCode") String nodeCode);
	
	/**
	 * 查询该任务当前所处环节，有且只有一条
	 * @param taskId
	 * @return
	 */
	TaskNode selectByTaskPresentLink(@Param("taskId") String taskId);
}