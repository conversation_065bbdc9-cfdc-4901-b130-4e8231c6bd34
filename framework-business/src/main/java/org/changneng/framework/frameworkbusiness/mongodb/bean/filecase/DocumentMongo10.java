package org.changneng.framework.frameworkbusiness.mongodb.bean.filecase;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文书制作-10-行政处罚事先告知书
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年11月22日 下午8:13:19
 */
@Document(collection = "ExecuDocMongo")
public class DocumentMongo10 {
	@Id
	private String id;
	
	private String desicionNum1;
	
	private String desicionNum2;
	
	/**
	 * 当事人姓名
	 */
	private String lawObjectName;
	
	private String year;
	
	private String month;
	
	private String day;
	
	/**
	 * 违法行为
	 */
	private String illegalAction;
	
	/**
	 * 证据
	 */
	private String evidence;
	
	/**
	 * 违反的规定
	 */
	private String illegalLaw;
	
	/**
	 * 相关法律
	 */
	private String lawItem;
	
	/**
	 * 裁量基准
	 */
	private String discreStan;
	
	/**
	 * 处罚1
	 */
	private String penalty1;
	
	/**
	 * 处罚2
	 */
	private String penalty2;
	
	/**
	 * 联系人
	 */
	private String contact;
	
	/**
	 * 电话
	 */
	private String phone;
	
	private String address;
	
	/**
	 * 邮政编码
	 */
	private String postalNumber;
	
	private String userDept;
	
	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getDesicionNum1() {
		return desicionNum1;
	}

	public void setDesicionNum1(String desicionNum1) {
		this.desicionNum1 = desicionNum1;
	}

	public String getDesicionNum2() {
		return desicionNum2;
	}

	public void setDesicionNum2(String desicionNum2) {
		this.desicionNum2 = desicionNum2;
	}

	public String getLawObjectName() {
		return lawObjectName;
	}

	public void setLawObjectName(String lawObjectName) {
		this.lawObjectName = lawObjectName;
	}

	public String getYear() {
		return year;
	}

	public void setYear(String year) {
		this.year = year;
	}

	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public String getDay() {
		return day;
	}

	public void setDay(String day) {
		this.day = day;
	}

	public String getLawItem() {
		return lawItem;
	}

	public void setLawItem(String lawItem) {
		this.lawItem = lawItem;
	}

	public String getDiscreStan() {
		return discreStan;
	}

	public void setDiscreStan(String discreStan) {
		this.discreStan = discreStan;
	}

	public String getPenalty1() {
		return penalty1;
	}

	public void setPenalty1(String penalty1) {
		this.penalty1 = penalty1;
	}

	public String getPenalty2() {
		return penalty2;
	}

	public void setPenalty2(String penalty2) {
		this.penalty2 = penalty2;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getPostalNumber() {
		return postalNumber;
	}

	public void setPostalNumber(String postalNumber) {
		this.postalNumber = postalNumber;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getIllegalAction() {
		return illegalAction;
	}

	public void setIllegalAction(String illegalAction) {
		this.illegalAction = illegalAction;
	}

	public String getEvidence() {
		return evidence;
	}

	public void setEvidence(String evidence) {
		this.evidence = evidence;
	}

	public String getIllegalLaw() {
		return illegalLaw;
	}

	public void setIllegalLaw(String illegalLaw) {
		this.illegalLaw = illegalLaw;
	}

	public String getUserDept() {
		return userDept;
	}

	public void setUserDept(String userDept) {
		this.userDept = userDept;
	}
}
