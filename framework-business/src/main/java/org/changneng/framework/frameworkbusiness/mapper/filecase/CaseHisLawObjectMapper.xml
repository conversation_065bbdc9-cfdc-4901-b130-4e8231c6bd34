<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.filecase.CaseHisLawObjectMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="CASE_ID" jdbcType="VARCHAR" property="caseId" />
    <result column="LAW_OBJECT_ID" jdbcType="VARCHAR" property="lawObjectId" />
    <result column="TYPE_CODE" jdbcType="VARCHAR" property="typeCode" />
    <result column="TYPE_NAME" jdbcType="VARCHAR" property="typeName" />
    <result column="LAW_OBJECT_NUMBER" jdbcType="VARCHAR" property="lawObjectNumber" />
    <result column="LAW_OBJECT_NAME" jdbcType="VARCHAR" property="lawObjectName" />
    <result column="BELONG_AREA_ID" jdbcType="VARCHAR" property="belongAreaId" />
    <result column="BELONG_AREA_NAME" jdbcType="VARCHAR" property="belongAreaName" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="POWER_AREA_ID" jdbcType="VARCHAR" property="powerAreaId" />
    <result column="POWER_AREA_NAME" jdbcType="VARCHAR" property="powerAreaName" />
    <result column="GIS_COORDINATE_X" jdbcType="VARCHAR" property="gisCoordinateX" />
    <result column="GIS_COORDINATE_Y" jdbcType="VARCHAR" property="gisCoordinateY" />
    <result column="LICENSE_NO" jdbcType="VARCHAR" property="licenseNo" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="SOCIAL_CREDIT_CODE" jdbcType="VARCHAR" property="socialCreditCode" />
    <result column="INDUSTRY_TYPE_CODE" jdbcType="VARCHAR" property="industryTypeCode" />
    <result column="INDUSTRY_TYPE_NAME" jdbcType="VARCHAR" property="industryTypeName" />
    <result column="LEVEL_CODE" jdbcType="VARCHAR" property="levelCode" />
    <result column="LEVEL_NAME" jdbcType="VARCHAR" property="levelName" />
    <result column="LEGAL_PERSON" jdbcType="VARCHAR" property="legalPerson" />
    <result column="LEGAL_PHONE" jdbcType="VARCHAR" property="legalPhone" />
    <result column="CHARGE_PERSON" jdbcType="VARCHAR" property="chargePerson" />
    <result column="CHARGE_PERSON_PHONE" jdbcType="VARCHAR" property="chargePersonPhone" />
    <result column="IS_KEY_SOURCE" jdbcType="DECIMAL" property="isKeySource" />
    <result column="IS_ONLINE_MONITOR" jdbcType="DECIMAL" property="isOnlineMonitor" />
    <result column="PRODUCT_STATE_CODE" jdbcType="VARCHAR" property="productStateCode" />
    <result column="PRODUCT_STATE_NAME" jdbcType="VARCHAR" property="productStateName" />
    <result column="IS_SOLIDWASTE_OPERUNIT" jdbcType="DECIMAL" property="isSolidwasteOperunit" />
    <result column="IS_SOLIDWASTE_CREATEUNIT" jdbcType="DECIMAL" property="isSolidwasteCreateunit" />
    <result column="IS_RISK_SOURCE" jdbcType="DECIMAL" property="isRiskSource" />
    <result column="IS_OUTFALL_STANDARD" jdbcType="DECIMAL" property="isOutfallStandard" />
    <result column="PERSON_NATURE_CODE" jdbcType="VARCHAR" property="personNatureCode" />
    <result column="PERSON_NATURE_NAME" jdbcType="VARCHAR" property="personNatureName" />
    <result column="BELONG_LEVEL_CODE" jdbcType="VARCHAR" property="belongLevelCode" />
    <result column="BELONG_LEVEL_NAME" jdbcType="VARCHAR" property="belongLevelName" />
    <result column="IS_LISTED_COMPANY" jdbcType="DECIMAL" property="isListedCompany" />
    <result column="STOCK_CODE" jdbcType="VARCHAR" property="stockCode" />
    <result column="IS_GROUP_COMPANY" jdbcType="DECIMAL" property="isGroupCompany" />
    <result column="GROUP_COMPANY_NAME" jdbcType="VARCHAR" property="groupCompanyName" />
    <result column="GROUP_COMPANY_ORGCODE" jdbcType="VARCHAR" property="groupCompanyOrgcode" />
    <result column="GROUP_COMPANY_STOCKCODE" jdbcType="VARCHAR" property="groupCompanyStockcode" />
    <result column="OBJECT_DESC" jdbcType="VARCHAR" property="objectDesc" />
    <result column="PERSONCARD_TYPE_CODE" jdbcType="VARCHAR" property="personcardTypeCode" />
    <result column="PERSONCARD_TYPE_NAME" jdbcType="VARCHAR" property="personcardTypeName" />
    <result column="CARD_NUMBER" jdbcType="VARCHAR" property="cardNumber" />
    <result column="CARD_TYPE_CODE" jdbcType="VARCHAR" property="cardTypeCode" />
    <result column="CARD_TYPE_NAME" jdbcType="VARCHAR" property="cardTypeName" />
    <result column="PROTECTED_AREA_DESC" jdbcType="VARCHAR" property="protectedAreaDesc" />
    <result column="MAIN_PROTECT_OBJECT" jdbcType="VARCHAR" property="mainProtectObject" />
    <result column="IS_SPECIAL_ORG" jdbcType="DECIMAL" property="isSpecialOrg" />
    <result column="MANAGE_ORG_NAME" jdbcType="VARCHAR" property="manageOrgName" />
    <result column="SEX" jdbcType="DECIMAL" property="sex" />
    <result column="DELMARK" jdbcType="DECIMAL" property="delmark" />
    <result column="RANDOM_STATE" jdbcType="VARCHAR" property="randomState" />
    <result column="DANGER_COEFFICIENT" jdbcType="VARCHAR" property="dangerCoefficient" />
    <result column="UPDATETIME" jdbcType="TIMESTAMP" property="updatetime" />
    <result column="CREATETIME" jdbcType="TIMESTAMP" property="createtime" />
    <result column="RANDOM_ATTR_NAME" jdbcType="VARCHAR" property="randomAttrName" />
    <result column="OBJECT_TYPE_CODE" jdbcType="VARCHAR" property="objectTypeCode" />
   <result column="BELONG_AREA_CITY_ID" jdbcType="VARCHAR" property="belongAreaCityId" />
   <result column="BELONG_AREA_CITY_NAME" jdbcType="VARCHAR" property="belongAreaCityName" />
   <result column="PROTECT_NATURE" jdbcType="VARCHAR" property="protectNature" />
   <result column="IS_LAW_PASS" jdbcType="DECIMAL" property="isLawPass" />
   <result column="STATE" jdbcType="DECIMAL" property="state" />
   <result column ="LASTUPDATEMAN" jdbcType="VARCHAR" property="lastupdateman"/>
   
   <result column="LEGAL_MAN_ID_CARD" jdbcType="VARCHAR" property="legalManIdCard" />
   <result column="CHARGE_MAN_ID_CARD" jdbcType="VARCHAR" property="chargeManIdCard" />
   <result column="WSCD" jdbcType="VARCHAR" property="WSCD" />
   <result column="WSNM" jdbcType="VARCHAR" property="WSNM" />
   <!-- v2.0.3新增begin-->
    <result column="POLLUTION_SOURCE_TYPE" jdbcType="VARCHAR" property="pollutionSourceType" />
    <result column="POLLUTION_SOURCE_TYPE_NAME" jdbcType="VARCHAR" property="pollutionSourceTypeName" />
    <result column="XSLW" jdbcType="VARCHAR" property="xslw" />
    <result column="SFFSCSDW" jdbcType="VARCHAR" property="sffscsdw" />
    <result column="SFFQCSDW" jdbcType="VARCHAR" property="sffqcsdw" />
    <result column="SFWFCSDW" jdbcType="VARCHAR" property="sfwfcsdw" />
    <result column="SFWFJYDW" jdbcType="VARCHAR" property="sfwfjydw" />
    <result column="SYPWXKHYJSGF_CODE" jdbcType="VARCHAR" property="sypwxkhyjsgfCode" />
    <result column="SYPWXKHYJSGF_NAME" jdbcType="VARCHAR" property="sypwxkhyjsgfName" />
    <result column="IS_CANCELLED" jdbcType="VARCHAR" property="isCancelled" />
    <!-- v2.0.3新增end-->
    <result column="GIS_COORDINATE_X84" jdbcType="VARCHAR" property="gisCoordinateX84" />
    <result column="GIS_COORDINATE_Y84" jdbcType="VARCHAR" property="gisCoordinateY84" />
    
    <result column="TAXATION_NUMBER" jdbcType="VARCHAR" property="taxationNumber" />
    <result column="COMPANY_NUMBER" jdbcType="VARCHAR" property="companyNumber" />
    <result column="SOCIAL_NUMBER" jdbcType="VARCHAR" property="socialNumber" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, CASE_ID, LAW_OBJECT_ID, TYPE_CODE, TYPE_NAME, LAW_OBJECT_NUMBER, LAW_OBJECT_NAME, 
    BELONG_AREA_ID, BELONG_AREA_NAME, ADDRESS, POWER_AREA_ID, POWER_AREA_NAME, GIS_COORDINATE_X, GIS_COORDINATE_X84, GIS_COORDINATE_Y84,
    GIS_COORDINATE_Y, LICENSE_NO, ORG_CODE, SOCIAL_CREDIT_CODE, INDUSTRY_TYPE_CODE, INDUSTRY_TYPE_NAME, 
    LEVEL_CODE, LEVEL_NAME, LEGAL_PERSON, LEGAL_PHONE, CHARGE_PERSON, CHARGE_PERSON_PHONE, 
    IS_KEY_SOURCE, IS_ONLINE_MONITOR, PRODUCT_STATE_CODE, PRODUCT_STATE_NAME, IS_SOLIDWASTE_OPERUNIT, 
    IS_SOLIDWASTE_CREATEUNIT, IS_RISK_SOURCE, IS_OUTFALL_STANDARD, PERSON_NATURE_CODE, LEGAL_MAN_ID_CARD,	CHARGE_MAN_ID_CARD,
    PERSON_NATURE_NAME, BELONG_LEVEL_CODE, BELONG_LEVEL_NAME, IS_LISTED_COMPANY, STOCK_CODE, 
    IS_GROUP_COMPANY, GROUP_COMPANY_NAME, GROUP_COMPANY_ORGCODE, GROUP_COMPANY_STOCKCODE, 
    OBJECT_DESC, PERSONCARD_TYPE_CODE, PERSONCARD_TYPE_NAME, CARD_NUMBER, CARD_TYPE_CODE, 
    CARD_TYPE_NAME, PROTECTED_AREA_DESC, MAIN_PROTECT_OBJECT, IS_SPECIAL_ORG, MANAGE_ORG_NAME, 
    SEX, DELMARK, RANDOM_STATE, DANGER_COEFFICIENT, UPDATETIME, CREATETIME, RANDOM_ATTR_NAME,
    OBJECT_TYPE_CODE,BELONG_AREA_CITY_ID,BELONG_AREA_CITY_NAME,PROTECT_NATURE,IS_LAW_PASS,STATE,LASTUPDATEMAN,WSCD,WSNM,
    POLLUTION_SOURCE_TYPE,POLLUTION_SOURCE_TYPE_NAME,XSLW,SFFSCSDW,SFFQCSDW,SFWFCSDW,SFWFJYDW,SYPWXKHYJSGF_CODE,SYPWXKHYJSGF_NAME,IS_CANCELLED,
    TAXATION_NUMBER,COMPANY_NUMBER,SOCIAL_NUMBER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_HIS_LAW_OBJECT
    where ID = #{id,jdbcType=VARCHAR}
  </select>
    <select id="selectByCaseId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_HIS_LAW_OBJECT
    where CASE_ID = #{caseId,jdbcType=VARCHAR}
  </select>
  <select id="selectByLawObjId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_HIS_LAW_OBJECT
    where LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR} and case_id=#{caseId,jdbcType=VARCHAR}
  </select>
  
  <select id="selectAreaCodeByCaseId" parameterType="java.lang.String" resultType="java.lang.String">
  	select belong_area_id 
  	from CASE_HIS_LAW_OBJECT
  	where case_id=#{caseId, jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CASE_HIS_LAW_OBJECT
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    insert into CASE_HIS_LAW_OBJECT (ID, CASE_ID, LAW_OBJECT_ID, 
      TYPE_CODE, TYPE_NAME, LAW_OBJECT_NUMBER, 
      LAW_OBJECT_NAME, BELONG_AREA_ID, BELONG_AREA_NAME, 
      ADDRESS, POWER_AREA_ID, POWER_AREA_NAME, 
      GIS_COORDINATE_X, GIS_COORDINATE_Y, LICENSE_NO, 
      ORG_CODE, SOCIAL_CREDIT_CODE, INDUSTRY_TYPE_CODE, 
      INDUSTRY_TYPE_NAME, LEVEL_CODE, LEVEL_NAME, 
      LEGAL_PERSON, LEGAL_PHONE, CHARGE_PERSON, 
      CHARGE_PERSON_PHONE, IS_KEY_SOURCE, IS_ONLINE_MONITOR, 
      PRODUCT_STATE_CODE, PRODUCT_STATE_NAME, IS_SOLIDWASTE_OPERUNIT, 
      IS_SOLIDWASTE_CREATEUNIT, IS_RISK_SOURCE, IS_OUTFALL_STANDARD, 
      PERSON_NATURE_CODE, PERSON_NATURE_NAME, BELONG_LEVEL_CODE, 
      BELONG_LEVEL_NAME, IS_LISTED_COMPANY, STOCK_CODE, 
      IS_GROUP_COMPANY, GROUP_COMPANY_NAME, GROUP_COMPANY_ORGCODE, 
      GROUP_COMPANY_STOCKCODE, OBJECT_DESC, PERSONCARD_TYPE_CODE, 
      PERSONCARD_TYPE_NAME, CARD_NUMBER, CARD_TYPE_CODE, 
      CARD_TYPE_NAME, PROTECTED_AREA_DESC, MAIN_PROTECT_OBJECT, 
      IS_SPECIAL_ORG, MANAGE_ORG_NAME, SEX, 
      DELMARK, RANDOM_STATE, DANGER_COEFFICIENT, 
      UPDATETIME, CREATETIME, RANDOM_ATTR_NAME,OBJECT_TYPE_CODE,
      BELONG_AREA_CITY_ID,BELONG_AREA_CITY_NAME,PROTECT_NATURE,IS_LAW_PASS,STATE,LASTUPDATEMAN,
      POLLUTION_SOURCE_TYPE,POLLUTION_SOURCE_TYPE_NAME,XSLW,SFFSCSDW,SFFQCSDW,SFWFCSDW,SFWFJYDW,SYPWXKHYJSGF_CODE,SYPWXKHYJSGF_NAME,IS_CANCELLED,GIS_COORDINATE_X, GIS_COORDINATE_Y
      )
          values (#{id,jdbcType=VARCHAR}, #{caseId,jdbcType=VARCHAR}, #{lawObjectId,jdbcType=VARCHAR}, 
      #{typeCode,jdbcType=VARCHAR}, #{typeName,jdbcType=VARCHAR}, #{lawObjectNumber,jdbcType=VARCHAR}, 
      #{lawObjectName,jdbcType=VARCHAR}, #{belongAreaId,jdbcType=VARCHAR}, #{belongAreaName,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{powerAreaId,jdbcType=VARCHAR}, #{powerAreaName,jdbcType=VARCHAR}, 
      #{gisCoordinateX,jdbcType=VARCHAR}, #{gisCoordinateY,jdbcType=VARCHAR}, #{licenseNo,jdbcType=VARCHAR}, 
      #{orgCode,jdbcType=VARCHAR}, #{socialCreditCode,jdbcType=VARCHAR}, #{industryTypeCode,jdbcType=VARCHAR}, 
      #{industryTypeName,jdbcType=VARCHAR}, #{levelCode,jdbcType=VARCHAR}, #{levelName,jdbcType=VARCHAR}, 
      #{legalPerson,jdbcType=VARCHAR}, #{legalPhone,jdbcType=VARCHAR}, #{chargePerson,jdbcType=VARCHAR}, 
      #{chargePersonPhone,jdbcType=VARCHAR}, #{isKeySource,jdbcType=DECIMAL}, #{isOnlineMonitor,jdbcType=DECIMAL}, 
      #{productStateCode,jdbcType=VARCHAR}, #{productStateName,jdbcType=VARCHAR}, #{isSolidwasteOperunit,jdbcType=DECIMAL}, 
      #{isSolidwasteCreateunit,jdbcType=DECIMAL}, #{isRiskSource,jdbcType=DECIMAL}, #{isOutfallStandard,jdbcType=DECIMAL}, 
      #{personNatureCode,jdbcType=VARCHAR}, #{personNatureName,jdbcType=VARCHAR}, #{belongLevelCode,jdbcType=VARCHAR}, 
      #{belongLevelName,jdbcType=VARCHAR}, #{isListedCompany,jdbcType=DECIMAL}, #{stockCode,jdbcType=VARCHAR}, 
      #{isGroupCompany,jdbcType=DECIMAL}, #{groupCompanyName,jdbcType=VARCHAR}, #{groupCompanyOrgcode,jdbcType=VARCHAR}, 
      #{groupCompanyStockcode,jdbcType=VARCHAR}, #{objectDesc,jdbcType=VARCHAR}, #{personcardTypeCode,jdbcType=VARCHAR}, 
      #{personcardTypeName,jdbcType=VARCHAR}, #{cardNumber,jdbcType=VARCHAR}, #{cardTypeCode,jdbcType=VARCHAR}, 
      #{cardTypeName,jdbcType=VARCHAR}, #{protectedAreaDesc,jdbcType=VARCHAR}, #{mainProtectObject,jdbcType=VARCHAR}, 
      #{isSpecialOrg,jdbcType=DECIMAL}, #{manageOrgName,jdbcType=VARCHAR}, #{sex,jdbcType=DECIMAL}, 
      #{delmark,jdbcType=DECIMAL}, #{randomState,jdbcType=VARCHAR}, #{dangerCoefficient,jdbcType=VARCHAR}, 
      #{updatetime,jdbcType=TIMESTAMP}, #{createtime,jdbcType=TIMESTAMP},
      #{randomAttrName,jdbcType=VARCHAR},#{objectTypeCode,jdbcType=VARCHAR},
      #{belongAreaCityId,jdbcType=VARCHAR},#{belongAreaCityName,jdbcType=VARCHAR},
      #{protectNature,jdbcType=VARCHAR},#{isLawPass,jdbcType=DECIMAL},#{state,jdbcType=DECIMAL},#{lastupdateman,jdbcType=VARCHAR},
      #{pollutionSourceType,jdbcType=VARCHAR},#{pollutionSourceTypeName,jdbcType=VARCHAR},#{xslw,jdbcType=VARCHAR}),
      #{sffscsdw,jdbcType=VARCHAR},#{sffqcsdw,jdbcType=VARCHAR}),#{sfwfcsdw,jdbcType=VARCHAR},#{sfwfjydw,jdbcType=VARCHAR},
      #{sypwxkhyjsgfCode,jdbcType=VARCHAR}),#{sypwxkhyjsgfName,jdbcType=VARCHAR},#{isCancelled,jdbcType=VARCHAR},#{gisCoordinateX84,jdbcType=VARCHAR}, #{gisCoordinateY84,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into CASE_HIS_LAW_OBJECT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="caseId != null">
        CASE_ID,
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID,
      </if>
      <if test="typeCode != null">
        TYPE_CODE,
      </if>
      <if test="typeName != null">
        TYPE_NAME,
      </if>
      <if test="lawObjectNumber != null">
        LAW_OBJECT_NUMBER,
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME,
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID,
      </if>
      <if test="belongAreaName != null">
        BELONG_AREA_NAME,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="powerAreaId != null">
        POWER_AREA_ID,
      </if>
      <if test="powerAreaName != null">
        POWER_AREA_NAME,
      </if>
      <if test="gisCoordinateX != null">
        GIS_COORDINATE_X,
      </if>
      <if test="gisCoordinateY != null">
        GIS_COORDINATE_Y,
      </if>
      <if test="licenseNo != null">
        LICENSE_NO,
      </if>
      <if test="orgCode != null">
        ORG_CODE,
      </if>
      <if test="socialCreditCode != null">
        SOCIAL_CREDIT_CODE,
      </if>
      <if test="industryTypeCode != null">
        INDUSTRY_TYPE_CODE,
      </if>
      <if test="industryTypeName != null">
        INDUSTRY_TYPE_NAME,
      </if>
      <if test="levelCode != null">
        LEVEL_CODE,
      </if>
      <if test="levelName != null">
        LEVEL_NAME,
      </if>
      <if test="legalPerson != null">
        LEGAL_PERSON,
      </if>
      <if test="legalPhone != null">
        LEGAL_PHONE,
      </if>
      <if test="chargePerson != null">
        CHARGE_PERSON,
      </if>
      <if test="chargePersonPhone != null">
        CHARGE_PERSON_PHONE,
      </if>
      <if test="isKeySource != null">
        IS_KEY_SOURCE,
      </if>
      <if test="isOnlineMonitor != null">
        IS_ONLINE_MONITOR,
      </if>
      <if test="productStateCode != null">
        PRODUCT_STATE_CODE,
      </if>
      <if test="productStateName != null">
        PRODUCT_STATE_NAME,
      </if>
      <if test="isSolidwasteOperunit != null">
        IS_SOLIDWASTE_OPERUNIT,
      </if>
      <if test="isSolidwasteCreateunit != null">
        IS_SOLIDWASTE_CREATEUNIT,
      </if>
      <if test="isRiskSource != null">
        IS_RISK_SOURCE,
      </if>
      <if test="isOutfallStandard != null">
        IS_OUTFALL_STANDARD,
      </if>
      <if test="personNatureCode != null">
        PERSON_NATURE_CODE,
      </if>
      <if test="personNatureName != null">
        PERSON_NATURE_NAME,
      </if>
      <if test="belongLevelCode != null">
        BELONG_LEVEL_CODE,
      </if>
      <if test="belongLevelName != null">
        BELONG_LEVEL_NAME,
      </if>
      <if test="isListedCompany != null">
        IS_LISTED_COMPANY,
      </if>
      <if test="stockCode != null">
        STOCK_CODE,
      </if>
      <if test="isGroupCompany != null">
        IS_GROUP_COMPANY,
      </if>
      <if test="groupCompanyName != null">
        GROUP_COMPANY_NAME,
      </if>
      <if test="groupCompanyOrgcode != null">
        GROUP_COMPANY_ORGCODE,
      </if>
      <if test="groupCompanyStockcode != null">
        GROUP_COMPANY_STOCKCODE,
      </if>
      <if test="objectDesc != null">
        OBJECT_DESC,
      </if>
      <if test="personcardTypeCode != null">
        PERSONCARD_TYPE_CODE,
      </if>
      <if test="personcardTypeName != null">
        PERSONCARD_TYPE_NAME,
      </if>
      <if test="cardNumber != null">
        CARD_NUMBER,
      </if>
      <if test="cardTypeCode != null">
        CARD_TYPE_CODE,
      </if>
      <if test="cardTypeName != null">
        CARD_TYPE_NAME,
      </if>
      <if test="protectedAreaDesc != null">
        PROTECTED_AREA_DESC,
      </if>
      <if test="mainProtectObject != null">
        MAIN_PROTECT_OBJECT,
      </if>
      <if test="isSpecialOrg != null">
        IS_SPECIAL_ORG,
      </if>
      <if test="manageOrgName != null">
        MANAGE_ORG_NAME,
      </if>
      <if test="sex != null">
        SEX,
      </if>
      <if test="delmark != null">
        DELMARK,
      </if>
      <if test="randomState != null">
        RANDOM_STATE,
      </if>
      <if test="dangerCoefficient != null">
        DANGER_COEFFICIENT,
      </if>
      <if test="updatetime != null">
        UPDATETIME,
      </if>
      <if test="createtime != null">
        CREATETIME,
      </if>
      <if test="randomAttrName != null">
        RANDOM_ATTR_NAME,
      </if>
      <if test="objectTypeCode != null">
        OBJECT_TYPE_CODE,
      </if>
        <if test="belongAreaCityId != null">
        BELONG_AREA_CITY_ID,
      </if>
        <if test="belongAreaCityName != null">
        BELONG_AREA_CITY_NAME,
      </if>
        <if test="protectNature != null">
        PROTECT_NATURE,
      </if>
        <if test="lastupdateman != null">
        LASTUPDATEMAN,
      </if>
        <if test="isLawPass != null">
        IS_LAW_PASS,
      </if>
        <if test="state != null">
        STATE,
      </if>
      <if test="legalManIdCard != null">
  		LEGAL_MAN_ID_CARD,
	  </if>
	  <if test="chargeManIdCard != null">
	    CHARGE_MAN_ID_CARD,
	  </if>
	  <if test="WSCD != null">
	    WSCD,
	  </if>
	  <if test="WSNM != null">
	    WSNM,
	  </if>
	  <if test="pollutionSourceType != null">
      	POLLUTION_SOURCE_TYPE,
      </if>
      <if test="pollutionSourceTypeName != null">
      	POLLUTION_SOURCE_TYPE_NAME,
      </if>
      <if test="xslw != null">
      	XSLW,
      </if>
      <if test="sffscsdw != null">
      	SFFSCSDW,
      </if>
      <if test="sffqcsdw != null">
      	SFFQCSDW,
      </if>
      <if test="sfwfcsdw != null">
      	SFWFCSDW,
      </if>
      <if test="sfwfjydw != null">
      	SFWFJYDW,
      </if>
      <if test="sypwxkhyjsgfCode != null">
      	SYPWXKHYJSGF_CODE,
      </if>
      <if test="sypwxkhyjsgfName != null">
      	SYPWXKHYJSGF_NAME,
      </if>
      <if test="isCancelled != null">
      	IS_CANCELLED,
      </if>
      <if test="gisCoordinateX84 != null">
        GIS_COORDINATE_X84,
      </if>
      <if test="gisCoordinateY84 != null">
        GIS_COORDINATE_Y84,
      </if>
      <if test="taxationNumber != null">
        TAXATION_NUMBER,
      </if>
      <if test="companyNumber != null">
        COMPANY_NUMBER,
      </if>
      <if test="socialNumber != null">
        SOCIAL_NUMBER,
      </if>
    </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectNumber != null">
        #{lawObjectNumber,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaName != null">
        #{belongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaId != null">
        #{powerAreaId,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaName != null">
        #{powerAreaName,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX != null">
        #{gisCoordinateX,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY != null">
        #{gisCoordinateY,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditCode != null">
        #{socialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeCode != null">
        #{industryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeName != null">
        #{industryTypeName,jdbcType=VARCHAR},
      </if>
      <if test="levelCode != null">
        #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        #{chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="chargePersonPhone != null">
        #{chargePersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="isKeySource != null">
        #{isKeySource,jdbcType=DECIMAL},
      </if>
      <if test="isOnlineMonitor != null">
        #{isOnlineMonitor,jdbcType=DECIMAL},
      </if>
      <if test="productStateCode != null">
        #{productStateCode,jdbcType=VARCHAR},
      </if>
      <if test="productStateName != null">
        #{productStateName,jdbcType=VARCHAR},
      </if>
      <if test="isSolidwasteOperunit != null">
        #{isSolidwasteOperunit,jdbcType=DECIMAL},
      </if>
      <if test="isSolidwasteCreateunit != null">
        #{isSolidwasteCreateunit,jdbcType=DECIMAL},
      </if>
      <if test="isRiskSource != null">
        #{isRiskSource,jdbcType=DECIMAL},
      </if>
      <if test="isOutfallStandard != null">
        #{isOutfallStandard,jdbcType=DECIMAL},
      </if>
      <if test="personNatureCode != null">
        #{personNatureCode,jdbcType=VARCHAR},
      </if>
      <if test="personNatureName != null">
        #{personNatureName,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelCode != null">
        #{belongLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelName != null">
        #{belongLevelName,jdbcType=VARCHAR},
      </if>
      <if test="isListedCompany != null">
        #{isListedCompany,jdbcType=DECIMAL},
      </if>
      <if test="stockCode != null">
        #{stockCode,jdbcType=VARCHAR},
      </if>
      <if test="isGroupCompany != null">
        #{isGroupCompany,jdbcType=DECIMAL},
      </if>
      <if test="groupCompanyName != null">
        #{groupCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyOrgcode != null">
        #{groupCompanyOrgcode,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyStockcode != null">
        #{groupCompanyStockcode,jdbcType=VARCHAR},
      </if>
      <if test="objectDesc != null">
        #{objectDesc,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeCode != null">
        #{personcardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeName != null">
        #{personcardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="cardNumber != null">
        #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeCode != null">
        #{cardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeName != null">
        #{cardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="protectedAreaDesc != null">
        #{protectedAreaDesc,jdbcType=VARCHAR},
      </if>
      <if test="mainProtectObject != null">
        #{mainProtectObject,jdbcType=VARCHAR},
      </if>
      <if test="isSpecialOrg != null">
        #{isSpecialOrg,jdbcType=DECIMAL},
      </if>
      <if test="manageOrgName != null">
        #{manageOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=DECIMAL},
      </if>
      <if test="delmark != null">
        #{delmark,jdbcType=DECIMAL},
      </if>
      <if test="randomState != null">
        #{randomState,jdbcType=VARCHAR},
      </if>
      <if test="dangerCoefficient != null">
        #{dangerCoefficient,jdbcType=VARCHAR},
      </if>
      <if test="updatetime != null">
        #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createtime != null">
        #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="randomAttrName != null">
        #{randomAttrName,jdbcType=VARCHAR},
      </if>
       <if test="objectTypeCode != null">
        #{objectTypeCode,jdbcType=VARCHAR},
      </if>
       <if test="belongAreaCityId != null">
        #{belongAreaCityId,jdbcType=VARCHAR},
      </if>
      
       <if test="belongAreaCityName != null">
        #{belongAreaCityName,jdbcType=VARCHAR},
      </if>
      
       <if test="protectNature != null">
        #{protectNature,jdbcType=VARCHAR},
      </if>
      
       <if test="lastupdateman != null">
        #{lastupdateman,jdbcType=VARCHAR},
      </if>
      <if test="isLawPass != null">
        #{isLawPass,jdbcType=DECIMAL},
      </if>
      <if test="state != null">
        #{state,jdbcType=DECIMAL},
      </if>
      <if test="legalManIdCard != null">
        #{legalManIdCard,jdbcType=VARCHAR},
      </if>
      <if test="chargeManIdCard != null">
        #{chargeManIdCard,jdbcType=VARCHAR},
      </if>
      <if test="WSCD != null">
        #{WSCD,jdbcType=VARCHAR},
      </if>
      <if test="WSNM != null">
        #{WSNM,jdbcType=VARCHAR},
      </if>
      <if test="pollutionSourceType != null">
      	 #{pollutionSourceType,jdbcType=VARCHAR},
      </if>
      <if test="pollutionSourceTypeName != null">
      	 #{pollutionSourceTypeName,jdbcType=VARCHAR},
      </if>
      <if test="xslw != null">
      	 #{xslw,jdbcType=VARCHAR},
      </if>
      <if test="sffscsdw != null">
      	 #{sffscsdw,jdbcType=VARCHAR},
      </if>
      <if test="sffqcsdw != null">
      	 #{sffqcsdw,jdbcType=VARCHAR},
      </if>
      <if test="sfwfcsdw != null">
      	 #{sfwfcsdw,jdbcType=VARCHAR},
      </if>
      <if test="sfwfjydw != null">
      	 #{sfwfjydw,jdbcType=VARCHAR},
      </if>
      <if test="sypwxkhyjsgfCode != null">
      	 #{sypwxkhyjsgfCode,jdbcType=VARCHAR},
      </if>
      <if test="sypwxkhyjsgfName != null">
      	 #{sypwxkhyjsgfName,jdbcType=VARCHAR},
      </if>
      <if test="isCancelled != null">
      	 #{isCancelled,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX84 != null">
         #{gisCoordinateX84,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY84 != null">
         #{gisCoordinateY84,jdbcType=VARCHAR},
      </if>
      <if test="taxationNumber != null">
        #{taxationNumber,jdbcType=VARCHAR},
      </if>
      <if test="companyNumber != null">
        #{companyNumber,jdbcType=VARCHAR},
      </if>
      <if test="socialNumber != null">
        #{socialNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  
   <update id="updateByCaseIdSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    update CASE_HIS_LAW_OBJECT
    <set>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        TYPE_CODE = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectNumber != null">
        LAW_OBJECT_NUMBER = #{lawObjectNumber,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaName != null">
        BELONG_AREA_NAME = #{belongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaId != null">
        POWER_AREA_ID = #{powerAreaId,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaName != null">
        POWER_AREA_NAME = #{powerAreaName,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX != null">
        GIS_COORDINATE_X = #{gisCoordinateX,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY != null">
        GIS_COORDINATE_Y = #{gisCoordinateY,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX84 != null">
        GIS_COORDINATE_X84 = #{gisCoordinateX84,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY84 != null">
        GIS_COORDINATE_Y84 = #{gisCoordinateY84,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditCode != null">
        SOCIAL_CREDIT_CODE = #{socialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeCode != null">
        INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeName != null">
        INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR},
      </if>
      <if test="levelCode != null">
        LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        CHARGE_PERSON = #{chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="chargePersonPhone != null">
        CHARGE_PERSON_PHONE = #{chargePersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="isKeySource != null">
        IS_KEY_SOURCE = #{isKeySource,jdbcType=DECIMAL},
      </if>
      <if test="isOnlineMonitor != null">
        IS_ONLINE_MONITOR = #{isOnlineMonitor,jdbcType=DECIMAL},
      </if>
      <if test="productStateCode != null">
        PRODUCT_STATE_CODE = #{productStateCode,jdbcType=VARCHAR},
      </if>
      <if test="productStateName != null">
        PRODUCT_STATE_NAME = #{productStateName,jdbcType=VARCHAR},
      </if>
      <if test="isSolidwasteOperunit != null">
        IS_SOLIDWASTE_OPERUNIT = #{isSolidwasteOperunit,jdbcType=DECIMAL},
      </if>
      <if test="isSolidwasteCreateunit != null">
        IS_SOLIDWASTE_CREATEUNIT = #{isSolidwasteCreateunit,jdbcType=DECIMAL},
      </if>
      <if test="isRiskSource != null">
        IS_RISK_SOURCE = #{isRiskSource,jdbcType=DECIMAL},
      </if>
      <if test="isOutfallStandard != null">
        IS_OUTFALL_STANDARD = #{isOutfallStandard,jdbcType=DECIMAL},
      </if>
      <if test="personNatureCode != null">
        PERSON_NATURE_CODE = #{personNatureCode,jdbcType=VARCHAR},
      </if>
      <if test="personNatureName != null">
        PERSON_NATURE_NAME = #{personNatureName,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelCode != null">
        BELONG_LEVEL_CODE = #{belongLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelName != null">
        BELONG_LEVEL_NAME = #{belongLevelName,jdbcType=VARCHAR},
      </if>
      <if test="isListedCompany != null">
        IS_LISTED_COMPANY = #{isListedCompany,jdbcType=DECIMAL},
      </if>
      <if test="stockCode != null">
        STOCK_CODE = #{stockCode,jdbcType=VARCHAR},
      </if>
      <if test="isGroupCompany != null">
        IS_GROUP_COMPANY = #{isGroupCompany,jdbcType=DECIMAL},
      </if>
      <if test="groupCompanyName != null">
        GROUP_COMPANY_NAME = #{groupCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyOrgcode != null">
        GROUP_COMPANY_ORGCODE = #{groupCompanyOrgcode,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyStockcode != null">
        GROUP_COMPANY_STOCKCODE = #{groupCompanyStockcode,jdbcType=VARCHAR},
      </if>
      <if test="objectDesc != null">
        OBJECT_DESC = #{objectDesc,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeCode != null">
        PERSONCARD_TYPE_CODE = #{personcardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeName != null">
        PERSONCARD_TYPE_NAME = #{personcardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="cardNumber != null">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeCode != null">
        CARD_TYPE_CODE = #{cardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeName != null">
        CARD_TYPE_NAME = #{cardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="protectedAreaDesc != null">
        PROTECTED_AREA_DESC = #{protectedAreaDesc,jdbcType=VARCHAR},
      </if>
      <if test="mainProtectObject != null">
        MAIN_PROTECT_OBJECT = #{mainProtectObject,jdbcType=VARCHAR},
      </if>
      <if test="isSpecialOrg != null">
        IS_SPECIAL_ORG = #{isSpecialOrg,jdbcType=DECIMAL},
      </if>
      <if test="manageOrgName != null">
        MANAGE_ORG_NAME = #{manageOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=DECIMAL},
      </if>
      <if test="delmark != null">
        DELMARK = #{delmark,jdbcType=DECIMAL},
      </if>
      <if test="randomState != null">
        RANDOM_STATE = #{randomState,jdbcType=VARCHAR},
      </if>
      <if test="dangerCoefficient != null">
        DANGER_COEFFICIENT = #{dangerCoefficient,jdbcType=VARCHAR},
      </if>
      <if test="updatetime != null">
        UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createtime != null">
        CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="randomAttrName != null">
        RANDOM_ATTR_NAME = #{randomAttrName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaCityId != null">
        OBJECT_TYPE_CODE = #{belongAreaCityId,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeCode != null">
        BELONG_AREA_CITY_ID = #{objectTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaCityName != null">
        BELONG_AREA_CITY_NAME = #{belongAreaCityName,jdbcType=VARCHAR},
      </if>
      <if test="objectTypeCode != null">
        PROTECT_NATURE = #{objectTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="lastupdateman != null">
        LASTUPDATEMAN = #{lastupdateman,jdbcType=VARCHAR},
      </if>
       <if test="isLawPass != null">
        IS_LAW_PASS = #{isLawPass,jdbcType=DECIMAL},
      </if>
       <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="legalManIdCard != null">
        LEGAL_MAN_ID_CARD = #{legalManIdCard,jdbcType=VARCHAR}, 
      </if>
      <if test="chargeManIdCard != null">
        CHARGE_MAN_ID_CARD = #{chargeManIdCard,jdbcType=VARCHAR}, 
      </if>
       <if test="pollutionSourceType != null">
      	POLLUTION_SOURCE_TYPE =#{pollutionSourceType,jdbcType=VARCHAR}, 
      </if>
      <if test="pollutionSourceTypeName != null">
      	POLLUTION_SOURCE_TYPE_NAME =#{pollutionSourceTypeName,jdbcType=VARCHAR}, 
      </if>
      <if test="xslw != null">
      	XSLW =#{xslw,jdbcType=VARCHAR}, 
      </if>
      <if test="sffscsdw != null">
      	SFFSCSDW =#{sffscsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sffqcsdw != null">
      	SFFQCSDW =#{sffqcsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sfwfcsdw != null">
      	SFWFCSDW =#{sfwfcsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sfwfjydw != null">
      	SFWFJYDW =#{sfwfjydw,jdbcType=VARCHAR}, 
      </if>
      <if test="sypwxkhyjsgfCode != null">
      	SYPWXKHYJSGF_CODE =#{sypwxkhyjsgfCode,jdbcType=VARCHAR}, 
      </if>
      <if test="sypwxkhyjsgfName != null">
      	SYPWXKHYJSGF_NAME =#{sypwxkhyjsgfName,jdbcType=VARCHAR}, 
      </if>
      <if test="isCancelled != null">
      	IS_CANCELLED =#{isCancelled,jdbcType=VARCHAR}, 
      </if>
      <if test="taxationNumber != null">
        TAXATION_NUMBER = #{taxationNumber,jdbcType=VARCHAR},
      </if>
      <if test="companyNumber != null">
        COMPANY_NUMBER = #{companyNumber,jdbcType=VARCHAR},
      </if>
      <if test="socialNumber != null">
        SOCIAL_NUMBER = #{socialNumber,jdbcType=VARCHAR},
      </if>
    </set>
      where CASE_ID = #{caseId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    update CASE_HIS_LAW_OBJECT
    <set>
      <if test="caseId != null">
        CASE_ID = #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectId != null">
        LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        TYPE_CODE = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="typeName != null">
        TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectNumber != null">
        LAW_OBJECT_NUMBER = #{lawObjectNumber,jdbcType=VARCHAR},
      </if>
      <if test="lawObjectName != null">
        LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaId != null">
        BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      </if>
      <if test="belongAreaName != null">
        BELONG_AREA_NAME = #{belongAreaName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaId != null">
        POWER_AREA_ID = #{powerAreaId,jdbcType=VARCHAR},
      </if>
      <if test="powerAreaName != null">
        POWER_AREA_NAME = #{powerAreaName,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX != null">
        GIS_COORDINATE_X = #{gisCoordinateX,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY != null">
        GIS_COORDINATE_Y = #{gisCoordinateY,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateX84 != null">
        GIS_COORDINATE_X84 = #{gisCoordinateX84,jdbcType=VARCHAR},
      </if>
      <if test="gisCoordinateY84 != null">
        GIS_COORDINATE_Y84 = #{gisCoordinateY84,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="orgCode != null">
        ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditCode != null">
        SOCIAL_CREDIT_CODE = #{socialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeCode != null">
        INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="industryTypeName != null">
        INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR},
      </if>
      <if test="levelCode != null">
        LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      </if>
      <if test="levelName != null">
        LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="legalPhone != null">
        LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      </if>
      <if test="chargePerson != null">
        CHARGE_PERSON = #{chargePerson,jdbcType=VARCHAR},
      </if>
      <if test="chargePersonPhone != null">
        CHARGE_PERSON_PHONE = #{chargePersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="isKeySource != null">
        IS_KEY_SOURCE = #{isKeySource,jdbcType=DECIMAL},
      </if>
      <if test="isOnlineMonitor != null">
        IS_ONLINE_MONITOR = #{isOnlineMonitor,jdbcType=DECIMAL},
      </if>
      <if test="productStateCode != null">
        PRODUCT_STATE_CODE = #{productStateCode,jdbcType=VARCHAR},
      </if>
      <if test="productStateName != null">
        PRODUCT_STATE_NAME = #{productStateName,jdbcType=VARCHAR},
      </if>
      <if test="isSolidwasteOperunit != null">
        IS_SOLIDWASTE_OPERUNIT = #{isSolidwasteOperunit,jdbcType=DECIMAL},
      </if>
      <if test="isSolidwasteCreateunit != null">
        IS_SOLIDWASTE_CREATEUNIT = #{isSolidwasteCreateunit,jdbcType=DECIMAL},
      </if>
      <if test="isRiskSource != null">
        IS_RISK_SOURCE = #{isRiskSource,jdbcType=DECIMAL},
      </if>
      <if test="isOutfallStandard != null">
        IS_OUTFALL_STANDARD = #{isOutfallStandard,jdbcType=DECIMAL},
      </if>
      <if test="personNatureCode != null">
        PERSON_NATURE_CODE = #{personNatureCode,jdbcType=VARCHAR},
      </if>
      <if test="personNatureName != null">
        PERSON_NATURE_NAME = #{personNatureName,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelCode != null">
        BELONG_LEVEL_CODE = #{belongLevelCode,jdbcType=VARCHAR},
      </if>
      <if test="belongLevelName != null">
        BELONG_LEVEL_NAME = #{belongLevelName,jdbcType=VARCHAR},
      </if>
      <if test="isListedCompany != null">
        IS_LISTED_COMPANY = #{isListedCompany,jdbcType=DECIMAL},
      </if>
      <if test="stockCode != null">
        STOCK_CODE = #{stockCode,jdbcType=VARCHAR},
      </if>
      <if test="isGroupCompany != null">
        IS_GROUP_COMPANY = #{isGroupCompany,jdbcType=DECIMAL},
      </if>
      <if test="groupCompanyName != null">
        GROUP_COMPANY_NAME = #{groupCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyOrgcode != null">
        GROUP_COMPANY_ORGCODE = #{groupCompanyOrgcode,jdbcType=VARCHAR},
      </if>
      <if test="groupCompanyStockcode != null">
        GROUP_COMPANY_STOCKCODE = #{groupCompanyStockcode,jdbcType=VARCHAR},
      </if>
      <if test="objectDesc != null">
        OBJECT_DESC = #{objectDesc,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeCode != null">
        PERSONCARD_TYPE_CODE = #{personcardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="personcardTypeName != null">
        PERSONCARD_TYPE_NAME = #{personcardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="cardNumber != null">
        CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeCode != null">
        CARD_TYPE_CODE = #{cardTypeCode,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeName != null">
        CARD_TYPE_NAME = #{cardTypeName,jdbcType=VARCHAR},
      </if>
      <if test="protectedAreaDesc != null">
        PROTECTED_AREA_DESC = #{protectedAreaDesc,jdbcType=VARCHAR},
      </if>
      <if test="mainProtectObject != null">
        MAIN_PROTECT_OBJECT = #{mainProtectObject,jdbcType=VARCHAR},
      </if>
      <if test="isSpecialOrg != null">
        IS_SPECIAL_ORG = #{isSpecialOrg,jdbcType=DECIMAL},
      </if>
      <if test="manageOrgName != null">
        MANAGE_ORG_NAME = #{manageOrgName,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        SEX = #{sex,jdbcType=DECIMAL},
      </if>
      <if test="delmark != null">
        DELMARK = #{delmark,jdbcType=DECIMAL},
      </if>
      <if test="randomState != null">
        RANDOM_STATE = #{randomState,jdbcType=VARCHAR},
      </if>
      <if test="dangerCoefficient != null">
        DANGER_COEFFICIENT = #{dangerCoefficient,jdbcType=VARCHAR},
      </if>
      <if test="updatetime != null">
        UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
      </if>
      <if test="createtime != null">
        CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      </if>
      <if test="randomAttrName != null">
        RANDOM_ATTR_NAME = #{randomAttrName,jdbcType=VARCHAR},
      </if>
        <if test="objectTypeCode != null">
        OBJECT_TYPE_CODE = #{objectTypeCode,jdbcType=VARCHAR},
      </if>
         <if test="belongAreaCityId != null">
        BELONG_AREA_CITY_ID = #{belongAreaCityId,jdbcType=VARCHAR},
      </if>
         <if test="belongAreaCityName != null">
        BELONG_AREA_CITY_NAME = #{belongAreaCityName,jdbcType=VARCHAR},
      </if>
         <if test="protectNature != null">
        PROTECT_NATURE = #{protectNature,jdbcType=VARCHAR},
      </if>
         <if test="lastupdateman != null">
        LASTUPDATEMAN = #{lastupdateman,jdbcType=VARCHAR},
      </if>
       <if test="isLawPass != null">
        IS_LAW_PASS = #{isLawPass,jdbcType=DECIMAL},
      </if>
       <if test="state != null">
        STATE = #{state,jdbcType=DECIMAL},
      </if>
      <if test="legalManIdCard != null">
        LEGAL_MAN_ID_CARD = #{legalManIdCard,jdbcType=VARCHAR}, 
      </if>
      <if test="chargeManIdCard != null">
        CHARGE_MAN_ID_CARD = #{chargeManIdCard,jdbcType=VARCHAR}, 
      </if>
       <if test="pollutionSourceType != null">
      	POLLUTION_SOURCE_TYPE =#{pollutionSourceType,jdbcType=VARCHAR}, 
      </if>
      <if test="pollutionSourceTypeName != null">
      	POLLUTION_SOURCE_TYPE_NAME =#{pollutionSourceTypeName,jdbcType=VARCHAR}, 
      </if>
      <if test="xslw != null">
      	XSLW =#{xslw,jdbcType=VARCHAR}, 
      </if>
      <if test="sffscsdw != null">
      	SFFSCSDW =#{sffscsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sffqcsdw != null">
      	SFFQCSDW =#{sffqcsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sfwfcsdw != null">
      	SFWFCSDW =#{sfwfcsdw,jdbcType=VARCHAR}, 
      </if>
      <if test="sfwfjydw != null">
      	SFWFJYDW =#{sfwfjydw,jdbcType=VARCHAR}, 
      </if>
      <if test="sypwxkhyjsgfCode != null">
      	SYPWXKHYJSGF_CODE =#{sypwxkhyjsgfCode,jdbcType=VARCHAR}, 
      </if>
      <if test="sypwxkhyjsgfName != null">
      	SYPWXKHYJSGF_NAME =#{sypwxkhyjsgfName,jdbcType=VARCHAR}, 
      </if>
      <if test="isCancelled != null">
      	IS_CANCELLED =#{isCancelled,jdbcType=VARCHAR}, 
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.filecase.CaseHisLawObject">
    update CASE_HIS_LAW_OBJECT
    set CASE_ID = #{caseId,jdbcType=VARCHAR},
      LAW_OBJECT_ID = #{lawObjectId,jdbcType=VARCHAR},
      TYPE_CODE = #{typeCode,jdbcType=VARCHAR},
      TYPE_NAME = #{typeName,jdbcType=VARCHAR},
      LAW_OBJECT_NUMBER = #{lawObjectNumber,jdbcType=VARCHAR},
      LAW_OBJECT_NAME = #{lawObjectName,jdbcType=VARCHAR},
      BELONG_AREA_ID = #{belongAreaId,jdbcType=VARCHAR},
      BELONG_AREA_NAME = #{belongAreaName,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      POWER_AREA_ID = #{powerAreaId,jdbcType=VARCHAR},
      POWER_AREA_NAME = #{powerAreaName,jdbcType=VARCHAR},
      GIS_COORDINATE_X = #{gisCoordinateX,jdbcType=VARCHAR},
      GIS_COORDINATE_Y = #{gisCoordinateY,jdbcType=VARCHAR},
      GIS_COORDINATE_X84 = #{gisCoordinateX84,jdbcType=VARCHAR},
      GIS_COORDINATE_Y84 = #{gisCoordinateY84,jdbcType=VARCHAR},
      LICENSE_NO = #{licenseNo,jdbcType=VARCHAR},
      ORG_CODE = #{orgCode,jdbcType=VARCHAR},
      SOCIAL_CREDIT_CODE = #{socialCreditCode,jdbcType=VARCHAR},
      INDUSTRY_TYPE_CODE = #{industryTypeCode,jdbcType=VARCHAR},
      INDUSTRY_TYPE_NAME = #{industryTypeName,jdbcType=VARCHAR},
      LEVEL_CODE = #{levelCode,jdbcType=VARCHAR},
      LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
      LEGAL_PERSON = #{legalPerson,jdbcType=VARCHAR},
      LEGAL_PHONE = #{legalPhone,jdbcType=VARCHAR},
      CHARGE_PERSON = #{chargePerson,jdbcType=VARCHAR},
      CHARGE_PERSON_PHONE = #{chargePersonPhone,jdbcType=VARCHAR},
      IS_KEY_SOURCE = #{isKeySource,jdbcType=DECIMAL},
      IS_ONLINE_MONITOR = #{isOnlineMonitor,jdbcType=DECIMAL},
      PRODUCT_STATE_CODE = #{productStateCode,jdbcType=VARCHAR},
      PRODUCT_STATE_NAME = #{productStateName,jdbcType=VARCHAR},
      IS_SOLIDWASTE_OPERUNIT = #{isSolidwasteOperunit,jdbcType=DECIMAL},
      IS_SOLIDWASTE_CREATEUNIT = #{isSolidwasteCreateunit,jdbcType=DECIMAL},
      IS_RISK_SOURCE = #{isRiskSource,jdbcType=DECIMAL},
      IS_OUTFALL_STANDARD = #{isOutfallStandard,jdbcType=DECIMAL},
      PERSON_NATURE_CODE = #{personNatureCode,jdbcType=VARCHAR},
      PERSON_NATURE_NAME = #{personNatureName,jdbcType=VARCHAR},
      BELONG_LEVEL_CODE = #{belongLevelCode,jdbcType=VARCHAR},
      BELONG_LEVEL_NAME = #{belongLevelName,jdbcType=VARCHAR},
      IS_LISTED_COMPANY = #{isListedCompany,jdbcType=DECIMAL},
      STOCK_CODE = #{stockCode,jdbcType=VARCHAR},
      IS_GROUP_COMPANY = #{isGroupCompany,jdbcType=DECIMAL},
      GROUP_COMPANY_NAME = #{groupCompanyName,jdbcType=VARCHAR},
      GROUP_COMPANY_ORGCODE = #{groupCompanyOrgcode,jdbcType=VARCHAR},
      GROUP_COMPANY_STOCKCODE = #{groupCompanyStockcode,jdbcType=VARCHAR},
      OBJECT_DESC = #{objectDesc,jdbcType=VARCHAR},
      PERSONCARD_TYPE_CODE = #{personcardTypeCode,jdbcType=VARCHAR},
      PERSONCARD_TYPE_NAME = #{personcardTypeName,jdbcType=VARCHAR},
      CARD_NUMBER = #{cardNumber,jdbcType=VARCHAR},
      CARD_TYPE_CODE = #{cardTypeCode,jdbcType=VARCHAR},
      CARD_TYPE_NAME = #{cardTypeName,jdbcType=VARCHAR},
      PROTECTED_AREA_DESC = #{protectedAreaDesc,jdbcType=VARCHAR},
      MAIN_PROTECT_OBJECT = #{mainProtectObject,jdbcType=VARCHAR},
      IS_SPECIAL_ORG = #{isSpecialOrg,jdbcType=DECIMAL},
      MANAGE_ORG_NAME = #{manageOrgName,jdbcType=VARCHAR},
      SEX = #{sex,jdbcType=DECIMAL},
      DELMARK = #{delmark,jdbcType=DECIMAL},
      RANDOM_STATE = #{randomState,jdbcType=VARCHAR},
      DANGER_COEFFICIENT = #{dangerCoefficient,jdbcType=VARCHAR},
      UPDATETIME = #{updatetime,jdbcType=TIMESTAMP},
      CREATETIME = #{createtime,jdbcType=TIMESTAMP},
      RANDOM_ATTR_NAME = #{randomAttrName,jdbcType=VARCHAR},
      OBJECT_TYPE_CODE = #{objectTypeCode,jdbcType=VARCHAR},
      BELONG_AREA_CITY_ID = #{belongAreaCityId,jdbcType=VARCHAR},
      BELONG_AREA_CITY_NAME = #{belongAreaCityName,jdbcType=VARCHAR},
      PROTECT_NATURE = #{protectNature,jdbcType=VARCHAR},
      LASTUPDATEMAN = #{lastupdateman,jdbcType=VARCHAR},
      IS_LAW_PASS = #{isLawPass,jdbcType=DECIMAL},
      STATE = #{state,jdbcType=DECIMAL},
      POLLUTION_SOURCE_TYPE = #{pollutionSourceType,jdbcType=VARCHAR},
          POLLUTION_SOURCE_TYPE_NAME = #{pollutionSourceTypeName,jdbcType=VARCHAR},
	      XSLW = #{xslw,jdbcType=VARCHAR},
	      SFFSCSDW = #{sffscsdw,jdbcType=VARCHAR},
	      SFFQCSDW = #{sffqcsdw,jdbcType=VARCHAR},
	      SFWFCSDW = #{sfwfcsdw,jdbcType=VARCHAR},
	      SFWFJYDW = #{sfwfjydw,jdbcType=VARCHAR},
	      SYPWXKHYJSGF_CODE = #{sypwxkhyjsgfCode,jdbcType=VARCHAR},
      SYPWXKHYJSGF_NAME = #{sypwxkhyjsgfName,jdbcType=VARCHAR},
      IS_CANCELLED = #{isCancelled,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <!-- getLawEnforObject根据案件的id查询执法对象 -->
    <select id="getLawEnforObject" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_HIS_LAW_OBJECT
    where CASE_ID = #{caseId,jdbcType=VARCHAR}
  </select>
  <select id="selectLawEnforceObjListBdGisNotNull" resultMap="BaseResultMap">
  		select ID,GIS_COORDINATE_X,GIS_COORDINATE_Y
  	   from CASE_HIS_LAW_OBJECT where GIS_COORDINATE_X is not null and GIS_COORDINATE_Y is not null
  </select>
  <update id="batchUpdateGps84" parameterType="java.util.List">
     <foreach collection="list" item="item" index="index" open="begin" close=";end;" separator=";">  
                update CASE_HIS_LAW_OBJECT   
                <set>  
                  GIS_COORDINATE_X84=#{item.gisCoordinateX84,jdbcType=VARCHAR},
                  GIS_COORDINATE_Y84=#{item.gisCoordinateY84,jdbcType=VARCHAR}
                </set>  
                where id = #{item.id,jdbcType=VARCHAR}  
     </foreach> 
  </update>
</mapper>