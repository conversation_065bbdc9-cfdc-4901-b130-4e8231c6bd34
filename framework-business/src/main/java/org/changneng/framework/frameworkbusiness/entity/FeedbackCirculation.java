package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

public class FeedbackCirculation {
    private String id;

    private String quesId;

    private String type;

    private String stateCode;

    private String state;

    private String stateDesc;

    private Date stateDate;

    private String remark;

    private Date creatDate;

    private Date planSolveDate;

    private Date actualSolveDate;
    
    private String strStateDate;
    
    private String actualSolveTime;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getQuesId() {
        return quesId;
    }

    public void setQuesId(String quesId) {
        this.quesId = quesId == null ? null : quesId.trim();
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode == null ? null : stateCode.trim();
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state == null ? null : state.trim();
    }

    public String getStateDesc() {
        return stateDesc;
    }

    public void setStateDesc(String stateDesc) {
        this.stateDesc = stateDesc == null ? null : stateDesc.trim();
    }

    public Date getStateDate() {
        return stateDate;
    }

    public void setStateDate(Date stateDate) {
        this.stateDate = stateDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark == null ? null : remark.trim();
    }

    public Date getCreatDate() {
        return creatDate;
    }

    public void setCreatDate(Date creatDate) {
        this.creatDate = creatDate;
    }

    public Date getPlanSolveDate() {
        return planSolveDate;
    }

    public void setPlanSolveDate(Date planSolveDate) {
        this.planSolveDate = planSolveDate;
    }

    public Date getActualSolveDate() {
        return actualSolveDate;
    }

    public void setActualSolveDate(Date actualSolveDate) {
        this.actualSolveDate = actualSolveDate;
    }

	public String getStrStateDate() {
		return strStateDate;
	}

	public void setStrStateDate(String strStateDate) {
		this.strStateDate = strStateDate;
	}

	public String getActualSolveTime() {
		return actualSolveTime;
	}

	public void setActualSolveTime(String actualSolveTime) {
		this.actualSolveTime = actualSolveTime;
	}
	

}