package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

public class AskingCustomModel {
    private String id;

    private String templateNumber;

    private String templateName;

    private String templateArea;

    private String templateObjectType;

    private String templateAreaname;

    private String templateIndustry;

    private String templateIndustryName;

    private Date createTime;

    private Date updateTime;

    private String userName;

    private String userId;

    private String contributionName;

    private String contributionId;

    /**
     * 提供给页面展示项
     */
    private String aumId;
    private String aumUserId;
    private String admId;
    private String admUserId;
    
    private String vueJson;
    
    private String askContentId;
    
    private String  usuallyStatus;//常用状态
    
    private String defaultStatus;//默认状态
    
    private String  chickItemListTemp;//模板项json字符串
    
    private String lastSeneCustomModelId;//模板库id
    
    
    public String getChickItemListTemp() {
		return chickItemListTemp;
	}

	public void setChickItemListTemp(String chickItemListTemp) {
		this.chickItemListTemp = chickItemListTemp;
	}

	public String getLastSeneCustomModelId() {
		return lastSeneCustomModelId;
	}

	public void setLastSeneCustomModelId(String lastSeneCustomModelId) {
		this.lastSeneCustomModelId = lastSeneCustomModelId;
	}

	public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public String getTemplateNumber() {
        return templateNumber;
    }

    public void setTemplateNumber(String templateNumber) {
        this.templateNumber = templateNumber == null ? null : templateNumber.trim();
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName == null ? null : templateName.trim();
    }

    public String getTemplateArea() {
        return templateArea;
    }

    public void setTemplateArea(String templateArea) {
        this.templateArea = templateArea == null ? null : templateArea.trim();
    }

    public String getTemplateObjectType() {
        return templateObjectType;
    }

    public void setTemplateObjectType(String templateObjectType) {
        this.templateObjectType = templateObjectType == null ? null : templateObjectType.trim();
    }

    public String getTemplateAreaname() {
        return templateAreaname;
    }

    public void setTemplateAreaname(String templateAreaname) {
        this.templateAreaname = templateAreaname == null ? null : templateAreaname.trim();
    }

    public String getTemplateIndustry() {
        return templateIndustry;
    }

    public void setTemplateIndustry(String templateIndustry) {
        this.templateIndustry = templateIndustry == null ? null : templateIndustry.trim();
    }

    public String getTemplateIndustryName() {
        return templateIndustryName;
    }

    public void setTemplateIndustryName(String templateIndustryName) {
        this.templateIndustryName = templateIndustryName == null ? null : templateIndustryName.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName == null ? null : userName.trim();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }

    public String getContributionName() {
        return contributionName;
    }

    public void setContributionName(String contributionName) {
        this.contributionName = contributionName == null ? null : contributionName.trim();
    }

    public String getContributionId() {
        return contributionId;
    }

    public void setContributionId(String contributionId) {
        this.contributionId = contributionId == null ? null : contributionId.trim();
    }

	public String getAumId() {
		return aumId;
	}

	public void setAumId(String aumId) {
		this.aumId = aumId;
	}

	public String getAumUserId() {
		return aumUserId;
	}

	public void setAumUserId(String aumUserId) {
		this.aumUserId = aumUserId;
	}

	public String getAdmId() {
		return admId;
	}

	public void setAdmId(String admId) {
		this.admId = admId;
	}

	public String getAdmUserId() {
		return admUserId;
	}

	public void setAdmUserId(String admUserId) {
		this.admUserId = admUserId;
	}

	public String getVueJson() {
		return vueJson;
	}

	public void setVueJson(String vueJson) {
		this.vueJson = vueJson;
	}

	public String getAskContentId() {
		return askContentId;
	}

	public void setAskContentId(String askContentId) {
		this.askContentId = askContentId;
	}

	public String getUsuallyStatus() {
		return usuallyStatus;
	}

	public void setUsuallyStatus(String usuallyStatus) {
		this.usuallyStatus = usuallyStatus;
	}

	public String getDefaultStatus() {
		return defaultStatus;
	}

	public void setDefaultStatus(String defaultStatus) {
		this.defaultStatus = defaultStatus;
	}
	
}