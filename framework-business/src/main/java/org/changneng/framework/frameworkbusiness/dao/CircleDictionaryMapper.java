package org.changneng.framework.frameworkbusiness.dao;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.CircleDictionary;
import org.changneng.framework.frameworkbusiness.entity.CircleDictionarySeach;

public interface CircleDictionaryMapper {
    int deleteByPrimaryKey(String id);

    int insert(CircleDictionary record);

    int insertSelective(CircleDictionary record);

    CircleDictionary selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(CircleDictionary record);

    int updateByPrimaryKey(CircleDictionary record);

	List<CircleDictionary> selectAvailableTag();

	CircleDictionary selectByCode(@Param("code")Integer code);
	/**
	 * web端任务类型配置页面查询列表
	 * @param SeachBean
	 * @return
	 */
	List<CircleDictionary> selectBySeachBean(@Param("searchBean")CircleDictionarySeach searchBean);
	/**
	 * 验证名称是否重复
	 * @param id
	 * @return
	 */
	Integer selectByName(@Param("id")String id,@Param("name")String name);
	/**
	 * 获取最大的执法类型编号
	 * @return
	 */
	Integer selectByMaxCode();
}