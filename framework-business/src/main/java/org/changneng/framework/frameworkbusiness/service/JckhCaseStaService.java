package org.changneng.framework.frameworkbusiness.service;

import java.text.ParseException;
import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.CaseAttemperStatistic;
import org.changneng.framework.frameworkbusiness.entity.CaseStatisSearchBean;

/**
 * 案件调度service
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年12月6日 上午11:08:18
 */
public interface JckhCaseStaService {

	/**
	 * 定时统计案件信息
	 * 
	 * <AUTHOR>
	 */
	void statisCaseInfo() throws Exception;


	void statisCaseByYear() throws Exception;

	/**
	 * 根据查询条件获取后台的开始结束时间
	 * @param searchListBean
	 * @return
	 * 
	 * <AUTHOR>
	 */
	CaseStatisSearchBean getStaDate(CaseStatisSearchBean searchListBean);

	/**
	 * 根据条件查询案件调度统计数据
	 * @param searchBean
	 * @return
	 * 
	 * <AUTHOR>
	 */
	List<CaseAttemperStatistic> selectDataList(CaseStatisSearchBean searchBean);

	/**
	 * 查询是否存在已生成的excel
	 * @param searchBean
	 * @return flag : true存在，false不存在
	 * 
	 * <AUTHOR>
	 */
	boolean queryHashExcel(CaseStatisSearchBean searchBean) throws Exception;
	
	/**
	 * 补充案件调度，维护方法<br/>
	 * 维护财主统计日期遗漏办法<br/>
	 * @param begDate
	 * @param endDate
	 * @throws Exception
	 * <AUTHOR>
	 * @date 2018年3月8日-上午9:49:39
	 */
	void  caseDispatch(String begDate,String endDate) throws Exception;

}
