package org.changneng.framework.frameworkbusiness.entity;

/**
 * 用户执法信息bean（精确到个人）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2018年1月31日 上午11:18:43
 */
public class UserLawDataBean {

	private String userId;

	private String loginName;

	private String userName;

	private String cardId;

	private String phone;

	private String jobId;

	private String jobName;

	private String belongDepartmentId;

	private String belongDepartmentName;

	private String belongCode;

	private String belongName;

	private Integer lawTimes;

	private Integer lawDays;

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getCardId() {
		return cardId;
	}

	public void setCardId(String cardId) {
		this.cardId = cardId;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getJobId() {
		return jobId;
	}

	public void setJobId(String jobId) {
		this.jobId = jobId;
	}

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getBelongDepartmentId() {
		return belongDepartmentId;
	}

	public void setBelongDepartmentId(String belongDepartmentId) {
		this.belongDepartmentId = belongDepartmentId;
	}

	public String getBelongDepartmentName() {
		return belongDepartmentName;
	}

	public void setBelongDepartmentName(String belongDepartmentName) {
		this.belongDepartmentName = belongDepartmentName;
	}

	public String getBelongCode() {
		return belongCode;
	}

	public void setBelongCode(String belongCode) {
		this.belongCode = belongCode;
	}

	public String getBelongName() {
		return belongName;
	}

	public void setBelongName(String belongName) {
		this.belongName = belongName;
	}

	public Integer getLawTimes() {
		return lawTimes;
	}

	public void setLawTimes(Integer lawTimes) {
		this.lawTimes = lawTimes;
	}

	public Integer getLawDays() {
		return lawDays;
	}

	public void setLawDays(Integer lawDays) {
		this.lawDays = lawDays;
	}

}
