package org.changneng.framework.frameworkbusiness.service;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.changneng.framework.frameworkbusiness.entity.AskingContent;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomBean;
import org.changneng.framework.frameworkbusiness.entity.AskingCustomModel;
import org.changneng.framework.frameworkbusiness.entity.AskingRecord;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.LawObjectTypeBean;
import org.changneng.framework.frameworkbusiness.entity.ModelerLocationBean;
import org.changneng.framework.frameworkbusiness.entity.ScanningAttachment;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkcore.exception.BusinessException;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;

public interface AskingRecordService {

	/**
	 * 根据taskid查询出最近更新的一条数据信息
	 * @param taskId
	 * @return
	 */
	AskingRecord getAskingRecordByUpdateDataAndTaskId(String taskId,String parentUrl) throws Exception; 
	
	/**
	 * 根据  询问笔录名称 和主干的任务id 查重
	 * 若是新增数据保证在同一个任务id下 询问笔录名称 唯一
	 * @param recordName
	 * @param taskId
	 * @return
	 */
	AskingRecord getAskingRecordByRecordNameAndTaskId(String recordName,String taskId)  throws Exception ;
	
	/**
	 * 根据AskingRecord id 直接查询
	 * @param id
	 * @return
	 */
	AskingRecord getAskingRecordById(String id);
	
	/**
	 * 在新增的时候，需要对新增对象进行封装
	 * @return
	 */
	AskingRecord addNewAskingRecord(LawObjectTypeBean lawObj,SysUsers sysUsers) throws Exception;
	
	/**
	 * 删除询问笔录
	 * @param id
	 * @return
	 */
	int deleteAskingRecordById(String id);
	
	/**
	 * 新增 询问笔录
	 * @param record
	 * @return
	 */
	int instrtAskingRecord(AskingRecord record) throws Exception;
	
	/**
	 * 修改 or 新增  询问笔录
	 * @param record
	 * @return
	 */
	int updateAskingRecord(AskingRecord record) throws Exception;
	/**
	 * 重新生成  询问笔录pdf
	 * @param record
	 * @return
	 */
	int generateAskingRecord(AskingRecord record) throws Exception;
	
	
	/**
	 * 删除 询问信息
	 * @param contentId
	 * @return
	 */
	int deleteAskingContent(String contentId);
	
	/**
	 * 新增 询问信息
	 * @param content
	 * @return
	 */
	int  insertSelective(AskingContent content);
	
	/**
	 * 修改 询问信息
	 * @param content
	 * @return
	 */
	int updateByPrimaryKeySelective(AskingContent content);
	
	/**
	 * 询问笔录 离线模版导入
	 * @param is
	 * @return
	 */
	public JsonResult readContentFroExcel(InputStream is,AskingRecord askRecord) throws Exception;
	
	
	/**
	 * 批量保存询问笔录问答信息
	 * @param list
	 * @return
	 */
	int batchInsertAskingContent(List<AskingContent> list );
	
	/**
	 * 根据askid查询唯一值，包装下载url（是否需要包装url:  type=0 不需要包装url   type=1 需要包装）
	 * @param askId
	 * @return
	 */
	public AskingRecord getAskRecordDocurl(String askId,Integer type) throws Exception;

	/**
	 * 保存询问笔录/现场检查上传的扫描件
	 * @param taskID
	 * @param recordID
	 * @param itemType
	 * @param list
	 */
	void saveSmj(String taskID, String recordID, String itemType, List<SysFiles> list) throws BusinessException;

	/**
	 * 根据询问笔录ID查询ScanningAttachment
	 * @param askingId
	 * @return
	 */
	List<ScanningAttachment> getAppendFile(String askingId);

	/**
	 * 根据attachmentid获取ScanningAttachment对象
	 * @param attachmentId
	 * @return
	 */
	ScanningAttachment getAttachmentById(String attachmentId);

	/**
	 * 删除扫描件信息并将SYS_FILES表中的数据状态置为2
	 * @param id
	 */
	void fileinfoDelete(String id) throws BusinessException;
	
	/**
	 * 批量删除扫描件信息并将SYS_FILES表中的数据状态置为2
	 * @param id
	 */
	void fileinfoDeleteGroup(String ids) throws BusinessException;
	
	/**
	 * 更具执法对象类型，和用户信息，查询出自定义模版信息
	 * @param lawObj
	 * @param sysUsers
	 * @return
	 */
	PageBean<AskingCustomModel>  getAskingCustomModelByUser(AskingCustomBean customBean);
	
	/**
	 *  修改默认和常用项和删除
	 * @param customBean
	 * @return
	 */
	JsonResult setUsuallyOrDefaultOrDelete(AskingCustomBean customBean) throws Exception;
	
	/**
	 * 询问笔录，现有记录模版
	 * @param customBean
	 * @return
	 */
	PageBean<AskingCustomModel>  getAskingExistModelByLawObject(AskingCustomBean customBean);
	
	/**
	 * 询问笔录询问项新增的时候，需要查询是否有设置默认模版，若有则采用模版输出，若没有则采用系统默认模版格式输出
	 * @param lawObjectType  执法对象类型
	 * @param code 0 新增默认查询输出 1 页面选中模版 2 现有记录模版选中 3 使用系统模版（目前系统模版就一份 id = 1  2017-05-17）
	 * @param customModelerId  当code为1时，必然会传递过来模版的id项
	 * @param contentId 当code为2时，从现有记录中查询询问项
	 * @return
	 */
	AskingRecord getNewContentList(String lawObjectType,String code,String customModelerId,String contentId);
	
	
	/**
	 * 询问笔录自定义模版保存
	 * @param sceneCustomModel 2
	 * @return
	 * @throws Exception
	 */
	JsonResult saveTemplate(AskingCustomModel sceneCustomModel) throws Exception;
	
	/**
	 * 修改状态下改变顺序
	 * @param askid 
	 * @param index
	 * @param type
	 * @return
	 * @throws Exception
	 */
	JsonResult saveAskingContentLocation(ModelerLocationBean locationBean) throws Exception;

	/**
	 * api 现场检查附件上传保存功能
	 * @param taskID
	 * @param recordID
	 * @param itemType
	 * @param request
	 * @param response
	 * @throws Exception 
	 * @throws IOException 
	 */
	void saveApiSmj(String taskID, String recordID, String itemType,
			HttpServletRequest request, HttpServletResponse response,SysUsers sysUsers) throws IOException, Exception;
	
	/**
	 * 旋转图片
	 * @param id 图片id
	 * @param url 图片Url
	 * @param tableName 图片所在的表
	 * @return
	 * <AUTHOR>
	 */
	ResponseJson rotateImage(String id,String url,String tableName,String column,String fileId,Integer degree)throws Exception;
}
