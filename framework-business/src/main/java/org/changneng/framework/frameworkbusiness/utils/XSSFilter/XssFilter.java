package org.changneng.framework.frameworkbusiness.utils.XSSFilter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;


/**
 * xss过滤
 */

public class XssFilter implements Filter {

    //不拦截的地址
    private List<String> excludedList = new ArrayList<String>();

    public static Logger log = LoggerFactory.getLogger(XssFilter.class);

    @Override
    public void init(FilterConfig config) throws ServletException {

        /*
         * 这里只处理了不拦截的url地址，如果想不拦截某个字段，比如富文本字段，
         * 需要自己在XssHttpServletRequestWrapper类中去添加逻辑
         */
//        excludedList.add("/api/htmlFiv/findOnLine");
//        excludedList.add("/proxy/aip/v1/permissions/verify");
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
                (HttpServletRequest) request);
        HttpServletRequest req = (HttpServletRequest) request;
        if ("POST".equals(req.getMethod().toUpperCase())) {
            String url = xssRequest.getServletPath();
            if (isExcluded(url)) {
                chain.doFilter(request, response);
            } else {
                //使用XSS过滤
                log.info("使用XSS过滤");
                chain.doFilter(xssRequest, response);
            }
        } else {
            chain.doFilter(request, response);
        }

    }

    @Override
    public void destroy() {
    }

    /**
     * 是否不拦截
     *
     * @param url 请求地址
     * @return true不拦截，false拦截
     */
    private boolean isExcluded(String url) {
        if (StringUtils.isBlank(url)) {
            return false;
        }

        for (String excluded : excludedList) {
            if (url.indexOf(excluded) > -1) {
                return true;
            }
        }
        return false;
    }


}