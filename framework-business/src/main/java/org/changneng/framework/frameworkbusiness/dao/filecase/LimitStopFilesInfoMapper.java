package org.changneng.framework.frameworkbusiness.dao.filecase;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.changneng.framework.frameworkbusiness.entity.filecase.LimitStopFilesInfo;

public interface LimitStopFilesInfoMapper {
    int deleteByPrimaryKey(String id);

    int insert(LimitStopFilesInfo record);

    int insertSelective(LimitStopFilesInfo record);

    LimitStopFilesInfo selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(LimitStopFilesInfo record);

    int updateByPrimaryKey(LimitStopFilesInfo record);

	/**
     * 根据itemid更改状态为1（保存时）
     * @param string
     */
	void updateStateByItemId(String itemId);

	/**
	 * 批量更改状态
	 * @param itemIdList
	 */
	void batchUpdateStateByItemIds(@Param("itemIdList")List<String> itemIdList);

	/**
	 * 批量更改状态（根据一键生成条目id）
	 * @param generateIdList
	 */
	void batchUpdateStateByGenerateIds(@Param("generateIdList")List<String> generateIdList);
}