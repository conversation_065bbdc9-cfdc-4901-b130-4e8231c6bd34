<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.CaseLawObjectRelationMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SUPERVISE_MONITOR_OVERPROOF_ID" jdbcType="VARCHAR" property="superviseMonitorOverproofId" />
    <result column="CASE_ID" jdbcType="VARCHAR" property="caseId" />
    <result column="CASE_CODE" jdbcType="VARCHAR" property="caseCode" />
    <result column="CASE_CREATE_TIME" jdbcType="TIMESTAMP" property="caseCreateTime" />
    <result column="CASE_PARTY" jdbcType="VARCHAR" property="caseParty" />
    <result column="PUNISH_MAIN" jdbcType="VARCHAR" property="punishMain" />
    <result column="CREATE_TIME" jdbcType="TIMESTAMP" property="createTime" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="formatDate" jdbcType="VARCHAR" property="formatDate" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SUPERVISE_MONITOR_OVERPROOF_ID, CASE_ID, CASE_CODE, CASE_CREATE_TIME, CASE_PARTY, 
    PUNISH_MAIN, CREATE_TIME, UPDATE_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from CASE_LAWOBJECT_RELATION
    where ID = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from CASE_LAWOBJECT_RELATION
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
    insert into CASE_LAWOBJECT_RELATION (ID, SUPERVISE_MONITOR_OVERPROOF_ID, 
      CASE_ID, CASE_CODE, CASE_CREATE_TIME, 
      CASE_PARTY, PUNISH_MAIN, CREATE_TIME, 
      UPDATE_TIME)
    values (#{id,jdbcType=VARCHAR}, #{superviseMonitorOverproofId,jdbcType=VARCHAR}, 
      #{caseId,jdbcType=VARCHAR}, #{caseCode,jdbcType=VARCHAR}, #{caseCreateTime,jdbcType=TIMESTAMP}, 
      #{caseParty,jdbcType=VARCHAR}, #{punishMain,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
   	 <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual
  	</selectKey>
    insert into CASE_LAWOBJECT_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="superviseMonitorOverproofId != null">
        SUPERVISE_MONITOR_OVERPROOF_ID,
      </if>
      <if test="caseId != null">
        CASE_ID,
      </if>
      <if test="caseCode != null">
        CASE_CODE,
      </if>
      <if test="caseCreateTime != null">
        CASE_CREATE_TIME,
      </if>
      <if test="caseParty != null">
        CASE_PARTY,
      </if>
      <if test="punishMain != null">
        PUNISH_MAIN,
      </if>
      <!-- <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if> -->
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="superviseMonitorOverproofId != null">
        #{superviseMonitorOverproofId,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="caseCode != null">
        #{caseCode,jdbcType=VARCHAR},
      </if>
      <if test="caseCreateTime != null">
        #{caseCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseParty != null">
        #{caseParty,jdbcType=VARCHAR},
      </if>
      <if test="punishMain != null">
        #{punishMain,jdbcType=VARCHAR},
      </if>
      <!-- <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if> -->
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
    update CASE_LAWOBJECT_RELATION
    <set>
      <if test="superviseMonitorOverproofId != null">
        SUPERVISE_MONITOR_OVERPROOF_ID = #{superviseMonitorOverproofId,jdbcType=VARCHAR},
      </if>
      <if test="caseId != null">
        CASE_ID = #{caseId,jdbcType=VARCHAR},
      </if>
      <if test="caseCode != null">
        CASE_CODE = #{caseCode,jdbcType=VARCHAR},
      </if>
      <if test="caseCreateTime != null">
        CASE_CREATE_TIME = #{caseCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="caseParty != null">
        CASE_PARTY = #{caseParty,jdbcType=VARCHAR},
      </if>
      <if test="punishMain != null">
        PUNISH_MAIN = #{punishMain,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="org.changneng.framework.frameworkbusiness.entity.CaseLawObjectRelation">
    update CASE_LAWOBJECT_RELATION
    set SUPERVISE_MONITOR_OVERPROOF_ID = #{superviseMonitorOverproofId,jdbcType=VARCHAR},
      CASE_ID = #{caseId,jdbcType=VARCHAR},
      CASE_CODE = #{caseCode,jdbcType=VARCHAR},
      CASE_CREATE_TIME = #{caseCreateTime,jdbcType=TIMESTAMP},
      CASE_PARTY = #{caseParty,jdbcType=VARCHAR},
      PUNISH_MAIN = #{punishMain,jdbcType=VARCHAR},
      CREATE_TIME = #{createTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <select id="selectCountCase" parameterType="java.util.Map" resultType="java.lang.Integer">
  	select count(*) from CASE_LAWOBJECT_RELATION where SUPERVISE_MONITOR_OVERPROOF_ID = #{lawID}
  </select>
  <select id="selectAllByLowID" parameterType="java.util.Map" resultMap="BaseResultMap" >
  select 
    <include refid="Base_Column_List" />,TO_CHAR(CASE_CREATE_TIME,'yyyy-MM-dd')formatDate
    from CASE_LAWOBJECT_RELATION
    <where>
    	<if test="lawID != null and ''!=lawID">
    		SUPERVISE_MONITOR_OVERPROOF_ID = #{lawID}
    	</if>
    </where>
  </select>
</mapper>