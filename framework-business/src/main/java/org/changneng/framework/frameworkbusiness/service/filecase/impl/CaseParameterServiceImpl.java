package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import java.util.List;

import org.changneng.framework.frameworkbusiness.dao.filecase.AdministrativeDetentionMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.AtvSanctionCaseMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.ExecutiveOrderMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.OtherTransferMapper;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.AdministrativeDetentionWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.AjtzSearchResultBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.OtherTransferWithBLOBs;
import org.changneng.framework.frameworkbusiness.service.filecase.CaseParameterService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;

@Service
public class CaseParameterServiceImpl implements CaseParameterService{
	@Autowired
	private ExecutiveOrderMapper executiveOrderMapper;
	@Autowired
	private OtherTransferMapper otherTransferMapper;
	@Autowired
	private AdministrativeDetentionMapper administrativeDetentionMapper;
	/**
	 * 其他移送分页列表信息
	 * @throws Exception 
	 */
	@Override
	public PageBean<AjtzSearchResultBean> getOrtherTransferList(AjtzSearchBean searchBean,
			SysUsers sysUsers) throws Exception {
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateMonth()) {
			case "01":
			case "03":
			case "05":
			case "07":
			case "08":
			case "10":
			case "12":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-31");
				break;
			case "02":
			case "04":
			case "06":
			case "09":
			case "11":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-30");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-03-31");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-06-30");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-09-30");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(15);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<>(otherTransferMapper.selectQTYSCaseParameter(searchBean, sysUsers.getBelongAreaId()));

	}
	/**
	 * 
	 * 其他移送els下载
	 */
	@Override
	public List<AjtzSearchResultBean> selectDownList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		String caseCreateYear = searchBean.getCaseCreateYear();
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(caseCreateYear)) {
			searchBean.setCaseCreateDateBegin(caseCreateYear+"-"+searchBean.getCaseCreateMonth()+"-01");
			String endYear = caseCreateYear;//案件开始时间结束年份
			Integer month = Integer.valueOf(searchBean.getCaseCreateMonth())+1;
			if(month>12){
				//如果月份+1>12，则说明进入下一年了，年度+1，月份-12
				month = month-12;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else if(month==12){
				month = 1;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else{
				month+=1;
			}
			searchBean.setCaseCreateDateEnd(endYear+"-"+month+"-01");
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(caseCreateYear)) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(caseCreateYear+"-01-01");
				searchBean.setCaseCreateDateEnd(caseCreateYear+"-04-01");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(caseCreateYear+"-04-01");
				searchBean.setCaseCreateDateEnd(caseCreateYear+"-07-01");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(caseCreateYear+"-07-01");
				searchBean.setCaseCreateDateEnd(caseCreateYear+"-10-01");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(caseCreateYear+"-10-01");
				searchBean.setCaseCreateDateEnd((Integer.valueOf(caseCreateYear)+1)+"-01-01");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		return otherTransferMapper.selectQTYSCaseParameter(searchBean, sysUsers.getBelongAreaId());
	}
	/**
	 * 行政拘留列表获取
	 */
	@Override
	public PageBean<AjtzSearchResultBean> getAdministrativeList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		String caseCreateYear = searchBean.getCaseCreateYear();
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
			Integer month = Integer.valueOf(searchBean.getCaseCreateMonth());
			String endYear = caseCreateYear;
			if(month>12){
				//如果月份+1>12，则说明进入下一年了，年度+1，月份-12
				month = month-12;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else if(month==12){
				month = 1;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else{
				month+=1;
			}
			//String v = month.toString().length()>1?month.toString():"0"+month.toString();
			searchBean.setCaseCreateDateEnd(endYear+"-"+month+"-01");
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-04-01");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-07-01");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-10-01");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd((Integer.valueOf(searchBean.getCaseCreateYear())+1)+"-01-01");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(15);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		return new PageBean<>(administrativeDetentionMapper.selectXZJLCaseParameter(searchBean, sysUsers.getBelongAreaId()));
	}
	/**
	 * 行政命令列表获取
	 */
	@Override
	public PageBean<AjtzSearchResultBean> getXZMLList(AjtzSearchBean searchBean, SysUsers sysUsers) throws Exception {
		String caseCreateYear = searchBean.getCaseCreateYear();
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
			Integer month = Integer.valueOf(searchBean.getCaseCreateMonth());
			String endYear = caseCreateYear;
			if(month>12){
				//如果月份+1>12，则说明进入下一年了，年度+1，月份-12
				month = month-12;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else if(month==12){
				month = 1;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else{
				month+=1;
			}
			//String v = month.toString().length()>1?month.toString():"0"+month.toString();
			searchBean.setCaseCreateDateEnd(endYear+"-"+month+"-01");
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-04-01");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-07-01");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-10-01");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd((Integer.valueOf(searchBean.getCaseCreateYear())+1)+"-01-01");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		if(searchBean.getPageNum()==null || searchBean.getPageNum()==0 ){
			searchBean.setPageNum(1);
		}
		if(searchBean.getPageSize()==null || searchBean.getPageSize()==0){
			searchBean.setPageSize(15);
		}
		PageHelper.startPage(searchBean.getPageNum(),searchBean.getPageSize());
		List<AjtzSearchResultBean> caseList = executiveOrderMapper.selectExecutiveCaseParameter(searchBean, sysUsers.getBelongAreaId());
		caseList = transferAttr(caseList);
		return new PageBean<>(caseList);
	}
	/**
	 * 行政命令els下载
	 */
	@Override
	public List<AjtzSearchResultBean> selectXZMLDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			String caseCreateYear =searchBean.getCaseCreateYear();
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
			Integer month = Integer.valueOf(searchBean.getCaseCreateMonth());
			String endYear = caseCreateYear;
			if(month>12){
				//如果月份+1>12，则说明进入下一年了，年度+1，月份-12
				month = month-12;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else if(month==12){
				month = 1;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else{
				month+=1;
			}
			//String v = month.toString().length()>1?month.toString():"0"+month.toString();
			searchBean.setCaseCreateDateEnd(endYear+"-"+month+"-01");
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-04-01");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-07-01");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-10-01");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd((Integer.valueOf(searchBean.getCaseCreateYear())+1)+"-01-01");
				break;
			default:
				break;
			}
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		List<AjtzSearchResultBean> caseList = executiveOrderMapper.selectExecutiveCaseParameter(searchBean, sysUsers.getBelongAreaId());
		caseList = transferAttr(caseList);
		return caseList;
	}
	@Override
	public List<AjtzSearchResultBean> selectAdministrativeDownList(AjtzSearchBean searchBean, SysUsers sysUsers)
			throws Exception {
		if (!ChangnengUtil.isNull(searchBean.getCaseCreateMonth()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			String caseCreateYear = searchBean.getCaseCreateYear();
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-"+searchBean.getCaseCreateMonth()+"-01");
			Integer month = Integer.valueOf(searchBean.getCaseCreateMonth());
			String endYear = caseCreateYear;
			if(month>12){
				//如果月份+1>12，则说明进入下一年了，年度+1，月份-12
				month = month-12;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else if(month==12){
				month = 1;
				endYear = Integer.parseInt(caseCreateYear)+1+"";
			}else{
				month+=1;
			}
			//String v = month.toString().length()>1?month.toString():"0"+month.toString();
			searchBean.setCaseCreateDateEnd(endYear+"-"+month+"-01");
		} else if (!ChangnengUtil.isNull(searchBean.getCaseCreateQuarter()) && !ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			switch (searchBean.getCaseCreateQuarter()) {
			case "1":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-04-01");
				break;
			case "2":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-04-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-07-01");
				break;
			case "3":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-07-01");
				searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-10-01");
				break;
			case "4":
				searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-10-01");
				searchBean.setCaseCreateDateEnd((Integer.valueOf(searchBean.getCaseCreateYear())+1)+"-01-01");
				break;
			default:
				break;
			}
		}else if (!ChangnengUtil.isNull(searchBean.getCaseCreateYear())) {
			searchBean.setCaseCreateDateBegin(searchBean.getCaseCreateYear()+"-01-01");
			searchBean.setCaseCreateDateEnd(searchBean.getCaseCreateYear()+"-12-31");
		} 
		return administrativeDetentionMapper.selectXZJLCaseParameter(searchBean, sysUsers.getBelongAreaId());
	}

	
	/**
	 * 导出属性转换
	 * 
	 * @param resultList
	 * @return
	 */
	private List<AjtzSearchResultBean> transferAttr(List<AjtzSearchResultBean> resultList){
		for (int i = 0; i < resultList.size(); i++) {
			AjtzSearchResultBean ajtzSearchResultBean = resultList.get(i);
			
			if (!ChangnengUtil.isNull(ajtzSearchResultBean.getPunishBasis()) && ajtzSearchResultBean.getPunishBasis().length()>0) {
				StringBuffer sb = new StringBuffer();

				String str = ajtzSearchResultBean.getPunishBasis();
				if (!"".equals(str) && str != null) {
					String[] split = str.split("&&");
					for (String string : split) {
						String[] split2 = string.split("[$]");
						try {
							sb.append(split2[1]);
						} catch (Exception e) {
							System.out.println(e);
							continue;
						}
					}
					ajtzSearchResultBean.setPunishBasis(sb.toString());
				}
			}
		}
		
		return resultList;
	}
}
