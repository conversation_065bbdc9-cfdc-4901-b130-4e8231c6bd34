package org.changneng.framework.frameworkbusiness.service.filecase.impl;

import java.util.Date;

import org.changneng.framework.frameworkbusiness.dao.filecase.CaseBaseInfoMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.DocumentMakeMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.DocumentRelationMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.FileDocumentMakeMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.ModelerDocumentMakeMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.PenaltyDayMapper;
import org.changneng.framework.frameworkbusiness.dao.filecase.SysConfigDocumentMapper;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseBaseInfo;
import org.changneng.framework.frameworkbusiness.entity.filecase.DocumentMake;
import org.changneng.framework.frameworkbusiness.entity.filecase.FileDocumentMake;
import org.changneng.framework.frameworkbusiness.entity.filecase.ModelerDocumentMake;
import org.changneng.framework.frameworkbusiness.entity.filecase.PenaltyDayWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.filecase.QRCodeDoc;
import org.changneng.framework.frameworkbusiness.entity.filecase.SysConfigDocumentWithBLOBs;
import org.changneng.framework.frameworkbusiness.mongodb.bean.filecase.DocPublicBean;
import org.changneng.framework.frameworkbusiness.mongodb.dao.impl.ExecuDocMongoPubDaoImpl;
import org.changneng.framework.frameworkbusiness.service.filecase.DocumentMakeService;
import org.changneng.framework.frameworkbusiness.service.filecase.MakeDocFreeMarkerService;
import org.changneng.framework.frameworkbusiness.utils.GenerateUtil;
import org.changneng.framework.frameworkbusiness.utils.ImageUtil;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.ZxingUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

/**
 * 文书制作保存相关的业务处理
 * <AUTHOR>
 * @date 2017年11月23日 下午2:49:25
 */
@Service
public class DocumentMakeServiceImpl implements DocumentMakeService {
	
	@Autowired
	private DocumentMakeMapper documentMakeMapper;
	
	@Autowired
	private DocumentRelationMapper documentRelationMapper;

	@Autowired
	private PenaltyDayMapper penaltyDayMapper;
	
	@Autowired
	private CaseBaseInfoMapper caseBaseInfoMapper;
	
	@Autowired
	private FileDocumentMakeMapper fdmMapper;
	
	@Autowired
	private ModelerDocumentMakeMapper mdmMapper;

	@Autowired
	private SysConfigDocumentMapper sysConfigDocumentMapper;
	
	@Autowired
	private ExecuDocMongoPubDaoImpl execuDocMongoPubDaoImpl;
	
	@Autowired
	private MakeDocFreeMarkerService makeDocFreeMarkerService; 
	
	/**
	 * 保存文书信息
	 * <AUTHOR>
	 */
	@Override
	@Transactional(rollbackFor=Exception.class)
	public String saveDocumentMongo1(DocPublicBean puBean, DocumentMake doc, String attachIds, String docType) throws Exception {
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		boolean isInsert = false;
		//保存结构化信息到mongodb
		if (ChangnengUtil.isNull(doc.getId())) {
			//插入
			if ("".equals(puBean.getMongoId())) {
				//如果为空字符串，MongoDB会把它当成有值得id，转成Null
				puBean.setMongoId(null);
			}
			puBean = execuDocMongoPubDaoImpl.save(puBean);
			isInsert = true;
		} else {
			//更新
			execuDocMongoPubDaoImpl.updateDocumentMongo1(puBean);
			
			//如果id不为空，查询上一次的pdfUrl（sysfiles表的id）
			DocumentMake documentMake = documentMakeMapper.selectByPrimaryKey(doc.getId());
			doc.setPdfUrl(documentMake.getPdfUrl());
		}
		
		doc.setMongodbId(puBean.getMongoId());
		
		//先存，再更新pdfurl和pdfQrUrl
		String docId = saveOnlineFileInfo(doc, attachIds);
		
		//生成freemarker
		JSONObject jsonObject =  puBean.getInfoJson();
		
		String pdfQrUrl = "";
		String fastdfsip = PropertiesHandlerUtil.getValue("fastdfs.nginx.iptwo", "fastdfs");//要用内网IP
		if (isInsert) {
			//生成文书二维码
			//如果不是新保存的数据不需要重新生成二维码
			QRCodeDoc qrCodeDoc = new QRCodeDoc();
			qrCodeDoc.setAppLogo("CHN-FJ");
			qrCodeDoc.setdId(doc.getId());
			qrCodeDoc.setType("05");
			qrCodeDoc.setmType("1");
			qrCodeDoc.setiCode(doc.getPageNo());
			String jsonString = JSON.toJSONString(qrCodeDoc);
			pdfQrUrl = ZxingUtil.get2CodeImage(jsonString, doc.getCaseId());
			doc.setPdfQrUrl(pdfQrUrl);
		} else {
			pdfQrUrl = documentMakeMapper.selectByPrimaryKey(doc.getId()).getPdfQrUrl();
			if (ChangnengUtil.isNull(pdfQrUrl)) {
				//更新时如果发现这条文书的二维码不存在，就重新生成
				QRCodeDoc qrCodeDoc = new QRCodeDoc();
				qrCodeDoc.setAppLogo("CHN-FJ");
				qrCodeDoc.setdId(doc.getId());
				qrCodeDoc.setType("05");
				qrCodeDoc.setmType("1");
				qrCodeDoc.setiCode(doc.getPageNo());
				String jsonString = JSON.toJSONString(qrCodeDoc);
				pdfQrUrl = ZxingUtil.get2CodeImage(jsonString, doc.getCaseId());
			}
		}
		//往json里放入二维码路径
		jsonObject.put("qrUrl", fastdfsip+pdfQrUrl);
		
		String pdfUrl = makeDocFreeMarkerService.createDocumentMongoPdf(jsonObject, docType, doc.getPdfUrl(), sysUsers);
		doc.setPdfUrl(pdfUrl);
		
		documentMakeMapper.updateByPrimaryKeySelective(doc);
		
		return docId;
	}
	
	
	/**
	 * 保存非结构化信息-公共调用
	 * <AUTHOR>
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public String saveOnlineFileInfo(DocumentMake dm, String attachIds)throws Exception {
		if (ChangnengUtil.isNull(dm.getCaseId()) || ChangnengUtil.isNull(dm.getMakeType())) {
			throw new Exception("参数错误，保存失败！");
		}
		try {
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
			String docId = dm.getId();
			CaseBaseInfo baseInfo = caseBaseInfoMapper.selectByPrimaryKey(dm.getCaseId());
			if(ChangnengUtil.isNull(docId)){
				//docId为空，则先保存到Document_Make表里生成docId
				dm.setCaseName(baseInfo.getCaseName());
				dm.setCaseNumber(baseInfo.getCaseNumber());
				dm.setCreateUserId(sysUsers.getId());
				dm.setCreateUserName(sysUsers.getLoginname());
				dm.setDepartmentId(sysUsers.getBelongDepartmentId());
				dm.setDepartmentName(sysUsers.getBelongDepartmentName());
				dm.setLawObjectId(baseInfo.getLawObjectId());
				dm.setLawObjectName(baseInfo.getLawObjectName());
				dm.setPunishSubject(baseInfo.getPunishSubject());
				dm.setPunishSubjectId(baseInfo.getPunishSubjectId());
				dm.setResearchOrgId(baseInfo.getResearchOrgId());
				dm.setResearchOrgName(baseInfo.getResearchOrgName());
				dm.setUserBelongAreaCode(baseInfo.getUserBelongAreaCode());
				dm.setUserBelongAreaName(baseInfo.getUserBelongAreaName());
				dm.setIsDelete(0);
				dm.setCreateDate(new Date());
				dm.setLastUpdateDate(new Date());
				//按日计罚信息
				if(!ChangnengUtil.isNull(dm.getPenaltyDayId())){
					PenaltyDayWithBLOBs penaltyDay = penaltyDayMapper.selectByPrimaryKey(dm.getPenaltyDayId());
					dm.setPenaltyCaseNumber(penaltyDay.getDecisionNumber());
				}
				String configDId = dm.getConfigDocumentId();
				if(!ChangnengUtil.isNull(configDId)){
					SysConfigDocumentWithBLOBs sysConfigInfo = sysConfigDocumentMapper.selectByPrimaryKey(configDId);
					dm.setDocumentName(sysConfigInfo.getDocumentName());
				}
				documentMakeMapper.insertSelective(dm);
				docId = dm.getId();
				//往fileDocumentMake
				String[] split = attachIds.split(",");
				for (String string : split) {
					FileDocumentMake fdm = new FileDocumentMake();
					fdm.setId(string);
					fdm.setState(1);
					fdm.setDocumentMakeId(docId);
					fdmMapper.updateByPrimaryKeySelective(fdm);
				}
				
				//维护模块和文书制作列表关系表
				//20180921文书改造注掉 start
				/*String[] split2 = dm.getModelerCodeList().split(",");
				for (String string : split2) {
					String documentCode = documentRelationMapper.selectDocumentCodeByConfigidAndModCode(dm.getConfigDocumentId(), string);
					ModelerDocumentMake mdm = new ModelerDocumentMake();
					mdm.setDocumentMakeId(docId);
					mdm.setCaseId(baseInfo.getId());
					mdm.setCreateUserId(sysUsers.getId());
					mdm.setCreateUserName(sysUsers.getLoginname());
					mdm.setDocumentCode(documentCode);
					mdm.setMakeType(dm.getMakeType());
					if(!ChangnengUtil.isNull(dm.getPenaltyDayId())){
						mdm.setPenaltyDayId(dm.getPenaltyDayId());
					}
					mdm.setModelerCode(Integer.parseInt(string));
					String modelerName = convertModelerCode2Name(string);
					mdm.setModelerName(modelerName);
					mdmMapper.insertSelective(mdm);
				}*/
				//20180921文书改造注掉 end
			}else{
				//id不为空的时候附件表不需要再维护
				//更新document_make表中的modelerCodeList字段
				dm.setLastUpdateDate(new Date());
				//按日计罚信息
				if(!ChangnengUtil.isNull(dm.getPenaltyDayId())){
					PenaltyDayWithBLOBs penaltyDay = penaltyDayMapper.selectByPrimaryKey(dm.getPenaltyDayId());
					dm.setPenaltyCaseNumber(penaltyDay.getDecisionNumber());
				}
				documentMakeMapper.updateByPrimaryKeySelective(dm);
				DocumentMake documentMake = documentMakeMapper.selectByPrimaryKey(docId);
				//维护模块和文书制作列表关系表
				mdmMapper.deleteByDocId(docId);
				String penaltyDayId = documentMake.getPenaltyDayId();
				//20180921文书改造注掉 start
				/*String[] split2 = dm.getModelerCodeList().split(",");
				for (String string : split2) {
					String documentCode = documentRelationMapper.selectDocumentCodeByConfigidAndModCode(dm.getConfigDocumentId(), string);
					ModelerDocumentMake mdm = new ModelerDocumentMake();
					mdm.setDocumentMakeId(documentMake.getId());
					mdm.setCaseId(baseInfo.getId());
					mdm.setCreateUserId(sysUsers.getId());
					mdm.setCreateUserName(sysUsers.getLoginname());
					mdm.setDocumentCode(documentCode);
					mdm.setMakeType(dm.getMakeType());
					if(!ChangnengUtil.isNull(penaltyDayId)){
						mdm.setPenaltyDayId(penaltyDayId);
					}
					mdm.setModelerCode(Integer.parseInt(string));
					String modelerName = convertModelerCode2Name(string);
					mdm.setModelerName(modelerName);
					mdmMapper.insertSelective(mdm);
				}*/
				//20180921文书改造注掉 end
			}
			return docId;
		} catch (Exception e) {
			e.printStackTrace();
			throw new Exception("系统异常，保存失败！");
		}
	}
	
	/**
	 * 根据模块code转换成模块名称
	 * @param code
	 * @return
	 * <AUTHOR>
	 */
	private static String convertModelerCode2Name(String code){
		String modelerName = "";
		if("0".equals(code)){
			modelerName = "基本信息";
		}else if("1".equals(code)){
			modelerName = "简易行政处罚";
		}else if("2".equals(code)){
			modelerName = "一般行政处罚";
		}else if("3".equals(code)){
			modelerName = "行政命令";
		}else if("4".equals(code)){
			modelerName = "查封扣押";
		}else if("5".equals(code)){
			modelerName = "限产停产";
		}else if("6".equals(code)){
			modelerName = "行政拘留";
		}else if("7".equals(code)){
			modelerName = "环境污染犯罪";
		}else if("8".equals(code)){
			modelerName = "申请法院强制执行";
		}else if("9".equals(code)){
			modelerName = "其他移送";
		}else if("10".equals(code)){
			modelerName = "按日计罚";
		}
		return modelerName;
	}

	@Override
	public ResponseJson rotateImage(String id, String url, Integer degree) {
		ResponseJson json = null;
		String fastdfs_ip = PropertiesHandlerUtil.getValue("fastdfs.nginx.iptwo","fastdfs");
		String newUrl = "";
		try {
			if(!ChangnengUtil.isNull(degree)&& degree!=0){
				newUrl = ImageUtil.RotationImage(fastdfs_ip+url, degree);
			}
			FileDocumentMake fdMake = new FileDocumentMake();
			fdMake.setId(id);
			fdMake.setFileUrl(newUrl);
			fdmMapper.updateByPrimaryKeySelective(fdMake);
			json = new ResponseJson().success("200", "200", "操作成功！", null, newUrl);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			json = new ResponseJson().failure("500", "500", "操作失败，请重试！", e.getMessage(), null);
		}
		return json;
	}
}
