package org.changneng.framework.frameworkbusiness.service.impl;

import java.util.*;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import org.changneng.framework.frameworkbusiness.dao.CheckItemConfigMapper;
import org.changneng.framework.frameworkbusiness.entity.CheckItemConfig;
import org.changneng.framework.frameworkbusiness.entity.vo.CheckItemConfigTreeVO;
import org.changneng.framework.frameworkbusiness.service.CheckItemConfigService;

/**
 * 环境监管一件事-检查项配置 Service实现类
 * 简单的两级树形结构查询功能
 *
 * <AUTHOR> Generated
 * @date 2025-01-29
 */
@Service
@Transactional
public class CheckItemConfigServiceImpl implements CheckItemConfigService {

    private static final Logger logger = LogManager.getLogger(CheckItemConfigServiceImpl.class);

    @Autowired
    private CheckItemConfigMapper checkItemConfigMapper;

    /**
     * 获取完整的两级树形结构数据
     * 一级节点的PARENT_ID为"0"，二级节点的PARENT_ID为对应一级节点的ID
     */
    @Override
    public List<CheckItemConfigTreeVO> getTreeStructure() {
        try {
            // 查询所有平铺数据
            List<CheckItemConfig> allItems = checkItemConfigMapper.selectAll();

            if (allItems == null || allItems.isEmpty()) {
                return new ArrayList<>();
            }

            // 构建两级树形结构
            return buildTwoLevelTreeStructure(allItems);

        } catch (Exception e) {
            logger.error("获取树形结构数据失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 构建简单的两级树形结构
     * 第一步：根据parentId构建树形结构
     * 第二步：对节点进行排序
     */
    private List<CheckItemConfigTreeVO> buildTwoLevelTreeStructure(List<CheckItemConfig> allItems) {
        // 第一步：构建树形结构

        // 1.1 转换所有节点为VO对象
        List<CheckItemConfigTreeVO> allVOItems = new ArrayList<>();
        for (CheckItemConfig item : allItems) {
            allVOItems.add(convertToTreeVO(item));
        }

        // 1.2 找出所有根节点（parentId为空、null或"0"的节点）
        List<CheckItemConfigTreeVO> rootNodes = new ArrayList<>();
        for (CheckItemConfigTreeVO item : allVOItems) {
            String parentId = item.getParentId();
            if (parentId == null || parentId.trim().isEmpty() || "0".equals(parentId)) {
                rootNodes.add(item);
            }
        }

        // 1.3 为每个根节点找到其子节点
        for (CheckItemConfigTreeVO rootNode : rootNodes) {
            List<CheckItemConfigTreeVO> children = new ArrayList<>();
            for (CheckItemConfigTreeVO item : allVOItems) {
                if (rootNode.getId().equals(item.getParentId())) {
                    children.add(item);
                }
            }
            rootNode.setChildren(children);
        }

        // 第二步：对节点进行排序

        // 2.1 对根节点排序
        rootNodes.sort((a, b) -> {
            Integer sortA = a.getItemSort();
            Integer sortB = b.getItemSort();

            // 处理null值：null值排在后面
            if (sortA == null && sortB == null) {
                return 0;
            } else if (sortA == null) {
                return 1;
            } else if (sortB == null) {
                return -1;
            } else {
                return sortA.compareTo(sortB);
            }
        });

        // 2.2 对每个根节点的子节点排序
        for (CheckItemConfigTreeVO rootNode : rootNodes) {
            if (rootNode.getChildren() != null && !rootNode.getChildren().isEmpty()) {
                rootNode.getChildren().sort((a, b) -> {
                    Integer sortA = a.getItemSort();
                    Integer sortB = b.getItemSort();

                    // 处理null值：null值排在后面
                    if (sortA == null && sortB == null) {
                        return 0;
                    } else if (sortA == null) {
                        return 1;
                    } else if (sortB == null) {
                        return -1;
                    } else {
                        return sortA.compareTo(sortB);
                    }
                });
            }
        }

        return rootNodes;
    }

    /**
     * 将CheckItemConfig转换为CheckItemConfigTreeVO
     */
    private CheckItemConfigTreeVO convertToTreeVO(CheckItemConfig entity) {
        if (entity == null) {
            return null;
        }

        CheckItemConfigTreeVO treeVO = new CheckItemConfigTreeVO();
        treeVO.setId(entity.getId());
        treeVO.setItemName(entity.getItemName());
        treeVO.setParentId(entity.getParentId());
        treeVO.setItemSort(entity.getItemSort());
        treeVO.setCreateTime(entity.getCreateTime());
        treeVO.setRemark(entity.getRemark());

        return treeVO;
    }
}
