<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.QuestionNumberMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.QuestionNumber">
    <result column="AREA_CODE" jdbcType="VARCHAR" property="areaCode" />
    <result column="ORDER_NUMBER" jdbcType="DECIMAL" property="orderNumber" />
    <result column="UPDATE_TIME" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
  	area_code,order_number,update_time
  </sql>
  <select id="getByAreacode" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from QUESTIONNUMBER
    where AREA_CODE = #{areaCode,jdbcType=VARCHAR}
  </select>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.QuestionNumber">
    insert into QUESTIONNUMBER (AREA_CODE, ORDER_NUMBER, UPDATE_TIME
      )
    values (#{areaCode,jdbcType=VARCHAR}, #{orderNumber,jdbcType=DECIMAL}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.QuestionNumber">
    insert into QUESTIONNUMBER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        AREA_CODE,
      </if>
      <if test="orderNumber != null">
        ORDER_NUMBER,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="areaCode != null">
        #{areaCode,jdbcType=VARCHAR},
      </if>
      <if test="orderNumber != null">
        #{orderNumber,jdbcType=DECIMAL},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
     <update id="updateSelectiveByAreaCode" parameterType="org.changneng.framework.frameworkbusiness.entity.QuestionNumber">
    update QUESTIONNUMBER
     <set >  
      <if test="orderNumber != null" >  
        ORDER_NUMBER = #{orderNumber,jdbcType=INTEGER},
      </if> 
      <if test="updateTime != null" >  
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>   
    </set>  
    where AREA_CODE = #{areaCode,jdbcType=VARCHAR}
  </update>
</mapper>