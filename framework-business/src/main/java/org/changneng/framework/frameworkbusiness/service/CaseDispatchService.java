package org.changneng.framework.frameworkbusiness.service;

import java.util.List;

import org.changneng.framework.frameworkbusiness.entity.*;
import org.changneng.framework.frameworkbusiness.entity.filecase.CaseDispatchBean;
import org.changneng.framework.frameworkbusiness.entity.filecase.OneMapLawTableParams;

/**
  * 检查考核-环保部案件调度统计，业务处理类
  * <p>Title:CaseDispatchService </p>
  * <p>Description: </p>
  * <p>Company: </p> 
  * <AUTHOR>
  * @date 2018年3月21日-下午4:58:31
 */
public interface CaseDispatchService {

	/**
	 * 根据查询条件组装出结果信息
	 * @param searchBean
	 * @param sysUsers
	 * @return
	 * <AUTHOR>
	 * @date 2018年3月21日-下午5:06:46
	 */
	List<CaseDispatchBean> getCaseDispatchForAreaCode(CaseStatisSearchBean searchBean,SysUsers sysUsers,CaseSumTitleBean titleBean );

	List<OneMapLawTableParams> getOneMapLawTableData(OneMapLawTableParams params);




	List<WaterBlueSearch> getWaterBlueList(JckhSearchListBean searchBean, SysUsers sysUsers, CaseSumTitleBean titleBean);



}
