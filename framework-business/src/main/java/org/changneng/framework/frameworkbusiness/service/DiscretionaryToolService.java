package org.changneng.framework.frameworkbusiness.service;

import org.changneng.framework.frameworkbusiness.entity.DtDiscretionFactor;
import org.changneng.framework.frameworkbusiness.entity.DtDiscretionGenerateSaveBackUseBean;
import org.changneng.framework.frameworkbusiness.entity.DtDiscretionGenerateWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.DtDiscretionInfo;
import org.changneng.framework.frameworkbusiness.entity.DtFactorDetailsWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkcore.utils.PageBean;

/** 
* <AUTHOR> 
* @version 2018年11月28日 下午1:52:38 
* 类说明         自由裁量工具service接口
*/
public interface DiscretionaryToolService {
	//自由裁量工具主页列表
	PageBean<DtDiscretionFactor> getDtDiscretionFactorList(DtDiscretionFactor dtDiscretionFactor,String pageNum,String pageSize);
	//裁量因素选择list
	PageBean<DtFactorDetailsWithBLOBs> getDtFactorDetailsList(String id,String pageNum,String pageSize);
	//保存自由裁量生产数据
	DtDiscretionGenerateSaveBackUseBean saveDtDiscretionGenerate(DtDiscretionGenerateWithBLOBs dtDiscretionGenerateWithBLOBs) throws Exception;
	//根据id自由裁量生产数据表
	DtDiscretionGenerateWithBLOBs selectById(String id);
	//根据id因子列表
	DtDiscretionFactor selectFactorById(String id);
	//根据id因子详情
	DtFactorDetailsWithBLOBs selectDetailsById(String id);
	//根据id查询法律法规
	DtDiscretionInfo selectDtDiscretionInfo(String id);

}
