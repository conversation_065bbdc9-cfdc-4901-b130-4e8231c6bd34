package org.changneng.framework.frameworkbusiness.mongodb.bean.filecase;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 文书制作-41-行政处罚案件结案审批表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2017年11月22日 下午8:13:19
 */
@Document(collection = "ExecuDocMongo")
public class DocumentMongo41 {
	@Id
	private String id;
	
	/**
	 * 
	 */
	private String userDept;

	/**
	 * 审批号第一部分
	 */
	private String approvalNum1;
	
	/**
	 * 审批号第二部分
	 */
	private String approvalNum2;
	/**
	 * 行政处罚
     * 决定书文号
	 */
	private String decisionNumber;
	
	/**
	 * 简要案情及
     * 查处经过
	 */
	private String briefCase;
	
	/**
	 * 处理依据及结果
	 */
	private String punishBasis;
	
	/**
	 * 行政复议行政诉讼情况
	 */
	private String litigationSituation;
	
	/**
	 * 处罚执行情况及罚没财物的处置情况
	 */
	private String confIllegalText;
	
	/**
	 * 备注
	 */
	private String remark;
	
	/**
	 * 执法对象名称
	 */
	private String lawObjName;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getApprovalNum1() {
		return approvalNum1;
	}

	public void setApprovalNum1(String approvalNum1) {
		this.approvalNum1 = approvalNum1;
	}

	public String getApprovalNum2() {
		return approvalNum2;
	}

	public void setApprovalNum2(String approvalNum2) {
		this.approvalNum2 = approvalNum2;
	}

	public String getDecisionNumber() {
		return decisionNumber;
	}

	public void setDecisionNumber(String decisionNumber) {
		this.decisionNumber = decisionNumber;
	}

	public String getBriefCase() {
		return briefCase;
	}

	public void setBriefCase(String briefCase) {
		this.briefCase = briefCase;
	}

	public String getPunishBasis() {
		return punishBasis;
	}

	public void setPunishBasis(String punishBasis) {
		this.punishBasis = punishBasis;
	}

	public String getLitigationSituation() {
		return litigationSituation;
	}

	public void setLitigationSituation(String litigationSituation) {
		this.litigationSituation = litigationSituation;
	}

	public String getConfIllegalText() {
		return confIllegalText;
	}

	public void setConfIllegalText(String confIllegalText) {
		this.confIllegalText = confIllegalText;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getLawObjName() {
		return lawObjName;
	}

	public void setLawObjName(String lawObjName) {
		this.lawObjName = lawObjName;
	}

	public String getUserDept() {
		return userDept;
	}

	public void setUserDept(String userDept) {
		this.userDept = userDept;
	}
	
}
