package org.changneng.framework.frameworkbusiness.entity;

import java.util.Date;

public class ZfqkCheckObjStatistic {
    private String id;

    private Date updateDate;

    private String lawDeptId;

    private String lawDeptName;

    private String belongCode;

    private String belongName;

    private String checkObjId;
    
    private Integer belongTaskType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    public Date getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Date updateDate) {
        this.updateDate = updateDate;
    }

    public String getLawDeptId() {
        return lawDeptId;
    }

    public void setLawDeptId(String lawDeptId) {
        this.lawDeptId = lawDeptId == null ? null : lawDeptId.trim();
    }

    public String getLawDeptName() {
        return lawDeptName;
    }

    public void setLawDeptName(String lawDeptName) {
        this.lawDeptName = lawDeptName == null ? null : lawDeptName.trim();
    }

    public String getBelongCode() {
        return belongCode;
    }

    public void setBelongCode(String belongCode) {
        this.belongCode = belongCode == null ? null : belongCode.trim();
    }

    public String getBelongName() {
        return belongName;
    }

    public void setBelongName(String belongName) {
        this.belongName = belongName == null ? null : belongName.trim();
    }

    public String getCheckObjId() {
        return checkObjId;
    }

    public void setCheckObjId(String checkObjId) {
        this.checkObjId = checkObjId == null ? null : checkObjId.trim();
    }

	public Integer getBelongTaskType() {
		return belongTaskType;
	}

	public void setBelongTaskType(Integer belongTaskType) {
		this.belongTaskType = belongTaskType;
	}

}