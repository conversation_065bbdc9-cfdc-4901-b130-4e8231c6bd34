<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.IndexScoreMapper" >
    <select id="getTaskList" parameterType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexTask" resultType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexTask">
        select uuid,area_Code as areaCode,area_Name as areaName,years,status,scores,store_Status as storeStatus,upload_Date as uploadDate,
        last_Modifier as lastModifier,last_Modify_Date as lastModifyDate from index_task where 1=1
        <if test="areaCode !=null and areaCode !='' ">
            and area_Code=#{areaCode}
        </if>
        <if test="years !=null and years !='' ">
            and years=#{years}
        </if>
        <if test="status !=null and status !=''">
            and status=#{status}
        </if>
        <if test="storeStatus !=null and storeStatus !=''">
            and store_Status=#{storeStatus}
        </if>
        <if test="uuid !=null and uuid !=''">
            and uuid=#{uuid}
        </if>
        order by  years desc,last_Modify_Date desc,upload_Date desc,area_Code
    </select>

    <insert id="insertTask" parameterType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexTask">
        insert into index_task (uuid,AREA_CODE,AREA_NAME,YEARS,STATUS,STORE_STATUS,CREATE_USER_NAME,CREATE_DATE) values
        (#{uuid},#{areaCode},#{areaName},#{years},#{status},#{storeStatus},#{createUserName},#{createDate})
    </insert>

    <update id="updateTask" parameterType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexTask">
        update index_task set create_Date=create_Date
        <if test="status !=null and status != '' ">
            ,status=#{status}
        </if>
        <if test="storeStatus !=null and storeStatus != '' ">
            ,STORE_STATUS=#{storeStatus}
        </if>
        <if test="scores !=null ">
            ,scores=#{scores}
        </if>
        <if test="uploader !=null and uploader != '' ">
            ,uploader=#{uploader}
        </if>
        <if test="uploadDate !=null and uploadDate != '' ">
            ,upload_Date=#{uploadDate}
        </if>
        <if test="lastModifier !=null and lastModifier != '' ">
            ,last_Modifier=#{lastModifier}
        </if>
        <if test="lastModifyDate !=null and lastModifyDate != '' ">
            ,last_Modify_Date=#{lastModifyDate}
        </if>
        where uuid=#{uuid}
    </update>

    <select id="indexList" resultType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexScoreVO">
        select b.uuid,#{uuid} as taskid,a.uuid as dictid,a.type_One as typeOne,a.type_Two as typeTwo,a.contents,
        b.filename,b.file_Url as fileUrl
        from INDEX_DICT a left join INDEX_FILE b on a.uuid=b.DICTID and b.TASKID=#{uuid}
        order by a.orderno
    </select>

    <insert id="insertFile" parameterType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexFile">
        insert into INDEX_FILE (uuid,TASKID,DICTID,FILENAME,FILE_URL,FILE_TYPE,STATE,FILE_SIZE,CREATE_DATE,CREATE_USER_ID,CREATE_USER_NAME) values
        (#{uuid},#{taskid},#{dictid},#{filename},#{fileUrl},#{fileType},#{state},#{fileSize},#{createDate},#{createUserId},#{createUserName})
    </insert>

    <select id="scoreInfo" resultType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexScoreVO">
        select d.uuid,#{uuid} as taskid,a.uuid as dictid,c.ITEMID,a.type_One as typeOne,a.type_Two as typeTwo,a.contents,
        b.filename,b.file_Url as fileUrl,c.names,a.source_Form as sourceForm,d.work_Info as workInfo,d.score
        from INDEX_DICT a left join INDEX_FILE b on a.uuid=b.DICTID and b.TASKID=#{uuid}
        left join INDEX_DICT_ITEM c on c.pid =a.uuid
        left join INDEX_SCORE d on d.ITEMID=c.ITEMID and  d.TASKID=#{uuid}
        order by a.orderno,c.orderno
    </select>

    <insert id="insertScore" parameterType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexScore">
        insert into INDEX_SCORE (UUID,TASKID,ITEMID,SCORE,WORK_INFO) values
        (#{uuid},#{taskid},#{itemid},#{score},#{workInfo})
    </insert>

    <select id="scoreReportByYear" resultType="org.changneng.framework.frameworkbusiness.entity.XnListInfo">
        select a.uuid as id,a.area_Code as belongAreaId,a.area_Code as belongCode,min(a.area_Name) as belongAreaName,min(a.scores) as score,
        sum(case when d.uuid='P10001001' then b.score else 0 end ) as jsxmhjjg,
        sum(case when d.uuid='P10001002' then b.score else 0 end ) as ssjzf,
        sum(case when d.uuid='P10001003' then b.score else 0 end ) as pwxkzf,
        sum(case when d.uuid='P10001004' then b.score else 0 end ) as lfxj,
        sum(case when d.uuid='P10001005' then b.score else 0 end ) as lhldzf,
        sum(case when d.uuid='P10001006' then b.score else 0 end ) as zrbhdzf,
        sum(case when d.uuid='P10001007' then b.score else 0 end ) as xfzf,
        sum(case when d.uuid='P10001008' then b.score else 0 end ) as sdzzf,
        sum(case when d.uuid='P10001009' then b.score else 0 end ) as zxxd,
        sum(case when d.uuid='P100010010' then b.score else 0 end ) as zfryhyd,
        sum(case when d.uuid='P100010011' then b.score else 0 end ) as yjjb,
        sum(case when d.uuid='P100010012' then b.score else 0 end ) as jdbf,
        sum(case when d.uuid='P100010013' then b.score else 0 end ) as bazl,
        sum(case when d.uuid='P100010014' then b.score else 0 end ) as bajc,
        sum(case when d.uuid='P100010015' then b.score else 0 end ) as jjcx,
        sum(case when d.uuid='P100010016' then b.score else 0 end ) as jjzl,
        sum(case when d.uuid='P100010017' then b.score else 0 end ) as jjnl
        from INDEX_TASK a inner join INDEX_SCORE b on a.uuid=b.taskid
        inner join INDEX_DICT_ITEM c on b.itemid=c.itemid
        inner join INDEX_DICT d on d.uuid=c.pid
        where a.years=#{years}
        group by a.area_Code,a.uuid
    </select>

    <select id="findByType" resultType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexFile">
        select UUID,TASKID,dictid,FILENAME,FILE_URL as fileUrl,file_Size as fileSize from INDEX_FILE where taskid=#{taskid} and dictid=#{dictid} and rownum &lt;2
    </select>
    <select id="findFileById" resultType="org.changneng.framework.frameworkbusiness.entity.efficiency.IndexFile">
        select UUID,TASKID,dictid,FILENAME,FILE_URL as fileUrl,file_Size as fileSize from INDEX_FILE where uuid=#{uuid}
    </select>
    <delete id="deleteFileById">
        delete from INDEX_FILE where uuid=#{uuid}
    </delete>
</mapper>