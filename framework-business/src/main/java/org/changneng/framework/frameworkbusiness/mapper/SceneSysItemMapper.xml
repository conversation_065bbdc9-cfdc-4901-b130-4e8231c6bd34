<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.changneng.framework.frameworkbusiness.dao.SceneSysItemMapper">
  <resultMap id="BaseResultMap" type="org.changneng.framework.frameworkbusiness.entity.SceneSysItem">
   <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="SYS_MODELER_ID" jdbcType="VARCHAR" property="sysModelerId" />
    <result column="SCENE_ITEM_ID" jdbcType="VARCHAR" property="sceneItemId" />
    <result column="LOCTION" jdbcType="VARCHAR" property="loction" />
    <result column="is_required" jdbcType="VARCHAR" property="isRequired" />
  </resultMap>
  <insert id="insert" parameterType="org.changneng.framework.frameworkbusiness.entity.SceneSysItem">
    insert into SCENE_SYS_ITEM (ID, SYS_MODELER_ID, SCENE_ITEM_ID, 
      LOCTION,is_required)
    values (#{id,jdbcType=VARCHAR}, #{sysModelerId,jdbcType=VARCHAR}, #{sceneItemId,jdbcType=VARCHAR},
      #{loction,jdbcType=VARCHAR}, #{isRequired,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SceneSysItem">
    <selectKey keyProperty="id" order="BEFORE" resultType="String">
  		select sys_guid() from dual 
  	</selectKey>
    insert into SCENE_SYS_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="sysModelerId != null">
        SYS_MODELER_ID,
      </if>
      <if test="sceneItemId != null">
        SCENE_ITEM_ID,
      </if>
      <if test="loction != null">
        LOCTION,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="sysModelerId != null">
        #{sysModelerId,jdbcType=VARCHAR},
      </if>
      <if test="sceneItemId != null">
        #{sceneItemId,jdbcType=VARCHAR},
      </if>
      <if test="loction != null">
        #{loction,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from SCENE_SYS_ITEM
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteBySysRefineTemplateId" parameterType="java.lang.String">
    delete from SCENE_SYS_ITEM
    where SYS_MODELER_ID = #{sysModelerId,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteBySysModelerId" parameterType="java.lang.String">
    delete from SCENE_SYS_ITEM
    where SYS_MODELER_ID = #{sysModelerId,jdbcType=VARCHAR}
  </delete>
  <select id="selectBySysRefineTemplateId" parameterType="java.lang.String" resultMap="BaseResultMap">
  	select ID, SYS_MODELER_ID, SCENE_ITEM_ID, LOCTION
  	from SCENE_SYS_ITEM where 1=1 and
 	SYS_MODELER_ID = #{sysModelerId,jdbcType=VARCHAR}
  </select>
  <!-- 根据专项行动类型查询  专项模板对应的检查项信息-->
  <select id="selectSceneSysItemTemplateType" parameterType="java.lang.String" resultMap="BaseResultMap">
	  	SELECT
		si.ID,si.SYS_MODELER_ID,si.SCENE_ITEM_ID,si.LOCTION
	FROM
		SCENE_SYSSPECAIL_MODEL ms
	LEFT JOIN SCENE_SYS_ITEM si ON MS. ID = SI.SYS_MODELER_ID
	WHERE
		MS.TEMPLATE_TYPE = #{templateType,jdbcType=VARCHAR}
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="org.changneng.framework.frameworkbusiness.entity.SceneSysItem">
    update SCENE_SYS_ITEM
    <set>
      <if test="sceneItemId != null">
        SCENE_ITEM_ID = #{sceneItemId,jdbcType=VARCHAR},
      </if>
      <if test="sysModelerId != null">
        SYS_MODELER_ID = #{sysModelerId,jdbcType=VARCHAR},
      </if>
      <if test="loction != null">
        LOCTION = #{loction,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
</mapper>