package org.changneng.framework.frameworkcore.utils;

import java.security.MessageDigest;
import java.util.Calendar;
import java.util.Random;

public class MD5Helper {
	 /** 
     * @Description:加密-32位小写 
     */  
    public static String encrypt32(String encryptStr) {  
        MessageDigest md5=null;  
        try {  
            md5 = MessageDigest.getInstance("MD5");  
            byte[] md5Bytes = md5.digest(encryptStr.getBytes());  
            StringBuffer hexValue = new StringBuffer();  
            for (int i = 0; i < md5Bytes.length; i++) {  
                int val = ((int) md5Bytes[i]) & 0xff;  
                if (val < 16)  
                    hexValue.append("0");  
                hexValue.append(Integer.toHexString(val));  
            }  
            encryptStr = hexValue.toString();  
        } catch (Exception e) {  
            throw new RuntimeException(e);  
        }  
        return encryptStr;  
    }  
  
    /** 
     * @Description:加密-16位小写 
     */  
        public static String encrypt16(String encryptStr) {
        return encrypt32(encryptStr).substring(8, 24);  
    }  
  
    public static void main(String[] args) { 
    	//System.out.println(System.currentTimeMillis());
        //String encryptStr = "admin123456";  
        //System.out.println(MD5Helper.encrypt32(encryptStr));  
        //System.out.println(MD5Helper.encrypt16(encryptStr));  
        /*Random rand =new Random();
        for (int i = 0; i <500; i++) {
        	 System.out.println(rand.nextInt(10));
		}*/
    	Calendar calendar = Calendar.getInstance();
    	int second = calendar.get(Calendar.SECOND);
    	System.out.println(second);
        
    }  
}
