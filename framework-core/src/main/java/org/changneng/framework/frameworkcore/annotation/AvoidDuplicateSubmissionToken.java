package org.changneng.framework.frameworkcore.annotation;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
/**
 * @ClassName AvoidDuplicateSubmitToken
 * @Description 自定义防止表单重复提交的基于token的注解
 * <AUTHOR>
 * @Date 2016年12月12日 下午12:47:11
 * @version 1.0.0
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AvoidDuplicateSubmissionToken {
    boolean adstSaveToken() default false;
    boolean adstRemoveToken() default false;
}
