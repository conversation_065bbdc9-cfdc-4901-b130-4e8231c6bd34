# Redis settings
#redis-mater-node
#master.host=*************
#master.port=26379
#redis-slave-node
#slaver.host=*************
#slaver.port=26379
#sentinel鐨勯壌鏉冨瘑鐮?
#redis.sentinel.masterName=mymaster
#redis.sentinel.password=mina
# local
redis.host=**************
redis.port=6379
redis.pass =123456.@
#鍐呯綉
#todo
#redis.host=127.0.0.1
#todo
#redis.port=6379
#澶栫綉
#redis.host=*************
#redis.port=16900
#todo
#redis.pass =
#鏈?12澶х┖闂茶繛鎺ユ暟
redis.maxIdle=500
#鏈?灏忛棽缃繛鎺ユ暟
redis.minIdle=300
#鏈?澶ц繛鎺ユ暟
redis.maxTotal=5000
#鍦ㄨ幏鍙栬繛鎺ョ殑鏃跺?欐鏌ユ湁鏁堟??, 榛樿false
redis.testOnBorrow=true

LOGIN_TOKEN_EXP_TIMES=43200

SECRET_KEY_EXP_TIMES=1440

REQUEST_TIME_OUT=********

LOCK_ACCOUNT_TIME=1
