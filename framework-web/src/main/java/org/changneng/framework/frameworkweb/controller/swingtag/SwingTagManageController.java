package org.changneng.framework.frameworkweb.controller.swingtag;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.entity.LawEnforceObjectWithBLOBs;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.ZTreeNodeBean;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTag;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagAdjunct;
import org.changneng.framework.frameworkbusiness.entity.swingtag.SwingTagManageSeach;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatCommit;
import org.changneng.framework.frameworkbusiness.repeatCommit.CheckRepeatToken;
import org.changneng.framework.frameworkbusiness.repeatCommit.PutSessionValue;
import org.changneng.framework.frameworkbusiness.service.ISysDeptMgService;
import org.changneng.framework.frameworkbusiness.service.ZfdxManagerService;
import org.changneng.framework.frameworkbusiness.service.swingtag.SwingTagService;
import org.changneng.framework.frameworkbusiness.service.swingtag.SwingTagSubjectService;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.ExcelUtiles;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

@RequestMapping("swingtagmanage")
@Controller
public class SwingTagManageController {
	
	@Autowired 
	private SwingTagService swingTagService;
	
	@Autowired
	private ZfdxManagerService zfdxManagerService;
	
	@Autowired
	private ISysDeptMgService deptService;
	
	@Autowired
	private SwingTagSubjectService swingTagSubjectService;
	
	/**
	 * 挂牌任务管理 跳转页面
	 * 
	 */
	@RequestMapping(value="/swing_tag_manage_page", method = RequestMethod.POST)
	public ModelAndView swingTagManagePage(String back, HttpServletRequest request,HttpServletResponse response){
		ModelAndView mav = new ModelAndView("gpdb/gpdb-glgp");
		// 1表示从返回按钮重新发起请求，只有是返回才把各种参数放到页面，否则会导致点击左侧菜单栏的时候也有参数存在
		if ("1".equals(back)) {
			HttpSession session = request.getSession();
			Map<String, String> paramsMap = (Map<String, String>) session.getAttribute("paramsInSession");
			for (String key : paramsMap.keySet()) {
				
					paramsMap.put(key, paramsMap.get(key));
			}
			// 防止多开窗口param混乱
			if (!"/swingtagmanage/swing_tag_manage_page".equals(session.getAttribute("preUrl"))) {
				return mav;
			}
			String params = JacksonUtils.toJsonString(paramsMap);
			mav.addObject("params", params);
			System.out.println(params.toString());
			session.removeAttribute("paramsInSession");
		}
		return mav;
	}
	/**
	 * start-swingtag-page
	 * 发起挂牌页面跳转
	 */
	@PutSessionValue
	@RequestMapping(value="/start-swingtag-page", method = RequestMethod.POST)
	public ModelAndView startSwingTagManagePage(HttpServletRequest request,HttpServletResponse response,String swingtag_id){
		//获取主体信息结果集
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		SwingTag swingTag = swingTagService.getSwingTag(swingtag_id,sysUser);
		request.setAttribute("swingTagSnapInList", JacksonUtils.toJsonString(swingTag.getSwingTagSnapInList()));
		request.setAttribute("swingTag", swingTag);
		//根据挂牌督办id在附件表查询到该挂牌督办下的附件列表。
		List<SwingTagAdjunct> swingTagAdjunctList = swingTagSubjectService.selectBySwingTagId(swingtag_id);
		//将当前违法对象的在用附件个数返回到前台，用于判断附件是否为空
		request.setAttribute("swingTagAdjunctCount", swingTagAdjunctList.size());
		
		
		return new ModelAndView("gpdb/gpdb-fqgp");
	}
	/**
	 * query-swingtag-page
	 * 发起挂牌展示页面(只读)
	 */
	@RequestMapping(value="/query-swingtag-page", method = RequestMethod.POST)
	public ModelAndView querySwingTagManagePage(HttpServletRequest request,HttpServletResponse response,String swingtag_id){
		//获取主体信息结果集
		SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		SwingTag swingTag = swingTagService.getSwingTag(swingtag_id,sysUser);
		request.setAttribute("swingTag", swingTag);
		request.setAttribute("swingTagSnapInList", JacksonUtils.toJsonString(swingTag.getSwingTagSnapInList()));
		return new ModelAndView("gpdb/gpdb-view");
	}
	/**
	 * 挂牌任务管理 搜索
	 * @param request
	 * @param response
	 * @param swingTagManageSeach
	 * @return
	 */
	@ResponseBody
	@RequestMapping(value ="/swingtagmanageList")
	public ResponseJson swingTagManageList(HttpServletRequest request,HttpServletResponse response,SwingTagManageSeach swingTagManageSeach){
		ResponseJson json = new ResponseJson();
		try {
			PageBean<SwingTag> pageBean =  swingTagService.swingtagmanageList(swingTagManageSeach);
			json.success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查询成功", "查询挂牌任务管理成功", pageBean);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查询失败", e.getMessage(),null);
		}
		return  json ;
	}
	
	/**
	 *	挂牌-导出Excel
	 * @param searchBean
	 * @param model
	 * @param request
	 * @param response
	 * @return
	 * 
	 */
	@RequestMapping("/download-swingTagManage")
	public void downloadSwingTagManageExecl(SwingTagManageSeach swingTagManageSeach, Model model,HttpServletRequest request, HttpServletResponse response) {
		// 1.获取用户信息，判断用户权限
		
		try {
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			List<SwingTag> swingTagList = swingTagService.swingtagmanageDownList(swingTagManageSeach);
			String name = "挂牌任务管理.xls";
			String path = SwingTagManageController.class.getClassLoader().getResource("excel/" + name).getPath();
			ExcelUtiles.downExcel(path, name, "挂牌任务管理", swingTagList, response);			
		} catch (Exception e) {
			//logger.error(e);
		}
	}
	
	
	/**
	 * law-object-page
	 * 添加执法对象（处罚主体）model
	 * 
	 */
	@RequestMapping(value="/law-object-page")
	public ModelAndView lawObjectModelPage(HttpServletRequest request,HttpServletResponse response,
			String index,String swingTagSnapInId,String snapInId){
			request.setAttribute("swingTagSnapInId", swingTagSnapInId);
			request.setAttribute("index", index);
			request.setAttribute("snapInId", snapInId);
			return new ModelAndView("gpdb/lawObjectModel");
	}
	
	@RequestMapping(value="/select-law-object-page")
	public ModelAndView selectLawObjectModelPage(Model model, HttpServletRequest request,HttpServletResponse response,
	String lawObjectId){
		ModelAndView mav = null;
		if(!ChangnengUtil.isNull(lawObjectId)){
			LawEnforceObjectWithBLOBs lawEnforceObject = zfdxManagerService.selectByPrimaryKey(lawObjectId);
			mav = new ModelAndView("model/lawObjectModel");
			mav.addObject("lawEnforceObject", lawEnforceObject);
		}
		return mav;
	}
	
	/**
	 * chick-department-page
	 * 部门模态框加载
	 */
	@RequestMapping(value="/chick-department-page")
	public ModelAndView chickDepartmentModelPage(HttpServletRequest request,HttpServletResponse response){
		return new ModelAndView("gpdb/departmentModel");
	}
	
	
	/**
	 * 加载部门树
	 * @param queryType 查询类型：1案件部门树
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/getSwingTagDepts")
	@ResponseBody
	public Map<String,Object> querySwingTagDeptsZtree(String queryType, HttpServletRequest request){
		HashMap<String, Object> resMap = new HashMap<String, Object>();
		//String areaCode = request.getParameter("areaCode");
		try {
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			List<ZTreeNodeBean> ztreeData = deptService.querySwingTagDeptsZtree(sysUsers);
			String zNodes =JacksonUtils.toJsonString(ztreeData);
			
			resMap.put("data", zNodes);
			resMap.put("type", "success");
		} catch (Exception e) {
			resMap.put("type", "error");
			e.printStackTrace();
		}
		
		return resMap;
	}
	/**
	 * saveSwingTag
	 * 保存 挂牌
	 */
	@ResponseBody
	//@CheckRepeatCommit
	@CheckRepeatToken
	@SysLogPoint(businessType=businessType.SWINGTAG_SAVE,dbOptType=dbType.ADD)
	@RequestMapping(value ="/saveSwingTag")
	public ResponseJson saveSwingTag(HttpServletRequest request,HttpServletResponse response,
			@Validated SwingTag swingTag ,BindingResult bResult){
		ResponseJson json = new ResponseJson();
		try {
			if(!bResult.hasErrors()){
				json=swingTagService.saveSwingTag(swingTag);
				json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功", "保存挂牌成功", null);
				}else{
					json=json.success("200", "200", bResult.getFieldError().getDefaultMessage(), null, null);
			}
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.SAVE_FAILURE.toString(), "保存失败", e.getMessage(),null);
		}
		return  json ;
	}
	/**
	 * timeSwingTag
	 * 暂存
	 */
	@ResponseBody
	@CheckRepeatCommit
	@SysLogPoint(businessType=businessType.SWINGTAG_SAVE,dbOptType=dbType.ADD)
	@RequestMapping(value ="/timeSwingTag")
	public ResponseJson timeSwingTag(HttpServletRequest request,HttpServletResponse response,
			 SwingTag swingTag){
		ResponseJson json = new ResponseJson();
		try {
				json=swingTagService.saveSwingTag(swingTag);
				System.out.println("id:"+swingTag.getId());
				System.out.println("id:"+swingTag.getId());
				System.out.println("id:"+swingTag.getId());
				System.out.println("id:"+swingTag.getId());
				System.out.println("sendAttId:"+swingTag.getSendAttId());
				
			//	json.success(HttpStatus.OK.toString(), SystemStatusCode.SAVE_SUCCESS.toString(), "保存成功", "保存挂牌成功", null);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.SAVE_FAILURE.toString(), "保存失败", e.getMessage(),null);
			e.printStackTrace();
		}
		return  json ;
	}
	/**
	 * deleteSnapIn
	 * 删除办理单位
	 */
	@ResponseBody
	@CheckRepeatCommit
	@RequestMapping(value ="/deleteSnapIn")
	public ResponseJson deleteSnapIn(HttpServletRequest request,HttpServletResponse response,
		String	swingTagId ,String swingTagSnapId){
		ResponseJson json = new ResponseJson();
		try {
				json=swingTagService.deleteSnapIn(swingTagId,swingTagSnapId);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "保存失败", e.getMessage(),null);
		}
		return  json ;
	}
	/**
	 * deleteSubject
	 * 删除违法主体
	 */
	@ResponseBody
	@CheckRepeatCommit
	@RequestMapping(value ="/deleteSubject")
	public ResponseJson deleteSubject(HttpServletRequest request,HttpServletResponse response,
		String	swingTagId ,String swingTagSubjectId){
		ResponseJson json = new ResponseJson();
		try {
				json=swingTagService.deleteSubject(swingTagId,swingTagSubjectId);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(),null);
		}
		return  json ;
	}
	
	/**
	 * deleteAdjuct
	 * 删除附件 
	 */
	@ResponseBody
	@CheckRepeatCommit
	@RequestMapping(value ="/deleteAdjuct")
	public ResponseJson deleteAdjuct(HttpServletRequest request,HttpServletResponse response,
		String	id){
		ResponseJson json = new ResponseJson();
		try {
				json=swingTagService.deleteAdjuct(id);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(),null);
		}
		return  json ;
	}
	
	/**
	 * deleteSwingTag
	 * 删除主体信息
	 */
	@ResponseBody
	@CheckRepeatCommit
	@SysLogPoint(businessType=businessType.SWINGTAG_DELETE,dbOptType=dbType.DELETE)
	@RequestMapping(value ="/deleteSwingTag")
	public ResponseJson deleteSwingTag(HttpServletRequest request,HttpServletResponse response,
			 String swingTagId){
		ResponseJson json = new ResponseJson();
		try {
			json = swingTagService.deleteSwingTag(swingTagId);
		} catch (Exception e) {
			json.error(HttpStatus.BAD_REQUEST.toString(), SystemStatusCode.DELETE_ERROR.toString(), "删除失败", e.getMessage(),null);
		}
		return  json ;
	}
	/**
	 * checkSwingTagMark
	 * 挂牌通知文号 去重查询
	 * 
	 */
	@RequestMapping(value="checkSwingTagMark")
	@ResponseBody
	public Map<String, Boolean> checkSwingTagMark(String swingTagMark,String id){
		Map<String, Boolean> map = new HashMap<String, Boolean>();
		List<SwingTag> list = swingTagService.checkSwingTagMark(swingTagMark);
		if (null == list || list.size() == 0) {
			// 为空的情况
			map.put("valid", true);
			
		} else {
			Boolean notExist = true;
			// 不为空的情况
			//map.put("valid", false);
			if(!"".equals(id) && id != null){
				//id不为空
				//for循环对比id，如果id不相等，说明已经存在有相同编号，返回false
				for (SwingTag reocrd : list) {
					if(!id.equals(reocrd.getId())){
						notExist = false;
						break;
					}
				}
				map.put("valid", notExist);
			}else{
				//如果id为空，说明肯定是false
				map.put("valid", false);
			}
		}
		return map;
	}
}
