package org.changneng.framework.frameworkweb.controller;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.LogManager;
import org.apache.log4j.Logger;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.businessType;
import org.changneng.framework.frameworkbusiness.aop.SysLogPoint.dbType;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.SysDepartment;
import org.changneng.framework.frameworkbusiness.entity.SysResources;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.ZTreeNodeBean;
import org.changneng.framework.frameworkbusiness.service.ICommonService;
import org.changneng.framework.frameworkbusiness.service.ISysDeptMgService;
import org.changneng.framework.frameworkbusiness.service.impl.TAreaServiceImpl;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.Const;
import org.changneng.framework.frameworkcore.utils.JacksonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

@Controller
@RequestMapping(value="/sysDept")
public class DeptMgController {

	private Logger logger = LogManager.getLogger(DeptMgController.class);
	
	@Autowired
	private ISysDeptMgService deptService;
	@Autowired
	private TAreaServiceImpl tAreaService;


	@Autowired  
	private StringRedisTemplate redisTemplate;  
	
	@Autowired
	private ICommonService commonService;
	/**
	 * 部门管理-跳转
	 * @return
	 */
	@SysLogPoint(businessType = businessType.COMMON_OPT,dbOptType = dbType.QUERY)
	@RequestMapping("/deptMain")
	public ModelAndView skipToDeptMain(String menuId){
		ModelAndView mav=new ModelAndView("system/userMg/deptMgMain");
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		List<SysResources>   res=commonService.queryRolesSysResources(sysUsers.getUsername(), menuId);
		String rs="404";
		String kbjhjjcbzrs = "404";
		String kxzhjjcbzrs = "404";
		for (int i = 0; i < res.size(); i++) {
			if(res.get(i).getResourceDesc().equals("editDepartmentName")){
				rs="200";
			}
			if(res.get(i).getResourceDesc().equals("KBJHJJCBZRS")){
				kbjhjjcbzrs="200";
			}
			if(res.get(i).getResourceDesc().equals("KXZHJJCBZRS")){
				kxzhjjcbzrs="200";
			}
			
		}
		mav.addObject("kbjhjjcbzrs",kbjhjjcbzrs);
		mav.addObject("kxzhjjcbzrs",kxzhjjcbzrs);
		mav.addObject("res",rs);
		mav.addObject("sysUsers",sysUsers.getUsername());
		mav.addObject("menuId",menuId);
		logger.info("部门管理！");
		return mav;
	}
	
	
	/**
	 * 加载部门树
	 * @param queryType 查询类型：1案件部门树
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/getDepts")
	@ResponseBody
	public Map<String,Object> queryAllDeptForZtree(String queryType, HttpServletRequest request){
		HashMap<String, Object> resMap = new HashMap<String, Object>();
		//String areaCode = request.getParameter("areaCode");
		try {
			SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			List<ZTreeNodeBean> ztreeData = deptService.loadDeptTree(sysUsers.getBelongAreaId(), queryType);
			
			String zNodes =JacksonUtils.toJsonString(ztreeData);
			
			resMap.put("data", zNodes);
			resMap.put("type", "success");
		} catch (Exception e) {
			resMap.put("type", "error");
			e.printStackTrace();
		}
		
		return resMap;
	}
	
	/**
	 * 获取全部部门
	 * @param queryType
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/getAllDepts")
	@ResponseBody
	public Map<String,Object> queryAllDeptsForZtree(String queryType, HttpServletRequest request){
		HashMap<String, Object> resMap = new HashMap<String, Object>();
		try {
			List<ZTreeNodeBean> ztreeData = deptService.loadDeptTree(Const.PROVINCE_CODE, queryType);
			String zNodes =JacksonUtils.toJsonString(ztreeData);
			resMap.put("data", zNodes);
			resMap.put("type", "success");
		} catch (Exception e) {
			resMap.put("type", "error");
			e.printStackTrace();
		}
		
		return resMap;
	}
	/**
	 * 获取用户角色权限
	 */
		@RequestMapping("/getCharacter")
		@ResponseBody
		public JsonResult getCharacter(HttpServletRequest request){
			JsonResult jsonResult = new JsonResult();
			try {
				HashMap<String,String> map = new HashMap<>();
				boolean b = tAreaService.selectRolesByModule(map);
				if(b){
					jsonResult.setResult("200");
				}else{
					jsonResult.setResult("400");
				}
			}catch (Exception e){
				jsonResult.setResult("500");
				e.printStackTrace();
			}
			return jsonResult;

		}

	
	/**
	 * 点击部门根据其id获取部门信息
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/getDeptInfo")
	@ResponseBody
	public Map<String,Object> queryDeptInfoByID(HttpServletRequest request){
		HashMap<String,Object> resMap = new HashMap<String,Object>();
		String deptId = request.getParameter("deptID");
		
		try {
			SysDepartment dept = deptService.selectDeptById(deptId);
			if(dept!=null){
				resMap.put("data", JacksonUtils.toJsonString(dept));
				resMap.put("type","success");
			}else{
				resMap.put("type","error");
			}
			
			
		} catch (Exception e) {
			resMap.put("type","error");
			e.printStackTrace();
		}
		
		return resMap;
	};
	
	/**
	 * 保存部门信息-即编辑
	 * @param sysdepartment
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/saveDept")
	@SysLogPoint(businessType=businessType.UPDATE_DEPARTMENT,dbOptType=dbType.EDIT)
	@ResponseBody
	public Map<String,Object> saveDeptInfo(@Validated SysDepartment sysdepartment,BindingResult result,HttpServletRequest request){
		HashMap<String,Object> resMap = new HashMap<String,Object>();
		
		try {
			if(!result.hasErrors()){
				SysDepartment oldDeparetment = deptService.selectDeptById(sysdepartment.getDepartmentId());
				int res = deptService.saveDeptInfo(sysdepartment);
				if(res==1){
					resMap.put("type", Const.RESULT_SUCCESS);
					if (!ChangnengUtil.isNull(oldDeparetment) && !ChangnengUtil.isNull(sysdepartment.getDepartmentName())&& !oldDeparetment.getDepartmentName().equals(sysdepartment.getDepartmentName())) {
						redisTemplate.opsForSet().add("today_update_deptment",sysdepartment.getDepartmentId());
					}
				}else{
					resMap.put("type", Const.RESULT_ERROR);
					resMap.put("message","保存数据失败");
				}
			}else{
					resMap.put("type",Const.RESULT_ERROR);
					resMap.put("message",result.getFieldError().getDefaultMessage());
				}
		} catch (Exception e) {
			resMap.put("type", Const.RESULT_ERROR);
			resMap.put("message","后台出现异常");
			e.printStackTrace();
		}
		
		return resMap;
	};
	
	/**
	 * 根据deptName查询该部门名称是否在同一个执法对象下已存在
	 * @param recordName
	 * @param taskId
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/checkRepeat",method=RequestMethod.POST)
	@ResponseBody
	public Map<String,Object> queryIsRepeatRecord(String deptID,String deptName,int flag, HttpServletRequest request){
		Map<String,Object> resMap = new HashMap<String,Object>();
		
		try {
			resMap.put("valid", deptService.isRepeatName(deptID, deptName, flag));
		} catch (Exception e) {
			resMap.put("valid", 1);
			e.printStackTrace();
		}
		return resMap;
	}
	
	
	/**
	 * 保存子部门信息-即新建
	 * @param sysdepartment
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/saveSubDept")
	@SysLogPoint(businessType=businessType.SAVE_DEPARTMENT,dbOptType=dbType.ADD)
	@ResponseBody
	public Map<String,Object> saveSubDeptInfo(@ModelAttribute("sysdepartment")SysDepartment sysdepartment,HttpServletRequest request){
		HashMap<String,Object> resMap = new HashMap<String,Object>();
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		sysdepartment.setCreateUserid(sysUsers.getId());
		sysdepartment.setCreateUsername(sysUsers.getUsername());
		try {
			SysDepartment oldDeparetment = deptService.selectDeptById(sysdepartment.getDepartmentId());
			
			int res = deptService.saveSubDeptInfo(sysdepartment);
			if(res==1){
				resMap.put("type", "success");
				if (!ChangnengUtil.isNull(oldDeparetment) && !ChangnengUtil.isNull(sysdepartment.getDepartmentName())&& !oldDeparetment.getDepartmentName().equals(sysdepartment.getDepartmentName())) {
					redisTemplate.opsForSet().add("today_update_deptment",sysdepartment.getDepartmentId());
				}
			}else{
				resMap.put("type", "error");
			}
		} catch (Exception e) {
			resMap.put("type", "error");
			e.printStackTrace();
		}
		
		return resMap;
	};
	
	
	/**
	 * 删除部门
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/delDept")
	@SysLogPoint(businessType=businessType.DELETE_DEPARTMENT,dbOptType=dbType.DELETE)
	@ResponseBody
	public Map<String,String> delRoel(HttpServletRequest request){
		HashMap<String, String> retMap = new HashMap<String, String>();
		
		String departmentId = request.getParameter("departmentId");
		
		if(departmentId!=null&&!"".equals(departmentId)){
			int res = 0;
			try {
				res = deptService.delDeptInfo(departmentId);
				if(res>0){//删除成功
					retMap.put("type", "success");
				}else{//删除失败
					if(res==-1){//初始部门
						retMap.put("type", "initdept");
					}else if(res==-3){//存在子部门
						retMap.put("type", "havesubdept");
					}else if(res==-2){//存在用户
						retMap.put("type", "haveuser");
					}else{
						retMap.put("type", "error");
					}
				}
			} catch (Exception e) {
				retMap.put("type", "error");
				e.printStackTrace();
			}
		}else{
			retMap.put("type", "error");
		}
		return retMap;
	}
	
	
	/**
	 * 新建执法部门时，查询上级部门是否已经存在执法部门 
	 * @param deptID
	 * @param deptName
	 * @param flag
	 * @param request
	 * @return
	 */
	@RequestMapping(value="/check-islawdept",method=RequestMethod.POST)
	@ResponseBody
	public Map<String,Object> queryIslawdept(String deptID,String deptName,int flag, HttpServletRequest request){
		Map<String,Object> resMap = new HashMap<String,Object>();
		try {
			resMap.put("valid", deptService.checkParentIslawdept(deptID, deptName, flag));
		} catch (Exception e) {
			resMap.put("valid", 1);
			e.printStackTrace();
		}
		return resMap;
	}
}
