package org.changneng.framework.frameworkweb.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.entity.SysFiles;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.service.ActionRequireFilessService;
import org.changneng.framework.frameworkbusiness.service.TXchjzfService;
import org.changneng.framework.frameworkbusiness.service.filecase.FtpService;
import org.changneng.framework.frameworkbusiness.service.filecase.InitCaseFileService;
import org.changneng.framework.frameworkcore.utils.FileUtil;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

@RequestMapping("actionFileManager")
@Controller
public class ActionlRequireFilessController {

	private static Logger logger = LogManager.getLogger(ActionlRequireFilessController.class);
	
	@Autowired
	private FtpService  ftpService;
 
	@Autowired
	private  InitCaseFileService initCaseFileService;
	
	@Autowired
	private ActionRequireFilessService actionRequireFilessService;
	@RequestMapping(value = "downloadFile")
	public void pdfDownload(HttpServletRequest request,HttpServletResponse response,String filename,String url){
 
		System.err.println("进入下载");
		try {
			if(url!=null && !"".equals(url)){
				
				FileUtil.downloadFDFSUtils(request, response, url, filename);
				System.out.println("下载结束");
			}
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.info(e);
		}
	}
	
	@RequestMapping(value="/uploadActionFile",method=RequestMethod.POST)
	@ResponseBody
	 public ResponseJson actionFileUpload(HttpServletRequest request,HttpServletResponse response) {
		System.out.println("actionFileUpload");
		SysUsers sysUsers = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
		List<SysFiles> list=null;
		try {
			list = actionRequireFilessService.uploadActionlRequireFiless(request, response, sysUsers);
			return new ResponseJson().success(HttpStatus.OK.toString(),"000","上传成功","文件上传成功",list);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(),"000","上传失败","文件上传失败",list);
		}
    } 
	
	/**
	 * 删除图片
	 * @param request
	 * @param response
	 * @param id
	 * @return
	 */
	@RequestMapping(value="/deleteActionFile",method=RequestMethod.POST)
	@ResponseBody
	public ResponseJson actionFileDelete(HttpServletRequest request,HttpServletResponse response,String id){
		
		ResponseJson json = actionRequireFilessService.deleteActionlRequireFilessById(id);
		
		return json;
	}
	
	@Autowired
	private TXchjzfService txService ;
	
	@RequestMapping(value = "/lhlFile")
	@ResponseBody
	public ResponseJson tt(HttpServletRequest request,HttpServletResponse response,String filename,String url){
 
		try {
			txService.intoTXchjzfList("");
			return new ResponseJson().success("200", "200", "终于搞定了", "打完收工", "打完收工");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			logger.info(e);
		}
		return new ResponseJson().success("200", "200", "终于搞定了", "-----失败了---", "打完收工");
	}
	
	
}
