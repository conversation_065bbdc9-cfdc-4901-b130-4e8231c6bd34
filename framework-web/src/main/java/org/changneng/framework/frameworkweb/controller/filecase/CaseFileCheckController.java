package org.changneng.framework.frameworkweb.controller.filecase;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.changneng.framework.frameworkbusiness.entity.JsonResult;
import org.changneng.framework.frameworkbusiness.entity.SceneSysSpecialModel;
import org.changneng.framework.frameworkbusiness.entity.SysUsers;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileCheck;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileDictionary;
import org.changneng.framework.frameworkbusiness.entity.filecase.CFileEntry;
import org.changneng.framework.frameworkbusiness.repeatCommit.PutSessionValue;
import org.changneng.framework.frameworkbusiness.service.CaseCheckService;
import org.changneng.framework.frameworkbusiness.service.SpecialTaskService;
import org.changneng.framework.frameworkcore.utils.APIResponseJson;
import org.changneng.framework.frameworkcore.utils.ChangnengUtil;
import org.changneng.framework.frameworkcore.utils.PageBean;
import org.changneng.framework.frameworkcore.utils.ResponseJson;
import org.changneng.framework.frameworkcore.utils.SystemStatusCode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

/**
  * 案件完成性、稽查，校验管理类 
  * <p>Title:CaseFileCheckController </p>
  * <p>Description: 案件是否参与  完成性校验  和  智能稽查校验 控制类</p>
  * <p>Company: </p> 
  * <AUTHOR>
  * @date 2018年9月3日-下午5:45:59
 */
@RequestMapping("/case-check")
@Controller
public class CaseFileCheckController {
	
	@Autowired
	private CaseCheckService caseCheckService;
	
	private static Logger logger = LogManager.getLogger(CaseFileCheckController.class.getName());
	
	@PutSessionValue
	@PostMapping("/toPage")
	public ModelAndView toApplyForcePage(Model model,HttpServletRequest request,HttpServletResponse response) {
		ModelAndView mv = null;
		try{
			mv = new ModelAndView("system/casefile/case-check");
		} catch (Exception e) {
			mv = new ModelAndView("error/404");
			logger.info("案件是否参与  完成性校验  和  智能稽查校验 控制类");
			e.printStackTrace();
		}
		return mv;
	}
	
	/**
	 * caseCheckList
	 * 配置表数据加载
	 */
	@RequestMapping(value = "/caseCheckList")
	@ResponseBody
	public ResponseJson getCaseCheckList(HttpServletRequest request,HttpServletResponse response,String modeleNum){
		List<CFileCheck> cFileCheckList = new ArrayList<CFileCheck>();
		System.out.println(modeleNum);
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			cFileCheckList = caseCheckService.getCaseCheckList(modeleNum, sysUser);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务台账信息成功！",cFileCheckList);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	
	/**
	 * caseCheckList
	 * 根据主键删除配置表数据
	 */
	@RequestMapping(value = "/deleteCaseCheckListById")
	@ResponseBody
	public JsonResult deleteCaseCheckListById(HttpServletRequest request,HttpServletResponse response,String id)throws Exception{
		JsonResult json = new JsonResult();
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			json = caseCheckService.deleteCaseCheckListById(id, sysUser);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
			return json;
		}
		System.out.print("==================="+json.getResult());
		return json;
	}
	
	/**
	 * cFileEntryList
	 * 查询所有附件条目信息
	 */
	@RequestMapping(value = "/chickEntryName")
	@ResponseBody
	public ResponseJson chickEntryName(HttpServletRequest request,HttpServletResponse response)throws Exception{
		List<CFileEntry> cFileEntryList = new ArrayList<CFileEntry>();
		try {
			cFileEntryList = caseCheckService.getCFileEntyList();
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务台账信息成功！",cFileEntryList);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	
	/**
	 * cFileEntryList
	 * 查询所有附件条目下的文书类型信息
	 */
	@RequestMapping(value = "/chickDictionary")
	@ResponseBody
	public ResponseJson chickDictionary(HttpServletRequest request,HttpServletResponse response, String id)throws Exception{
		List<CFileDictionary> cFileDictionaryList = new ArrayList<CFileDictionary>();
		try {
			cFileDictionaryList = caseCheckService.getCFileDictionary(id);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务台账信息成功！",cFileDictionaryList);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	
	/**
	 * caseCheckList
	 * 新增配置表数据
	 */
	@RequestMapping(value = "/saveFileCheck")
	@ResponseBody
	public JsonResult saveFileCheck(HttpServletRequest request,HttpServletResponse response,String entryId,String entryName,Integer dictionaryCode,String dictionaryName,Integer isIntact,Integer isInspection,Integer isStage,String modeleNum)throws Exception{
		JsonResult json = new JsonResult();
		CFileCheck cFileCheck = new CFileCheck();
		int location = 0;
		try {
			if(caseCheckService.getCaseCheckByCode(dictionaryCode,modeleNum).size()>0){
				json.setResult("isExists");
			}else{
				if(ChangnengUtil.isNull(caseCheckService.getMaxLocation(modeleNum))){
					location = 1;
				}else{
					location = caseCheckService.getMaxLocation(modeleNum)+1;
				}
				cFileCheck.setEntryId(entryId);
				cFileCheck.setEntryName(entryName.trim());
				cFileCheck.setDictionaryCode(dictionaryCode);
				cFileCheck.setDictionaryName(dictionaryName.trim());
				cFileCheck.setLocation(location);
				cFileCheck.setIsIntact(isIntact);
				cFileCheck.setIsInspection(isInspection);
				cFileCheck.setIsStage(isStage);
				cFileCheck.setModelerNumber(modeleNum);
				json = caseCheckService.insertIntoCaseCheck(cFileCheck);
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
			return json;
		}
		System.out.print("==================="+json.getResult());
		return json;
	}
	
	/**
	 * caseCheckList
	 * 配置表数据加载
	 */
	@RequestMapping(value = "/caseCheckById")
	@ResponseBody
	public ResponseJson getCaseCheckById(HttpServletRequest request,HttpServletResponse response,String id){
		CFileCheck cFileCheck = new CFileCheck();
		System.out.println(id);
		try {
			SysUsers sysUser = (SysUsers) SecurityContextHolder.getContext().getAuthentication() .getPrincipal();
			cFileCheck = caseCheckService.getCaseCheckByid(id);
			return new ResponseJson().success(HttpStatus.OK.toString(), SystemStatusCode.QUERY_SUCCESS.toString(), "查找成功！", "查询专项任务台账信息成功！",cFileCheck);
		} catch (Exception e) {
			e.printStackTrace();
			return new ResponseJson().success(HttpStatus.INTERNAL_SERVER_ERROR.toString(), SystemStatusCode.QUERY_FAILURE.toString(), "查找失败！", e.getMessage(), null);
		}
	}
	
	/**
	 * caseCheckList
	 * 根据主键更新表数据
	 */
	@RequestMapping(value = "/updateFileCheck")
	@ResponseBody
	public JsonResult updateFileCheck(HttpServletRequest request,HttpServletResponse response,String id,Integer isIntact,Integer isInspection,Integer isStage,String modeleNum)throws Exception{
		JsonResult json = new JsonResult();
		CFileCheck cFileCheck = new CFileCheck();
		try {
			cFileCheck.setId(id);
			cFileCheck.setIsIntact(isIntact);
			cFileCheck.setIsInspection(isInspection);
			cFileCheck.setIsStage(isStage);
			cFileCheck.setModelerNumber(modeleNum);
			json = caseCheckService.updateFileCheckById(cFileCheck);
		} catch (Exception e) {
			e.printStackTrace();
			logger.info(e);
			return json;
		}
		System.out.print("==================="+json.getResult());
		return json;
	}
}
