<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ page language="java" import="java.util.*"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<jsp:include page="publicJs.jsp"/>
<div class="main-container">
	<div class="padding-md">
	<!--第二层任务办理row-->
		<div class="row">
			<!--任务办理-->
			<div class="col-md-12">                                    
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
					    <i class="fa fa-arrow-right"></i> 行政处罚事先告知书
					</div>
					<div class="smart-widget-inner table-responsive">
	                   	<div class="col-md-1"></div>
	                   	<div class="col-md-9">
							<div style="float:left; padding:5px 10px; width:100%;">
								<%-- <button type="button" id="relateCaseBtn" class="btn btn-info" data-toggle="modal" data-target="#glaj"
									data-remote="${webpath }/execuDoc/relateCaseModal">关联案件</button> --%>
								<div id="caseInfoDisplay" style="display:none;">
									<input type="hidden" id="docType" value="wszz10">
									<input type="hidden" id="docId" name="docId" value="${docId }"/>
									<input type="hidden" id="pageNo" name="pageNo" value="${pageNo }"/>
									<input type="hidden" id="docNum" name="docNum" value="${docNum }"/>
									<input type="hidden" id="caseId" name="caseId" value="${caseInfo.id }"/>
									<input type="hidden" id="mongoId" name="mongoId" value="${docInfo.mongodbId }"/>
									<p style="font-size:16px; padding-top:10px; font-weight:bold; color:#23B7E5;">
										<span id="divCaseName">${caseInfo.caseName }</span></p>
									<p style="margin:-5px 0 0 20px;"><span style="font-weight:bold;">案件编号：</span>
										<span id="divCaseNumber">${caseInfo.caseNumber }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">当事人名称：</span>
										<span id="divLawObjectName">${caseInfo.lawObjectName }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">处罚主体：</span>
										<span id="divPunishSubject">${caseInfo.punishSubject }</span></p>
									<p style="margin:3px 0 0 20px;"><span style="font-weight:bold;">调查机构：</span>
										<span id="divResearchOrgName">${caseInfo.researchOrgName }</span></p>
										<!-- 20180921文书制作改造注掉 start -->
									<%-- <p style="margin:0 0 0 20px;">
										<div style="font-weight:bold; margin:10px 0 0 20px;width:120px;float:left;">案件类型选择：</div>
										<div style="margin:10px 0 0 20px;">
											<div id="model1" style="display: none;float:left; margin-left:10px;">
												<div class="custom-checkbox">
											        <input type="checkbox" id="checkbox1" name="moduleName" ${hisReadOnly } value="1"> <label
											            for="checkbox1" class="checkbox-blue" checked></label>
											    </div>简易行政处罚
										    </div>
										    <div id="model2" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox2" name="moduleName" ${hisReadOnly } value="2"> <label
											            for="checkbox2" class="checkbox-blue" checked></label>
											    </div>一般行政处罚
										    </div>
										    <div id="model3" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox3" name="moduleName" ${hisReadOnly } value="3"> <label
											            for="checkbox3" class="checkbox-blue" checked></label>
											    </div>行政命令
										    </div>
										    <div id="model4" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox4" name="moduleName" ${hisReadOnly } value="4"> <label
											            for="checkbox4" class="checkbox-blue" checked></label>
											    </div>查封扣押
										    </div>
										    <div id="model5" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox5" name="moduleName" ${hisReadOnly } value="5"> <label
											            for="checkbox5" class="checkbox-blue" checked></label>
											    </div>限产停产
										    </div>
										    <div id="model6" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox6" name="moduleName" ${hisReadOnly } value="6"> <label
											            for="checkbox6" class="checkbox-blue" checked></label>
											    </div>行政拘留
										    </div>
										    <div id="model7" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox7" name="moduleName" ${hisReadOnly } value="7"> <label
											            for="checkbox7" class="checkbox-blue" checked></label>
											    </div>环境污染犯罪
										    </div>
										    <div id="model8" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox8" name="moduleName" ${hisReadOnly } value="8"> <label
											            for="checkbox8" class="checkbox-blue" checked></label>
											    </div>申请法院强制执行
										    </div>
										    <div id="model9" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox9" name="moduleName" ${hisReadOnly } value="9"> <label
											            for="checkbox9" class="checkbox-blue" checked></label>
											    </div>其他移送
										    </div>
										    <div id="model10" style="display: none;float:left; margin-left:10px;">
											    <div class="custom-checkbox">
											        <input type="checkbox" id="checkbox10" name="moduleName" ${hisReadOnly } value="10"> <label
											            for="checkbox10" class="checkbox-blue" checked></label>
											    </div>按日计罚
										    </div>
										</div>
									</p> --%>
									<!-- 20180921文书制作改造注掉 end -->
									<div class="form-group" style="float:left;display:none;padding-top:10px;padding-left:20px;" 
										id="penaltyRela">
										<label class="control-label" style="width:120px; padding:5px 0;">按日计罚案件关联：</label>
										<div class="input-group" style="width:400px; float:right;">
											<input type="hidden" id="penaltyId">
											<input type="text" class="form-control" id="penaltyDeciNum" readonly="readonly">
										    <div class="input-group-btn">
										        <button type="button" class="btn btn-info no-shadow" tabindex="-1" 
										        data-toggle="modal" data-target="#arjfgl" data-remote="${webpath }/execuDoc/toPenaltyListPage"
										        	>选择</button>
										    </div>
										</div>
									</div>
								</div>
							</div>
							<div class="smart-widget-body form-horizontal text-center font-14">
							<form id="compInfo">
								<div class="col-md-12 text-center">
                                    <p style="font-size:16px; margin-top:20px;"><c:if test="${empty params.userDept }">${userDept }</c:if>
	                                    	<c:if test="${not empty params.userDept }">${params.userDept }</c:if></p>
                                    <p style="font-size:16px;">行政处罚事先告知书</p>
                                    <p style="font-size:14px; float:right;">决定书文号：<input type="zfzh" class="from_gaozhi" value="${params.desicionNum1 }" id="desicionNum1" name="desicionNum1" ${hisReadOnly }
                                    	style="width:150px;outline:none;"></p>
                                    </div>
                                	<table width="100%" border="0" cellspacing="0" cellpadding="0" style="text-align:left; line-height:2em;">
                                      <tr>
                                        <td><input type="zfzh" class="from_gaozhi" value="${params.lawObjectName }" id="lawObjectName" name="lawObjectName" ${hisReadOnly } 
                                        	style="width:380px;outline:none;" placeholder="当事人名称或者姓名，与营业执照、居民身份证一致">：</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;"><input type="zfzh" id="bureaus1" name="bureaus1"  value="${params.bureaus1 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>于
                                        	<input type="zfzh" id="docDate1" name="docDate1" readonly="readonly"  value="${params.docDate1 }" ${hisReadOnly }
                                        	class="from_gaozhi" style="width:120px;text-align: center;" placeholder="xxxx年xx月xx日"/>
										对你（单位）进行了调查，发现你（单位)实施了以下环境违法行为：</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;"><input type="zfzh" value="${params.illegalAction }" id="illegalAction" name="illegalAction" ${hisReadOnly } class="from_gaozhi" 
                                        	style="outline:none;width: 100%;" placeholder="（陈述违法事实，如违法行为发生的时间、地点、情节、动机、危害后果等内容）"></td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">以上事实，有<input type="zfzh" value="${params.evidence }" class="from_gaozhi" id="evidence" name="evidence" ${hisReadOnly }
                                        	style="width:500px;outline:none;" placeholder="（列举证据形式，阐述证据所要证明的内容）">等证据为凭。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">你（单位）的上述行为违反了
                                        	<input type="zfzh" class="from_gaozhi" value="${params.illegalLaw }" id="illegalLaw" name="illegalLaw" ${hisReadOnly } style="width:500px;outline:none;">的规定。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">依据<input type="zfzh" value="${params.lawItem }" class="from_gaozhi" id="lawItem" name="lawItem" ${hisReadOnly } style="width:500px;outline:none;" 
                                        placeholder="（相关法律、法规、规章名称及条款序号）">的规定，<input type="zfzh" class="from_gaozhi" style="width:250px;outline:none;" 
                                        placeholder="阐述适用行政处罚裁量基准制度" value="${params.discreStan }" id="discreStan" name="discreStan" ${hisReadOnly }>。<input type="zfzh" id="bureaus2" name="bureaus2"  value="${params.bureaus2 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>拟对你（单位）作出如下行政处罚：。</td>
                                      </tr>
                                      <tr>
                                        <td style="text-indent:2em;">1.<input type="zfzh" value="${params.penalty1 }" id="penalty1" name="penalty1"  ${hisReadOnly }
                                        	class="from_gaozhi" style="width:500px;outline:none;">；</td>                                            
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">2.<input type="zfzh" value="${params.penalty2 }" id="penalty2" name="penalty2"  ${hisReadOnly }
                                      		class="from_gaozhi" style="width:500px;outline:none;">。（其中为罚款的，罚款数额大写）</td>
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">根据《中华人民共和国行政处罚法》第三十二条的规定，你（单位）有权进行陈述和申辩。未提出陈述申辩意见的，视为放弃此权利。</td>
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">根据《中华人民共和国行政处罚法》第四十二条的规定，对上述拟作出的（符合听证条件的行政处罚种类和幅度），你（单位）有要求举行听证的权利。你（单位）如果要求听证，可以在收到本告知书之日起3日内向<input type="zfzh" id="bureaus3" name="bureaus3"  value="${params.bureaus3 }" ${hisReadOnly }
	                                        class="from_gaozhi" style="width:200px;text-align: center;" placeholder="我厅（局）"/>提出举行听证的要求；逾期未提出听证申请的，视为你（单位）放弃听证权利。</td>
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">联系人：<input type="zfzh" value="${params.contact }" id="contact" name="contact"  ${hisReadOnly }
                                      		class="from_gaozhi" style="width:235px;outline:none;">
                                      	电话：<input type="zfzh" value="${params.phone }" id="phone" name="phone"  ${hisReadOnly }
                                      		class="from_gaozhi" style="width:200px;outline:none;"></td>
                                      </tr>
                                      <tr>
                                      	<td style="text-indent:2em;">地址：<input type="zfzh" value="${params.address }" id="address" name="address"  ${hisReadOnly }
                                      		class="from_gaozhi" style="width:250px;outline:none;">
                                      	邮政编码：<input type="zfzh" value="${params.postalNumber }" id="postalNumber" name="postalNumber"  ${hisReadOnly }
                                      		class="from_gaozhi" style="width:172px;outline:none;"></td>
                                      </tr>
                                      <tr>
                                      	<td class="text-right"><input type="zfzh" value="<c:if test="${empty params.userDept }">${userDept }</c:if><c:if test="${not empty params.userDept }">${params.userDept }</c:if>" 
	                                    		id="userDept" name="userDept" ${hisReadOnly } placeholder="×××环境保护厅（局）" class="from_gaozhi" style="width:200px;outline:none;">
	                                    	</td>
                                      </tr>
                                      <tr>
                                        <td class="text-right" style="word-spacing:2em;">
                                        	<input type="zfzh" id="docDate" name="docDate" readonly="readonly"  value="${params.docDate }" ${hisReadOnly }
                                        	class="from_gaozhi" style="margin-right:60px;width:120px;text-align: center;" placeholder="xxxx年xx月xx日"/>
                                        </td>
                                      </tr>
                                    </table>  
                                    </form>    
								</div>
							</div>     
							<div style="float:right; margin:5px 0 0 0;" class="col-md-2">
								<button href="#" class="btn btn-info" data-toggle="modal" data-target="#wszd" data-remote="${webpath }/execuDoc/wszdModal?id=${docNum}">文书指导</button>&nbsp;
								<button class="btn btn-info" onClick="goBack()">返回</button>
							</div>
						</div>
						<div class="modal-footer">
							<c:if test="${not empty docId }">
								<c:if test="${empty hisReadOnly }">
									<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
									<button type="button" class="btn btn-info" data-toggle="modal" data-target="#scsmj"
									data-remote="${webpath }/execuDoc/uploadPicModal">上传扫描件</button>
								</c:if>
								<button type="button" class="btn btn-info" onclick="showPicModal()" tabindex="-1"
									>查看扫描件</button>
								<button type="button" class="btn btn-info" style="width:80px;" data-toggle="modal" 
								data-target="#inputImgModeler" data-remote="${webpath}/execuDoc/printModal?id=${docInfo.pdfUrl}">打印</button>
							</c:if>
							<c:if test="${empty docId }">
								<button type="button" class="btn btn-info" style="width:80px;" onclick="saveModelInfo()">保存</button>
							</c:if>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 关联案件（Modal） -->
    <!-- <div class="modal fade" id="glaj" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                
            </div>
        </div>
    </div> -->
	<!-- 按日计罚案件关联（Modal） -->
	<div class="modal fade" id="arjfgl" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 上传扫描件（Modal） -->
	<div class="modal fade" id="scsmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
	    aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	            
	        </div>
	    </div>
	</div>
	<!-- 文书指导 -->
	<div class="modal fade" id="wszd" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
	    <div class="modal-dialog modal-lg">
	        <div class="modal-content">
	        </div>
	    </div>
	</div>

<!-- 附件预览  -->
<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal"
					aria-hidden="true">&times;</button>
				<h4 class="modal-title" id="myModalLabel">附件预览</h4>
			</div>
			<div class="modal-footer">
				<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
			</div>

		</div>
	</div>
</div>

<script type="text/javascript">

	$(document).ready(function(){
		var day1 = new Date();
	    day1.setTime(day1.getTime());
	    var d = day1.getDate();
	    if (d < 10){d = "0"+d;}
	    var s1 = day1.getFullYear()+"年" + (day1.getMonth()+1) + "月" + d + "日";
	    if($("#docDate").val() == null || $("#docDate").val() == ''){
		    $("#docDate").val(s1);
	    }
	    $("#docDate").datetimepicker({
			language:'cn',
		    format:'yyyy-mm-dd',
		    autoclose: true,
		    minView:'month',
		    maxView:'decade',
		    todayBtn:true,
		    clearBtn:true
		 }).on('hide',function(ev){
			 var date = $("#docDate").val();
			 if(date != ''){
				 $("#docDate").val(date.split('-')[0] + '年' + date.split('-')[1] + '月' + date.split('-')[2] + '日');
			 }
		 }).on('show',function(ev){
			 var date = $("#docDate").val();
			 if(date != ''){
				 $("#docDate").val(date.substring(0,4) + '-' + date.substring(5,(date.indexOf('月'))) 
						 + '-' + date.substring((date.indexOf('月')+1),date.indexOf('日')));
				 $("#docDate").datetimepicker('update');
			 }
		 });
	    
	    $("#docDate1").datetimepicker({
			language:'cn',
		    format:'yyyy-mm-dd',
		    autoclose: true,
		    minView:'month',
		    maxView:'decade',
		    todayBtn:true,
		    clearBtn:true
		 }).on('hide',function(ev){
			 var date = $("#docDate1").val();
			 if(date != ''){
				 $("#docDate1").val(date.split('-')[0] + '年' + date.split('-')[1] + '月' + date.split('-')[2] + '日');
			 }
		 }).on('show',function(ev){
			 var date = $("#docDate1").val();
			 if(date != ''){
				 $("#docDate1").val(date.substring(0,4) + '-' + date.substring(5,(date.indexOf('月'))) 
						 + '-' + date.substring((date.indexOf('月')+1),date.indexOf('日')));
				 $("#docDate1").datetimepicker('update');
			 }
		 });
	})

	//保存校验
	function wszzValidate(){
		var desicionNum1 = $("#desicionNum1").val();
		//var desicionNum2 = $("#desicionNum2").val();
		var illegalAction = $("#illegalAction").val();
		var illegalLaw = $("#illegalLaw").val();
		var lawItem = $("#lawItem").val();
		var penalty1 = $("#penalty1").val();
		var penalty2 = $("#penalty2").val();
		var contact = $("#contact").val();
		var phone = $("#phone").val();
		var postalNumber = $("#postalNumber").val();
		var address = $("#address").val();
		
		if(desicionNum1.length > 60){
			swal('提示','行政处罚事先告知书文号长度不能大于60！','info');
			return false;
		}
		/*if(desicionNum2.length > 200){
			swal('提示','案件号长度不能大于200！','info');
			return false;
		}*/
		if(illegalAction.length > 2000){
			swal('提示','陈述违法事实长度不能大于2000！','info');
			return false;
		}
		if(lawItem.length > 2000){
			swal('提示','违法依据长度不能大于2000！','info');
			return false;
		}
		if(illegalLaw.length > 2000){
			swal('提示','违反法律条款长度不能大于2000！','info');
			return false;
		}
		if(contact.length > 30){
			swal('提示','联系人长度不能大于30！','info');
			return false;
		}
		if(phone.length > 30){
			swal('提示','电话长度不能大于30！','info');
			return false;
		}
		if(address.length > 100){
			swal('提示','地址长度不能大于100！','info');
			return false;
		}
		if(postalNumber.length > 30){
			swal('提示','邮政编码长度不能大于30！','info');
			return false;
		}
		return true;
	}
</script>		