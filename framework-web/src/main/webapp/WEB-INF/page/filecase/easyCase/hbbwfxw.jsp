<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
    <script type="text/javascript">
	var  entProtectionBaseIDArr = new Array();
    $(document).ready(function(){
		var entProtectionIDArr =  $("#"+'${entProtectionCodeModelerId}').val();
		if(entProtectionIDArr!='' && $.trim(entProtectionIDArr).length > 0){
			var entProtectionBaseIDArrCode = entProtectionIDArr.split(",");
			
			for( i = 0; i< entProtectionBaseIDArrCode.length ; i++){
				entProtectionBaseIDArr.push(entProtectionBaseIDArrCode[i]);
			}
			// 获取页面复选框集合 
			var entProtectionBaseObj = document.getElementsByName('ProtectionCheckbox');
			for(var i=0; i < entProtectionBaseObj.length ; i++ ){
				//不能用attrIdsStr.indexOf，因为如果有13，则会把1和3也选中。。。。  使用  $.inArray(rowCode, idsArr) >-1
				if($.inArray(entProtectionBaseObj[i].value, entProtectionBaseIDArr) >-1){
					entProtectionBaseObj[i].checked=true;
				}
			}
		}
    });
    
    function saveBtnForm(){
    	var obj = document.getElementsByName('ProtectionCheckbox');
		var ids = "";
		var names = "";
		for (var i = 0; i < obj.length; i++) {
			if (obj[i].checked)
				ids += obj[i].value + ',';
			if (obj[i].checked)
				names += $.trim($("#ProtectionCheckbox" + obj[i].value).html()) + ',';
		}
		ids = ids.substring(0, ids.length - 1);//选中的所有的id
		names = names.substring(0, names.length - 1);//选中的所有的name
		$("#"+'${entProtectionCodeModelerId}').val(ids);
		$("#"+'${entProtectionNameModelerName}').val(names);
		var checkFormName =  '${entProtectionCheckFormName}';
		if(checkFormName!='' && $.trim(checkFormName).length > 0){
			$('#'+checkFormName).formValidation('revalidateField', '${entProtectionNameModelerName}');
		}
    }
    </script>
        <!-- 环保部对应违法行为（Modal） -->
		  <div class="modal-header">
              <div style="float:right; margin-top:-5px;">
              <button type="button" class="btn btn-info" data-dismiss="modal" onclick="saveBtnForm()">确定</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
              </div>
              <h4 class="modal-title" id="myModalLabel">环保部对应违法行为</h4>
          </div>
          <div class="modal-body padding-lg">
		<div class="smart-widget-body">
			<form class="form-horizontal">
				<div class="form-group">
					<c:forEach items="${entProtectionTypeList}" var="item" varStatus="status">
						<div
							<c:if test="${status.count%2 == '0'}">
									  	 class="col-lg-6" 
									  </c:if>
							<c:if test="${status.count%2 != '0'}">
									 	 	class="col-lg-6" 
									  </c:if>>
							<div class="checkbox inline-block" style="padding-right: 30px;">
								<div class="custom-checkbox">
									<input type="checkbox" id="checkboxProtection${status.index+1}" name="ProtectionCheckbox" value="${item.code }"> 
									<label for="checkboxProtection${status.index+1}" class="checkbox-blue" checked></label>
								</div>
								<div class="inline-block vertical-top" id="ProtectionCheckbox${item.code }">${item.name }</div>
							</div>
						</div>
					</c:forEach>
				</div>
			</form>
		</div>
	</div>
</body>
</html>