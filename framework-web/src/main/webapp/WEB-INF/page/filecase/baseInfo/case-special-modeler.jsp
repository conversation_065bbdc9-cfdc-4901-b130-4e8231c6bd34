<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
<script type="text/javascript">
	  var  caseSpecialBase = new Array();
	  $(document).ready(function(){
 			//初始化时违法案件类型选中处理
			var caseSpecialIds = '${caseSpecialIds}';
			if(caseSpecialIds != null && caseSpecialIds != '' && $.trim(caseSpecialIds).length > 0){
				var caseSpecialIdsArry = caseSpecialIds.split(",");
				for( i = 0; i< caseSpecialIdsArry.length ; i++){
					caseSpecialBase.push(caseSpecialIdsArry[i]);
				}
				// 获取页面复选框集合 
				var obj = document.getElementsByName('caseSpecialCheckbox');
				for(var i=0; i < obj.length ; i++ ){
					//不能用attrIdsStr.indexOf，因为如果有13，则会把1和3也选中。。。。  使用  $.inArray(rowCode, idsArr) >-1
					console.log(obj[i].value);
					if($.inArray(obj[i].value, caseSpecialBase) >-1){
						obj[i].checked=true;
					}
				}
			}  
	  });
		//处理选中的违法类型
	 function caseSpecialTableSave() {
		 	//选中的违法类型
			var obj = document.getElementsByName('caseSpecialCheckbox');
			var ids = "";
			var names = "";
			for (var i = 0; i < obj.length; i++) {
				if (obj[i].checked)
					ids += obj[i].value + ',';
				if (obj[i].checked)
					names += $.trim($("#caseSpecialCheckbox" + obj[i].value).html()) + ',';
			}
			ids = ids.substring(0, ids.length - 1);//选中的所有的id
			names = names.substring(0, names.length - 1);//选中的所有的name
		
			var objFormData ={caseId:$("#formCaseId").val(),caseSpecialIds:ids,caseSpecialName:names};
			//开启遮罩  
			business.openwait();
			$.ajax({
		        type: "POST",
		        url: WEBPATH+'/atvEasy/save-case-special',
		        data:objFormData, 
		        error: function(request) {
		        	business.closewait();//关闭遮罩层
		        	swal("错误!","系统错误", "error");
		        },
		        success: function(data) {
		        	business.closewait();//关闭遮罩层
		   			if (data.meta.result == 'success') {// 成功
		   				$("#specialActionNameDiv").html(names);
					} else if (data.meta.result == 'error') { // 失败  
						swal({ title : data.meta.message, text : "", type : "error" });
					} else {
						swal({ title : "返回信息错误", text : "", type : "error" });
					} 
		        }
		    });
			$("#ajglzxxd").modal('hide');   
		}
	 
	</script>  
 					<div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                        <h4 class="modal-title" id="myModalLabel">专项行动选择</h4>
                    </div>
                    <div class="modal-body" style="padding-top:20px;">
                        <div class="smart-widget-body form-horizontal">
                                <div class="form-group">
                                   <c:forEach items="${caseSpecialTableList}" var="item" varStatus="status">
                                
                                     <div>
                                        <div class="checkbox inline-block" style="padding-right:30px;">
                                            <div class="custom-checkbox">
                                                <input type="checkbox" id="checkboxSpecial${status.index+1}" name="caseSpecialCheckbox" value="${item.id}">
	                                       		<label for="checkboxSpecial${status.index+1}" class="checkbox-blue" checked></label>
                                            </div>
                                            <div class="inline-block vertical-top" id="caseSpecialCheckbox${item.id}" >
                                                	${item.specialName}
                                            </div>
                                        </div>
                                     </div>
								</c:forEach>
                               </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info" data-dismiss="modal" onclick="caseSpecialTableSave()">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                    </div>
                    
                    
</body>
</html>