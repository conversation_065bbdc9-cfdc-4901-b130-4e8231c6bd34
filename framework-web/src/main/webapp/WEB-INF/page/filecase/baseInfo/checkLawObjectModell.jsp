<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<html>
<body>
	<!-- 执法对象查看（Modal） -->
	<div class="modal fade" id="zfdx" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">执法对象</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> ${caseHisLawObject.lawObjectName } <span
								class="smart-widget-option"> <span
								class="refresh-icon-animated"> <i
									class="fa fa-circle-o-notch fa-spin"></i>
							</span> 
							</span>
						</div>
						<div class="smart-widget-inner">
							<div class="widget-tab clearfix">
								<ul class="tab-bar">
									<li class="active"><a href="#jbxx" data-toggle="tab"><i class="fa fa-pencil"></i> 基本信息</a></li>
									<li class=""><a href="#xczf" id ="spotInspectionBtn" data-toggle="tab"><i class="fa fa-file-text-o"></i> 执法信息</a></li>
									<li class=""><a href="#ajxx" id ="caseInfoBtn" data-toggle="tab"><i class="fa fa-file-text-o"></i> 案件信息</a></li>
									<c:if test="${caseHisLawObject.typeCode ==1 ||caseHisLawObject.typeCode ==2 ||caseHisLawObject.typeCode ==3 }">
										<li class=""><a href="#isSuperviseModel" id ="" onclick="loadDiscreditlist()" data-toggle="tab"><i class="fa fa-file-text-o"></i> 失信信息</a></li>
									</c:if>
									<li class=""><a href="#xcxc" data-toggle="tab" onclick="loadObjectCommentTree()"><i class="fa fa-user-circle"></i> 现场巡查</a></li>
								</ul>
							</div>
							<div class="smart-widget-body">
								<div class="tab-content">
									<div class="tab-pane fade in active" id="jbxx">
										<div class="form-horizontal">
											<c:choose>
												<c:when test="${caseHisLawObject.typeCode ==1 }">
													<!--企业  -->
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象类别 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">企事业单位</div>
														</div>
													</div>
										<%-- 			<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															执法对象ID</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																<c:when test="${lawObj.taskStateType=='0'}"> 
																${caseHisLawObject.lawObjectId }
																 </c:when>
																<c:otherwise>
																${caseHisLawObject.id  }
																 </c:otherwise>
																</c:choose>
															</div>

														</div>
													</div> --%>
													<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															污染源编码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectNumber }</div>

														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															名称 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															执法对象所在行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.belongAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地址</label>
														<div class="col-md-6">
															<div style="margin-top: 8px;">${caseHisLawObject.address }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															权属行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.powerAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地理坐标</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.gisCoordinateX }
																E ${caseHisLawObject.gisCoordinateY } N</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															营业执照证件号</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.licenseNo }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															组织机构代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.orgCode }">无</c:if>${caseHisLawObject.orgCode }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															统一社会信用代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.socialCreditCode }">无</c:if>${caseHisLawObject.socialCreditCode }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															行业类型</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.industryTypeName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															污染源类别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.pollutionSourceTypeName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															监管级别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.levelName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															法人代表</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPerson }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															法人电话</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPhone }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															环保负责人</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.chargePerson }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															环保负责人电话</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.chargePersonPhone }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否重点源</label>
														<div class="col-md-3">
															<c:choose>
																<c:when test="${caseHisLawObject.isKeySource=='1'}"> 
												   		    是	  
													   </c:when>
																<c:when test="${caseHisLawObject.isKeySource=='0'}">   
												       	否
													   </c:when>
																<c:otherwise>
												      	 无
												       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															适用排污许可行业技术规范</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.sypwxkhyjsgfName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否“小散乱污”企业</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.xslw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.xslw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否在线监控企业</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isOnlineMonitor=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isOnlineMonitor=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															生产状态</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">

																<c:choose>
																	<c:when test="${caseHisLawObject.productStateCode=='1'}"> 
												   			在建未投产  
															   </c:when>
																			<c:when test="${caseHisLawObject.productStateCode=='2'}">   
														         正常生产
															   </c:when>
																			<c:when test="${caseHisLawObject.productStateCode=='3'}">   
														        长时间停产
															   </c:when>
															   <c:when test="${caseHisLawObject.productStateCode=='4'}">   
														       已关停
															   </c:when>
															   <c:when test="${caseHisLawObject.productStateCode=='5'}">   
														       不存在
															   </c:when>
														 	   <c:when test="${caseHisLawObject.productStateCode=='6'}">   
														     重复企业
															   </c:when>
														 	   <c:when test="${caseHisLawObject.productStateCode=='8'}">   
														     被限制生产
															   </c:when>
														<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否废水产生单位</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.sffscsdw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.sffscsdw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否废气产生单位</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.sffqcsdw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.sffqcsdw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否危废产生单位</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.sfwfcsdw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.sfwfcsdw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否危废经营单位</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.sfwfjydw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.sfwfjydw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否注销</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.isCancelled=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.isCancelled=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否固废经营单位</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when
																		test="${caseHisLawObject.isSolidwasteOperunit=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when
																		test="${caseHisLawObject.isSolidwasteOperunit=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否固废生产单位</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when
																		test="${caseHisLawObject.isSolidwasteCreateunit=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when
																		test="${caseHisLawObject.isSolidwasteCreateunit=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否风险源</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isRiskSource=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isRiskSource=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															排污口是否规范化</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isOutfallStandard=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isOutfallStandard=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															当事人性质</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																${caseHisLawObject.personNatureName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属级别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																${caseHisLawObject.belongLevelName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否上市公司</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isListedCompany=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isListedCompany=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															股票代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.stockCode }">无</c:if>${caseHisLawObject.stockCode}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否所属集团公司</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isGroupCompany=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isGroupCompany=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属集团公司名称</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.groupCompanyName }">无</c:if>${caseHisLawObject.groupCompanyName }
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属集团公司组织机构代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if
																	test="${empty  caseHisLawObject.groupCompanyOrgcode }">无</c:if>${ caseHisLawObject.groupCompanyOrgcode }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属集团公司股票代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if
																	test="${empty  caseHisLawObject.groupCompanyStockcode }">无</c:if>${caseHisLawObject.groupCompanyStockcode }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象介绍 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.objectDesc }">无</c:if>${caseHisLawObject.objectDesc }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															双随机属性 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.randomAttrName }">无</c:if>${caseHisLawObject.randomAttrName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域代码 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSCD }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域名称 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSNM }</div>
														</div>
													</div>
												</c:when>
												<c:when test="${caseHisLawObject.typeCode ==2 }">
													<!--个人  -->

													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象类别 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">个人</div>
														</div>
													</div>
										<%-- 			<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															执法对象ID</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">	<c:choose>
																<c:when test="${lawObj.taskStateType=='0'}"> 
																${caseHisLawObject.lawObjectId }
																 </c:when>
																<c:otherwise>
																${caseHisLawObject.id  }
																 </c:otherwise>
																</c:choose></div>

														</div>
													</div> --%>
													<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															污染源编码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectNumber }</div>

														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															姓名 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															执法对象所在行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.belongAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地址</label>
														<div class="col-md-6">
															<div style="margin-top: 8px;">${caseHisLawObject.address }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															权属行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.powerAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地理坐标</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.gisCoordinateX }
																E ${caseHisLawObject.gisCoordinateY } N</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															身份证类型</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.personcardTypeName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															身份证号码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.cardNumber }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系方式</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPhone }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域代码 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSCD }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域名称 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSNM }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															污染源类别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.pollutionSourceTypeName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															适用排污许可行业技术规范</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.sypwxkhyjsgfName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否“小散乱污”企业</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.xslw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.xslw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否注销</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.isCancelled=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.isCancelled=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
												</c:when>
												<c:when test="${caseHisLawObject.typeCode ==3 }">
													<!--个体 -->
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象类别 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">个体、三无、小三产</div>
														</div>
													</div>
													<%-- <div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															执法对象ID</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																<c:when test="${lawObj.taskStateType=='0'}"> 
																${caseHisLawObject.lawObjectId }
																 </c:when>
																<c:otherwise>
																${caseHisLawObject.id  }
																 </c:otherwise>
																</c:choose>
															</div>

														</div>
													</div> --%>
													<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															污染源编码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectNumber }</div>

														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															名称 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															执法对象所在行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.belongAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地址</label>
														<div class="col-md-6">
															<div style="margin-top: 8px;">${caseHisLawObject.address }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															权属行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.powerAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地理坐标</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.gisCoordinateX }
																E ${caseHisLawObject.gisCoordinateY } N</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															证件类型</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.cardTypeName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															证件号码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.cardNumber }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系人</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPerson }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系方式</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPhone }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															双随机属性 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.randomAttrName }">无</c:if>${caseHisLawObject.randomAttrName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域代码 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSCD }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域名称 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSNM }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															污染源类别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.pollutionSourceTypeName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															适用排污许可行业技术规范</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.sypwxkhyjsgfName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否“小散乱污”企业</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.xslw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.xslw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否注销</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.isCancelled=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.isCancelled=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
												</c:when>
												<c:when test="${caseHisLawObject.typeCode ==4 }">
													<!--自然保护区  -->
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象类别 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">自然保护区</div>
														</div>
													</div>
												<%-- 	<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															执法对象ID</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																<c:when test="${lawObj.taskStateType=='0'}"> 
																${caseHisLawObject.lawObjectId }
																 </c:when>
																<c:otherwise>
																${caseHisLawObject.id  }
																 </c:otherwise>
																</c:choose>
															</div>

														</div>
													</div> --%>
													<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															污染源编码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectNumber }</div>

														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															名称 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															执法对象所在行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.belongAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地址</label>
														<div class="col-md-6">
															<div style="margin-top: 8px;">${caseHisLawObject.address }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															权属行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.powerAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地理坐标</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.gisCoordinateX }
																E ${caseHisLawObject.gisCoordinateY } N</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															保护区范围描述</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																${caseHisLawObject.protectedAreaDesc }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															主要保护对象</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.mainProtectObject }
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否设立专门的管理机构</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:choose>
																	<c:when test="${caseHisLawObject.isSpecialOrg=='1'}"> 
												   		    是	  
													   </c:when>
																	<c:when test="${caseHisLawObject.isSpecialOrg=='0'}">   
												       	否
													   </c:when>
																	<c:otherwise>
												      	 无
												       </c:otherwise>
																</c:choose>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															管理机构名称</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.manageOrgName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															管理机构组织机构代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.orgCode }">无</c:if>
																${caseHisLawObject.orgCode}
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															管理机构统一社会信用代码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.socialCreditCode }">无</c:if>${caseHisLawObject.socialCreditCode}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															管理机构负责人</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
															    <c:choose>
															        <c:when test="${empty  caseHisLawObject.chargePerson or caseHisLawObject.isSpecialOrg eq 0}">无</c:when>
															        <c:otherwise>${caseHisLawObject.chargePerson}</c:otherwise>
															    </c:choose>
																<%-- <c:if test="${empty  caseHisLawObject.chargePerson or caseHisLawObject.isSpecialOrg eq 0}">无</c:if>
																${caseHisLawObject.chargePerson} --%>
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															管理机构负责人联系方式</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
															    <c:choose>
															         <c:when test="${empty  caseHisLawObject.chargePersonPhone or caseHisLawObject.isSpecialOrg eq 0}">无</c:when>
															         <c:otherwise>${caseHisLawObject.chargePersonPhone}</c:otherwise>
															    </c:choose>
																<%-- <c:if test="${empty  caseHisLawObject.chargePersonPhone or caseHisLawObject.isSpecialOrg eq 0}">无</c:if>${caseHisLawObject.chargePersonPhone} --%>
																
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系人</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.legalPerson }">无</c:if>${caseHisLawObject.legalPerson}
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系方式</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.legalPhone }">无</c:if>${caseHisLawObject.legalPhone}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域代码 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSCD }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域名称 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSNM }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否注销</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.isCancelled=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.isCancelled=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
												</c:when>
												<c:when test="${caseHisLawObject.typeCode ==5 }">
													<!--无主 -->
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															对象类别 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">无主</div>
														</div>
													</div>
													<%-- <div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															执法对象ID</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">	<c:choose>
																<c:when test="${lawObj.taskStateType=='0'}"> 
																${caseHisLawObject.lawObjectId }
																 </c:when>
																<c:otherwise>
																${caseHisLawObject.id  }
																 </c:otherwise>
																</c:choose></div>

														</div>
													</div> --%>
													<div class="form-group">
														<label for="inputEmail3" class="col-md-3 control-label">
															污染源编码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectNumber }</div>

														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															名称 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.lawObjectName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															执法对象所在行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.belongAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地址</label>
														<div class="col-md-6">
															<div style="margin-top: 8px;">${caseHisLawObject.address }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															权属行政区 </label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.powerAreaName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															地理坐标</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.gisCoordinateX }
																E ${caseHisLawObject.gisCoordinateY } N</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															证件类型</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.cardTypeName }
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															证件号码</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.cardNumber }
															</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系人</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPerson }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															联系方式</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.legalPhone }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															双随机属性 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																<c:if test="${empty  caseHisLawObject.randomAttrName }">无</c:if>${caseHisLawObject.randomAttrName }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域代码 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSCD }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															所属流域名称 </label>
														<div class="col-md-9">
															<div style="margin-top: 8px;">
																${caseHisLawObject.WSNM }</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															污染源类别</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.pollutionSourceTypeName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															适用排污许可行业技术规范</label>
														<div class="col-md-3">
															<div style="margin-top: 8px;">${caseHisLawObject.sypwxkhyjsgfName}</div>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否“小散乱污”企业</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.xslw=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.xslw=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
													<div class="form-group">
														<label for="inputtext3" class="col-md-3 control-label">
															是否注销</label>
														<div class="col-md-3" style="margin-top: 8px;">
															<c:choose>
																<c:when test="${caseHisLawObject.isCancelled=='1'}"> 
																   		    是	  
																	   </c:when>
																<c:when test="${caseHisLawObject.isCancelled=='0'}">   
																       	否
																	   </c:when>
																<c:otherwise>
																      	 无
																       </c:otherwise>
															</c:choose>
														</div>
													</div>
												</c:when>
											</c:choose>

										</div>
									</div>
								<div class="tab-pane fade" id="xczf">
									<c:choose>
										<c:when test="${lawObj.taskStateType=='0'}"> 
										<input type="hidden" name="id" id="id" value="${caseHisLawObject.lawObjectId  }">
										 </c:when>
										<c:otherwise>
										<input type="hidden" name="id" id="id" value="${caseHisLawObject.id  }">
										 </c:otherwise>
								    </c:choose>
									<div class="form-horizontal">
										<div class="form-group">
											<label for="inputtext3" class="col-md-2 control-label">
												开始时间 </label>
											<div class="col-lg-2 col-md-2">
												<input type="text" id="goods-info-start" value="${startTimes}"
													name="startTimes" class="form-control" placeholder="开始时间"
													data-date-format="yyyy-mm-dd" readonly="readonly">
											</div>
											<label for="inputtext3" class="col-md-2 control-label">
												结束时间 </label>
											<div class="col-lg-2 col-md-2">
												<input type="text" id="goods-info-start2" value="${endTimes}"
													name="endTimes" class="form-control" placeholder="结束时间"
													data-date-format="yyyy-mm-dd" readonly="readonly">
											</div>
											<div class="col-lg-2 col-md-2">
												<button class="btn btn-info" id="searchButt">查询</button>
											</div>
										</div>
										<hr>
										<div class="padding-xs">温馨提示：点击工单摘要可以查看任务详情。</div>
										<div class="row padding-md">
											<div class="col-lg-8 col-md-8">
												<div class="timeline-wrapper clearfix">
													<div id="contentDiv">
													</div>
													<div id="msgDiv"></div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<!-- 案件信息 -->
								<div class="tab-pane fade" id="ajxx">
									<div class="form-horizontal">
										<div class="form-group">
											<label for="inputtext3" class="col-lg-1 col-md-1 control-label">
												创建时间 </label>
											<div class="col-lg-4 col-md-4">
												<div class="col-lg-5 col-md-5">
													<input type="text" id="caseStartTimes" value="${caseStartTimes}"
														name="caseStartTimes" class="form-control" placeholder="开始时间"
														data-date-format="yyyy-mm-dd" readonly="readonly">
												</div>
												<label for="inputtext3" class="col-lg-1 col-md-1 control-label">
													至 </label>
												<div class="col-lg-5 col-md-5">
													<input type="text" id="caseEndTimes" value="${caseEndTimes}"
														name="caseEndTimes" class="form-control" placeholder="结束时间"
														data-date-format="yyyy-mm-dd" readonly="readonly">
												</div>
											</div>
											<label for="inputtext3" class="col-lg-1 col-md-1 control-label">
												案件状态</label>
		                                      <div class="col-lg-2 col-md-2">
		                                          <select class="form-control" id="caseState">
		                                              <option value="">全部</option>
		                                              <option value="0">进行中</option>
		                                              <option value="1">已完成</option>
		                                          </select>
		                                      </div>
		                                      <label for="inputtext3" class="col-lg-1 col-md-1 control-label">
												案件类型</label>
		                                      <div class="col-lg-2 col-md-2">
		                                          <select class="form-control" id="caseType">
		                                              <option value="">全部</option>
		                                              <option value="0">简易行政处罚</option>
		                                              <option value="1">一般行政处罚</option>
		                                              <option value="2">行政命令</option>
		                                              <option value="3">查封扣押</option>
		                                              <option value="4">限产停产</option>
		                                              <option value="5">行政拘留</option>
		                                              <option value="6">环境污染犯罪</option>
		                                              <option value="7">申请法院强制执行</option>
		                                              <option value="8">其他移送</option>
		                                              <option value="9">按日计罚</option>
		                                          </select>
		                                      </div>
											<div class="col-lg-1 col-md-1">
												<button class="btn btn-info" id="caseSearchBtn">查询</button>
											</div>
										</div>
										<hr>
										<div class="padding-xs">温馨提示：点击工单摘要可以查看案件详情。</div>
										<div class="row padding-md">
											<div class="col-lg-8 col-md-8">
												<div class="timeline-wrapper clearfix">
													<div id="caseContentDiv">
													</div>
													<div id="caseMsgDiv"></div>
												</div>
											</div>
										</div>
									</div>
								</div>
								
								<!-- anjian lishi./  -->
								<div class="tab-pane fade" id="isSuperviseModel">
										<div class="smart-widget-inner table-responsive">
											<table class="table table-striped no-margin table-no-bordered" id="isSuperviseDataTable">
											</table>
										</div>
									</div>
								<div class="tab-pane fade" id="xcxc">
										<div class="form-horizontal">
											<div class="row padding-md">
												<div class="col-lg-10 col-md-10">
													<div class="timeline-wrapper clearfix" id="objectComment">
														<ul>
															<li v-for="(item,index) in year">
																<div class="timeline-year font-semi-bold">{{item}}年</div>
																<div class="timeline-row alt" v-for="(item1,index1) in month">
																	<div class="timeline-item" v-if="contains(item,item1)">
																		<div class="timeline-icon">{{item1 | getMonth}}月</div>
																		<div class="timeline-item-inner" v-for="(item2,index2) in map[item1]">
																			<div>
																				<div class="font-14 padding-xs">
																					<span>{{item2.creatDate}}</span><span
																						style="padding: 0 50px 0 100px;">{{item2.creatUserName}}</span><span>{{item2.creatDepartmentName}}</span>
																				</div>
																				<div class="font-14 padding-xs">{{item2.title}}</div>
																				
																				<div  v-if="item2.lawCircleFiles !=null && item2.lawCircleFiles.length>0">
																						<img class="padding-xs" v-for="(item3,index3) in item2.lawCircleFiles" :src="item3.fileUrl | getFileUrl" v-on:click="showImgModal(item3.lawCircleId,item3.originallyImgId)"
																							style="width: 100px;height:80px; cursor: pointer;">
																				</div>
																			</div>
																			<hr>
																		</div>
																	</div>
																</div>
															</li>
															<li v-if="year.length==0">
															<div  style="text-align: center;color: red;margin-top: 10px">没有现场巡查数据！</div>
															</li>
														</ul>
													
													</div>
												</div>
											</div>
										</div>
									
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 附件预览modal -->
	<div class="modal fade" id="fileModal" tabindex="-1" 
		role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-md">
			<div class="modal-content" id="modalContent">
				
			</div>
		</div>
	</div>
	<!-- 失信执行人详情（Modal） -->
    <div class="modal fade" id="punishmentModel" tabindex="-1" role="dialog"
        aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
            </div>
        </div>
    </div>
	<script type="text/javascript">
		//现场检查
		$("#goods-info-start").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		}).on("click",function(ev) {
					$("#goods-info-start").datetimepicker("setEndDate",	$("#goods-info-start2").val());
		});
		$("#goods-info-start2").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		}).on("click",function(ev) {
			$("#goods-info-start2").datetimepicker("setStartDate",$("#goods-info-start").val());
		});
		$("#spotInspectionBtn").click(
					function() {
						var startTimes = "";
						var endTimes = "";
						var id = $("#lawObjectId").val();
						$.ajax({
							type : "post",
							url : WEBPATH + "/zfdx/lawObjectByTaskJosn",
							data : {
								startTimes : startTimes,
								endTimes : endTimes,
								id : id
							},
							dataType : "json",
							success : function(data) {
								$("#contentDiv").children().remove();
								$("#msgDiv").html("");
								if (data.yearList.length > 0) {
									for (var i = 0; i < data.yearList.length; i++) {
										$("#contentDiv").append("<div class='timeline-year font-semi-bold'>"+ data.yearList[i]+ "</div>");
										for (var j = 0; j < data.mounthList.length; j++) {
											var mounth = data.mounthList[j];
											var year = data.yearList[i];
											if (mounth.indexOf(year) == 0) {
												$("#contentDiv").append(" <div class='timeline-row alt'> <div class='timeline-item'>  <div class='timeline-icon'>"+ (data.mounthList[j]).substring(4)+ "</div>")
												for (var z = 0; z < data.taskList.length; z++) {
													var yearMonth = data.taskList[z].yearMonth;
													if (data.taskList[z].yearMonth == data.mounthList[j]) {
														$("#contentDiv").append("<div class='timeline-item-inner' style='border: 1px solid #CCC;padding:20px; margin:20px 50px;background-color:#f2f2f2;cursor:pointer;'><div onclick=switchHistoryTask('"+data.taskList[z].id+"','"+data.taskList[z].lawObjectType+"','"+data.taskList[z].nodeCode+"') class='timeline-body'><div class='font-16 font-semi-bold padding-xs'>任务编号:"+ data.taskList[z].taskId
															+ "</div><div class='font-14 padding-xs'>任务来源："
															+ data.taskList[z].taskFromName
															+ "</div> <div class='font-14 padding-xs'>检查开始时间:"
															+ data.taskList[z].startTime
															+ " </div><div class='font-14 padding-xs'>检查结束时间:"
															+ data.taskList[z].endTime
															+ " </div></div></div></div></div></div>");
													}
												}
											}
										}
									}

								}
							}
						});
					});
				$("#searchButt").click(
						function() {
							var startTimes = $("#goods-info-start").val();
							var endTimes = $("#goods-info-start2").val();
							var id = $("#lawObjectId").val();
							$.ajax({
								type : "post",
								url : WEBPATH+ "/zfdx/lawObjectByTaskJosn",
								data : {
									startTimes : startTimes,
									endTimes : endTimes,
									id : id
								},
								dataType : "json",
								success : function(data) {
									$("#contentDiv").children().remove();
									$("#msgDiv").html("");
									if (data.yearList.length > 0) {
										for (var i = 0; i < data.yearList.length; i++) {
											$("#contentDiv").append("<div class='timeline-year font-semi-bold'>"+ data.yearList[i]+ "</div>");
											for (var j = 0; j < data.mounthList.length; j++) {
												var mounth = data.mounthList[j];
												var year = data.yearList[i];
												if (mounth.indexOf(year) == 0) {
													$("#contentDiv").append(" <div class='timeline-row alt'> <div class='timeline-item'>  <div class='timeline-icon'>"+ (data.mounthList[j]).substring(4)+ "</div>")
													for (var z = 0; z < data.taskList.length; z++) {
														var yearMonth = data.taskList[z].yearMonth;
														if (data.taskList[z].yearMonth == data.mounthList[j]) {
															$("#contentDiv").append("<div class='timeline-item-inner' style='border: 1px solid #CCC;padding:20px; margin:20px 50px;background-color:#f2f2f2;cursor:pointer;'><div onclick=switchHistoryTask('"+data.taskList[z].id+"','"+data.taskList[z].lawObjectType+"','"+data.taskList[z].nodeCode+"') class='timeline-body'><div class='font-16 font-semi-bold padding-xs'>任务编号:"
																+ data.taskList[z].taskId
																+ "</div><div class='font-14 padding-xs'>任务来源："
																+ data.taskList[z].taskFromName
																+ "</div> <div class='font-14 padding-xs'>检查开始时间:"
																+ data.taskList[z].startTime
																+ " </div><div class='font-14 padding-xs'>检查结束时间:"
																+ data.taskList[z].endTime
																+ " </div></div></div></div></div></div>");
														}
													}
												}
											}
										}

									}
								}
							});
	});
		function switchHistoryTask(taskId,objectType,nodeCode){
			$("#zfdx").modal('hide');
		      document.getElementById("mask").style.display = "block";
		      setTimeout(function () {
			var obj={taskId:taskId,lawObjectType:objectType,parentUrl:'0',nodeCode:nodeCode};
		    if(nodeCode =='0'){
		    	business.addMainContentParserHtml(WEBPATH+'/taskNodeManager/history-rwfp',obj); 
		    }else  if(nodeCode=='1'){
		    	  business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType=0',obj);
		    }else{
		    	swal({title: "该状态下不能查看任务" ,text: "",type:"error"});
		    }
		     },1000)
		      
		}

	</script>
	<!-- ./执法对象查看（Modal） -->
	<!-- 案件信息  -->
	<script type="text/javascript">
		$('#fileModal').on('hidden.bs.modal', function() {
			$(this).removeData("bs.modal");
			if($('.modal.in').length > 0){
				$(document.body).addClass("modal-open");
	      	}  
		});
		$('#punishmentModel').on('hidden.bs.modal', function() {
			$(this).removeData("bs.modal");
			if($('.modal.in').length > 0){
				$(document.body).addClass("modal-open");
	      	}  
		});
		$("#caseStartTimes").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		}).on("click",function(ev) {
					$("#caseStartTimes").datetimepicker("setEndDate",	$("#caseEndTimes").val());
		});
		$("#caseEndTimes").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		}).on("click",function(ev) {
			$("#caseEndTimes").datetimepicker("setStartDate",$("#caseStartTimes").val());
		});
		
		function showCaseInfo(){
			var caseStartTimes = $("#caseStartTimes").val();
			var caseEndTimes = $("#caseEndTimes").val();
			var caseState = $("#caseState").val();
			var caseType = $("#caseType").val();
			var lawObjId = $("#lawObjectId").val();
			$.ajax({
				type : "post",
				url : WEBPATH + "/caseManager/caseInfoList",
				data : {
					caseStartTimes : caseStartTimes,
					caseEndTimes : caseEndTimes,
					caseState : caseState,
					caseType : caseType,
					lawObjId : lawObjId
				},
				dataType : "json",
				success : function(data) {
					$("#caseContentDiv").children().remove();
					$("#caseMsgDiv").html("");
					if (data.yearList.length > 0) {
						for (var i = 0; i < data.yearList.length; i++) {
							$("#caseContentDiv").append("<div class='timeline-year font-semi-bold'>"+ data.yearList[i]+ "</div>");
							for (var j = 0; j < data.mounthList.length; j++) {
								var mounth = data.mounthList[j];
								var year = data.yearList[i];
								if (mounth.indexOf(year) == 0) {
									var ht = " <div class='timeline-row alt'> <div class='timeline-item'>  <div class='timeline-icon'>"+ (data.mounthList[j]).substring(4)+ "</div>";
									for (var z = 0; z < data.caseInfoList.length; z++) {
										var yearMonth = data.caseInfoList[z].yearMonth;
										if (data.caseInfoList[z].yearMonth == data.mounthList[j]) {
											var color = data.caseInfoList[z].caseState=='已完成'?"#00D900":"#FF6600";
											ht += "<div class='timeline-item-inner' onclick=toCaseInfo('"+data.caseInfoList[z].caseId+"','"+data.caseInfoList[z].id+"','"+data.caseInfoList[z].caseType+"') style='cursor:pointer;'><div style='float:right;padding:10px;'>"+
												"<button class='btn btn-sm' style='background:"+color+";color:#FFF;'>"+data.caseInfoList[z].caseState+"</button></div>"+
												"<div class='timeline-body'>"
													+ "<div class='font-14 padding-xs'>案件类型:"
													+ data.caseInfoList[z].caseTypeName
													+ "</div><div class='font-14 padding-xs'>案件编号："
													+ data.caseInfoList[z].caseNumber
													+ "</div><div class='font-14 padding-xs'>案件名称:"
													+ data.caseInfoList[z].caseName
													+ "</div><div class='font-14 padding-xs'>处罚主体:"
													+ data.caseInfoList[z].punishSubject
													+ "</div></div></div></br>"
										}
									}
									ht += "</div></div>";
									$("#caseContentDiv").append(ht);
								}
							}
						}

					}
				}
			});
		}
		
		$("#caseInfoBtn").click(
			function(){
				showCaseInfo();
			}
		);
		$("#caseSearchBtn").click(
			function(){
				showCaseInfo();
			}
		);
	function toCaseInfo(caseId,id,caseType){
		$("#zfdx").modal('hide');
		document.getElementById("mask").style.display = "block";
		setTimeout(function () {
			if(caseType == '0'){
				business.addMainContentParserHtml(WEBPATH + '/atvEasy/easy-case', "caseId="+caseId+"&parentUrl=0&selectType=2");
			} else if (caseType == '1'){
				business.addMainContentParserHtml(WEBPATH + '/generalCase/toFilingPage', "caseId="+caseId+"&parentUrl=0&selectType=3");
			} else if (caseType == '2'){
				business.addMainContentParserHtml(WEBPATH + '/ajbl_xzml/ybxzcf_xzml', "caseId="+caseId+"&parentUrl=0&selectType=4");
			} else if (caseType == '3'){
				business.addMainContentParserHtml(WEBPATH + '/sequestration/cfky-input', "caseId="+caseId+"&parentUrl=0&selectType=5");
			} else if (caseType == '4'){
				business.addMainContentParserHtml(WEBPATH + '/limitStopProduct/xctc-input', "caseId="+caseId+"&parentUrl=0&selectType=6");
			} else if (caseType == '5'){
				business.addMainContentParserHtml(WEBPATH + '/detention/xzjl-input', "caseId="+caseId+"&parentUrl=0&selectType=7");
			} else if (caseType == '6'){
				business.addMainContentParserHtml(WEBPATH + '/pollution/toPollutionPage', "caseId="+caseId+"&parentUrl=0&selectType=8");
			} else if (caseType == '7'){
				business.addMainContentParserHtml(WEBPATH + '/apply/toApplyForcePage', "caseId="+caseId+"&parentUrl=0&selectType=9");
			} else if (caseType == '8'){
				business.addMainContentParserHtml(WEBPATH + '/ajbl_qtys/ybxzcf_qtys', "caseId="+caseId+"&parentUrl=0&selectType=10");
			} else if (caseType == '9'){
				business.addMainContentParserHtml(WEBPATH + '/penalty/toPenaltyDayPage', "caseId="+caseId+"&penaltyId="+id+"&parentUrl=0&selectType=11&type=2");
			}
		},1000)
	}
	var imageServer = window.parent.$("#fastdfsServer").val();
	var vm = new Vue({
		el:'#objectComment',
		data:{
			year:[],
			month:[],
			map:{
				
			}
			
		},
		methods: {
			contains:function(year,month){
				var temp = month.substring(0,4);
				if(temp==year){
					return true;
				}
				return false;
			},
			showImgModal:function(lawCircleId,orgImageId){
				 $.ajax({
	                 type:"post",
	                 url:WEBPATH+"/zfdx/showImageModal",
	                 dataType:"json",
	                 data:{
	                	 lawCircleId:lawCircleId,
	                	 currentImageOrgId:orgImageId
	                 },
	                 success:function(data){
	                 		$("#fileModal").modal('show');
	                 		$("#modalContent").empty();
	                 		var html = '';
	                 		html = '<div class="modal-header"><div style="float: right; margin-top: -5px;">'
	                 		+'<button type="button" class="btn btn-default" onclick="closeModal()">关闭</button>'
	                 		+'</div><h4 class="modal-title" id="myModalLabel">预览</h4></div>'
	                 		+'<div class="modal-body"><div class="col-sm-12"><div id="myCarousel" class="carousel slide">'
	                 		+'<ol class="carousel-indicators">';
	                 	
	                 		$.each(data,function(i,item){
	        					if(item.selected !=null &&item.selected=='1'){
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" class="active"></li>';
	        					}else{
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" ></li>';
	        					}
	        				});
	                 		html = html + '</ol><div class="carousel-inner">';
	                 		$.each(data,function(i,item){
	        					if(item.selected !=null &&item.selected=='1'){
	        						html = html + '<div class="item active"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%;height:558px; "'
	        						+'alt="'+i+' slide"></div>';
	        					}else{
	        						html = html + '<div class="item"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%; height:558px;"'
	        						+'alt="'+i+' slide"></div>';
	        					}
	        				});
	                 		html = html + '</div><a class="left carousel-control" href="#myCarousel" role="button"'
	         				+'data-slide="prev"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>'
	         				+'<span class="sr-only">Previous</span></a> <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">'
	         				+'<span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>'+'<span class="sr-only">Next</span></a>'
	         				+'</div></div></div><div class="modal-footer"></div>';
	                 		$("#modalContent").append(html);
	                 		
	                 		
	                 }
	             });
			}
		},
		filters:{
			getMonth:function(value){
				var month = value.substring(4,5);
				if(month=='0'){
					return value.substring(5);
				}else{
					return value.substring(4);
				}
				
			},
			getFileUrl:function(url){
				return  imageServer + url;
			}
		}
	});
	function loadObjectCommentTree() {
		var lawObjectId = '${caseHisLawObject.lawObjectId}';
		$.ajax({
					type : "post",
					url : WEBPATH + "/zfdx/getObjectCommentTreeData",
					data : {
						lawObjectId:lawObjectId
					},
					dataType : "json",
					success : function(data) {
						console.log(data);
						vm.$data.year = data.year;
						vm.$data.month = data.month;
						vm.$data.map = data.datas;
					
					}
				});
	}
	function closeModal(){
		$("#fileModal").modal('hide');
	}
	/**加载失信列表信息*/
	function loadDiscreditlist(){
		$('#isSuperviseDataTable').bootstrapTable({       
			 method: 'post',
			 dataType: "json", 
			 url:  WEBPATH+'/zfdx/law-supervise-history',
		     undefinedText : '-',  
		     pagination : true, // 分页  
		     striped : true, // 是否显示行间隔色  
		     cache : false, // 是否使用缓存  
		     pageSize:15, // 设置默认分页为 20
		     pageNumber: 1,
		     queryParamsType: "",
		     locale:'zh-CN',
		     pageList: [50, 100, 200, 300, 500], // 自定义分页列表
		     singleSelect: false,
		     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
		     // showColumns : true, // 显示隐藏列  
		     sidePagination: "server", //服务端请求
		     queryParams:function (params) {
		    	var lawId = '${caseHisLawObject.lawObjectId}';
	            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
            		 pageNum: params.pageNumber,
                     pageSize: params.pageSize,
                     lawId:lawId
	            };
	            return temp;
		    	 
		     },//参数
		     uniqueId : "id", // 每一行的唯一标识  
			 columns: [
			           {
			        	   field: "",
				           title: "序号",
				           align: 'center',
				           formatter: function(value,row,index){
					        	 return index + 1;
					       }
			           },
			           {
				           field: "entityname",
				           title: "名称",
				           align: 'center'
				       },
			           {
				           field: "caseno",
				           title: "案号",
				           align: 'center'
			           },
			           {
				           field: "court",
				           title: "执法法院",
				           align: 'center' 
			           },
			           {
				           field: "reason",
				           title: "失信被执行人行为具体情况",
				           align: 'center'
			           },
			           {
				           field: "publishdate",
				           title: "发布时间",
				           align: 'center',
				           formatter: function(value,row,index){
				        		if(value==null || value==''){return '';}
				                var date = new Date(value);
				                var y = date.getFullYear();
				                var m = date.getMonth() + 1;
				                var d = date.getDate();
				                var h = date.getHours();  
				                var i = date.getMinutes(); //分
				                var s = date.getSeconds(); //秒
				                return y + '-' +m + '-' + d+' '+h+':'+i+':'+s;
					       }
			           },
			           {
				           field: "appstatus",
				           title: "是否有效",
				           align: 'center',
				   		   formatter : function(value,row,index){
				               if(value!=null){
				            	   if(value==4){
				            		   return "有效"; 
				            	   }else{
				            		   return "无效"; 
				            	   }
				               }else{
				            	  return ""; 
				               }
				            }
			           }
			 ],
			 responseHandler : function(res) {  
	               return {  
	                   total : res.total,  
	                   rows : res.list  
	               };  
	         },
             onCheck: function(row, $element) {
             },//单击row事件
	         onUncheck: function(row, $element) {
	        	
		     },
		     onUncheckAll: function(row, $element) {
		     },
		     onClickRow: function (row) {
		    	 punishmentModel(row)
		     },
		     onCheckAll:function(row, $element) {
		     },
		     onRefresh: function () {
		     },
	         formatLoadingMessage: function () {
	        	   return "玩命加载中...";
	         },
	         formatNoMatches: function () { //没有匹配的结果
	        		   return '无符合条件的记录';
	         }
		});
	}
	/**弹出的模态框信息**/
	function punishmentModel(row){
		$('#punishmentModel').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		var options = {
				remote : encodeURI(WEBPATH + '/zfdx/law-punishment?infoid='+row.infoid)
		};
		$('#punishmentModel').modal(options);
	}
	</script>
</body>
</html>