<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
  	<head>
	    <title>环境监察全过程业务智能办理系统</title>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
	    <meta name="description" content="">
	    <meta name="author" content="">
</head>
<body>
				<div class="modal-header">
					<div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info" onclick="saveTjqttmItemForm()">确定</button>
                        <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
					<h4 class="modal-title" id="myModalLabel">添加其他条目</h4>
				</div>
				<div class="modal-body">
				<form id="tjqttm-itemForm">
					<input type="hidden" name="attachIds" id="attachIds_other" value=""/>
					<input type="hidden" name="typeCode"  value="56"/>
					<input type="hidden" name="typeName"  value="公共-其它"/>
					<div class="smart-widget-body form-horizontal padding-md">                    
						<div class="form-group" style="margin-top:-20px;">
                            <h5 style="margin-left:10px;"><span style="color:red;">*</span> 条目名称</h5>
                            <div class="col-lg-12 col-sm-12 col-xs-12">
                                <input  class="form-control" placeholder="条目名称" id="tjqttm-name" name="name">
                            </div>
                        </div>
                        <div class="form-group">
                            <h5 style="margin-left:10px;"><span style="color:red;">*</span> 补充附件上传</h5>
                            <div class="col-lg-12">
                                <form enctype="multipart/form-data">
									  <input id="file-other" name="file-es[]" type="file" multiple>
                                </form>
                            </div>
                        </div>


					</div>
				</form>
				</div>
				<div class="modal-footer">
                </div>
</body>

<script type="text/javascript">
	$(function(){
		//监听回退键
		business.listenBackSpace();    
	});
</script>
<script type="text/javascript">
			$("#tjqttm-itemForm").formValidation({
				framework : 'bootstrap',
				message : 'This value is not valid',
				icon : {
					valid : 'glyphicon glyphicon-ok',
					invalid : 'glyphicon glyphicon-remove',
					validating : 'glyphicon glyphicon-refresh'
				},
				fields : {
					name: {
				           validators: {
				                    notEmpty: {
				                        message: '条目名称不能为空'
				                    },remote:{
			                    	url: '/atvEasy/check-generate-name', 
			                        message: '条目名称已存在', 
			                        delay :  0,// 设置2秒发送一次ajax（默认输入一个字符，提交一次，服务器压力太大）
			                        type: 'POST', 
			                        data:{tablesName:'${generateBean.tablesName}',objectId:'${generateBean.objectId}'}
			                    },
				      				stringLength: {
				                        max: 100,
				                        message: '条目名称最大长度100个字'
			                   	    }
				            	}
					}
				}
			});
			$("#file-other").fileinput({
				theme: "explorer",
				language : 'zh',
				uploadUrl : WEBPATH + '/atvEasy/itemFileUpload?tableName='+'${generateBean.tablesName}'+'&caseId='+'${generateBean.baseId}'+'&objectId='+'${generateBean.objectId}',
				allowedFileExtensions : [ 'jpg', 'png', 'bmp', 'pdf'],
				maxFileSize : 1024*50,
				maxFilePreviewSize:1024*50,
				maxFileCount: 6,
				uploadAsync:false,
			  	initialCaption: "附件格式支持png,jpg,bmp,pdf，每个文件不大于50M，数量为6",
				slugCallback : function(filename) {
					return filename.replace('(', '_').replace(']', '_');
				}
			}).on('filebatchuploadsuccess', function(event, data, previewId, index) {
				if(data.response.data.success == '200'){
					var responseData = data.response.data;
					var ids = $("#attachIds_other").val();
					if (ids != null && ids != '' && ids != 'undefined'){
						$("#attachIds_other").val(ids + "," + responseData.attachIds);
					} else {
						$("#attachIds_other").val(responseData.attachIds);
					}
					swal({title:"上传成功", text:"附件上传成功", type:"success",allowOutsideClick :true});
				} else {
					swal({title:"上传失败", text:data.response.meta.detailMessage, type:"error",allowOutsideClick :true});
				}
			}).on('fileuploaded',function(event,data){
				if(data.response.data.success == '200'){
					var responseData = data.response.data;
					var ids = $("#attachIds_other").val();
					if (ids != null && ids != '' && ids != 'undefined'){
						$("#attachIds_other").val(ids + "," + responseData.attachIds);
					} else {
						$("#attachIds_other").val(responseData.attachIds);
					}
					swal({title:"上传成功", text:"附件上传成功", type:"success",allowOutsideClick :true});
				} else {
					swal({title:"上传失败", text:data.response.meta.detailMessage, type:"error",allowOutsideClick :true});
				}
			}).on('filesuccessremove', function(event, id) {
			});
		
		function saveTjqttmItemForm(){
			var validate = false;
			$("#tjqttm-itemForm").data('formValidation').validate();
			validate = $("#tjqttm-itemForm").data('formValidation').isValid();
			var data = $('#tjqttm-itemForm').serialize();
			var tablesName = '${generateBean.tablesName}'; var  trunkId = '${generateBean.objectId}';
			var caseId = '${generateBean.baseId}';
			var url = '/atvEasy/saveDocumentItem?tablesName='+tablesName+'&trunkId='+trunkId+'&caseId='+caseId;
			if(validate) {
				var ids = $("#attachIds_other").val();
				if(ids==null || ids == ''){
					swal({ title : "请完成上传", text : "附件未完成上传，或未传递附件", type : "info",allowOutsideClick :true });
					return ;
				}
				business.openwait();
				$.ajax({
		            cache: true,
		            type: "POST",
		            url: url,
		            data:data,
		            error: function(request) {
		            	business.closewait();//关闭遮罩层
		            	swal("错误!","系统错误", "error");
		            },
		            success: function(data) {
		            	business.closewait();//关闭遮罩层
		        		if (data.result == 'success') {// 成功
		        			 var obj ={id:data.code,name:data.message, typeName:data.url, typeCode:data.data,penaltyCaseNumber:data.penaltyCaseNumber,filesSize:data.filesSize}; 
							 generateBaseArr.push(obj);
							 generateVue.$data.items = generateBaseArr;
							 statisticalVue.$data.totalCount = sumAllFileSize();
							 statisticalVue.$data.itemCount=statisticalVue.$data.itemCount+1;
							swal({ title : "保存成功", text : "", type : "success",allowOutsideClick :true })
						} else if (data.result == 'error') { // 失败  
							swal({ title : data.message, text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
		            }
		        });
				$('#tjqttm').modal('hide');
				$('#tjqttm').on('hide.bs.modal', function () {
					   $(this).removeData("bs.modal");  
				});
			}else if (validate == null) {
				business.closewait();// 关闭遮罩层
				$("#tjqttm-itemForm").data('formValidation').validate();
			}
		}
</script>
</html>