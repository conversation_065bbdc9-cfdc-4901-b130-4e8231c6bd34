<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
    <%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="fileModalLabel">附件预览</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-body">
		<c:if test="${fileInfoModel.fileType==2 || fileInfoModel.fileType == 0}">
			<div class="pricing-value">
				<a href="javascript:void(0)"><div id="B${fileInfoModel.id}"></div></a>
			</div>
		</c:if>
		<c:if test="${fileInfoModel.fileType==1}">
			<div class="pricing-value">
				<span class="value"><a href="javascript:void(0)"><img style="height:100%;" src="${FASTDFS_ADDR}/${fileInfoModel.fileUrl}" /></a></span>		
			</div>
		</c:if>
	</div>
</div>
<div class="modal-footer">	
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
</div>
<script type="text/javascript">
	$(function(){
		//监听回退键
		business.listenBackSpace();    
	});
</script>
<script type="text/javascript">
function download(){
	var ifGene = '${ifGene}';
	var fileName = '${fileName}';
	if(ifGene != null && ifGene != 'undefined' && ifGene == '1'){
		location.href="${webpath}/attachmentReadOnly/downloadFile?id=${fileInfoModel.id}&type=1&fileName="+fileName;
	} else {
		location.href="${webpath}/attachmentReadOnly/downloadFile?id=${fileInfoModel.id}&tableName=${tableName}";
	}
}
$(document).ready(function(){
		

		$('#inputImgModeler').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		var obj=$.parseJSON('${fileInfo}');
		if(obj != null) {
			var select="#B"+obj.id;
			if(obj.fileType==2 || obj.fileType == 0){
				if(isIEWhether){
					if(PDFObject.supportsPDFs){
						PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select);
					} else {
						 swal({ 
						        title: "提示",  
						        text: "您的浏览器不支持pdf预览功能，请安装pdf阅读器后重试！",  
						        type: "warning", 
						        showCancelButton: true, 
						        closeOnConfirm: true, 
						        confirmButtonText: "下载并安装", 
						        confirmButtonColor: "#ec6c62" 
						    }, function() { 
						        window.location.href=WEBPATH+"/sysUser/downloadPdfReader"
						    }); 
					}
				}else{
					var options = {
							/* pdfOpenParams: {
								navpanes: 0,
								toolbar: 0,
								statusbar: 0,
								page:1
							}, */
							forcePDFJS: false,
							PDFJS_URL: WEBPATH+"/static/pdfjs/web/viewer.html"
					};
					PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select,options);
				}
			}
		}
		
})

</script>