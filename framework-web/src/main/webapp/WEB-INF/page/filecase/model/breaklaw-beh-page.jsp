<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page
	import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%String app_myenvirprotect = PropertiesHandlerUtil.getValue("app_myenvirprotect","fastdfs");%>
<c:set var="SERVER_APP"><%=app_myenvirprotect%></c:set>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
 <div class="modal-header">
       <div style="float:right; margin-top:-5px;">       
       <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
       </div>
       <h4 class="modal-title" id="myModalLabel">本系统对应违法行为</h4>
   </div>
   <div class="modal-body">
       <div class="smart-widget-body form-horizontal">
           <div class="form-group">
               <label for="违法类型" class="col-lg-2 control-label">违法类型</label>
               <div class="col-lg-8">
                   <select class="form-control" id ="behtype" name ="behtype">
                   </select>
               </div>
           </div>
           <div class="form-group">
               <label for="违法事实" class="col-lg-2 control-label">违法事实</label>
               <div class="col-lg-8">
                   <input type="text" class="form-control" id="fact" placeholder="违法事实">
               </div>
           </div>
           <div class="form-group">
               <div class="col-lg-offset-2 col-lg-10">
                   <button type="button" class="btn btn-info"
                    id ="behSearchBtn"   style="width: 120px;">查 询</button>
               </div>
           </div>
           <p></p>
            <table id="behTable" class ="table table-no-bordered">
            <div style="position:absolute; right:60px; margin-top:11px;">
            <button type="button" class="btn btn-info" data-dismiss="modal" onclick="checkBeh()">确定</button>
            </div>
            </table>
       </div>
   </div>
   <div class="modal-footer">
   </div>
<script type="text/javascript">
	var beh_ids = new Array();
	var beh_facts = new Array();
	var beh_types = new Array();
	var beh_delIds = new Array();
   		 
	function checkBeh(){
		
		$("#sys_beh_ids").val(beh_ids);
		$("#sys_beh_names").val(beh_facts);
		var checkFormName =  '${formName}';
		behCodes = beh_ids.join(",");
		var FormName =  '${formName}';

		if(FormName=='upperHalf' || FormName=='xctc_Up_Form'){ 
			if(FormName=='upperHalf'){
				$('#'+FormName).formValidation('revalidateField', 'majorViolationsNames');
			}else{
				$('#'+FormName).formValidation('revalidateField', 'majorViolations'); 
			}
			$('#'+FormName).formValidation('revalidateField', 'majorViolationsText');
		}
		//要判断一下是否是第一次填写如果是，则级联显示处罚依据，如果不是，则不级联
		var isRelation = '${isEmpty}';
		if(isRelation=='empty'){
			if(beh_delIds.length>0){
			  	$.ajax({
					type:'GET',
					url:'https://www.12369.com/' + "/api/search/behCase",
					async:false,
					data:{behId:encodeURI(beh_delIds.toString())},
					success:function(data){
						if(data.lawList !=null && data.lawList.length>0){
							var lawList = data.lawList;
							$.each(data.lawList,function(i,value){
								$("#"+value.id).remove();
								if(codes!=null && codes.length > 0){
									for(var z = 0; z< codes.length; z++){
										if(codes[z] == value.id){
											codes.splice(z,1);
											names.splice(z,1);
											factors.splice(z,1);
										}
									}
								}
							});	
						}
					}
			  	})
			}
			
			//请求https://www.12369.com/api/search/behCase?behId=1,2
			//返回两个list，给循环展示一下~~，第二个比较麻烦，要做循环对比
			$.ajax({
				type:'GET',
				url:'${SERVER_APP}' + "/api/search/behCase",
				async:false,
				data:{behId:encodeURI(beh_ids.toString())},
				success:function(data){
					if(data.behList != null && data.behList.length>0){
					 //违法案件类型
					}
					if(data.lawList !=null && data.lawList.length>0){
						//处罚依据
						var namesTemp = new Array();
						var obj = eval(data.lawList);
						/* if(codes.length>0){
							codes.splice(0,codes.length);
							names.splice(0,names.length);
							factors.splice(0,factors.length);
							$("#punishReason").empty();
						} */
						var tempDataArr = new Array();
						function tempData(x1,x2,x3){
							this.id=parseInt(x1);
							this.contents=x2;
							this.factors=x3;
						}
						if(namesTemp.length>0){
							$.each(namesTemp,function(index,val){
								var xxx = val.split("$");
								codes.push(parseInt(xxx[0]));
								names.push(val);
								factors.push(xxx[2]);
								tempDataArr.push(new tempData(xxx[0],xxx[1],xxx[2]));
								// $("#punishReason").append('<tr id="'+xxx[0]+'"> <td>'+xxx[1]
								//+'</td> <td class="text-center" style="width:40px; background-color:#ccc; padding:3% 0;"><a href="javascript:removeId('+xxx[0]+',\''+xxx[2]+'\');"><i class="fa fa-times" style="color:#F00; font-size:20px;"></i></a></td> </tr>');
							});
						}
   	   					   
						$(obj).each(function(index){
							var val = obj[index];
							
							var locate = $.inArray(val.id, codes);
							if(locate==-1){
								var contents = "《"+val.title+"》";
								if(val.chapter!=null && val.chapter!=''&& val.chapter!='undefined'){
									contents+=("第"+val.chapter+"章");
								}
								if(val.article!=null && val.article!=''&& val.article!='undefined'){
									contents+=("第"+val.article+"条");
								}
								if(val.paragraph!=null && val.paragraph!=''&& val.paragraph!='undefined'){
									contents+=("第"+val.paragraph+"款");
								}
								if(val.content!=null && val.content!=''&& val.content!='undefined'){
									contents+=val.content.replace(/\s/g, "");
								}
								//$("#punishReason").append('<tr id="'+val.id+'"> <td>'+contents
								//+'</td> <td class="text-center" style="width:40px; background-color:#ccc; padding:3% 0;"><a href="javascript:removeId('+val.id+',\''+val.factor+'\');"><i class="fa fa-times" style="color:#F00; font-size:20px;"></i></a></td> </tr>');
								tempDataArr.push(new tempData(val.id,contents,val.factor));
								codes.push(val.id);
								names.push(val.id+"$"+contents+"$"+val.factor);
								factors.push(val.factor); 
							}
						});
						
						$.each(tempDataArr,function(index,value){
							$("#punishReason").append('<tr id="'+value.id+'"> <td><textarea rows="5" id="c'+value.id+'" class="form-control" placeholder="处罚依据">'+value.contents
							+'</textarea ></td> <td class="text-center" style="width:40px; background-color:#ccc; padding:3% 0;"><a href="javascript:removeId('+value.id+',\''+value.factors+'\');"><i class="fa fa-times" style="color:#F00; font-size:20px;"></i></a></td> </tr>');
						});
						
						//if(pSDate=='' || pSDate==null || pSDate=='undefined'){
						$("#punishBasisCode").val(codes);
						var namestr = names.join("&&");
						$("#punishBasisName").val(namestr);
						proofCodes=codes;
   	   					var lowerName = '${lowerName}';
   	   						 
						if(checkFormName!='' && $.trim(checkFormName).length > 0){
							$('#'+checkFormName).formValidation('revalidateField', '${lowerName}');
						}
   	   					    
						if(checkFormName!='' && $.trim(checkFormName).length > 0 && $.trim(codes).length>0){
							$('#'+checkFormName).formValidation('revalidateField', '${attrName}');
						}else{
							$('#'+checkFormName).data('formValidation').updateStatus('${attrName}', 'NOT_VALIDATED', null);
						}
						//if(pSDate=='' || pSDate==null || pSDate=='undefined'){
						if(checkFormName=='xctc_Up_Form'){
						}else{
						    typeTransfer(factors,typeCodes,typeNames);
						}	
   	   						 
					}
				}
			});
		
			//如果值为空，清空关联的处罚依据和下面的东西
			if(beh_ids.length==0){
				$("#punishBasisCode").val(null);
				$("#punishBasisName").val(null);
				// $("#"+checkFormName).formValidation('enableFieldValidators', 'punishBasisName', false);
				//$("#punishReason").empty();
				$("#punishReason").html("");
				$("#littleCaseTypeCode").val(null);
				$("#littleCaseTypeName").val("");
				codes.splice(0,codes.length);
				names.splice(0,names.length);
				factors.splice(0,factors.length);
				
				if(basis.length>0){
					$.each(basis,function(i,value){
						if($.inArray(value.id, codes) == -1){
							var contents = "《"+value.title+"》";
							if(value.chapter!=null && value.chapter!=''&& value.chapter!='undefined'){
								contents+=("第"+value.chapter+"章");
							}
							 if(value.article!=null && value.article!=''&& value.article!='undefined'){
								contents+=("第"+value.article+"条");
							 }
							if(value.paragraph!=null && value.paragraph!=''&& value.paragraph!='undefined'){
								contents+=("第"+value.paragraph+"款");
							 }
							if(value.content!=null && value.content!=''&& value.content!='undefined'){
								contents+=value.content.replace(/\s/g, "");
							 }
							
							$("#punishReason").append('<tr id="'+value.id+'"> <td><textarea rows="5" id="c'+value.id+'" class="form-control" placeholder="处罚依据">'+contents+'</textarea ></td> <td class="text-center" style="width:40px; background-color:#ccc; padding:3% 0;"><a href="javascript:removeId('+value.id+',\''+value.factor+'\');"><i class="fa fa-times" style="color:#F00; font-size:20px;"></i></a></td> </tr>');
							codes.push(value.id);
							names.push(value.id+"$"+contents+"$"+value.factor);
							factors.push(value.factor);
						}
	   		   				
					});

					$("#punishBasisCode").val(codes);
					var namestr = names.join("&&");
					$("#punishBasisName").val(namestr);
					proofCodes=codes;
					
					if(checkFormName!='' && $.trim(checkFormName).length > 0){
					      $('#'+checkFormName).formValidation('revalidateField', '${lowerName}');
					}
					if(checkFormName!='' && $.trim(checkFormName).length > 0 && $.trim(codes).length>0){
						$('#'+checkFormName).formValidation('revalidateField', '${attrName}');
					}else{
					    $('#'+checkFormName).data('formValidation').updateStatus('${attrName}', 'NOT_VALIDATED', null);
					}
					//if(pSDate=='' || pSDate==null || pSDate=='undefined'){
					if(checkFormName=='xctc_Up_Form'){
					}else{
						typeCodes.splice(0,typeCodes.length);
						typeNames.splice(0,typeNames.length);
					    typeTransfer(factors,typeCodes,typeNames);
					}	
				}
			}
		}
	}
   		 
   		$(document).ready(function() {
   			$('#bxtwfxw').on('hide.bs.modal', function () {
   				   $(this).removeData("bs.modal");  
   			});
   		    var SERVER_APP = '${SERVER_APP}';
	   		$.ajax({
	   			cache : true,
	   			type : "GET",
	   			url :SERVER_APP + "/api/search/behtype",
	   			error : function(request) {
	   				swal("错误!","获取违法类型失败！", "error");
	   			},
	   			success : function(data) {
	   				 $("#behtype").children().remove();
	   				$("#behtype").append("<option value =''>请选择</option>")
	   				if(data != null && data.length>0){
	   					for(var i=0;i<data.length;i++){
	   						$("#behtype").append("<option value ='"+data[i].behtype_id+"'>"+data[i].typename+"</option>");
	   					}
	   				} 
				}
			})
			
			LoadingDataBehTableItems(SERVER_APP);
	   		$('#behTable').bootstrapTable('hideColumn', 'factor_name');
	   	   	$('#behTable').bootstrapTable('hideColumn', 'beh_id');
   		})
   		//搜索
   		$("#behSearchBtn").click(function(){
   		    $('#behTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:10});
   		})
   		
  		function LoadingDataBehTableItems(url){
  	   		$('#behTable').bootstrapTable({
  			 method: 'get',
  			 dataType: "json", 
  			  url:  url+'/api/search/behlist',
  			  //http://localhost:8099/public/api/search/behlist?behtypeid=&fact=&page=1
  			  // url :"http://localhost:8099/public/api/search/behlist",
  		       undefinedText : '-',  
  		       pagination : true, // 分页  
  		       striped : true, // 是否显示行间隔色  
  		       cache : false, // 是否使用缓存  
  		       pageSize: 10, // 设置默认分页为 20
  		       pageNumber: 1,
  		       clickToSelect:true,
  		       queryParamsType: "",
  		       pageList: [10, 25, 50, 100, 200], // 自定义分页列表
  		       singleSelect: false,
  		  	   clickToSelect:true,
  		   	   checkboxHeader:false,
  		       contentType: "application/x-www-form-urlencoded;charset=UTF-8",
  		      // pageList : [ 5, 10, 20 ],  
  		      // showColumns : true, // 显示隐藏列  
  		       sidePagination: "server", //服务端请求
  			   queryParams: queryBehParams,//参数
  			   showRefresh : true, // 显示刷新按钮  
  			   uniqueId : "id", // 每一行的唯一标识 
  			   //belongAreaName   belongDepartmentName  username  jobName 行政区	部门	姓名	职务
  			   columns: [
  				       {
  				    	   title: '序号',//标题  可不加  
  		                   align: 'center',
  		                   formatter: function (value, row, index) {  
  		                       return index+1;  
  		                   } 
  				       },
  				    {
  				           field: "factor_name",
  				           title: "违法事实",
  				           align: 'center',
  				       },
  				       {
  				           field: "typename",
  				          // field: "factor_name",
  				           title: "违法类型",
  				           align: 'center',
  				          
  				       },
  				       {
  				           field: "beh_fact",
  				           title: "违法事实",
  				           align: 'center',
  				        formatter:function(value, row, index){
			        	   return row.beh_fact+"("+row.factor_name+")";
			           }
  				       },
  				  	  {
  				           checkbox: true,
  				           title: "操作",
  				           align: 'center',
  				           }
  			           ],
  			           responseHandler : function(res) {  
  			               return {  
  			                   total : res.total-1,  
  			                   rows : res.data  
  			               };  
  			           },
  			           onCheck: function(row, $element) {  
  			        	   beh_ids.push(row.beh_id);
  			        	   beh_facts.push(row.beh_fact);
  			        	   beh_types.push(row.factor_name);
  			           },//单击row事件  getAllSelections
  			           onUncheck: function(row, $element) {
  			        	   var index = beh_ids.indexOf(row.beh_id);
  			        	   beh_ids.splice(index,1);
  			        	   beh_facts.splice(index,1);
  			        	   beh_types.splice(index,1);
  			        	   
  			        	 	beh_delIds.push(row.beh_id);
  		        	   },
  		        	   onPageChange:function(){
  		        	   },
  				       onLoadSuccess:function(data,e){
	  				    	$('#behTable').bootstrapTable('hideColumn', 'factor_name');
	  				   	   	$('#behTable').bootstrapTable('hideColumn', 'beh_id');
	  				   	   	
	  				   	   	//加载已选中的效果
	  				     	if(behCodes !='' && behCodes.length>0){
	  				     		var xArr = behCodes.split(",");
		  			   			var rows = data.rows;
		  		  				if(rows.length>0){
		  		  					// 若数据，则不许要循环
		  		  					for( var j =0; j < rows.length; j++ ){
			   		  					for(var x=0;x<xArr.length;x++){
			   		  						if(xArr[x]==rows[j].beh_id){
			   		  						    $('#behTable').bootstrapTable('check', j);
			   		  						}
			   		  					}
		  		  					}
		  		  				}
  			   		 		}
		        	 },
		        	 onRefresh:function () {
		        	 },
			         formatLoadingMessage: function () {
			        	   return "数据加载中...";
			      	 },
			         formatNoMatches: function () { //没有匹配的结果
			        		   return '无符合条件的记录';
			       	}
  				})
   	   	}
		
		function queryBehParams(params) {
	    		var fact = encodeURI($("#fact").val());
				var behtype = $('#behtype option:selected').val();
				if(behtype == null || behtype ==''){
					behtype =" ";
				}
				if(fact == null || fact ==''){
					fact =" ";
				}
	          var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
	              page: params.pageNumber,
	              fact:fact,
	              behtypeid:behtype
	          };
	          return temp;
	      }
   		
   		$(document).ready(function(){
   			//监听enter查询，内包含监听回退键
   		    business.listenEnter("behSearchBtn");
   		});
        </script>
    