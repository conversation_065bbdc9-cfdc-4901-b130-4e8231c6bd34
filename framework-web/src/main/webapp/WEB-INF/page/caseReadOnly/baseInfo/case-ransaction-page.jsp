<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta charset="utf-8">
<title>福建环境监察全过程业务智能办理系统</title>
<meta name="renderer" content="webkit">
<meta http-equiv="pragma" content="no-cache">
<meta http-equiv="cache-control" content="no-cache">
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
 
</head>
<script type="text/javascript">
$(function(){
    //监听回退键
    business.listenBackSpace();    
   });
		//办理跳转 
		function chooseCase(id){
			business.addMainContentParserHtml(WEBPATH+"/caseInfo/baseInfoCasePage?caseId="+id+"&selectType=1&parentUrl=1",null);
		}
		$(document).ready(function(){
			
			business.listenEnter("searchButt");
			$('#caseDataTable').bootstrapTable({       
				 method: 'post',
				 dataType: "json", 
				 url:  WEBPATH+'/hisoryCase/caseRansaction',
			     undefinedText : '-',  
			     pagination : true, // 分页  
			     striped : true, // 是否显示行间隔色  
			     cache : false, // 是否使用缓存  
			     pageSize:10, // 设置默认分页为 20
			     pageNumber: 1,
			     queryParamsType: "",
			     locale:'zh-CN',
			     pageList: [5, 10, 20,30,50], // 自定义分页列表
			     singleSelect: false,
			     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			     // showColumns : true, // 显示隐藏列  
			     sidePagination: "server", //服务端请求
			     queryParams:function (params) {
			      		var caseNumber = $("[name='caseNumber']").val();
			      		var caseName=$("[name='caseName']").val();
			      		var lawObjectName=$("[name='lawObjectName']").val();
			            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
		            		 pageNum: params.pageNumber,
		                     pageSize: params.pageSize,
		                     caseNumber:caseNumber,
		                     caseName:caseName,
		                     lawObjectName:lawObjectName
			            };
			            return temp;
			     },//参数
			     uniqueId : "id", // 每一行的唯一标识  
				 columns: [
				        /*    {
					           field: "taskNumber",
					           title: "进度状态",
					           align: 'center'
					       }, */
				           {
					           field: "caseNumber",
					           title: "案件编号",
					           align: 'center',
					           width: '200px'
					       },
				           {
					           field: "lawObjectName",
					           title: "当事人名称",
					           align: 'center'
				           },
				           {
					           field: "caseName",
					           title: "案件名称",
					           align: 'center'
				           }/*,
							{
								field : "isPowerEdit",
								title : "是否有权限修改",
								align : 'center',
								formatter: function (value, row, index) {
									if(row.isPowerEdit == 0) {
										return "<div>不可修改</div>";
									} else if (row.isPowerEdit == 1) {
										var powerDateEnd = row.powerDateEnd;
										var date = new Date(powerDateEnd);
						                var y = date.getFullYear();
						                var m = date.getMonth() + 1;
						                var d = date.getDate();
						                var dateStr = y + '-' +m + '-' + d;
										return "<div style='color:red'>可修改（截止到 "+dateStr+" 00:00:00）</div>";
									}
				                }
							}*/,
				           {    
				        	   field: 'id',
					           title: "操作",
					           align: 'center',
					           formatter: function(value,row,index){
					        	   var html="";
						        	  html+="<a onclick=\"chooseCase('"+row.id+"')\" style=\"cursor:pointer;\"><i class='fa fa-pencil-square-o' style='color:#23b7e5;'>查看</i></a>"
						            return html;
					           }
				           }
				 ],
				 responseHandler : function(res) {  
		              return {  
		                   total : res.data.total,  
		                   rows : res.data.list  
		               }; 
		         },
	             onCheck: function(row, $element) {
	        	   
	             },//单击row事件
		         onUncheck: function(row, $element) {
		        		
			     },
			     onUncheckAll: function(row, $element) {
			       			
			     },
			     onCheckAll:function(row, $element) {
			        		
			     },
			     onRefresh: function () {
			        		
			     },
		         formatLoadingMessage: function () {
		        	   return "玩命加载中...";
		         },
		         formatNoMatches: function () { //没有匹配的结果
		        		   return '无符合条件的记录';
		         }
			});
			
			//返回按钮之后参数和页码的回显
			var params = $("#groundBackParams").val();
			//console.info(params);
			if(params != null && params != '' && params != 'undefined'){
				
				var jsonParam = $.parseJSON(params);
				
				for(var key in jsonParam){
					//绑定设定条件
					$("[name="+key+"]").val(jsonParam[key]);
					//console.info("["+key+"]="+jsonParam[key]);
				}
				//$('#caseDataTable').bootstrapTable('refresh');
				$('#caseDataTable').bootstrapTable('refresh',{pageNumber:parseInt(jsonParam['pageNum']),pageSize:parseInt(jsonParam['pageSize'])});
			}
			
       		//绑定搜索按钮
			$('#searchButt').click(function() {
				//$('#caseDataTable').bootstrapTable('refresh');
				$('#caseDataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:10});
		   	});
       		
	  });
</script>
<body class="overflow-hidden">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
	<div class="main-container">
		<div class="padding-md">
			<!--第一层快速查询row-->
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget">
						<div class="smart-widget-inner">
							<div class="smart-widget-body form-horizontal">
							
					 			<form  id= "searchForm" role="form"  >
								<div class="form-group">
									<label class="control-label col-lg-2">当事人名称</label>
									<div class="col-lg-3">
										<input type="text" placeholder="当事人名称" class="form-control"  name="lawObjectName" >
									</div>
									<label class="control-label col-lg-2">案件名称</label>
									<div class="col-lg-3">
										<input type="text" placeholder="案件名称" class="form-control"  name="caseName" >
									</div>
								</div>
									<div class="form-group">
									<label class="control-label col-lg-2">案件编号</label>
									<div class="col-lg-3">
										<input type="text" placeholder="案件编号" class="form-control"  name="caseNumber" >
									</div>
								</div>
								<div class="form-group">
									<div class="col-lg-10 text-right">
										<button class="btn btn-info" type="button" id="searchButt"
											style="width: 150px;">查询</button>
									</div>
								</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--./第一层快速查询row-->

			<!--第二层任务办理row-->
			<div class="row">
				<!--任务办理-->
				<div class="col-lg-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 历史案件 <span
								class="smart-widget-option"> <span
								class="refresh-icon-animated"> <i
									class="fa fa-circle-o-notch fa-spin"></i>
							</span> 
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">
							<table class="table table-striped no-margin table-no-bordered" id="caseDataTable">
							
							</table>
						</div>
					</div>
				</div>
			</div>
	</div>
</body>
</html>
