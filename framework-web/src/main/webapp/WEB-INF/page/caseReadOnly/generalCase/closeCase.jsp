<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<sec:authentication property="principal" var="authentication" />
<script
	src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"
	type="text/javascript"></script>
<div class="main-container">
	<div class="padding-md">

		<div class="smart-widget">
			<div class="smart-widget-inner">
				<jsp:include page="../caseBusiness.jsp"></jsp:include>
				<div class="row">
					<div class="col-lg-9">
						<div class="tab-content" style="margin-top: -20px;">
							<div class="tab-pane fade in active" id="style1Tab1">
								<div class="smart-widget-inner table-responsive">
									<div class="smart-widget-body form-horizontal">
										<div style="padding: 5px 0;">
											<span
												style="font-size: 16px; font-weight: bold; color: #666;">一般行政处罚案件编号：</span><span
												style="font-size: 16px; font-weight: bold; color: #666;">${generalCaseInfo.easySanctionNumber }</span>
										</div>
										<!--start-->
										<legend class="font-16"
											style="font-weight: bold; color: #23b7e5; margin-top: 10px;">执行情况</legend>

										<div style="float: right; margin: -55px 0 0 100px;">
											<c:if test="${caseStateInfo.parentUrl=='2' && not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
												<button class="btn btn-danger btn-sm" onclick="deleteById()">删除</button>
											</c:if>
											<c:if test="${caseStateInfo.parentUrl eq 0 and caseStateInfo.isPowerEdit==1 and not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
											</c:if>
											<c:if test="${caseStateInfo.parentUrl=='2' && empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">新增</button>
											</c:if>
										</div>
										<input type="hidden" id="id" name="id" value="${generalCaseInfo.id }"/>
										<form id="closeForm">
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">本环节开始办理时间</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
												<span>
												<fmt:formatDate value='${generalCaseInfo.closedStartDate }' pattern='yyyy-MM-dd'></fmt:formatDate>
												</span>	
											</div>
										</div>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">执行情况</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
												<span>
                                                    <c:if test="${generalCaseInfo.implementationCode=='1' }">代履行</c:if> 
                                                    <c:if test="${generalCaseInfo.implementationCode=='2' }">自觉履行</c:if>
                                                    <c:if test="${generalCaseInfo.implementationCode=='3' }">申请法院强制执行</c:if>
                                                    <c:if test="${generalCaseInfo.implementationCode=='4' }">尚未执行</c:if> 
                                                    <c:if test="${generalCaseInfo.implementationCode=='5' }">无须执行</c:if>
                                                    <c:if test="${generalCaseInfo.implementationCode=='6' }">已无法执行</c:if> 
												</span>
											</div>
										</div>
											<c:if test="${generalCaseInfo.implementationCode=='5' or generalCaseInfo.implementationCode=='6' }">
										<!-- 开始------------- -->
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">
												说明理由</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
												<span>${generalCaseInfo.explainReasons }</span>
											</div>
										</div>
										</c:if>
										<c:if test="${generalCaseInfo.implementationCode=='1' or generalCaseInfo.implementationCode=='2' or generalCaseInfo.implementationCode=='3' }">
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">执行完毕日期</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
												<span><fmt:formatDate value='${generalCaseInfo.impleOverDate }' pattern='yyyy-MM-dd'></fmt:formatDate></span>
											</div>
										</div>
										</c:if>
										<legend class="font-16"
											style="font-weight: bold; color: #23b7e5; margin-top: 10px;">结案信息</legend>
                                       
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">复议情况</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
												<span>	
													<c:if test="${generalCaseInfo.reviewSituation=='1' }">是</c:if>
													<c:if test="${generalCaseInfo.reviewSituation=='0' }">否</c:if>
												</span>
											</div>
										</div>
										
										<c:if test="${generalCaseInfo.reviewSituation=='1' }">
										<!--复议情况 start-->
										<div id="fyqk"  >
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">复议结果</label>
                                                <div class="col-lg-8 col-sm-8 col-xs-12">
			                                         <span>
			                                            <c:if test="${generalCaseInfo.reviewResultCode=='1' }">维持</c:if>
			                                            <c:if test="${generalCaseInfo.reviewResultCode=='2' }">撤销</c:if>
			                                            <c:if test="${generalCaseInfo.reviewResultCode=='3' }">变更</c:if>
			                                            <c:if test="${generalCaseInfo.reviewResultCode=='4' }">确认违法<</c:if>
			                                         </span>  
                                                </div>
											</div>
										</div>
										<!--复议情况 end-->
										</c:if>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">诉讼情况</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>
													<c:if test="${generalCaseInfo.litigationSituation=='1' }">是</c:if>
													<c:if test="${generalCaseInfo.litigationSituation=='0' }">否</c:if>
												</span>	
											</div>
										</div>
										<c:if test="${generalCaseInfo.litigationSituation=='1' }">
										<!--诉讼情况 start-->
										<div id="ssqk"  >
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">诉讼结果</label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
													<span>
			                                            <c:if test="${generalCaseInfo.litigationResult=='1' }">维持</c:if>
			                                            <c:if test="${generalCaseInfo.litigationResult=='2' }">撤销或部分撤销</c:if>
			                                            <c:if test="${generalCaseInfo.litigationResult=='3' }">变更</c:if>
                                                    </span> 
												</div>
											</div>
										</div>
										</c:if>
										<!--移送情况 start-->
										<div class="form-group">
											<label for="移送情况"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">移送情况</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<div class="input-group">
                                                   <span>${generalCaseInfo.transferName}</span>
												</div>
											</div>
										</div>
										<div class="form-group" <c:if test="${empty generalCaseInfo.specificSituation }">style="display:none;"</c:if>>											
										     <label class="col-lg-3 col-sm-3 col-xs-5 control-label">具体情形</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>
		                                            <c:if test="${generalCaseInfo.specificSituation=='1' }">行政拘留</c:if> 
		                                            <c:if test="${generalCaseInfo.specificSituation=='2' }">涉嫌犯罪,立案侦查</c:if> 
		                                        </span>
											</div>
										</div>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">是否纳入银行征信系统</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>
		                                            <c:if test="${generalCaseInfo.isBank=='1' }">是</c:if>
		                                            <c:if test="${generalCaseInfo.isBank=='0' }">否</c:if>
		                                        </span>
											</div>
										</div>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">结案日期</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span><fmt:formatDate value='${generalCaseInfo.closeCaseDate }' pattern='yyyy-MM-dd'></fmt:formatDate></span>
											</div>
										</div>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">案卷号</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>${generalCaseInfo.closeCaseNumber}</span>
											</div>
										</div>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">是否公开</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>
		                                            <c:if test="${generalCaseInfo.isOpen=='1' }">是</c:if>
		                                            <c:if test="${generalCaseInfo.isOpen=='0' }">否</c:if>
		                                        </span>
											</div>
										</div>
										<c:if test="${generalCaseInfo.isOpen=='1' }">
										<!--公开信息 start-->
										<div id="xxgk"  >
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">公开日期</label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
													<span><fmt:formatDate value='${generalCaseInfo.openTime }' pattern='yyyy-MM-dd'></fmt:formatDate></span>
												</div>
											</div>
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">公开方式</label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
                                                    <span>
                                                    	<c:if test="${generalCaseInfo.openWay=='0' }">网站公开</c:if>
			                                            <c:if test="${generalCaseInfo.openWay=='1' }">其他方式公开</c:if>
                                                    </span>
												</div>
											</div>
											<c:if test="${generalCaseInfo.openWay=='0'  }">
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">公开网址</label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
													<span>${generalCaseInfo.openWebsite}</span>
												</div>
											</div>
											</c:if>
											<c:if test="${generalCaseInfo.openWay=='1'  }">
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">其他公开方式描述</label>
												<div class="col-lg-8 col-sm-8 col-xs-12">
													<span>${generalCaseInfo.otherWayDesc}</span>
												</div>
											</div>
											</c:if>
										</div>
										<!--公开信息 end-->
										</c:if>
										<div class="form-group">
											<label class="col-lg-3 col-sm-3 col-xs-5 control-label">备注</label>
											<div class="col-lg-8 col-sm-8 col-xs-12">
												<span>${generalCaseInfo.remark}</span>
											</div>
										</div>
										<div class="form-group">
											<label for="后督察现场检查记录"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">后督察现场检查记录</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck360"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="后督察报告"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">后督察报告</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck361"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="复议申请书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">复议申请书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck368"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="复议裁决"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">复议裁决</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck369"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="法院受理通知书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">法院受理通知书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck373"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="环保局应诉答辩状"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">环保局应诉答辩状</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck374"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="法院判决（裁定）书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">法院判决（裁定）书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck375"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="同意分期（延期）缴纳罚款通知书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">同意分期（延期）缴纳罚款通知书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck356"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="同意分期（延期）缴纳罚款通知书送达回证"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">同意分期（延期）缴纳罚款通知书送达回证</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck357"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="督促履行义务催告书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">督促履行义务催告书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck38"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="督促履行义务催告书送达回证"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">督促履行义务催告书送达回证</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck39"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="行政处罚强制执行申请书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">行政处罚强制执行申请书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck40"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="行政处罚强制执行申请书送达回执"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">行政处罚强制执行申请书送达回执</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck41"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="申请强制执行授权委托书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">申请强制执行授权委托书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck70"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="申请强制执行法院裁定书"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">申请强制执行法院裁定书</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck42"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="整改材料"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">整改材料</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck362"></div>
											</div>
										</div>
										<div class="form-group">
											<label for="结案报告(含审批表）"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">结案报告(含审批表）</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck16"></div>
											</div>
										</div>
										<!-- <div class="form-group">
											<label for="其他"
												class="col-lg-3 col-sm-3 col-xs-5 control-label">其他</label>
											<div class="col-sm-5 col-xs-12">
												<div id="fjck56-1"></div>
											</div>
										</div> -->
										<div class="form-group">
											<label for="一般行政处罚案卷"
												class="col-lg-3 col-sm-3 col-xs-5 control-label" style="color:red;">一般行政处罚案卷</label>
											<div class="col-sm-5 col-xs-12" style="margin-top:7px;">
												<div id="fjckybcf2"></div>
											</div>
										</div>
										</form>
									</div>
									<!--end-->
								</div>
							</div>

						</div>
					</div>
					<!--时间轴-->
					<jsp:include page="./timeAxis.jsp"></jsp:include>
				</div>
			</div>
		</div>

	</div>
</div>
		<!-- 附件预览  -->
	<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">

			</div>
		</div>
	</div>
	
	<!--  附件查看 -->
	<div class="modal fade" id="view" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>

<script>

$(function(){
	/* hisShowFiles("atv_sanction_case","${generalCaseInfo.id}","一般行政处罚-结案审批表","16");	 
	 hisShowFiles("atv_sanction_case","${generalCaseInfo.id}","一般行政处罚-其他1","56","1");*/
	 
	 hisShowFiles("${caseStateInfo.caseId }",null,360);
	 hisShowFiles("${caseStateInfo.caseId }",null,361);	
	 hisShowFiles("${caseStateInfo.caseId }",null,368);	
	 hisShowFiles("${caseStateInfo.caseId }",null,369);	
	 hisShowFiles("${caseStateInfo.caseId }",null,373);	
	 hisShowFiles("${caseStateInfo.caseId }",null,374);	
	 hisShowFiles("${caseStateInfo.caseId }",null,375);	
	 hisShowFiles("${caseStateInfo.caseId }",null,356);	
	 hisShowFiles("${caseStateInfo.caseId }",null,357);	
	 hisShowFiles("${caseStateInfo.caseId }",null,38);	
	 hisShowFiles("${caseStateInfo.caseId }",null,39);	
	 hisShowFiles("${caseStateInfo.caseId }",null,40);	
	 hisShowFiles("${caseStateInfo.caseId }",null,41);	
	 hisShowFiles("${caseStateInfo.caseId }",null,70);	
	 hisShowFiles("${caseStateInfo.caseId }",null,42);	
	 hisShowFiles("${caseStateInfo.caseId }",null,362);	
	 hisShowFiles("${caseStateInfo.caseId }",null,16);	
	 
	 showGeneFile("atv_sanction_case","${generalCaseInfo.id}","fjckybcf2","一般行政处罚案卷");	 
})
function editCaseInfo(){
  	 swal({
 	        title: "请确认环保部已同意该案件信息进行修改，否则会造成本地数据污染无法更新环保部情况。",
 	        type: "warning",
 	        showCancelButton: true,
 	        closeOnConfirm: true,
 	        confirmButtonText: "确认，我要修改",
 	        confirmButtonColor: "#ec6c62"
 	    	}, function() {
 	    		//跳转到编辑页面
 	    		console.info("data is :"+$("#caseStateObjectForm").serialize());
 	    		business.addMainContentParserHtml(WEBPATH + '/generalCase/toClosePage?editType=1', $("#caseStateObjectForm").serialize()+"&selectType=3");
 	  });
   }
</script>


