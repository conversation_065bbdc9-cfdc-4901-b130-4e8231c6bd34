<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>

<sec:authentication property="principal" var="authentication" />
<script
	src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"
	type="text/javascript"></script>
<div class="main-container">
	<div class="padding-md">
		<div class="smart-widget">
			<div class="smart-widget-inner">
				<jsp:include page="../caseBusiness.jsp"></jsp:include>
				<div class="row">
					<div class="col-lg-9">
						<div class="tab-content" style="margin-top: -20px;">
							<div class="tab-pane fade in active" id="style1Tab1">
								<div class="smart-widget-inner table-responsive">
									<div class="smart-widget-body form-horizontal">
										<div style="padding: 0px 0px 10px 0px;">
											<span
												style="font-size: 16px; font-weight: bold; color: #666;">一般行政处罚案件编号：</span><span
												style="font-size: 16px; font-weight: bold; color: #666;">${generalCaseInfo.easySanctionNumber }</span>
										</div>
										<legend class="font-16"
											style="font-weight: bold; color: #23b7e5;">处罚决定</legend>
										<div style="float: right; margin: -55px 0 0 100px;">
											<c:if test="${caseStateInfo.parentUrl=='2' && not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
												<button class="btn btn-danger btn-sm" onclick="deleteById()">删除</button>
											</c:if>
											<!-- 历史页面跳入，已办结案件，或正常的，已被下放编辑权限 -->
											<c:if test="${caseStateInfo.parentUrl eq 0 and caseStateInfo.isPowerEdit==1 and not empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">编辑</button>
												<c:if test="${generalCaseInfo.upState < 3 and empty generalCaseInfo.punishStartDate}">
													<!-- 如果没有提交，就可以删除 -->
													<button class="btn btn-danger btn-sm" onclick="deleteById()">删除</button>
												</c:if>
											</c:if>
											<c:if test="${caseStateInfo.parentUrl=='2' && empty generalCaseInfo.id }">
												<button class="btn btn-info btn-sm" onclick="editCaseInfo()">新增</button>
											</c:if>
										</div>
										<input type="hidden" id="id" name="id"
											value="${generalCaseInfo.id }" />
											 <div class="col-lg-10 col-lg-offset-1">
											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">
													年度</label>
												<div class="col-lg-8 col-sm-8 col-xs-12"
													style="margin-top: 7px;">
													<span>${generalCaseInfo.year }年度</span>
												</div>
											</div>
                                          </div>
                                           <div class="col-lg-10 col-lg-offset-1">
											<div class="form-group">

												<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
													style="color: red;"></span> 本环节开始办理时间</label>
												<div class="col-lg-8 col-sm-8 col-xs-12"
													style="margin-top: 7px;">
													<%-- <input type="hidden" id="id" name="id" value="${generalCaseInfo.id }">
											    <input type="hidden" id="pageType" name="pageType" value="3"/> --%>
													<span><fmt:formatDate
															value='${generalCaseInfo.punishStartDate }' type='date'
															pattern="yyyy-MM-dd" /></span>
												</div>
											</div>
                                          </div>

											<div class="cfjd_panel col-lg-10 col-lg-offset-1">
												<div class="cfjd_titlt">行政命令</div>
												<div class="form-group">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">行政命令种类</label>
													<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top: 7px;">
														<c:forEach items="${atvCommandCodeList}" var="item"
															varStatus="status">
															<c:choose>
																<c:when
																	test="${item.code == generalCaseInfo.atvCommandCode}">
																	<span>${item.name}</span>
																</c:when>

															</c:choose>
														</c:forEach>
													</div>
												</div>

												<c:if test="${generalCaseInfo.atvCommandCode==9}">
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label">其他种类</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<span>${generalCaseInfo.orderOtherType}</span>
														</div>
													</div>
												</c:if>
												<div class="form-group">
													<label for="责令改正违法行为决定书"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>责令改正违法行为决定书</label>
													<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
														<div id="fjck17"></div>
													</div>
												</div>
											 	<div class="form-group">
													<label for="责令改正违法行为决定书审批表"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"></span>责令改正违法行为决定书审批表</label>
													<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
														<div id="fjck74"></div>
													</div>
												</div>
												<div class="form-group">
													<label for="行政命令送达回证"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>行政命令送达回证</label>
													<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
														<div id="fjck18"></div>
													</div>
												</div>
											</div>

											<div class="form-group">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label">是否处罚</label>
												<div class="col-lg-8 col-sm-8 col-xs-12"
													style="margin-top: 7px;">
													<select class="form-control" id="isPunish" name="isPunish"
														style="display: none">
														<option value="">——请选择——</option>
														<option value="1"
															<c:if test="${'1' eq generalCaseInfo.isPunish}">selected</c:if>>是</option>
														<option value="0"
															<c:if test="${'0' eq generalCaseInfo.isPunish}">selected</c:if>>否</option>
													</select>
													<c:if test="${empty generalCaseInfo.isPunish }">
														<span></span>
													</c:if>
													<c:if test="${not empty generalCaseInfo.isPunish }">
														<span> ${generalCaseInfo.isPunish==0?"否":"是" } </span>
													</c:if>
												</div>
											</div>
										<div class="cfjd_panel col-lg-10 col-lg-offset-1">
											<div class="cfjd_titlt">事先告知书</div>
											<div class="form-group">
												<label for="行政处罚事先告知书"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>行政处罚事先告知书</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck64"></div>
												</div>
											</div>
											<!-- <div class="form-group">
                                                <label for="行政处罚事先告知书审批表"
                                                class="col-lg-3 col-sm-3 col-xs-5 control-label">行政处罚事先告知书审批表</label>
                                                <div class="col-sm-5 col-xs-12">
                                                    <div id="fjck75"></div>
                                                </div>
                                            </div> -->
											<div class="form-group">
												<label for="告知书送达回证"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>告知书送达回证</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck11"></div>
												</div>
											</div>
											<div class="form-group">
												<label for="案件集体审议记录（告知前）"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>案件集体审议记录（告知前）</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck384"></div>
												</div>
											</div>
											<div class="form-group">
												<label for="案件处理内部审批表（告知前）"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>案件处理内部审批表（告知前）</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck385"></div>
												</div>
											</div>

										</div>

										<div class="cfjd_panel col-lg-10 col-lg-offset-1">
											<div class="cfjd_titlt">听证告知书</div>
											<div class="form-group">
												<label for="行政处罚听证告知书"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>行政处罚听证告知书</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck10"></div>
												</div>
											</div>

											<!-- <div class="form-group">
                                                <label for="行政处罚听证告知书审批表"
                                                    class="col-lg-3 col-sm-3 col-xs-5 control-label"> 行政处罚听证告知书审批表</label>
                                                <div class="col-sm-5 col-xs-12">
                                                    <div id="fjck76"></div>
                                                </div>
                                            </div> -->

											<div class="form-group">
												<label for="听证告知书送达回证"
													   class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>听证告知书送达回证</label>
												<div class="col-sm-5 col-xs-12">
													<div id="fjck63"></div>
												</div>
											</div>
										</div>
										<!--是否处罚-是 start-->
										 
											<div style="display: none;" id="environmentElementCss">
																							<div class="cfjd_panel col-lg-10 col-lg-offset-1">
												<div class="cfjd_titlt">违法行为</div>
												<div class="form-group">
													<label for="环保部对应违法行为"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span> 环保部对应违法行为</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<div class="input-group">
															<input type="hidden" id="entProtectionCode"
																name="entProtectionCode"
																value="${generalCaseInfo.entProtectionCode }"> <span>${generalCaseInfo.entProtectionName }</span>
															<div class="input-group-btn">
																<%-- <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#hbbwfxw"
                                                        		 data-remote="${webpath}/atvEasy/Illegal-behavior?code=entProtectionCode&name=entProtectionName&checkFormName=easyAtvUpForm";
                                                        >选择</button> --%>
															</div>
														</div>
													</div>
												</div>
												<div class="form-group">
													<label for="本系统对应违法行为"
														class="col-lg-3 col-sm-3 col-xs-5 control-label">
														本系统对应违法行为</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<div class="input-group">
															<input type="hidden" id="sys_beh_ids"
																name="majorViolationsId"
																value="${generalCaseInfo.majorViolationsId }"> <span>${generalCaseInfo.majorViolationsName }</span>

														</div>
													</div>
												</div>
												<div class="form-group">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span>处罚依据</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<%-- <button style="float:right; margin-bottom:2px;" type="button" class="btn btn-info no-shadow" 
                                            	  data-remote="${webpath}/caseInfo/punish-proof-page"
                                            	tabindex="-1" data-toggle="modal" data-target="#cfyj">选择处罚依据</button> --%>
														<input type="hidden" id="punishBasisCode"
															name="punishBasisCode"
															value="${generalCaseInfo.punishBasisCode }" /> <input
															type="hidden" id="punishBasisName" name="punishBasisName"
															value="${generalCaseInfo.punishBasisName }" />
														<table class="table table-striped table-hover no-margin">
															<tbody id="punishReason">
																<c:forEach items="${basisArr }" var="obj"
																	varStatus="status">
																	<tr id="${obj.id }">
																		<td>${obj.content }</td>
																		<td class="text-center"
																			style="width: 40px; background-color: #ccc; padding: 3% 0;"
																			onclick="removeExistId('${obj.id}','${obj.factor }','${generalCaseInfo.id }')"></td>
																	</tr>
																</c:forEach>
															</tbody>
														</table>

														<span>${generalCaseInfo.punishBasisText }</span>
													</div>
												</div>
													<div class="form-group">
													<label for="违法案件类型"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span> 违法案件类型</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<div class="input-group">
															<input type="hidden" id="littleCaseTypeCode"
																name="littleCaseTypeCode"
																value="${generalCaseInfo.littleCaseTypeCode }">
															<span>${generalCaseInfo.littleCaseTypeName }</span>
															<div class="input-group-btn">
																<%-- <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#wfajlx"
                                                        		data-remote="${webpath}/atvEasy/illegal-case-type?code=littleCaseTypeCode&name=littleCaseTypeName&checkFormName=easyAtvUpForm";
                                                        >选择</button> --%>
															</div>
														</div>
													</div>
												</div>
												
												<div class="form-group" >
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">违法行为严重程度</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<span>
														<c:if test="${generalCaseInfo.seriousViolations == 1 }">一般</c:if>
														<c:if test="${generalCaseInfo.seriousViolations == 2 }">严重</c:if>
														</span>
													</div>
												</div>
												
												
												<div class="form-group">
													<label for="处罚种类"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span> 处罚种类</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<div class="input-group">
															<input type="hidden" id="punishTypeCode"
																name="punishTypeCode"
																value="${generalCaseInfo.punishTypeCode }"> <span>${generalCaseInfo.punishTypeName }</span>
															<div class="input-group-btn">
															</div>
														</div>
													</div>
												</div>
												<div class="form-group" style="display: none;" id="qtcfzl">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">其他处罚种类名称</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<span>${generalCaseInfo.otherPunishName }</span>
													</div>
												</div>
												
												<div class="form-group" style="display: none;" id="mswfsd">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span>没收违法所得的内容</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<span>${generalCaseInfo.confIllegalText }</span>
													</div>
												</div>
												
												<div class="form-group" style="display: none;" id="zkdxzz">
												<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span>暂扣或吊销证照名称及编号</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<span>${generalCaseInfo.withholdRevocationLicence }</span>
													</div>
												</div>
												
												
												<div class="form-group" style="display: none;" id="fkse">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">罚款数额<span style="color:red;">（单位：万元）</span></label>
													<div class="col-lg-7 col-sm-7 col-xs-10"
														style="margin-top: 7px;">
														<span>${generalCaseInfo.penaltyAmount }</span>
													</div>
													<div class="col-lg-2 col-sm-2 col-xs-2"
														style="margin-top: 7px; color: red;">万元</div>
												</div>
												
												<div class="form-group" >
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">违法事实</label>
													<div class="col-lg-7 col-sm-7 col-xs-10"
														style="margin-top: 7px;">
														 <textarea readonly="readonly" style="margin-top:2px;" name="penaltyContent" rows="8" class="form-control">${generalCaseInfo.illegalFacts }</textarea>
													</div>
													
												</div>
												
												<div class="form-group" >
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">处罚内容</label>
													<div class="col-lg-7 col-sm-7 col-xs-10"
														style="margin-top: 7px;">
														<textarea readonly="readonly" style="margin-top:2px;" name="penaltyContent" rows="8" class="form-control">${generalCaseInfo.penaltyContent }</textarea>
													</div>
													
												</div>
												
												
												
												</div>
												<div class="cfjd_panel col-lg-10 col-lg-offset-1" >
													<div class="cfjd_titlt">处罚决定书</div>
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: red;"></span>决定书发文日期</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<span><fmt:formatDate
																	value='${generalCaseInfo.decisionDate }'
																	pattern='yyyy-MM-dd'></fmt:formatDate></span>
														</div>
													</div>
													
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: red;"></span>处罚有效期</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<span><fmt:formatDate
																	value='${generalCaseInfo.punishValidityData }'
																	pattern='yyyy-MM-dd'></fmt:formatDate></span>
														</div>
													</div>
													
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: red;"></span>公示截止期</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<span><fmt:formatDate
																	value='${generalCaseInfo.publicityClosingData }'
																	pattern='yyyy-MM-dd'></fmt:formatDate></span>
														</div>
													</div>
													
													
													
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label">
															<span style="color: red;"></span> 实际下达的决定书文号
														</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<input type="hidden" id="pageType" name="pageType"
																value="3" /> <span>${generalCaseInfo.decisionNumber }</span>
														</div>
													</div>
													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label">
															<span style="color: red;"></span>在线申请的决定书文号 
														</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															style="margin-top: 7px;">
															<input type="hidden" id="pageType" name="pageType"
																value="3" /> <span>${generalCaseInfo.onlineDecisionNumber }</span>
														</div>
													</div>
													
													<div class="form-group">
														<label for="处罚决定书"
															class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: #FC0;"></span>处罚决定书</label>
														<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
															<div id="fjck7"></div>
														</div>
													</div>
													<!-- <div class="form-group">
														<label for="处罚决定书审批表"
															class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: #FC0;"></span>处罚决定书审批表</label>
														<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
															<div id="fjck77"></div>
														</div>
													</div> -->
													<div class="form-group">
														<label for="送达回证"
															class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
															style="color: #FC0;"></span>送达回证</label>
														<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
															<div id="fjck8"></div>
														</div>
													</div>
													<div class="form-group">
													<label for="调查报告"
														class="col-lg-3 col-sm-3 col-xs-5 control-label">调查报告</label>
													<div class="col-sm-5 col-xs-12">
														<div id="fjck60"></div>
													</div>
												</div>
												<div class="form-group">
													<label for="案件集体审议记录（处罚前）"
														class="col-lg-3 col-sm-3 col-xs-5 control-label">案件集体审议记录（处罚前）</label>
													<div class="col-sm-5 col-xs-12">
														<div id="fjck386"></div>
													</div>
												</div>
												<div class="form-group">
													<label for="案件处理内部审批表（处罚前）"
														class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: #FC0;"></span>案件处理内部审批表（处罚前）</label>
													<div class="col-sm-5 col-xs-12">
														<div id="fjck387"></div>
													</div>
												</div>
												</div>
											

												<div class="form-group">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span>是否举行听证</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<span>${generalCaseInfo.atvIsHearing==1?"是":"否" }</span> <select
															class="form-control" name="atvIsHearing"
															id="atvIsHearing" style="display: none;">
															<option value=""
																<c:if test="${generalCaseInfo.atvIsHearing=='' }">selected</c:if>>——请选择——</option>
															<option value="1"
																<c:if test="${generalCaseInfo.atvIsHearing=='1' }">selected</c:if>>是</option>
															<option value="0"
																<c:if test="${generalCaseInfo.atvIsHearing=='0' }">selected</c:if>>否</option>
														</select>
													</div>
												</div>
												<div class="cfjd_panel col-lg-10 col-lg-offset-1" 
																<c:choose>
															       <c:when test="${generalCaseInfo.atvIsHearing=='1'}">
															       </c:when>
															       <c:otherwise>
															    	      style="display:none;"
															       </c:otherwise>
															</c:choose> >
													<div class="cfjd_titlt">听证告知书</div>
															<div class="form-group">
																<label for="行政处罚听证通知书"
																	class="col-lg-3 col-sm-3 col-xs-5 control-label">行政处罚听证通知书</label>
																<div class="col-sm-5 col-xs-12">
																	<div id="fjck13"></div>
																</div>
															</div>
															<div class="form-group">
																<label for="行政处罚听证通知书送达回证"
																	class="col-lg-3 col-sm-3 col-xs-5 control-label">行政处罚听证通知书送达回证</label>
																<div class="col-sm-5 col-xs-12">
																	<div id="fjck334"></div>
																</div>
															</div>
															<div class="form-group">
																<label for="听证笔录"
																	class="col-lg-3 col-sm-3 col-xs-5 control-label">听证笔录</label>
																<div class="col-sm-5 col-xs-12">
																	<div id="fjck14"></div>
																</div>
															</div>
															<div class="form-group">
																<label for="听证报告"
																	class="col-lg-3 col-sm-3 col-xs-5 control-label">听证报告</label>
																<div class="col-sm-5 col-xs-12">
																	<div id="fjck335"></div>
																</div>
															</div>
															<div class="form-group">
																<label for="陈述申辩书"
																	class="col-lg-3 col-sm-3 col-xs-5 control-label">陈述申辩书</label>
																<div class="col-sm-5 col-xs-12">
																	<div id="fjck15"></div>
																</div>
															</div>
													
												</div>

												
												 <div class="form-group">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
														style="color: red;"></span>是否是建设项目相关案件</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<select class="form-control" name="isProject"
															id="isProject" onchange="jsxmchange();return false;"
															style="display: none;">
															<option value=""
																<c:if test="${empty generalCaseInfo.isProject }">selected</c:if>>——请选择——</option>
															<option value="1"
																<c:if test="${generalCaseInfo.isProject=='1' }">selected</c:if>>是</option>
															<option value="0"
																<c:if test="${generalCaseInfo.isProject=='0' }">selected</c:if>>否</option>
														</select> <span>${generalCaseInfo.isProject==1?"是":"否" }</span>
													</div>
												</div>


													<div class="form-group">
														<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
																style="color: red;"></span>是否撤案</label>
														<div class="col-lg-8 col-sm-8 col-xs-12"
															 style="margin-top: 7px;">
															<span>${generalCaseInfo.isBackout==1?"是":"否" }</span>
														</div>
													</div>

												<div class="cfjd_panel col-lg-10 col-lg-offset-1" 
														<c:choose>
															       <c:when test="${generalCaseInfo.isProject=='1'}">
															       </c:when>
															       <c:otherwise>
															    	      style="display:none;"
															       </c:otherwise>
															</c:choose> 
												>
													<div class="cfjd_titlt">建设项目相关案件</div>
														<div class="form-group">
															<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
																style="color: red;"></span>建设单位</label>
															<div class="col-lg-8 col-sm-8 col-xs-12"
																style="margin-top: 7px;">
																<span>${generalCaseInfo.createDept }</span>
															</div>
														</div>
														<div class="form-group">
															<label class="col-lg-3 col-sm-3 col-xs-5 control-label">移送时间</label>
															<div class="col-lg-8 col-sm-8 col-xs-12"
																style="margin-top: 7px;">
																<span><fmt:formatDate
																		value='${generalCaseInfo.transferDate }'
																		type='date' pattern='yyyy-MM-dd' /></span>
															</div>
														</div>
														<div class="form-group">
															<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
																style="color: red;"></span>审批单位</label>
															<div class="col-lg-8 col-sm-8 col-xs-12"
																style="margin-top: 7px;">
																<span>${generalCaseInfo.approvalCompany }</span>
															</div>
														</div>
														<div class="form-group">
															<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
																style="color: red;"></span>项目名称</label>
															<div class="col-lg-8 col-sm-8 col-xs-12"
																style="margin-top: 7px;">
																<span>${generalCaseInfo.projectName }</span>
															</div>
														</div>
														<div class="form-group">
															<label class="col-lg-3 col-sm-3 col-xs-5 control-label"><span
																style="color: red;"></span>项目建设时间</label>
															<div class="col-lg-8 col-sm-8 col-xs-12"
																style="margin-top: 7px;">
																<span><fmt:formatDate
																		value='${generalCaseInfo.projectCreatorDate }'
																		type='date' pattern='yyyy-MM-dd' /></span>
															</div>
														</div>
										
												</div>
												
											</div>
									 
										<!--是否处罚-是 end-->
										<!--是否处罚-否 start-->

										<form id="unpunishForm" name="unpunishForm">
											<div style="display: none;" id="behaviourNameCss">
												<div class="form-group">
													<label class="col-lg-3 col-sm-3 col-xs-5 control-label">不处罚原因</label>
													<div class="col-lg-8 col-sm-8 col-xs-12"
														style="margin-top: 7px;">
														<input type="hidden" id="pageType" name="pageType"
															value="3" /> <span>${generalCaseInfo.noPunishReasons }</span>
													</div>
												</div>
												<div class="form-group">
													<label for="案件材料"
														class="col-lg-3 col-sm-3 col-xs-5 control-label">不处罚审批材料</label>
													<div class="col-sm-5 col-xs-12">
														<div id="fjck57"></div>
													</div>
												</div>
											</div>
											<!--是否处罚-否 end-->

										</form>



										<div class="form-group">
											<label for="一般处罚部分案卷"
												class="col-lg-3 col-sm-3 col-xs-5 control-label" style="color:red;">一般处罚部分案卷</label>
											<div class="col-sm-5 col-xs-12" style="margin-top: 7px;">
												<div id="fjckybcf1"></div>
											</div>
										</div>
										
										<div class="col-lg-12  padding-xs">
												<legend class="font-16" style="font-weight:bold;color:#23b7e5; margin-top:10px;">主要执法人员信息</legend>
					                        	<div class="form-group">
					                              <label class="col-lg-3 col-sm-3 col-xs-5 control-label"></label>
					                              <div class="col-lg-8 col-sm-8 col-xs-12">
					                                     <div class="padding-xs" style="float:right; margin-top:-20px;">
					                             		 </div>
						                             <table class="table table-striped table-bordered" id="lawEnforcementTable" >
							                              <thead>
							                                <tr>
							                                  <th class="text-center">序号</th>
							                                  <th class="text-center">执法人员</th>
							                                  <th class="text-center">执法证号</th>
							                                  <th class="text-center">身份证号</th>
							                                  <th class="text-center" style="width: 200px;">执法日期</th>
							                                </tr>
							                              </thead>
							                              <tbody>
																<tr v-for="(item, index) in items">
																	<td class="text-center">{{ index+1 }}</td>
																	<td class="text-center">{{item.lawUserName }}</td>
																	<td class="text-center">{{item.lawEnforcId }}</td>
																	<td class="text-center">{{item.userCardId.substring(0,3)+'******'+ item.userCardId.substring((item.userCardId.length-3))}}</td>
																	<td class="text-center">{{item.lawDate }}</td>
																</tr>
															</tbody>
						                             </table>
		                                          </div>
		                                       </div> 
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!--时间轴-->
					<jsp:include page="./timeAxis.jsp"></jsp:include>
				</div>
				<div class="modal-footer" style="margin-top: 20px;">
				</div>
			</div>
		</div>

	</div>
	<!-- 关联违法行为（Modal） -->

	<!-- 关联违法行为（Modal） -->


	<!-- 处罚行为 -->
</div>
<!-- 附件预览  -->
<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content"></div>
	</div>
</div>

<!--  附件查看 -->
<div class="modal fade" id="view" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content"></div>
	</div>
</div>

<script type="text/javascript">
	$(function() {
		hisShowFiles("${caseStateInfo.caseId }",null,60);	 
		hisShowFiles("${caseStateInfo.caseId }",null,17);	 
		hisShowFiles("${caseStateInfo.caseId }",null,74);	 
		hisShowFiles("${caseStateInfo.caseId }",null,18);	 
		hisShowFiles("${caseStateInfo.caseId }",null,7);	 
		hisShowFiles("${caseStateInfo.caseId }",null,8);	 
		hisShowFiles("${caseStateInfo.caseId }",null,386);	 
		hisShowFiles("${caseStateInfo.caseId }",null,387);	 
		hisShowFiles("${caseStateInfo.caseId }",null,64);	 
		hisShowFiles("${caseStateInfo.caseId }",null,11);	 
		hisShowFiles("${caseStateInfo.caseId }",null,60);	 
		hisShowFiles("${caseStateInfo.caseId }",null,10);	 
		hisShowFiles("${caseStateInfo.caseId }",null,63);
		
		hisShowFiles("${caseStateInfo.caseId }",null,13);	 
		hisShowFiles("${caseStateInfo.caseId }",null,334);	 
		hisShowFiles("${caseStateInfo.caseId }",null,14);	 
		hisShowFiles("${caseStateInfo.caseId }",null,335);	 
		hisShowFiles("${caseStateInfo.caseId }",null,15);	 
		/* hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"调查报告", "60");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"行政命令-责令改正违法行为决定书", "17");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-责令改正违法行为决定书审批表", "74");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}", "行政命令-送达回证",
				"18");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-处罚决定书", "7");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-送达回证", "8");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-案件处理案件处理内部审批表（处罚前）（处罚前）", "9");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-行政处罚听证告知书", "10");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-听证告知书送达回证", "63");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-行政处罚事先告知书", "64");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-告知书送达回证", "11");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-集体讨论记录", "12");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-行政处罚听证通知书", "13");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-听证笔录", "14");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-陈述申辩书", "15");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}",
				"一般行政处罚-不处罚审批材料", "57"); */
		showGeneFile("atv_sanction_case", "${generalCaseInfo.id}", "fjckybcf1","一般处罚部分案卷");
		
		/* hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}", "一般行政处罚-行政处罚事先告知书审批表", "75");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}", "一般行政处罚-行政处罚听证告知书审批表", "76");
		hisShowFiles("atv_sanction_case", "${generalCaseInfo.id}", "一般行政处罚-处罚决定书审批表", "77"); */
		
		
	})

	//一键生成
	function selectGenerate() {
		var isPunish = $("#isPunish").val();
		if (isPunish == 1) {
			generatePdf('${generalCaseInfo.generateTimeUp }',
					'atv_sanction_case', 1, '${generalCaseInfo.id }',
					'${caseStateInfo.caseId }', '4,5,7,8,9,10,11,12,13,14,15');
		} else {
			generatePdf('${generalCaseInfo.generateTimeUp }',
					'atv_sanction_case', 1, '${generalCaseInfo.id }',
					'${caseStateInfo.caseId }', '4,5,57');
		}
	}

	function saveInfo() {
		var isPunish = $("#isPunish").val();
		var punishStartDate = $("#punishStartDate").val();
		//console.info($('#punishForm').serialize())
		$("#baseForm").data('formValidation').validate();
		if (!$('#baseForm').data('formValidation').isValid()) {
			return;
		}
		//var isPunish = $("#isPunish").val();
		//var punishStartDate = $("#punishStartDate").val();
		var data;
		if (isPunish == 1) {
			//处罚
			$("#punishForm").data('formValidation').validate();
			if (!$('#punishForm').data('formValidation').isValid()) {
				return;
			}
			data = $('#punishForm').serialize();
		} else if (isPunish == 0) {
			//不处罚
			$("#unpunishForm").data('formValidation').validate();
			if (!$('#unpunishForm').data('formValidation').isValid()) {
				return;
			}
			data = $('#unpunishForm').serialize();
		} else {
			return;
		}
		business.openwait();//开启遮罩
		$.ajax({
			method : 'POST',
			url : WEBPATH + '/generalCase/saveOrUpdate?punishStartDate='
					+ $("#punishStartDate").val() + '&isPunish='
					+ $("#isPunish").val(),
			data : data,
			success : function(data) {
				business.closewait();//关闭遮罩层
				if (data.meta.code == 200) {
					swal({
						title : "保存成功",
						text : data.meta.message,
						type : "success"
					});
					//刷新当前页面
					punishCase();
				} else {
					swal({
						title : "保存失败",
						text : data.meta.message,
						type : "info"
					});
				}
			}
		});
	}

	function jsxmchange() {

		var sel = document.getElementsByName('isProject');
		for (var i = 0; i < isProject.options.length; i++) {
			if (isProject.options[i].selected) {
				if (isProject.options[i].value != 1) {
					document.getElementById("jsxm").style.display = "none";
					document.getElementById("jsxm_originalno").value = "";

				}
				if (isProject.options[i].value == 1) {
					document.getElementById("jsxm").style.display = "";
				}
			}
		}

	}
	//本环节开始时间
	$("#punishStartDate").datetimepicker({
		language : 'cn',
		format : 'yyyy-mm-dd',
		todayBtn : true,
		autoclose : true,
		endDate : new Date(),
		minView : 'month',
		maxView : 'decade'
	}).on(
			'hide',
			function(e) {
				$('#punishForm').data('formValidation').updateStatus(
						'punishStartDate', 'NOT_VALIDATED', null)
						.validateField('punishStartDate');
			});
	//决定下达时间
	$("#decisionDate").datetimepicker({
		language : 'cn',
		format : 'yyyy-mm-dd',
		todayBtn : true,
		autoclose : true,
		endDate : new Date(),
		minView : 'month',
		maxView : 'decade'
	}).on(
			'hide',
			function(e) {
				$('#punishForm').data('formValidation').updateStatus(
						'decisionDate', 'NOT_VALIDATED', null).validateField(
						'decisionDate');
			});
	//项目建设时间
	$("#decisionDate").datetimepicker({
		language : 'cn',
		format : 'yyyy-mm-dd',
		todayBtn : true,
		autoclose : true,
		endDate : new Date(),
		minView : 'month',
		maxView : 'decade'
	}).on(
			'hide',
			function(e) {
				$('#punishForm').data('formValidation').updateStatus(
						'decisionDate', 'NOT_VALIDATED', null).validateField(
						'decisionDate');
			});
	//projectCreatorDate
	$("#projectCreatorDate").datetimepicker({
		language : 'cn',
		format : 'yyyy-mm-dd',
		todayBtn : true,
		autoclose : true,
		endDate : new Date(),
		minView : 'month',
		maxView : 'decade'
	}).on(
			'hide',
			function(e) {
				$('#punishForm').data('formValidation').updateStatus(
						'projectCreatorDate', 'NOT_VALIDATED', null)
						.validateField('projectCreatorDate');
			});
</script>
<script type="text/javascript">
	//基础信息校验
	$(document).ready(function() {
		$("#baseForm").formValidation({
			//excluded:[":hidden",":disabled",":not(visible)"] ,//不可见的，不可填的，隐藏域不做校验
			framework : 'bootstrap',
			message : 'This value is not valid',
			icon : {
				valid : 'glyphicon glyphicon-ok',
				invalid : 'glyphicon glyphicon-remove',
				validating : 'glyphicon glyphicon-refresh'
			},
			fields : {

				punishStartDate : {
					message : '本环节开始办理时间不能为空',
					validators : {
						notEmpty : {
							message : '本环节开始办理时间不能为空'
						}
					}

				},
				isPunish : {
					message : '销案时间不能为空',
					validators : {
						notEmpty : {
							message : '销案时间不能为空'
						}
					}

				}
			}
		});

	});

	var isPunish = $("#isPunish").val();
	var isProject = '${generalCaseInfo.isProject}';
	if (isPunish == null || isPunish == '' || isPunish == 'undefined') {
		$("#behaviourNameCss").hide();
		$("#environmentElementCss").hide();
	} else if (isPunish == 1) {
		$("#behaviourNameCss").hide();
		$("#environmentElementCss").show();
		console.info("233333" + isProject)
		if (isProject == null || isProject == '' || isProject == 'undefined') {
			$("#jsxm").hide();
		} else if (isPunish == 1) {

			$("#jsxm").show();
		} else if (isPunish == 0) {
			$("#jsxm").hide();
		}
	} else if (isPunish == 0) {
		$("#behaviourNameCss").show();
		$("#environmentElementCss").hide();
	}
</script>
<script type="text/javascript">
	$(document).ready(function() {
		$("#unpunishForm").formValidation({
			//excluded:[":hidden",":disabled",":not(visible)"] ,//不可见的，不可填的，隐藏域不做校验
			framework : 'bootstrap',
			message : 'This value is not valid',
			icon : {
				valid : 'glyphicon glyphicon-ok',
				invalid : 'glyphicon glyphicon-remove',
				validating : 'glyphicon glyphicon-refresh'
			},
			fields : {

				noPunishReasons : {
					message : '不处罚原因最大不能超过5000个字符',
					validators : {
						stringLength : {
							max : 5000,
							message : '不处罚原因最大不能超过5000个字符'
						}
					}

				}
			}
		});
	});
</script>

<script type="text/javascript">
	//

	var pageStatus = '${generalCaseInfo.upState}';
	if (pageStatus == 3) {
		//本页面不可编辑
		$('input,select,textarea', $('form[name="baseForm"]')).attr('disabled',
				true);
		$('input,select,textarea', $('form[name="punishForm"]')).attr(
				'disabled', true);
		$('input,select,textarea', $('form[name="unpunishForm"]')).attr(
				'disabled', true);
	}

	function editCaseInfo() {
		swal({
			title : "请确认环保部已同意该案件信息进行修改，否则会造成本地数据污染无法更新环保部情况。",
			type : "warning",
			showCancelButton : true,
			closeOnConfirm : true,
			confirmButtonText : "确认，我要修改",
			confirmButtonColor : "#ec6c62"
		}, function() {
			//跳转到编辑页面
			business.addMainContentParserHtml(WEBPATH
					+ '/generalCase/toPunishPage?editType=1', $(
					"#caseStateObjectForm").serialize()
					+ "&selectType=3");
		});
	}
	
	var punishCode = '${generalCaseInfo.punishTypeCode }';
	if(punishCode.indexOf("1")>-1){
		$("#mswfsd").css("display","");
	}
	if(punishCode.indexOf("5")>-1){
		$("#zkdxzz").css("display","");
	}
    if(punishCode.indexOf("2")>-1){
    	$("#fkse").show();
	}
    if(punishCode.indexOf("7")>-1){
    	$("#qtcfzl").show();
	}
    /**
	* 动态添加  主要执法人员信息 vue
	*/
	$(function(){
		var lawEData = null;
		var modelerType = '${caseStateInfo.selectType}';
		var infoId = '${generalCaseInfo.id}';
		var obj={infoId:infoId,modelerType:modelerType};
		// 请求数据 
		$.ajax({
            cache: true,
            type: "POST",
            url: WEBPATH+'/caseLawEnf/law-user-list',
            data:obj, 
            async: false,
            error: function(request) {
            	swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
				business.closewait();
            },
            success: function(data) {
            	lawEData = data; // 绑定 VUE （json）
            	for (var obj in lawEData) {
            		// 格式化日期
            		if(lawEData[obj].lawDate!=null && lawEData[obj].lawDate!=''){
            			var lawDate  =  new Date(lawEData[obj].lawDate)
            			lawEData[obj].lawDate = lawDate.getFullYear()+'-'+((lawDate.getMonth()+1)< 10 ?'0'+(lawDate.getMonth()+1):(lawDate.getMonth()+1))+'-'+(lawDate.getDate()<10?'0'+lawDate.getDate():lawDate.getDate());
            		}
            		
				}  
            }
        });
		lawEnforcementTableData = new Vue({ // 不要用var 声明，定义为全局 
			  el: '#lawEnforcementTable',
			  data: {
			    items:lawEData,
			  },
			  methods: {
			  }
		});

	});
</script>
<!-- 模态框开始  -->
<!-- 环保部对应违法行为（Modal） -->
<div class="modal fade" id="hbbwfxw" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content"></div>
	</div>
</div>


<!-- 处罚种类（Modal） -->
<div class="modal fade" id="cfzl" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content"></div>
	</div>
</div>
<!-- 移送情况（Modal） -->
<div class="modal fade" id="ysqk" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog">
		<div class="modal-content"></div>
	</div>
</div>
<!-- 附件管理（Modal） -->
<div class="modal fade" id="fjgl" tabindex="-1" role="dialog"
	aria-labelledby="myModalLabel" aria-hidden="true">
	<div class="modal-dialog modal-lg">
		<div class="modal-content"></div>
	</div>
</div>