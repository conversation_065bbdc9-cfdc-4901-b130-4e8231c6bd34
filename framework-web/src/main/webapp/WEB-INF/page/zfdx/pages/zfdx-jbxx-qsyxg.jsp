<%@ page language="java" import="java.util.*" pageEncoding="UTF-8" %>
<%@ page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<!DOCTYPE html>
<html lang="en">
<%
    String wgServerIp = PropertiesHandlerUtil.getValue("wg.server.ip", "fujianWG");
%>
<c:set var="WG_SERVER_IP"><%=wgServerIp%>
</c:set>
<style type="text/css">
    .has-feedback .form-control {
        padding-right: 13.5px;
    }

    .form-control {
        display: block;
        width: 100%;
        padding: 2px 5px;
        line-height: 1.428571;
    }
</style>
<script type="text/javascript">
    var wGServerIp = '${WG_SERVER_IP}';
</script>
<head>
    <meta charset="utf-8">
    <style>
        .tangram-suggestion-main {
            z-index: 1060;
        }
    </style>
</head>
<body>
<div class="main-container">
    <div class="padding-md">
        <div class="row">
            <div class="col-lg-12">
                <div class="smart-widget widget-blue">
                    <div class="smart-widget-inner table-responsive">
                        <div class="smart-widget-header">
                            <i class="fa fa-comment"></i> ${entity.objectName } <span
                                class="smart-widget-option" style="margin-top: -7px;"> <span
                                class="refresh-icon-animated"><i
                                class="fa fa-circle-o-notch fa-spin"></i></span>
								</span>
                            <span class="smart-widget-option">
									<span class="refresh-icon-animated">
										<i class="fa fa-circle-o-notch fa-spin"></i>
									</span>
			                    	<a href="#" onclick="goBack('${preUrl}')"><i class="fa fa-arrow-right"></i> 返回</a>
								</span>
                        </div>
                        <div class="smart-widget-inner table-responsive">
                            <div class="smart-widget-body form-horizontal">
                                <div class="text-right m-top-md" style="margin-right: 15px;">
                                    <button class="btn btn-info" style="width: 100px;"
                                            id='submitformId'>保存
                                    </button>
                                    <button class="btn btn-info" id="saveObjectAndStartBtn"
                                            style="width: 140px;">保存并发起执法
                                    </button>
                                    <c:if test="${entity!=null}">
                                        <button class="btn btn-info" onclick="changeLawObjectType('${entity.id}')"
                                                data-toggle="modal"
                                                style="width: 140px;">修改执法对象类型
                                        </button>
                                    </c:if>

                                </div>
                                <form id="toChangeFormId" method="post">
                                    <input type="hidden" name="objectNumber" value='${entity.objectNumber}'>
                                    <input name="token" value="${tokenReport }" type="hidden">
                                    <input type="hidden" name="typeCode" value='${typeCode}'>
                                    <input type="hidden" name="id" value="${entity.id}">
                                    <input id="belongAreaName" name="belongAreaName" type="hidden"
                                           value="${ entity.belongAreaName}"></input>
                                    <input id="belongAreaId" name="belongAreaId" type="hidden"
                                           value="${ entity.belongAreaId}"></input>
                                    <input id="powerAreaId" name="powerAreaId" type="hidden"
                                           value="${ entity.powerAreaId}"></input>
                                    <input id="powerAreaName" name="powerAreaName" type="hidden"
                                           value="${entity.powerAreaName }"></input>
                                    <input id="industryTypeCode" name="industryTypeCode" type="hidden"
                                           value="${entity.industryTypeCode }">
                                    <input type="hidden" id="cityStatus" value="${areaInfo.cityStatus }">
                                    <input type="hidden" id="cityCode" value="${areaInfo.cityCode }">
                                    <input type="hidden" id="cityName" value="${areaInfo.cityName }">
                                    <input type="hidden" id="countyCode" value="${areaInfo.countyCode }">
                                    <input type="hidden" id="countyName" value="${areaInfo.countyName }">
                                    <input type="hidden" id="powerCityStatus" value="${areaInfo.powerCityStatus }">
                                    <input type="hidden" id="powerCityCode" value="${areaInfo.powerCityCode }">
                                    <input type="hidden" id="powerCityName" value="${areaInfo.powerCityName }">
                                    <input type="hidden" id="powerCountyCode" value="${areaInfo.powerCountyCode }">
                                    <input type="hidden" id="powerCountyName" value="${areaInfo.powerCountyName }">
                                    <input type="hidden" id="gridCityCode" value="${areaInfo.gridCityCode }">
                                    <input type="hidden" id="gridCountyCode" value="${areaInfo.gridCountyCode }">
                                    <input type="hidden" id="gridStreetCode" value="${areaInfo.gridStreetCode }">
                                    <input type="hidden" id="gridCommunityCode" value="${areaInfo.gridCommunityCode }">
                                    <!-- <input type="hidden" id="gridUcodeCode" value="${areaInfo.gridUcode }"> -->

                                    <input id="belongAreaCityId" name="belongAreaCityId" type="hidden"
                                           value="${ entity.belongAreaCityId}">
                                    <input id="belongAreaCityName" name="belongAreaCityName" type="hidden"
                                           value="${entity.belongAreaCityName}">
                                    <input type="hidden" id="arealevel" value="${areaInfo.arealevel }">
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label"><span
                                                style="color: red;">*</span>对象类别</label>
                                        <div class="col-md-6" style="margin-top: 5px;">
                                            <span>${objectType}</span>
                                        </div>
                                    </div>
                                    <%-- 	<c:if test="${not empty  entity.id}">
											<div class="form-group">
												<label for="inputtext3" class="col-md-3 control-label">
													<span style="color: red;">*</span>执法对象ID
												</label>
												<div class="col-md-6" style="margin-top: 5px;" name="id">
													<span>${entity.id}</span>
												</div>
												<input type="hidden" name="id" value='${entity.id}' /> <input
													type="hidden" name="typeName" value='${objectType}' />
											</div>
										</c:if> --%>

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>名称
                                        </label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="inputtext3"
                                                   name="objectName" value="${entity.objectName }"
                                                   placeholder="名称">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>执法对象所在行政区:
                                        </label>
                                        <div class="col-lg-2">
                                            <select class="form-control" id="law_object_province"
                                                    name="law_object_province">
                                                <option value="35000000">福建省</option>
                                            </select>
                                        </div>

                                        <div class="col-lg-2">
                                            <select class="form-control" id="law_object_city" name="law_object_city">
                                            </select>
                                        </div>
                                        <div class="col-lg-2">
                                            <select class="form-control" id="law_object_county"
                                                    name="law_object_county">
                                            </select>
                                        </div>
                                    </div>

                                    <!-- <div class="form-group"> -->
                                    <!-- 2019-08-07 新增by Gengchao-->
                                    <!-- <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>执法对象所属网格:
                                    </label>
                                    <div class="col-md-1">
                                        <select class="form-control" id="law_object_province1"
                                                name="law_object_province1">
                                            <option value="35000000">福建省</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-5 no-padding">
                                        <div class="col-lg-3">
                                            <select class="form-control" id="gridCity" name="gridCity">
                                            </select>
                                        </div>
                                        <div class="col-lg-3">
                                            <select class="form-control" id="gridCounty"
                                                    name="gridCounty">
                                            </select>
                                        </div>
                                        <div class="col-lg-3">
                                            <select class="form-control" id="gridStreet" name="gridStreet">
                                            </select>
                                        </div>
                                        <div class="col-lg-3">
                                            <select class="form-control" id="gridCommunity" name="gridCommunity">
                                            </select>
                                        </div>
                                    </div> -->

                                    <!-- </div> -->
                                    <!-- <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>执法对象所属网格编码：
                                        </label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" name="gridUcode"
                                                   id="gridUcode"
                                                   readonly="readnoly"
                                            >
                                        </div>
                                    </div> -->

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>地址
                                        </label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" name="address"
                                                   value="${entity.address}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="control-label col-lg-3">权属行政区</label>
                                        <div class="col-lg-2">
                                            <select class="form-control" id="power_province">
                                                <option value="35000000">福建省</option>
                                            </select>
                                        </div>

                                        <div class="col-lg-2">
                                            <select class="form-control" id="power_city">
                                            </select>
                                        </div>
                                        <div class="col-lg-2">
                                            <select class="form-control" id="power_county">
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="地理坐标" class="col-md-3 control-label">地理坐标</label>
                                        <div class="col-lg-2">
                                            <span style="color: red;">*</span>经度<input class="form-control" id="mapJD"
                                                                                         name="gisCoordinateX"
                                                                                         placeholder="经度"
                                                                                         value="${entity.gisCoordinateX}">
                                        </div>
                                        <div class="col-lg-2">
                                            <span style="color: red;">*</span>纬度<input class="form-control" id="mapWD"
                                                                                         name="gisCoordinateY"
                                                                                         placeholder="纬度"
                                                                                         value="${entity.gisCoordinateY}">
                                        </div>
                                        <div class="col-lg-2">
                                            <!-- 	<button class="btn btn-info" type="button"
													data-toggle="modal" data-target="#myModal" id="createMap"
													onclick="createMapOnclick()" style="margin-top: 18px;">定位</button> -->
                                            <button class="btn btn-info" type="button"
                                                    onclick="createMapClick('${entity.address}','${entity.gisCoordinateX}','${entity.gisCoordinateY}')"
                                                    style="margin-top: 18px;">定位
                                            </button>
                                            <button type="button" class="btn btn-info" id="folder"
                                                    style="margin-top: 18px;" data-container="body" tabindex="-1"
                                                    data-toggle="popover" data-placement="bottom" data-html="true"
                                                    data-content="请使用福建环境执法APP扫一扫定位<br>坐标。<button type='button' class='close' data-dismiss='modal' aria-hidden='true' style='margin-top:-20px;'>&times;</button>注意获取的是手机当前位置的<br>经纬度。
		 												<button type='button' class='btn btn-danger btn-xs' id='refreshQR' onclick='refreshQR()' style='float:right; margin-right:15px;'>刷新</button><br><div id='appQrCode' style=' width:230px; height:235px;padding-top:7px;'></div>">
                                                手机定位
                                            </button>
                                            <li id="getPositionSuccess" style="color:cornflowerblue; display:none;">
                                                坐标获取成功，请保存信息。
                                            </li>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>固定污染源排污许可分类</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sewageClassify" id="sewageClassify" onchange="document.getElementById('sewageClassifyName').value=this.options[this.selectedIndex].text">
                                                <option value="">请选择</option>
                                                <c:forEach items="${SEWAGECLASSIFY}" var="sypwxkhyjsgf">
                                                    <option value="${sypwxkhyjsgf.code}" <c:if test="${entity.sewageClassify eq sypwxkhyjsgf.code}"> selected</c:if>>${sypwxkhyjsgf.name}</option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="sewageClassifyName" name="sewageClassifyName" value="${entity.sewageClassifyName}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>管理类型</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="managementType" onchange="manageSelect(this.options[this.selectedIndex].text)">
                                                <option value="">请选择</option>
                                                <option value="重点管理"<c:if test="${entity.managementType=='重点管理'}"> selected </c:if>>重点管理</option>
                                                <option value="简化管理"<c:if test="${entity.managementType=='简化管理'}"> selected </c:if>>简化管理</option>
                                                <option value="登记管理"<c:if test="${entity.managementType=='登记管理'}"> selected </c:if>>登记管理</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div id="manageShow">
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>是否发证</label>
                                            <div class="col-md-6">
                                                <select class="form-control" name="isCertification" id="isCertification">
                                                    <option value="">请选择</option>
                                                    <option value="已发证"<c:if test="${entity.isCertification=='已发证'}"> selected </c:if>>已发证</option>
                                                    <option value="未发证"<c:if test="${entity.isCertification=='未发证'}"> selected </c:if>>未发证</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group isCertificationShow">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>许可证编号</label>
                                            <div class="col-md-6">
                                                <input type="text" placeholder="许可证编号" class="form-control"
                                                       id="licenseNumber" name="licenseNumber"
                                                       value="${entity.licenseNumber}">
                                            </div>
                                        </div>
                                        <div class="form-group isCertificationShow">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>发证机构</label>
                                            <div class="col-md-6">
                                                <select class="form-control" name="certifyingAuthority" id="certifyingAuthority">
                                                    <option value="">请选择</option>
                                                    <option value="0"<c:if test="${entity.certifyingAuthority== 0}"> selected </c:if>>市级环保部门</option>
                                                    <option value="1"<c:if test="${entity.certifyingAuthority== 1}"> selected </c:if>>县级环保部门</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="form-group isCertificationShow">
                                            <label for="inputtext3" class="col-md-3 control-label">发证时间</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"  readonly="readonly" placeholder="发证时间" id="startDateStr" name="startDateStr" value="<fmt:formatDate value='${entity.startDate}' pattern='yyyy-MM-dd'></fmt:formatDate>">
                                            </div>
                                        </div>
                                        <div class="form-group isCertificationShow">
                                            <label for="inputtext3" class="col-md-3 control-label">截止时间</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control" readonly="readonly"  placeholder="截止时间" id="endDateStr" name="endDateStr" value="<fmt:formatDate value='${entity.endDate}' pattern='yyyy-MM-dd'></fmt:formatDate>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>统一社会信用代码</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" oninput="sixItemsCodeChick()"
                                                   id="socialCreditCode" name="socialCreditCode"
                                                   value="${entity.socialCreditCode}" placeholder="统一社会信用代码">
                                        </div>
                                    </div>
                                    <!--<div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            营业执照证件号</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="licenseNo"
                                                   oninput="sixItemsCodeChick()"
                                                   name="licenseNo" value="${entity.licenseNo}"
                                                   placeholder="营业执照证件号">
                                        </div>
                                    </div>-->
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            组织机构代码</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="orgCode"
                                                   name="orgCode" oninput="sixItemsCodeChick()"
                                                   value="${entity.orgCode}"
                                                   placeholder="组织机构代码">
                                        </div>
                                    </div>


                                    <!--<div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">税务登记号</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" oninput="sixItemsCodeChick()"
                                                   id="taxationNumber" name="taxationNumber"
                                                   value="${entity.taxationNumber}" placeholder="税务登记号">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">事业单位证书号</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" oninput="sixItemsCodeChick()"
                                                   id="companyNumber" name="companyNumber"
                                                   value="${entity.companyNumber}" placeholder="事业单位证书号">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">社会组织登记证号</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" oninput="sixItemsCodeChick()"
                                                   id="socialNumber" name="socialNumber" value="${entity.socialNumber}"
                                                   placeholder="社会组织登记证号">
                                        </div>
                                    </div>-->


                                    <div class="form-group">
                                        <label for="行业类型" class="col-md-3 control-label">

                                            <span style="color: red;">*</span>行业类型 </label>
                                        <div class="col-lg-6">
                                            <div class="input-group">
                                                <input type="text" placeholder="请选择行业类型"
                                                       readonly="readonly" class="form-control"
                                                       id='industryTypeName' name="industryTypeName"
                                                       value="${entity.industryTypeName}">
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-info no-shadow"
                                                            tabindex="-1" data-toggle="modal" id="IndustryTypeChoose"
                                                            data-remote="${webpath}/zfdx/industry-type-page"
                                                            data-target="#Industrytype">行业类型选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>污染源类别</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="pollutionSourceType"
                                                    id="pollutionSourceType"
                                                    onchange="document.getElementById('pollutionSourceTypeName').value=this.options[this.selectedIndex].text">
                                                <option value="">请选择</option>
                                                <c:forEach items="${pollutionSourceTypes}" var="pst">
                                                    <option value="${pst.code}" <c:if
                                                            test="${entity.pollutionSourceType eq pst.code}"> selected</c:if>>${pst.name}</option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="pollutionSourceTypeName"
                                                   name="pollutionSourceTypeName"
                                                   value="${entity.pollutionSourceTypeName}">
                                        </div>
                                    </div>
                                    <!--<div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            监管级别</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="levelCode" id="levelCode">
                                                <option value="">请选择</option>
                                                <option value="1"
                                                        <c:if test="${entity.levelCode=='1'}"> selected</c:if>>国控
                                                </option>
                                                <option value="2"
                                                        <c:if test="${entity.levelCode=='2'}"> selected</c:if>>省控
                                                </option>
                                                <option value="3"
                                                        <c:if test="${entity.levelCode=='3'}"> selected</c:if>>市控
                                                </option>
                                                <option value="4"
                                                        <c:if test="${entity.levelCode=='4'}"> selected</c:if>>县控
                                                </option>
                                                <option value="5"
                                                        <c:if test="${entity.levelCode=='5'}"> selected</c:if>>非控
                                                </option>
                                            </select>
                                        </div>
                                    </div>-->
                                    <input type="hidden" name='levelName'/>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span> 法人代表</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="legalPerson"
                                                   placeholder="法人代表" name="legalPerson"
                                                   value="${entity.legalPerson}">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>证件类型</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="cardTypeCode">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.cardTypeCode=='0'}"> selected </c:if>>身份证
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.cardTypeCode=='1'}"> selected</c:if>>香港居民身份证/澳门居民身份证/台胞证
                                                </option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label"> <span
                                                style="color: red;">*</span>法人身份证号</label>
                                        <div class="col-md-6">
                                            <input type="text" placeholder="法人身份证号" class="form-control"
                                                   id="legalManIdCard" name="legalManIdCard"
                                                   value="${entity.legalManIdCard}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            法人电话</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="legalPhone"
                                                   placeholder="法人电话" name="legalPhone"
                                                   value="${entity.legalPhone}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>环保负责人</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="chargePerson"
                                                   placeholder="环保负责人" name="chargePerson"
                                                   value="${entity.chargePerson}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">环保负责人身份证号</label>
                                        <div class="col-md-6">
                                            <input type="text" placeholder="环保负责人身份证号" class="form-control"
                                                   id="chargeManIdCard" name="chargeManIdCard"
                                                   value="${entity.chargeManIdCard}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>环保负责人电话</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control"
                                                   id="chargePersonPhone" placeholder="环保负责人电话"
                                                   name="chargePersonPhone"
                                                   value="${entity.chargePersonPhone}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否重点源</label>
                                        <div class="col-md-6">
                                            <c:if test="${empty entity.id}">
                                                <select class="form-control" name="isKeySource">
                                                    <option value="">请选择</option>
                                                    <option value="1">是</option>
                                                    <option value="0" selected>否</option>
                                                </select>
                                            </c:if>
                                            <c:if test="${not empty entity.id}">

                                                <select class="form-control" name="isKeySource">
                                                    <option value="">请选择</option>
                                                    <option value="1"
                                                            <c:if test="${entity.isKeySource=='1'}"> selected</c:if>>是
                                                    </option>
                                                    <option value="0"
                                                            <c:if test="${entity.isKeySource=='0'}"> selected </c:if>>否
                                                    </option>
                                                </select>
                                            </c:if>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>适用排污许可行业技术规范</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sypwxkhyjsgfCode" id="sypwxkhyjsgfCode"
                                                    onchange="document.getElementById('sypwxkhyjsgfName').value=this.options[this.selectedIndex].text">
                                                <option value="">请选择</option>
                                                <c:forEach items="${SYPWXKHYJSGF}" var="sypwxkhyjsgf">
                                                    <option value="${sypwxkhyjsgf.code}" <c:if
                                                            test="${entity.sypwxkhyjsgfCode eq sypwxkhyjsgf.code}"> selected</c:if>>${sypwxkhyjsgf.name}</option>
                                                </c:forEach>
                                            </select>
                                            <input type="hidden" id="sypwxkhyjsgfName" name="sypwxkhyjsgfName"
                                                   value="${entity.sypwxkhyjsgfName}">
                                        </div>
                                    </div>



                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否属于“小散乱污”企业</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="xslw">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.xslw=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.xslw=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否在线监控企业</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isOnlineMonitor">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isOnlineMonitor=='0'}">selected</c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isOnlineMonitor=='1'}">selected</c:if>>是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>生产状态</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="productStateCode">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="2"
                                                        <c:if test="${entity.productStateCode=='2'}">selected</c:if>>
                                                    正常生产
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.productStateCode=='1'}">selected</c:if>>
                                                    在建未投产
                                                </option>
                                                <option value="3"
                                                        <c:if test="${entity.productStateCode=='3'}">selected</c:if>>
                                                    长时间停产
                                                </option>
                                                <option value="4"
                                                        <c:if test="${entity.productStateCode=='4'}">selected</c:if>>已关停
                                                </option>
                                                <option value="5"
                                                        <c:if test="${entity.productStateCode=='5'}">selected</c:if>>不存在
                                                </option>
                                                <option value="6"
                                                        <c:if test="${entity.productStateCode=='6'}">selected</c:if>>
                                                    重复企业
                                                </option>
                                                <option value="7"
                                                        <c:if test="${entity.productStateCode=='7'}">selected</c:if>>
                                                    临时性停产
                                                </option>
                                                <option value="8"
                                                        <c:if test="${entity.productStateCode=='8'}">selected</c:if>>
                                                    被限制生产
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否废水产生单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sffscsdw">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.sffscsdw=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.sffscsdw=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否废气产生单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sffqcsdw">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.sffqcsdw=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.sffqcsdw=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否危废产生单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sfwfcsdw">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.sfwfcsdw=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.sfwfcsdw=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否危废经营单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="sfwfjydw">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.sfwfjydw=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.sfwfjydw=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否信用评价涉及单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isSocialCreditUnit">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isSocialCreditUnit=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isSocialCreditUnit=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>


                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>是否注销</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isCancelled">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isCancelled=='0'}"> selected </c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isCancelled=='1'}"> selected</c:if>>是
                                                </option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否固废经营单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isSolidwasteOperunit">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isSolidwasteOperunit=='0'}">selected</c:if>>
                                                    否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isSolidwasteOperunit=='1'}">selected</c:if>>
                                                    是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否固废产生单位</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isSolidwasteCreateunit">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isSolidwasteCreateunit=='0'}">selected</c:if>>
                                                    否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isSolidwasteCreateunit=='1'}">selected</c:if>>
                                                    是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否风险源</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isRiskSource">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isSolidwasteCreateunit=='0'}">selected</c:if>>
                                                    否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isSolidwasteCreateunit=='1'}">selected</c:if>>
                                                    是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            <span style="color: red;">*</span>排污口是否规范化</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="isOutfallStandard">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="1"
                                                        <c:if test="${entity.isOutfallStandard=='1'}">selected</c:if>>是
                                                </option>
                                                <option value="0"
                                                        <c:if test="${entity.isOutfallStandard=='0'}">selected</c:if>>否
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <!-- <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            当事人性质</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="personNatureCode">
                                                <option value="">请选择</option>
                                                <option value="1"
                                                        <c:if test="${entity.personNatureCode=='1'}">selected</c:if>>
                                                    国有企业
                                                </option>
                                                <option value="2"
                                                        <c:if test="${entity.personNatureCode=='2'}">selected</c:if>>
                                                    国有独资公司
                                                </option>
                                                <option value="3"
                                                        <c:if test="${entity.personNatureCode=='3'}">selected</c:if>>其他
                                                </option>
                                                <option value="4"
                                                        <c:if test="${entity.personNatureCode=='4'}"></c:if>>未了解
                                                </option>


                                            </select>
                                        </div>
                                    </div>-->
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            所属级别</label>
                                        <div class="col-md-6">
                                            <select class="form-control" name="belongLevelCode">
                                                <option value="">请选择</option>
                                                <option value="1"
                                                        <c:if test="${entity.belongLevelCode=='1'}">selected</c:if>>央属
                                                </option>
                                                <option value="2"
                                                        <c:if test="${entity.belongLevelCode=='2'}">selected</c:if>>省属
                                                </option>
                                                <option value="3"
                                                        <c:if test="${entity.belongLevelCode=='3'}">selected</c:if>>市属
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否上市公司</label>
                                        <div class="col-md-6">
                                            <select class="form-control" id="isListedCompany"
                                                    name="isListedCompany">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isListedCompany=='0'}">selected</c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isListedCompany=='1'}">selected</c:if>>是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group" id="stockCodeHidden"
                                         style="display: none">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            股票代码</label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" name="stockCode"
                                                   id="stockCode" placeholder="股票代码" name="stockCode"
                                                   value="${entity.stockCode}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否所属集团公司</label>
                                        <div class="col-md-6">
                                            <select class="form-control" id="isGroupCompany"
                                                    name="isGroupCompany">
                                                <!-- <option value="">请选择</option> -->
                                                <option value="0"
                                                        <c:if test="${entity.isGroupCompany=='0'}">selected</c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.isGroupCompany=='1'}">selected</c:if>>是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <div id="isGroupCompanyHidden" style="display: none">
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司名称</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"
                                                       id="groupCompanyName" placeholder="所属集团公司名称"
                                                       name="groupCompanyName" value="${entity.groupCompanyName}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司组织机构代码</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"
                                                       id="groupCompanyOrgcode" name="groupCompanyOrgcode"
                                                       placeholder="所属集团公司组织机构代码"
                                                       value="${entity.groupCompanyOrgcode}">
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label">
                                                所属集团公司股票代码</label>
                                            <div class="col-md-6">
                                                <input type="text" class="form-control"
                                                       id="groupCompanyStockcode" placeholder="所属集团公司股票代码"
                                                       name="groupCompanyStockcode"
                                                       value="${entity.groupCompanyStockcode}">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            对象介绍</label>
                                        <div class="col-md-6">
												<textarea class="form-control" rows="3" name="objectDesc"
                                                          placeholder="对象介绍">${entity.objectDesc}</textarea>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label">
                                            是否正面清单企业</label>
                                        <div class="col-md-6">
                                            <select class="form-control" id="ispositive"
                                                    name="ispositive">
                                                <option value="">请选择</option>
                                                <option value="0"
                                                        <c:if test="${entity.ispositive=='0'}">selected</c:if>>否
                                                </option>
                                                <option value="1"
                                                        <c:if test="${entity.ispositive=='1'}">selected</c:if>>是
                                                </option>

                                            </select>
                                        </div>
                                    </div>
                                    <c:forEach items="${res}" var="res" varStatus="status">
                                    <c:if test="${res.resourceDesc=='QYDWSJXZ'}">
                                    <div class="form-group">
                                        <label for="双随机属性" class="col-md-3 control-label">双随机属性</label>
                                        <div class="col-lg-6">
                                            <div class="input-group">
                                                <input type="hidden" id="hiddenAttrIds" name="hiddenAttrIds"
                                                       value="${entity.hiddenAttrIds }"/>
                                                <input type="text" class="form-control" readonly
                                                       id="selectedAttrs">
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-info no-shadow"
                                                            tabindex="-1" data-toggle="modal"
                                                            data-target="#ssjsx">双随机属性选择
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </c:if>
                                    </c:forEach>
                            </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- 执法对象类型修改modal -->
<div class="modal fade" id="zfdxxg" tabindex="-1" role="dialog" aria-labelledby="myModalLabel2" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">

        </div>
    </div>
</div>

<!-- 双随机属性（Modal） -->
<div class="modal fade" id="ssjsx" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <div style="float:right; margin-top:-5px;">
                    <button type="button" class="btn btn-info" id="addRandomAttr">确定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
                <h4 class="modal-title" id="myModalLabel">双随机属性</h4>
            </div>
            <div class="modal-body padding-lg">
                <div class="smart-widget-body form-horizontal">
                    <div class="form-group">
                        <div class="col-lg-12">
                            <c:forEach items="${randomAttr }" var="item" varStatus="status">
                                <div class="checkbox inline-block col-md-4">
                                    <div class="custom-checkbox">
                                        <input type="checkbox" id="checkbox${status.index+1}" name="randomAttrs"
                                               value="${item.id }">
                                        <label for="checkbox${status.index+1}" class="checkbox-blue" checked></label>
                                    </div>
                                    <div class="inline-block vertical-top" id="attrName${item.id }">
                                            ${item.attrName }
                                    </div>
                                </div>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <!--<button type="button" class="btn btn-info" id="addRandomAttr">确定</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
            </div>

        </div>
    </div>
</div>

<!-- 双随机属性JS -->
<script type="text/javascript">

    $(document).ready(function () {

        var attrIdsStr = $("#hiddenAttrIds").val();
        if (attrIdsStr != null && attrIdsStr != '') {
            var attrIdsArr = attrIdsStr.split(",");
            var obj = document.getElementsByName('randomAttrs');
            var names = "";
            for (var i = 0; i < obj.length; i++) {
                //不能用attrIdsStr.indexOf，因为如果有13，则会把1和3也选中。。。。
                for (var j = 0; j < attrIdsArr.length; j++) {
                    if (attrIdsArr[j] == obj[i].value) {
                        obj[i].checked = true;
                        names += $.trim($("#attrName" + obj[i].value).html()) + ',';
                    }
                }
            }
            names = names.substring(0, names.length - 1);
            $("#selectedAttrs").val(names);//在输入框显示选中的属性名
        }
    });
    $("#addRandomAttr").click(function () {
        //选中的双随机属性
        var obj = document.getElementsByName('randomAttrs');
        var ids = "";
        var names = "";
        for (var i = 0; i < obj.length; i++) {
            if (obj[i].checked) ids += obj[i].value + ',';
            if (obj[i].checked) names += $.trim($("#attrName" + obj[i].value).html()) + ',';
        }
        ids = ids.substring(0, ids.length - 1);//选中的所有的id
        names = names.substring(0, names.length - 1);//选中的所有的name
        $("#selectedAttrs").val(names);//在输入框显示选中的属性名
        $("#hiddenAttrIds").val(ids);//在隐藏框存上选中的属性id，方便点击保存时获取
        $("#ssjsx").modal('hide');
    });
</script>
<!-- /双随机属性JS结束 -->
<!--模态框（Modal） -->
<div class="modal fade" id="myModal" role="dialog" class="tangram-suggestion-main"
     aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

        </div>
    </div>
</div>

<!-- 行业类型选择（Modal） -->
<div class="modal fade" id="Industrytype" tabindex="-1" role="dialog"
     aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">

        </div>
    </div>
</div>
<!-- ./行业类型选择（Modal） -->


<script type="text/javascript">
    $(function () {
        $("[name='objectDesc']").focus(function () {
            business.listenTextAreaComeEnter();
        })
        $("[name='objectDesc']").blur(function () {
            business.listenTextAreaGoEnter();
        })
        //初始化加载
        var isListedCompany = $("#isListedCompany").val();
        var isGroupCompany = $("#isGroupCompany").val();
        if (isListedCompany == "1") {
            //上市公司
            $("#stockCodeHidden").show();
        }
        if (isGroupCompany == "1") {
            $("#isGroupCompanyHidden").show();
        }

        $("#startDateStr").datetimepicker({
            format : 'yyyy-mm-dd',
            todayBtn : true,
            /*clearBtn:true,*/
            language: 'zh-CN',
            autoclose : true
        }).on("changeDate",function(ev){
            $("#endDateStr").datetimepicker('setStartDate',new Date($("#startDateStr").val()));
            $('#toChangeFormId').formValidation('revalidateField', 'startDateStr');

            if ($("#endDateStr").val() && $("#startDateStr").val() > $("#endDateStr").val()) {
                swal({title:"提示", text:"发证时间不能晚于截止时间!", type:"error",allowOutsideClick :true},function(){
                    $("#startDateStr").val("");
                });
                return;
            }
        });
        $("#endDateStr").datetimepicker({
            format : 'yyyy-mm-dd',
            todayBtn : true,
            /*clearBtn:true,*/
            language: 'cn',
            autoclose : true
        }).on("changeDate",function(ev){
            $("#startDateStr").datetimepicker('setEndDate',new Date($("#endDateStr").val()));
            $('#toChangeFormId').formValidation('revalidateField', 'endDateStr');
            if ($("#startDateStr").val() && $("#startDateStr").val() > $("#endDateStr").val()) {
                swal({title:"提示", text:"截止时间不能早于发证时间!", type:"error",allowOutsideClick :true},function(){
                    $("#endDateStr").val("");
                });
                return;
            }
        });
        // 当证件类型发生变化时，重新校验身份证字段
        $('[name="cardTypeCode"]').on('change', function () {
            $('#toChangeFormId').formValidation('revalidateField', 'legalManIdCard');
            $('#toChangeFormId').formValidation('revalidateField', 'chargeManIdCard');
        });
    });


    function sixItemsCodeChick() {
        $('#toChangeFormId').formValidation('revalidateField', 'socialCreditCode');
        $('#toChangeFormId').formValidation('revalidateField', 'orgCode');
        $('#toChangeFormId').formValidation('revalidateField', 'licenseNo');
        $('#toChangeFormId').formValidation('revalidateField', 'taxationNumber');
        $('#toChangeFormId').formValidation('revalidateField', 'companyNumber');
        $('#toChangeFormId').formValidation('revalidateField', 'socialNumber');
        $('#toChangeFormId').formValidation('revalidateField', 'orgCode');
    }


    //document.getElementById("stockCodeHidden").style.display="none";
    //document.getElementById("isGroupCompanyHidden").style.display="none";
    $("#isListedCompany").change(function () {
        var isListedCompany = $("#isListedCompany").val();
        if (isListedCompany == "1") {
            $("#stockCodeHidden").show();
        } else {
            $("#stockCodeHidden").hide();
        }
    });
    $("#isGroupCompany").change(function () {
        var isGroupCompany = $("#isGroupCompany").val();
        if (isGroupCompany == "1") {
            $("#isGroupCompanyHidden").show();
        } else {
            $("#isGroupCompanyHidden").hide();
        }
    });
</script>
<script type="text/javascript">
    $(function () {

        var htmlCity = "<option value=''>请选择</option>";
        var htmlCounty = "<option value=''>请选择 </option>";
        var htmlAddr = "<option value=''>请选择 </option>";
        var htmlAddr2 = "<option value=''></option>";


        //执法对象所在行政区
        var cityStatus = $("#cityStatus").val();
        var cityCode = $("#cityCode").val();
        var cityName = $("#cityName").val();
        var countyCode = $("#countyCode").val();
        var countyName = $("#countyName").val();
        var powerCityStatus = $("#powerCityStatus").val();
        var powerCityCode = $("#powerCityCode").val();
        var powerCityName = $("#powerCityName").val();
        var powerCountyCode = $("#powerCountyCode").val();
        var powerCountyName = $("#powerCountyName").val();
        var arealevel = $("#arealevel").val();
        // var gridCity = $("#gridCityCode").val();
        // var gridCounty = $("#gridCountyCode").val();
        // var gridStreet = $("#gridStreetCode").val();
        // var gridCommunity = $("#gridCommunityCode").val();
        // var gridUcode =  $("#gridUcodeCode").val();
        // 状态
        if (cityStatus == "1") {
            $.ajax({
                type: "post",
                url: WEBPATH + "/tArea/cityList",
                dataType: "json",
                success: function (data) {
                    $("#law_object_city").append(
                        "<option value=''>请选择</option>");
                    $("#law_object_county").append(
                        "<option value=''>请选择</option>");
                    $.each(data, function (i, item1) {
                        $("#law_object_city").append(
                            "<option value=" + item1.code + ">"
                            + item1.name + "</option>");
                    });

                    // $("#gridCity").append(
                    //     "<option value=''>请选择</option>");
                    // $("#gridCounty").append(
                    //     "<option value=''>请选择</option>");
                    // $.each(data, function (i, item1) {
                    //     $("#gridCity").append(
                    //         "<option value=" + item1.code + ">"
                    //         + item1.name + "</option>");
                    // });


                }
            });
        } else if (cityStatus == '2') {
            //市级
            $("#law_object_city").append("<option selected  value=" + cityCode + ',' + cityName + ">" + cityName + "</option>");
            // $("#gridCity").append("<option selected  value=" + cityCode + ',' + cityName + ">" + cityName + "</option>");
            $.ajax({
                type: "post",
                url: WEBPATH + "/tArea/countyListByCode",
                dataType: "json",
                data: {
                    parentCode: cityCode
                },
                success: function (data) {
                    $("#law_object_county").append(
                        "<option value=''>请选择</option>");
                    $.each(data, function (i, item) {
                        if (countyCode == item.code) {
                            $("#law_object_county").append("<option selected value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                        } else {
                            $("#law_object_county").append("<option  value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                        }
                    });

                    // $("#gridCounty").append(
                    //     "<option value=''>请选择</option>");
                    // $.each(data, function (i, item) {
                    //     if (countyCode == item.code) {
                    //         $("#gridCounty").append("<option selected value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                    //     } else {
                    //         $("#gridCounty").append("<option  value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                    //     }
                    // });
                }
            });
        } else if (cityStatus == '3') {
            if (arealevel == '3') {
                $("#law_object_county").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                $("#law_object_city").append("<option selected  value=" + cityCode + ',' + cityName + ">" + cityName + "</option>");
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/cityList",
                    dataType: "json",
                    async: false, //使用同步的方式,true为异步方式
                    success: function (data) {
                        $.each(data,
                            function (i, item2) {
                                if (item2.code == cityCode) {
                                    // $("#gridCity").append("<option selected value=" + item2.code + ">" + item2.name + "</option>");
                                } else {
                                    // $("#gridCity").append("<option  value=" + item2.code + ">" + item2.name + "</option>");
                                }
                            });
                    }
                });

                // var parentCode = $('#gridCity').val();
                // var code = parentCode.split(",");
                // var tempa = code[0];
                // if (tempa == "35810000") {
                //     tempa = "350A0000";
                // }
                // $('#gridUcode').val(tempa.substring(0, tempa.length - 4));

                // $("#gridCounty option").remove();
                // $("#gridStreet option").remove();
                // $("#gridCommunity option").remove();
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     data: {parentId: tempa},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridCounty").html(htmlCounty);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var regex = /\((.+?)\)/g;
                //                 var options = dataArr[i].name.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 3);
                //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //                     }
                //                 }
                //                 if (result == tempa.substring(0, code[0].length - 4)) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //             $.each(newArr, function (i, item) {
                //                 $("#gridCounty").append(
                //                     "<option value=" + item.id.substring(0, item.id.length - 4) + ',' + item.name + "  >"
                //                     + item.name + "</option>");
                //             });
                //
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // if(gridCity!=null && gridCity !=""){
                //     var parentCode = $('#gridCity').val();
                //     var code = parentCode.split(",");
                //     var tempa = code[0];
                //     if (tempa == "35810000") {
                //         tempa = "350A0000";
                //     }


                //     $("#gridCounty option").remove();
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     // $.ajax({
                //     //     type: "post",
                //     //     dataType: "json",
                //     //     async: false, //使用同步的方式,true为异步方式
                //     //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     //     data: {parentId: tempa},
                //     //     success: function (data) {
                //     //         if (data.meta.code == "200") {
                //     //             $("#gridCounty").html(htmlCounty);
                //     //             var z = data.data;
                //     //             var dataArr = JSON.parse(z);
                //     //             var newArr = new Array();
                //     //             var result = '';
                //     //             for (var i = 0; i < dataArr.length; i++) {
                //     //                 var regex = /\((.+?)\)/g;
                //     //                 var options = dataArr[i].name.match(regex);
                //     //                 if (options != null && options != "") {
                //     //                     var option = options[0]
                //     //                     if (options != null && options != "") {
                //     //                         result = option.substring(1, option.length - 3);
                //     //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //     //                     }
                //     //                 }
                //     //                 if (result == tempa.substring(0, code[0].length - 4)) {
                //     //                     newArr.push(dataArr[i]);
                //     //                 }
                //     //             }
                //     //             $('#gridUcode').val(gridCounty.substring(0,gridCounty.length-2));
                //     //             $.each(newArr, function (i, item) {
                //     //                 if (item.id.substring(0, item.id.length - 4) == gridCounty) {
                //     //                     $("#gridCounty").append("<option selected value=" + item.id.substring(0, item.id.length - 4) + ',' + item.name +">" + item.name + "</option>");
                //     //                 } else {
                //     //                     $("#gridCounty").append("<option  value=" + item.id.substring(0, item.id.length - 4) +',' + item.name + ">" + item.name + "</option>");
                //     //                 }
                //     //             });
                //     //
                //     //         } else {
                //     //             swal("服务异常，请求数据失败!", "", "warning");
                //     //         }
                //     //
                //     //     },
                //     //     error: function () {
                //     //         swal("网络异常，请求数据失败!", "", "error");
                //     //     }
                //     // });
                // }

                /* $("#law_object_county").append("<option selected value="+countyCode+','+countyName+">"+ countyName + "</option>"); */
                // if(gridCounty!=""&&gridCounty!=null){
                //     var parentCode = $("#gridCounty").val();
                //     var code = parentCode.split(",");
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     if(gridStreet!=null && gridStreet!=""){
                //         $('#gridUcode').val(gridStreet.substring(0,gridStreet.length-3));
                //     }
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     data: {parentId: code[0]},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridStreet").html(htmlAddr);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var regex = /\((.+?)\)/g;
                //                 var options = dataArr[i].name.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 4);
                //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //                     }
                //                 }
                //                 if (result == code[0].substring(0, code[0].length - 2)) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //             $.each(newArr, function (i, item) {
                //                 if (gridStreet == item.id) {
                //                     $("#gridStreet").append(
                //                         "<option selected value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                     // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                 } else {
                //                     $("#gridStreet").append(
                //                         "<option value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                 }
                //
                //             });
                //
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // }

                // if (gridStreet != null&&gridStreet !="") {
                //     var parentCode = $("#gridStreet").val();
                //     var code = parentCode.split(",");
                //     $("#gridCommunity option").remove();
                //     if(gridCommunity!=null && gridCommunity!=""){
                //         $('#gridUcode').val(gridCommunity);
                //     }
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByParentId',
                //     data: {parentId: code[0]},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridCommunity").html(htmlAddr);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             var regex = /\((.+?)\)/g;
                //             var comARe = code[0].substring(0, code[0].length - 3);
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var options = dataArr[i].gridName.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 4)
                //                         dataArr[i].gridName = dataArr[i].gridName.substring(0, dataArr[i].gridName.indexOf('('))
                //
                //                     }
                //                 }
                //                 if (result == comARe) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //
                //             $.each(newArr, function (i, item) {
                //                 if (gridCommunity == item.id) {
                //                     $("#gridCommunity").append(
                //                         "<option selected value=" + item.id + ',' + item.gridName + "  >"
                //                         + item.gridName + "</option>");
                //                     // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                 } else {
                //                     $("#gridCommunity").append(
                //                         "<option value=" + item.id + ',' + item.gridName + "  >"
                //                         + item.gridName + "</option>");
                //                 }
                //             });
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // }


            } else if (arealevel == '2') {
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/cityList",
                    dataType: "json",
                    async: false, //使用同步的方式,true为异步方式
                    success: function (data) {
                        $.each(data,
                            function (i, item2) {
                                if (item2.code == cityCode) {
                                    // $("#gridCity").append("<option selected value=" + item2.code + ">" + item2.name + "</option>");
                                } else {
                                    // $("#gridCity").append("<option  value=" + item2.code + ">" + item2.name + "</option>");
                                }
                            });
                    }
                });
                // var parentCode = $('#gridCity').val();
                // var code = parentCode.split(",");
                // var tempa = code[0];
                // if (tempa == "35810000") {
                //     tempa = "350A0000";
                // }
                // $('#gridUcode').val(tempa.substring(0, tempa.length - 4));

                // $("#gridCounty option").remove();
                // $("#gridStreet option").remove();
                // $("#gridCommunity option").remove();

                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/countyListByCode",
                    async: false, //使用同步的方式,true为异步方式
                    dataType: "json",
                    data: {
                        parentCode: cityCode
                    },
                    success: function (data) {

                        $("#law_object_county").append(
                            "<option value=''>请选择</option>");
                        $.each(data,
                            function (i, item2) {
                                if (countyCode == item2.code) {
                                    $("#law_object_county").append("<option selected value=" + item2.code + ',' + item2.name + "  >" + item2.name + "</option>");
                                } else {
                                    $("#law_object_county").append("<option  value=" + item2.code + ',' + item2.name + "  >" + item2.name + "</option>");
                                }
                            });

                        // $("#gridCounty").append(
                        //     "<option value=''>请选择</option>");
                        // $.each(data,
                        //     function (i, item2) {
                        //         if (item2.code == gridCounty ) {

                        //             $("#gridCounty").append("<option selected value=" + item2.code + ',' + item2.name + "  >" + item2.name + "</option>");
                        //         } else {
                        //             $("#gridCounty").append("<option  value=" + item2.code + ',' + item2.name + "  >" + item2.name + "</option>");
                        //         }
                        //     });
                    }
                });



                // if(gridCity!=null && gridCity !=""){
                //     var parentCode = $('#gridCity').val();
                //     var code = parentCode.split(",");
                //     var tempa = code[0];
                //     if (tempa == "35810000") {
                //         tempa = "350A0000";
                //     }


                //     $("#gridCounty option").remove();
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     // $.ajax({
                //     //     type: "post",
                //     //     dataType: "json",
                //     //     async: false, //使用同步的方式,true为异步方式
                //     //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     //     data: {parentId: tempa},
                //     //     success: function (data) {
                //     //         if (data.meta.code == "200") {
                //     //             $("#gridCounty").html(htmlCounty);
                //     //             var z = data.data;
                //     //             var dataArr = JSON.parse(z);
                //     //             var newArr = new Array();
                //     //             var result = '';
                //     //             for (var i = 0; i < dataArr.length; i++) {
                //     //                 var regex = /\((.+?)\)/g;
                //     //                 var options = dataArr[i].name.match(regex);
                //     //                 if (options != null && options != "") {
                //     //                     var option = options[0]
                //     //                     if (options != null && options != "") {
                //     //                         result = option.substring(1, option.length - 3);
                //     //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //     //                     }
                //     //                 }
                //     //                 if (result == tempa.substring(0, code[0].length - 4)) {
                //     //                     newArr.push(dataArr[i]);
                //     //                 }
                //     //             }
                //     //             $('#gridUcode').val(gridCounty.substring(0,gridCounty.length-2));
                //     //             $.each(newArr, function (i, item) {
                //     //                 if (item.id.substring(0, item.id.length - 4) == gridCounty) {
                //     //                     $("#gridCounty").append("<option selected value=" + item.id.substring(0, item.id.length - 4) + ',' + item.name +">" + item.name + "</option>");
                //     //                 } else {
                //     //                     $("#gridCounty").append("<option  value=" + item.id.substring(0, item.id.length - 4) +',' + item.name + ">" + item.name + "</option>");
                //     //                 }
                //     //             });
                //     //
                //     //         } else {
                //     //             swal("服务异常，请求数据失败!", "", "warning");
                //     //         }
                //     //
                //     //     },
                //     //     error: function () {
                //     //         swal("网络异常，请求数据失败!", "", "error");
                //     //     }
                //     // });
                // }

                /* $("#law_object_county").append("<option selected value="+countyCode+','+countyName+">"+ countyName + "</option>"); */
                // if(gridCounty!=""&&gridCounty!=null){
                //     var parentCode = $("#gridCounty").val();
                //     var code = parentCode.split(",");
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     if(gridStreet!=null && gridStreet!=""){
                //         $('#gridUcode').val(gridStreet.substring(0,gridStreet.length-3));
                //     }
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     data: {parentId: code[0]},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridStreet").html(htmlAddr);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var regex = /\((.+?)\)/g;
                //                 var options = dataArr[i].name.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 4);
                //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //                     }
                //                 }
                //                 if (result == code[0].substring(0, code[0].length - 2)) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //             $.each(newArr, function (i, item) {
                //                 if (gridStreet == item.id) {
                //                     $("#gridStreet").append(
                //                         "<option selected value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                     // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                 } else {
                //                     $("#gridStreet").append(
                //                         "<option value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                 }
                //
                //             });
                //
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // }

                // if (gridStreet != null&&gridStreet !="") {
                //     var parentCode = $("#gridStreet").val();
                //     var code = parentCode.split(",");
                //     $("#gridCommunity option").remove();
                //     if(gridCommunity!=null && gridCommunity!=""){
                //         $('#gridUcode').val(gridCommunity);
                //     }
                //     $.ajax({
                //         type: "post",
                //         dataType: "json",
                //         async: false, //使用同步的方式,true为异步方式
                //         url: wGServerIp + '/zhjg/publicVisit/selectGridByParentId',
                //         data: {parentId: code[0]},
                //         success: function (data) {
                //             if (data.meta.code == "200") {
                //                 $("#gridCommunity").html(htmlAddr);
                //                 var z = data.data;
                //                 var dataArr = JSON.parse(z);
                //                 var newArr = new Array();
                //                 var result = '';
                //                 var regex = /\((.+?)\)/g;
                //                 var comARe = code[0].substring(0, code[0].length - 3);
                //                 for (var i = 0; i < dataArr.length; i++) {
                //                     var options = dataArr[i].gridName.match(regex);
                //                     if (options != null && options != "") {
                //                         var option = options[0]
                //                         if (options != null && options != "") {
                //                             result = option.substring(1, option.length - 4)
                //                             dataArr[i].gridName = dataArr[i].gridName.substring(0, dataArr[i].gridName.indexOf('('))
                //
                //                         }
                //                     }
                //                     if (result == comARe) {
                //                         newArr.push(dataArr[i]);
                //                     }
                //                 }
                //
                //                 $.each(newArr, function (i, item) {
                //                     if (gridCommunity == item.id) {
                //                         $("#gridCommunity").append(
                //                             "<option selected value=" + item.id + ',' + item.gridName + "  >"
                //                             + item.gridName + "</option>");
                //                         // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                     } else {
                //                         $("#gridCommunity").append(
                //                             "<option value=" + item.id + ',' + item.gridName + "  >"
                //                             + item.gridName + "</option>");
                //                     }
                //                 });
                //             } else {
                //                 swal("服务异常，请求数据失败!", "", "warning");
                //             }
                //
                //         },
                //         error: function () {
                //             swal("网络异常，请求数据失败!", "", "error");
                //         }
                //     });
                // }

                $("#law_object_city").append("<option selected  value=" + cityCode + ',' + cityName + ">" + cityName + "</option>");
                // $("#gridCity").append("<option selected  value=" + cityCode + ',' + cityName + ">" + cityName + "</option>");
            } else if (arealevel == '1') {
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/cityList",
                    dataType: "json",
                    async: false, //使用同步的方式,true为异步方式
                    success: function (data) {
                        // $("#gridCity").html(htmlCounty);
                        $.each(data,
                            function (i, item2) {
                                if (item2.code == cityCode) {
                                    $("#law_object_city").append("<option selected value=" + item2.code + ">" + item2.name + "</option>");
                                } else {
                                    $("#law_object_city").append("<option  value=" + item2.code + ">" + item2.name + "</option>");
                                }
                            });

                        $.each(data,
                            function (i, item2) {
                                // if (item2.code == gridCity) {
                                //     $("#gridCity").append("<option selected value=" + item2.code +','+item2.name+ ">" + item2.name + "</option>");
                                // } else {
                                //     $("#gridCity").append("<option  value=" + item2.code + ','+item2.name+" >" + item2.name + "</option>");
                                // }
                            });
                    }
                });
                var law_object_city = $("#law_object_city").val();
                $("#law_object_county option").remove();
                // $("#gridCounty option").remove();
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/countyListByCode",
                    dataType: "json",
                    async: false, //使用同步的方式,true为异步方式
                    data: {
                        parentCode: cityCode
                    },
                    success: function (data) {
                        $("#law_object_county").html(htmlCounty);
                        $.each(data, function (i, item) {
                            if (countyCode == item.code) {
                                $("#law_object_county").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                            } else {
                                $("#law_object_county").append("<option value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                            }
                        });
                    }
                });
                // if(gridCity!=null && gridCity !=""){
                //     var parentCode = $('#gridCity').val();
                //     var code = parentCode.split(",");
                //     var tempa = code[0];
                //     if (tempa == "35810000") {
                //         tempa = "350A0000";
                //     }


                //     $("#gridCounty option").remove();
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     // $.ajax({
                //     //     type: "post",
                //     //     dataType: "json",
                //     //     async: false, //使用同步的方式,true为异步方式
                //     //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     //     data: {parentId: tempa},
                //     //     success: function (data) {
                //     //         if (data.meta.code == "200") {
                //     //             $("#gridCounty").html(htmlCounty);
                //     //             var z = data.data;
                //     //             var dataArr = JSON.parse(z);
                //     //             var newArr = new Array();
                //     //             var result = '';
                //     //             for (var i = 0; i < dataArr.length; i++) {
                //     //                 var regex = /\((.+?)\)/g;
                //     //                 var options = dataArr[i].name.match(regex);
                //     //                 if (options != null && options != "") {
                //     //                     var option = options[0]
                //     //                     if (options != null && options != "") {
                //     //                         result = option.substring(1, option.length - 3);
                //     //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //     //                     }
                //     //                 }
                //     //                 if (result == tempa.substring(0, code[0].length - 4)) {
                //     //                     newArr.push(dataArr[i]);
                //     //                 }
                //     //             }
                //     //             $('#gridUcode').val(gridCounty.substring(0,gridCounty.length-2));
                //     //             $.each(newArr, function (i, item) {
                //     //                 if (item.id.substring(0, item.id.length - 4) == gridCounty) {
                //     //                     $("#gridCounty").append("<option selected value=" + item.id.substring(0, item.id.length - 4) + ',' + item.name +">" + item.name + "</option>");
                //     //                 } else {
                //     //                     $("#gridCounty").append("<option  value=" + item.id.substring(0, item.id.length - 4) +',' + item.name + ">" + item.name + "</option>");
                //     //                 }
                //     //             });
                //     //
                //     //         } else {
                //     //             swal("服务异常，请求数据失败!", "", "warning");
                //     //         }
                //     //
                //     //     },
                //     //     error: function () {
                //     //         swal("网络异常，请求数据失败!", "", "error");
                //     //     }
                //     // });
                // }

                /* $("#law_object_county").append("<option selected value="+countyCode+','+countyName+">"+ countyName + "</option>"); */
                // if(gridCounty!=""&&gridCounty!=null){
                //     var parentCode = $("#gridCounty").val();
                //     var code = parentCode.split(",");
                //     $("#gridStreet option").remove();
                //     $("#gridCommunity option").remove();
                //     if(gridStreet!=null && gridStreet!=""){
                //         $('#gridUcode').val(gridStreet.substring(0,gridStreet.length-3));
                //     }
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
                //     data: {parentId: code[0]},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridStreet").html(htmlAddr);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var regex = /\((.+?)\)/g;
                //                 var options = dataArr[i].name.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 4);
                //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
                //                     }
                //                 }
                //                 if (result == code[0].substring(0, code[0].length - 2)) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //             $.each(newArr, function (i, item) {
                //                 if (gridStreet == item.id) {
                //                     $("#gridStreet").append(
                //                         "<option selected value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                     // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                 } else {
                //                     $("#gridStreet").append(
                //                         "<option value=" + item.id + ',' + item.name + "  >"
                //                         + item.name + "</option>");
                //                 }
                //
                //             });
                //
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // }

                // if (gridStreet != null&&gridStreet !="") {
                //     var parentCode = $("#gridStreet").val();
                //     var code = parentCode.split(",");
                //     $("#gridCommunity option").remove();
                //     if(gridCommunity!=null && gridCommunity!=""){
                //         $('#gridUcode').val(gridCommunity);
                //     }
                // $.ajax({
                //     type: "post",
                //     dataType: "json",
                //     async: false, //使用同步的方式,true为异步方式
                //     url: wGServerIp + '/zhjg/publicVisit/selectGridByParentId',
                //     data: {parentId: code[0]},
                //     success: function (data) {
                //         if (data.meta.code == "200") {
                //             $("#gridCommunity").html(htmlAddr);
                //             var z = data.data;
                //             var dataArr = JSON.parse(z);
                //             var newArr = new Array();
                //             var result = '';
                //             var regex = /\((.+?)\)/g;
                //             var comARe = code[0].substring(0, code[0].length - 3);
                //             for (var i = 0; i < dataArr.length; i++) {
                //                 var options = dataArr[i].gridName.match(regex);
                //                 if (options != null && options != "") {
                //                     var option = options[0]
                //                     if (options != null && options != "") {
                //                         result = option.substring(1, option.length - 4)
                //                         dataArr[i].gridName = dataArr[i].gridName.substring(0, dataArr[i].gridName.indexOf('('))
                //
                //                     }
                //                 }
                //                 if (result == comARe) {
                //                     newArr.push(dataArr[i]);
                //                 }
                //             }
                //
                //             $.each(newArr, function (i, item) {
                //                 if (gridCommunity == item.id) {
                //                     $("#gridCommunity").append(
                //                         "<option selected value=" + item.id + ',' + item.gridName + "  >"
                //                         + item.gridName + "</option>");
                //                     // $("#gridCounty").append("<option selected value=" + countyCode + ',' + countyName + ">" + countyName + "</option>");
                //                 } else {
                //                     $("#gridCommunity").append(
                //                         "<option value=" + item.id + ',' + item.gridName + "  >"
                //                         + item.gridName + "</option>");
                //                 }
                //             });
                //         } else {
                //             swal("服务异常，请求数据失败!", "", "warning");
                //         }
                //
                //     },
                //     error: function () {
                //         swal("网络异常，请求数据失败!", "", "error");
                //     }
                // });
                // }


            }
        }

        $.ajax({
            type: "post",
            url: WEBPATH + "/tArea/cityList",
            dataType: "json",
            success: function (data) {
                // $("#power_city").html(htmlCity);
                $("#power_city").append("<option value=''>请选择</option>");
                $("#power_county").append("<option value=''>请选择</option>");
                $.each(data, function (i, item) {
                    if (powerCityStatus == "") {
                        $("#power_city").append("<option  value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                    }
                    if (powerCityStatus == "1") {
                        $("#power_city").append("<option  value=" + item.code + ',' + item.name + "  >" + item.name + "</option>");
                    } else if (powerCityStatus == '2') {
                        //市级
                        if (powerCityCode == item.code) {
                            $("#power_city").append("<option selected  value=" + item.code + ',' + item.name + ">" + item.name + "</option>");
                        } else {
                            $("#power_city").append("<option value=" + item.code + ',' + item.name + ">" + item.name + "</option>");
                        }
                        $.ajax({
                            type: "post",
                            url: WEBPATH + "/tArea/countyListByCode",
                            dataType: "json",
                            data: {
                                parentCode: powerCityCode
                            },
                            success: function (data) {
                                $.each(data, function (power1, poweritem1) {
                                    $("#power_county").append("<option  value=" + poweritem1.code + ',' + poweritem1.name + "  >" + poweritem1.name + "</option>");
                                });
                            }
                        });

                    } else if (powerCityStatus == '3') {
                        //$("#power_city").html(htmlCity);
                        // $("#law_object_city").html(htmlCity);
                        $.ajax({
                            type: "post",
                            url: WEBPATH
                                + "/tArea/countyListByCode",
                            dataType: "json",
                            data: {
                                parentCode: powerCityCode
                            },
                            success: function (data) {
                                $("#power_county").html(htmlCounty);
                                $.each(data, function (power2, poweritem2) {
                                    if (powerCountyCode == poweritem2.code) {
                                        $("#power_county").append(
                                            "<option selected value=" + poweritem2.code + ',' + poweritem2.name + "  >"
                                            + poweritem2.name
                                            + "</option>");
                                    } else {
                                        $("#power_county").append(
                                            "<option  value=" + poweritem2.code + ',' + poweritem2.name + "  >"
                                            + poweritem2.name
                                            + "</option>");
                                    }
                                });
                            }
                        });
                        if (powerCityCode == item.code) {
                            $("#power_city").append("<option selected value=" + item.code + ',' + item.name + ">"
                                + item.name
                                + "</option>");

                        } else {
                            $("#power_city").append("<option value=" + item.code + ',' + item.name + ">"
                                + item.name
                                + "</option>");
                        }
                        //$("#law_object_city").append("<option selected  value="+item.code+','+item.name+">"+item.name+"</option>");
                    }
                });
            }
        });

        $("#law_object_city").change(
            function () {
                if ($(this).val() == "") {
                    $("#law_object_county option").remove();
                    $("#law_object_county").html(htmlCounty);
                    return;
                }
                var parentCode = $(this).val();
                var code = parentCode.split(",");
                $("#law_object_county option").remove();
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/countyListByCode",
                    dataType: "json",
                    data: {
                        parentCode: code[0]
                    },
                    success: function (data) {
                        $("#law_object_county").html(htmlCounty);
                        $.each(data, function (i, item) {
                            $("#law_object_county").append(
                                "<option value=" + item.code + ',' + item.name + "  >"
                                + item.name + "</option>");
                        });
                    }
                });
            });
        <!-- create by gc ，适用新增网格字段 -->
        // $("#gridCity").change(
        //     function () {

        //         if ($(this).val() == "") {
        //             $("#gridCounty option").remove();
        //             $("#gridCounty").html(htmlCounty);
        //             return;
        //         }
        //         var parentCode = $(this).val();
        //         var code = parentCode.split(",");
        //         var tempa = code[0];
        //         if (tempa == "35810000") {
        //             tempa = "350A0000";
        //         }
        //         $('#gridUcode').val(tempa.substring(0, tempa.length - 4));

        //         $("#gridCounty option").remove();
        //         $("#gridStreet option").remove();
        //         $("#gridCommunity option").remove();
        // $.ajax({
        //     type: "post",
        //     dataType: "json",
        //     async: false, //使用同步的方式,true为异步方式
        //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
        //     data: {parentId: tempa},
        //     success: function (data) {
        //         if (data.meta.code == "200") {
        //             $("#gridCounty").html(htmlCounty);
        //             var z = data.data;
        //             var dataArr = JSON.parse(z);
        //             var newArr = new Array();
        //             var result = '';
        //             for (var i = 0; i < dataArr.length; i++) {
        //                 var regex = /\((.+?)\)/g;
        //                 var options = dataArr[i].name.match(regex);
        //                 if (options != null && options != "") {
        //                     var option = options[0]
        //                     if (options != null && options != "") {
        //                         result = option.substring(1, option.length - 3);
        //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
        //                     }
        //                 }
        //                 if (result == tempa.substring(0, code[0].length - 4)) {
        //                     newArr.push(dataArr[i]);
        //                 }
        //             }
        //             $.each(newArr, function (i, item) {
        //                 $("#gridCounty").append(
        //                     "<option value=" + item.id.substring(0, item.id.length - 4) + ',' + item.name + "  >"
        //                     + item.name + "</option>");
        //             });
        //
        //         } else {
        //             swal("服务异常，请求数据失败!", "", "warning");
        //         }
        //
        //     },
        //     error: function () {
        //         swal("网络异常，请求数据失败!", "", "error");
        //     }
        // });
        // });

        // $("#gridCounty").change(
        //     function () {

        //         if ($(this).val() == "") {
        //             $("#gridStreet option").remove();
        //             $("#gridCommunity option").remove();
        //             $("#gridStreet").html(htmlAddr);
        //             $("#gridCommunity").html(htmlAddr2);
        //             return;
        //         }
        //         var parentCode = $(this).val();
        //         var code = parentCode.split(",");
        //         var tempa = code[0];
        //         if (tempa == "35810100") {
        //             tempa = "350A0000";
        //         }
        //         $('#gridUcode').val(tempa.substring(0, tempa.length - 2));

        //         $("#gridStreet option").remove();
        //         $("#gridCommunity option").remove();
        // $.ajax({
        //     type: "post",
        //     dataType: "json",
        //     async: false, //使用同步的方式,true为异步方式
        //     url: wGServerIp + '/zhjg/publicVisit/selectGridByCountry',
        //     data: {parentId: tempa},
        //     success: function (data) {
        //         if (data.meta.code == "200") {
        //             $("#gridStreet").html(htmlAddr);
        //             $("#gridCommunity").html(htmlAddr2);
        //             var z = data.data;
        //             var dataArr = JSON.parse(z);
        //             var newArr = new Array();
        //             var result = '';
        //             for (var i = 0; i < dataArr.length; i++) {
        //                 var regex = /\((.+?)\)/g;
        //                 var options = dataArr[i].name.match(regex);
        //                 if (options != null && options != "") {
        //                     var option = options[0]
        //                     if (options != null && options != "") {
        //                         result = option.substring(1, option.length - 4);
        //                         dataArr[i].name = dataArr[i].name.substring(0, dataArr[i].name.indexOf('('))
        //                     }
        //                 }
        //                 if (result == tempa.substring(0, code[0].length - 2)) {
        //                     newArr.push(dataArr[i]);
        //                 }
        //             }
        //             $.each(newArr, function (i, item) {
        //                 $("#gridStreet").append(
        //                     "<option value=" + item.id + ',' + item.name + "  >"
        //                     + item.name + "</option>");
        //             });
        //
        //         } else {
        //             swal("服务异常，请求数据失败!", "", "warning");
        //         }
        //
        //     },
        //     error: function () {
        //         swal("网络异常，请求数据失败!", "", "error");
        //     }
        // });
        // });

        // $("#gridStreet").change(
        //     function () {

        //         if ($(this).val() == "") {
        //             $("#gridCommunity option").remove();
        //             $("#gridCommunity").html(htmlAddr);
        //             var a = $('#gridUcode').val();
        //             $('#gridUcode').val(a.substring(0, 6));
        //             return;
        //         }
        //         var parentCode = $(this).val();
        //         var code = parentCode.split(",");
        //         $('#gridUcode').val(code[0].substring(0, code[0].length - 3));
        //         $("#gridCommunity option").remove();
        // $.ajax({
        //     type: "post",
        //     dataType: "json",
        //     async: false, //使用同步的方式,true为异步方式
        //     url: wGServerIp + '/zhjg/publicVisit/selectGridByParentId',
        //     data: {parentId: code[0]},
        //     success: function (data) {
        //         if (data.meta.code == "200") {
        //             $("#gridCommunity").html(htmlAddr);
        //             var z = data.data;
        //             var dataArr = JSON.parse(z);
        //             var newArr = new Array();
        //             var result = '';
        //             var regex = /\((.+?)\)/g;
        //             var comARe = code[0].substring(0, code[0].length - 3);
        //             for (var i = 0; i < dataArr.length; i++) {
        //                 var options = dataArr[i].gridName.match(regex);
        //                 if (options != null && options != "") {
        //                     var option = options[0]
        //                     if (options != null && options != "") {
        //                         result = option.substring(1, option.length - 4)
        //                         dataArr[i].gridName = dataArr[i].gridName.substring(0, dataArr[i].gridName.indexOf('('))
        //
        //                     }
        //                 }
        //                 if (result == comARe) {
        //                     newArr.push(dataArr[i]);
        //                 }
        //             }
        //             $.each(newArr, function (i, item) {
        //                 $("#gridCommunity").append(
        //                     "<option value=" + item.id + ',' + item.gridName + "  >"
        //                     + item.gridName + "</option>");
        //             });
        //         } else {
        //             swal("服务异常，请求数据失败!", "", "warning");
        //         }
        //
        //     },
        //     error: function () {
        //         swal("网络异常，请求数据失败!", "", "error");
        //     }
        // });

        // });

        // $("#gridCommunity").change(
        //     function () {
        //         if ($(this).val() == "") {
        //             var a = $('#gridUcode').val();
        //             $('#gridUcode').val(a.substring(0, 9));
        //             return;
        //         }
        //         var parentCode = $(this).val();
        //         var code = parentCode.split(",");
        //         $('#gridUcode').val(code[0]);
        //     }
        // );


        $("#power_city").change(
            function () {
                if ($(this).val() == "") {
                    $("#power_county option").remove();
                    $("#power_county").html(htmlCounty);
                    return;
                }
                var parentCode = $(this).val();
                var code = parentCode.split(",");
                $("#power_county option").remove();
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/tArea/countyListByCode",
                    dataType: "json",
                    data: {
                        parentCode: code[0]
                    },
                    success: function (data) {
                        $("#power_county").html(htmlCounty);
                        $.each(data, function (i, item) {
                            $("#power_county").append(
                                "<option value=" + item.code + ',' + item.name + "  >"
                                + item.name + "</option>");

                        });
                    }
                });
            });

        $("#toChangeFormId").formValidation({
            framework: 'bootstrap',
            message: 'This value is not valid',
            icon: {
                valid: 'glyphicon glyphicon-ok',
                invalid: 'glyphicon glyphicon-remove',
                validating: 'glyphicon glyphicon-refresh'
            },
            fields: {
                objectName: {
                    message: '执法对象不能为空！',
                    validators: {
                        notEmpty: {
                            message: '执法对象不能为空！'
                        }
                    }
                },
                address: {
                    message: '地址不能为空！',
                    validators: {
                        notEmpty: {
                            message: '地址不能为空！'
                        },
                        stringLength: {
                            max: 150,
                            message: '地址最大150个字符'
                        }

                    }
                },
                industryTypeName: {
                    message: '行业类型不能为空！',
                    validators: {
                        notEmpty: {
                            message: '行业类型不能为空！'
                        }
                    }
                },
                pollutionSourceType: {
                    message: '污染源类别不能为空！',
                    validators: {
                        notEmpty: {
                            message: '污染源类别不能为空！'
                        }
                    }
                },
                isKeySource: {
                    message: '是否重点源不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否重点源不能为空！'
                        }
                    }
                },
                sypwxkhyjsgfCode: {
                    message: '适用排污许可行业技术规范不能为空！',
                    validators: {
                        notEmpty: {
                            message: '适用排污许可行业技术规范不能为空！'
                        }
                    }
                },
                sewageClassify: {
                    message: '固定污染源排污许可分类不能为空！',
                    validators: {
                        notEmpty: {
                            message: '固定污染源排污许可分类不能为空！'
                        }
                    }
                },
                managementType: {
                    message: '管理类型不能为空！',
                    validators: {
                        notEmpty: {
                            message: '管理类型不能为空！'
                        }
                    }
                },
                isCertification: {
                    message: '是否发证不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否发证不能为空！'
                        }
                    }
                },
                certifyingAuthority: {
                    message: '发证机构不能为空！',
                    validators: {
                        notEmpty: {
                            message: '发证机构不能为空！'
                        }
                    }
                },

                licenseNumber: {
                    message: '许可证编号不能为空！',
                    validators: {
                        notEmpty: {
                            message: '许可证编号不能为空！'
                        },

                        regexp:{
                            regexp:/^(?![A-Z]+$)[0-9A-Z]{22}$/,
                            message: '许可证编号为为22位数字和大写字母组成',
                        }
                    }
                },
                isOnlineMonitor: {
                    message: '是否在线监控企业不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否在线监控企业不能为空！'
                        }
                    }
                },
                productStateCode: {
                    message: '生产状态不能为空！',
                    validators: {
                        notEmpty: {
                            message: '生产状态不能为空！'
                        }
                    }
                },
                xslw: {
                    message: '是否小散乱污企业不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否小散乱污企业不能为空！'
                        }
                    }
                },
                sffscsdw: {
                    message: '是否废水产生单位不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否废水产生单位不能为空！'
                        }
                    }
                },
                sffqcsdw: {
                    message: '是否废气产生单位不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否废气产生单位不能为空！'
                        }
                    }
                },
                sfwfcsdw: {
                    message: '是否危废产生单位不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否危废产生单位不能为空！'
                        }
                    }
                },
                sfwfjydw: {
                    message: '是否危废经营单位不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否危废经营单位不能为空！'
                        }
                    }
                },
                isCancelled: {
                    message: '是否注销不能为空！',
                    validators: {
                        notEmpty: {
                            message: '是否注销不能为空！'
                        }
                    }
                },
                isOutfallStandard: {
                    message: '排污口是否规范化不能为空！',
                    validators: {
                        notEmpty: {
                            message: '排污口是否规范化不能为空！'
                        }
                    }
                },
                // gridCity: {
                //     validators: {
                //         notEmpty: {
                //             message: '执法对象所属网格必须为第三级！'
                //         }
                //     }

                // },
                // gridCounty: {
                //     validators: {
                //         notEmpty: {
                //             message: '执法对象所属网格必须为第三级！'
                //         }
                //     }

                // },
                law_object_county: {
                    validators: {
                        notEmpty: {
                            message: '执法对象所在行政区必须为第三级！'
                        }
                    }

                }, law_object_city: {
                    validators: {
                        notEmpty: {
                            message: '执法对象所在行政区必须为第三级！'
                        }
                    }

                }, law_object_province: {
                    validators: {
                        notEmpty: {
                            message: '执法对象所在行政区必须为第三级！'
                        }
                    }
                },
                gisCoordinateY: {
                    validators: {
                        notEmpty: {
                            message: '纬度不能为空'
                        },
                        stringLength: {
                            max: 50,
                            message: '纬度最大50个字符'
                        },
                        regexp: {
                            regexp: /^[\-\+]?([0-8]?\d{1}\.\d{1,15}|90\.0{1,15})$/,

                            message: '纬度必须在 -90.0～+90.0之间',
                        },

                    }
                },
                gisCoordinateX: {
                    validators: {
                        notEmpty: {
                            message: '经度不能为空'
                        },
                        stringLength: {
                            max: 50,
                            message: '经度 最大50个字符'
                        },
                        regexp: {
                            regexp: /^[\-\+]?(0?\d{1,2}\.\d{1,15}|1[0-7]?\d{1}\.\d{1,15}|180\.0{1,15})$/,
                            message: '经度必须在 -180.0～+180.0之间',
                        },
                    }
                },
                licenseNo: {
                    validators: {
                        stringLength: {
                            max: 50,
                            message: '营业执照证件号最大50个字符'
                        },
                        regexp: {
                            regexp: /^[0-9a-zA-Z]{1,}$/,
                            message: '请输入正确的营业执照证件号',
                        },
                        // callback: {
                        //     message: "营业、组织代码、统一信用代码、税务、事业、社会组织，六项必须选择一项！",
                        //     callback: function (value, validator) {
                        //         var orgCode = $("[name='orgCode']").val();
                        //         var socialCreditCode = $("[name='socialCreditCode']").val();
                        //         var taxationNumber = $("[name='taxationNumber']").val();
                        //         var socialNumber = $("[name='socialNumber']").val();
                        //         var companyNumber = $("[name='companyNumber']").val();
                        //         //【统一社会信用代码】【营业执照证件号】【组织机构代码】【税务登记号】【事业单位证书号】【社会组织登记证号】
                        //         // 组织机构代码 orgCode 9
                        //         // 统一社会信用代码 socialCreditCode 18
                        //         // 营业执照证 --》 工商注册号 licenseNo  100
                        //         // 税务登记号 taxationNumber 15
                        //         // 事业单位证书号 company_number 12
                        //         // 社会组织登记证号 social_number 50
                        //         if ((orgCode == null || orgCode == '') && (socialCreditCode == null || socialCreditCode == '')
                        //             && (taxationNumber == null || taxationNumber == '') && (socialNumber == null || socialNumber == '') && (companyNumber == null || companyNumber == '')) {
                        //             if (value == null || value == '') {
                        //                 return false;
                        //             }
                        //         }
                        //
                        //         return true;
                        //     }
                        // }
                    }
                },
                orgCode: {
                    message: '组织机构代码不能为空',
                    validators: {
                        stringLength: {
                            max: 9,
                            message: '组织机构代码最大9个字符'
                        },
                        regexp: {
                            regexp: /^[0-9a-zA-Z]{1,}$/,
                            message: '请输入正确的组织机构代码',
                        },
                        // callback: {
                        //     message: "营业、组织代码、统一信用代码、税务、事业、社会组织，六项必须选择一项！",
                        //     callback: function (value, validator) {
                        //         var socialCreditCode = $("[name='socialCreditCode']").val();
                        //         var licenseNo = $("[name='licenseNo']").val();
                        //         var taxationNumber = $("[name='taxationNumber']").val();
                        //         var socialNumber = $("[name='socialNumber']").val();
                        //         var companyNumber = $("[name='companyNumber']").val();
                        //         //【统一社会信用代码】【营业执照证件号】【组织机构代码】【税务登记号】【事业单位证书号】【社会组织登记证号】
                        //         // 组织机构代码 orgCode 9
                        //         // 统一社会信用代码 socialCreditCode 18
                        //         // 营业执照证 --》 工商注册号 licenseNo  100
                        //         // 税务登记号 taxationNumber 15
                        //         // 事业单位证书号 company_number 12
                        //         // 社会组织登记证号 social_number 50
                        //         if ((socialCreditCode == null || socialCreditCode == '') && (licenseNo == null || licenseNo == '')
                        //             && (taxationNumber == null || taxationNumber == '') && (socialNumber == null || socialNumber == '') && (companyNumber == null || companyNumber == '')) {
                        //             if (value == null || value == '') {
                        //                 return false;
                        //             }
                        //         }
                        //         return true;
                        //     }
                        // }
                    }
                },

                socialCreditCode: {
                    validators: {
                        notEmpty: {
                            message: '统一社会信用代码不能为空！'
                        },
                        regexp:{
                            regexp:/^(?![A-Z]+$)[0-9A-Z]{18}$/,
                            message: '统一社会信用代码为数字或大写字母共18位组成',
                        },

                    }

                },
                legalPerson: {
                    validators: {
                        notEmpty: {
                            message: '法人代表不能为空！'
                        }
                    }

                },

                taxationNumber: {
                    message: '请输入正确的税务登记号',
                    validators: {
                        regexp: {
                            regexp: /^([A-Za-z0-9]{15})$/,
                            message: '请输入正确的税务登记号',
                        },
                        // callback: {
                        //     message: "营业、组织代码、统一信用代码、税务、事业、社会组织，六项必须选择一项！",
                        //     callback: function (value, validator) {
                        //         var orgCode = $("[name='orgCode']").val();
                        //         var licenseNo = $("[name='licenseNo']").val();
                        //         var socialCreditCode = $("[name='socialCreditCode']").val();
                        //         var socialNumber = $("[name='socialNumber']").val();
                        //         var companyNumber = $("[name='companyNumber']").val();
                        //         //【统一社会信用代码】【营业执照证件号】【组织机构代码】【税务登记号】【事业单位证书号】【社会组织登记证号】
                        //         // 组织机构代码 orgCode 9
                        //         // 统一社会信用代码 socialCreditCode 18
                        //         // 营业执照证 --》 工商注册号 licenseNo  100
                        //         // 税务登记号 taxationNumber 15
                        //         // 事业单位证书号 company_number 12
                        //         // 社会组织登记证号 social_number 50
                        //         if ((orgCode == null || orgCode == '') && (licenseNo == null || licenseNo == '')
                        //             && (socialCreditCode == null || socialCreditCode == '') && (socialNumber == null || socialNumber == '') && (companyNumber == null || companyNumber == '')) {
                        //             if (value == null || value == '') {
                        //                 return false;
                        //             }
                        //         }
                        //
                        //         return true;
                        //     }
                        // }
                    }
                },
                companyNumber: {
                    message: '请输入正确的事业单位证书号',
                    validators: {
                        regexp: {
                            regexp: /^([A-Za-z0-9]{12})$/,
                            message: '请输入正确的事业单位证书号',
                        },
                        // callback: {
                        //     message: "营业、组织代码、统一信用代码、税务、事业、社会组织，六项必须选择一项！",
                        //     callback: function (value, validator) {
                        //         var orgCode = $("[name='orgCode']").val();
                        //         var licenseNo = $("[name='licenseNo']").val();
                        //         var socialCreditCode = $("[name='socialCreditCode']").val();
                        //         var taxationNumber = $("[name='taxationNumber']").val();
                        //         var socialNumber = $("[name='socialNumber']").val();
                        //         //【统一社会信用代码】【营业执照证件号】【组织机构代码】【税务登记号】【事业单位证书号】【社会组织登记证号】
                        //         // 组织机构代码 orgCode 9
                        //         // 统一社会信用代码 socialCreditCode 18
                        //         // 营业执照证 --》 工商注册号 licenseNo  100
                        //         // 税务登记号 taxationNumber 15
                        //         // 事业单位证书号 company_number 12
                        //         // 社会组织登记证号 social_number 50
                        //         if ((orgCode == null || orgCode == '') && (licenseNo == null || licenseNo == '')
                        //             && (socialCreditCode == null || socialCreditCode == '') && (taxationNumber == null || taxationNumber == '') && (socialNumber == null || socialNumber == '')) {
                        //             if (value == null || value == '') {
                        //                 return false;
                        //             }
                        //         }
                        //
                        //         return true;
                        //     }
                        // }
                    }
                },
                socialNumber: {
                    message: '请输入正确的社会组织登记证号',
                    validators: {
                        stringLength: {
                            max: 50,
                            message: '社会组织登记证号最大50个字符'
                        }, regexp: {
                            regexp: /^[0-9a-zA-Z]{1,}$/,
                            message: '请输入正确的社会组织登记证号',
                        },
                        // callback: {
                        //     message: "营业、组织代码、统一信用代码、税务、事业、社会组织，六项必须选择一项！",
                        //     callback: function (value, validator) {
                        //         var orgCode = $("[name='orgCode']").val();
                        //         var licenseNo = $("[name='licenseNo']").val();
                        //         var socialCreditCode = $("[name='socialCreditCode']").val();
                        //         var taxationNumber = $("[name='taxationNumber']").val();
                        //         var companyNumber = $("[name='companyNumber']").val();
                        //         //【统一社会信用代码】【营业执照证件号】【组织机构代码】【税务登记号】【事业单位证书号】【社会组织登记证号】
                        //         // 组织机构代码 orgCode 9
                        //         // 统一社会信用代码 socialCreditCode 18
                        //         // 营业执照证 --》 工商注册号 licenseNo  100
                        //         // 税务登记号 taxationNumber 15
                        //         // 事业单位证书号 company_number 12
                        //         // 社会组织登记证号 social_number 50
                        //         if ((orgCode == null || orgCode == '') && (licenseNo == null || licenseNo == '')
                        //             && (socialCreditCode == null || socialCreditCode == '') && (taxationNumber == null || taxationNumber == '') && (companyNumber == null || companyNumber == '')) {
                        //             if (value == null || value == '') {
                        //                 return false;
                        //             }
                        //         }
                        //
                        //         return true;
                        //     }
                        // }
                    }
                },
                legalPhone: {

                    validators: {
                        // ([0-9]{3,4}-)?[0-9]{7,8} /^0?1[3|4|5|8][0-9]\d{8}$/
                        regexp: {
                            // regexp: /^(((\d{3,4}-{0,1}\d{7,8})|(\d{3,4}){0,1}\d{7,8})|(1\d{10}))$/,
                            regexp: /(^(\d{3,4}-)?\d{7,8})$|(^1[3|4|5|6|7|8|9]\d{9}$)/,
                            message: '请输入正确的法人电话',
                        }
                    }
                },
                legalPerson: {
                    validators: {
                        stringLength: {
                            max: 50,
                            message: '法人代表最大50个字符'
                        },
                        notEmpty: {
                            message: '法人代表不能为空！'
                        }
                    }
                },
                chargePerson: {
                    validators: {
                        notEmpty: {
                            message: '环保负责人不能为空'
                        },
                        stringLength: {
                            max: 100,
                            message: '环保负责人最大100个字符'
                        }
                    }
                },
                chargePersonPhone: {
                    validators: {
                        notEmpty: {
                            message: '环保负责人电话不能为空'
                        },
                        // ([0-9]{3,4}-)?[0-9]{7,8} /^0?1[3|4|5|8][0-9]\d{8}$/
                        regexp: {
                            //regexp: /^(((\d{3,4}-){0,1}\d{7,8})|(1\d{10}))$/,
                            regexp: /(^(\d{3,4}-)?\d{7,8})$|(^1[3|4|5|6|7|8|9]\d{9}$)/,
                            message: '请输入正确的环保负责人电话',
                        }
                    }
                },
                stockCode: {
                    validators: {
                        stringLength: {
                            max: 100,
                            message: '股票代码最大100个字符'
                        }
                    }
                },
                groupCompanyName: {
                    validators: {
                        stringLength: {
                            max: 100,
                            message: '所属集团公司名称最大100个字符'
                        }
                    }
                },
                groupCompanyOrgcode: {
                    validators: {
                        stringLength: {
                            max: 100,
                            message: '所属集团公司组织机构代码最大100个字符'
                        }
                    }
                },
                legalManIdCard: {
                    validators: {
                        callback: {
                            message: '请输入有效的证件号码',
                            callback: function(value, validator, $field) {
                                const cardType = $('[name="cardTypeCode"]').val();
                                if (cardType === '0') { // 身份证
                                    const idCardRegex = /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                                    return idCardRegex.test(value);
                                } else if (cardType === '1') { // 香港居民身份证/澳门居民身份证/台胞证
                                    const hkMacaoTaiwanRegex = /^[A-Za-z0-9\-()（）]{1,11}$/;
                                    return hkMacaoTaiwanRegex.test(value);
                                }
                                return true; // 如果没有选择证件类型，则跳过验证
                            }
                        }
                    }
                },
                chargeManIdCard: {
                    validators: {
                        callback: {
                            message: '请输入有效的证件号码',
                            callback: function(value, validator, $field) {
                                const cardType = $('[name="cardTypeCode"]').val();
                                if (cardType === '0') { // 身份证
                                    const idCardRegex = /^[1-9]\d{5}(18|19|20|(3\d))\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
                                    return idCardRegex.test(value);
                                } else if (cardType === '1') { // 香港居民身份证/澳门居民身份证/台胞证
                                    const hkMacaoTaiwanRegex = /^[A-Za-z0-9\-()（）]{1,11}$/;
                                    return hkMacaoTaiwanRegex.test(value);
                                }
                                return true; // 如果没有选择证件类型，则跳过验证
                            }
                        }
                    }
                },
                groupCompanyStockcode: {
                    validators: {
                        stringLength: {
                            max: 100,
                            message: '所属集团公司股票代码最大100个字符'
                        }
                    }
                },
                objectDesc: {
                    validators: {
                        stringLength: {
                            max: 4000,
                            message: '对象介绍最大4000个字符'
                        }
                    }
                },

            }
        })

        $("#addRandomAttr").click(function () {
            //选中的双随机属性
            var obj = document.getElementsByName('randomAttrs');
            var ids = "";
            var names = "";
            for (var i = 0; i < obj.length; i++) {
                if (obj[i].checked) ids += obj[i].value + ',';
                if (obj[i].checked) names += $.trim($("#attrName" + obj[i].value).html()) + ',';
            }
            ids = ids.substring(0, ids.length - 1);//选中的所有的id
            names = names.substring(0, names.length - 1);//选中的所有的name
            $("#selectedAttrs").val(names);//在输入框显示选中的属性名
            $("#hiddenAttrIds").val(ids);//在隐藏框存上选中的属性id，方便点击保存时获取
            $("#ssjsx").modal('hide');
        });

        //去左右空格;
        function trim(s) {
            return s.replace(/(^\s*)|(\s*$)/g, "");
        }

        //保存并发起任务
        $("#saveObjectAndStartBtn").click(function () {
            //权属行政区
            var power_province = $("#power_province").val();
            var power_city = $("#power_city").val();
            var power_county = $("#power_county").val();
            if (power_county != null && power_county != '') {
                var county = power_county.split(",");
                $("#powerAreaId").val(county[0]);
                $("#powerAreaName").val(county[1]);
            } else if (power_county == ''
                && power_city != '') {
                //选择市
                var city = power_city.split(",");
                $("#powerAreaId").val(city[0]);
                $("#powerAreaName").val(city[1]);
            } else {
                $("#powerAreaId").val('35000000');
                $("#powerAreaName").val("福建省");
            }
            //执法对象所在行政区
            var law_object_province = $(
                "#law_object_province").val();
            var law_object_city = $("#law_object_city")
                .val();
            var law_object_county = $("#law_object_county")
                .val();

            //执法对象所属网格
            // var gridCity = $("#gridCity")
            //     .val();
            // var gridCounty = $("#gridCounty")
            //     .val();
            var gridUcode = $("#gridUcode")
                .val();

            if (law_object_county != null && law_object_county != '') {
                var county = law_object_county.split(",");
                $("#belongAreaId").val(county[0]);
                $("#belongAreaName").val(county[1]);
            } else if (law_object_county == '' && law_object_city != '') {
                //选择市
                var city = law_object_city.split(",");
                $("#belongAreaId").val(city[0]);
                $("#belongAreaName").val(city[1]);
            } else {
                $("#belongAreaId").val('35000000');
                $("#belongAreaName").val("福建省");
            }

            if (law_object_city != 'undefeated' && law_object_city != '') {
                var city = law_object_city.split(",");
                $("#belongAreaCityId").val(city[0]);
                $("#belongAreaCityName").val(city[1]);
            }
            loding('saveObjectAndStartBtn', '保存并发起任务');
            $("#toChangeFormId").data('formValidation').validate();
            var validate = $("#toChangeFormId").data('formValidation').isValid();
            var menuId = '${menuId}';
            if (validate) {
                var options = {
                    url: WEBPATH + '/zfdx/zfdx-save-all?menuId=' + menuId,
                    type: 'post',
                    data: {
                        startAndSavestatus: 1
                    },
                    success: function (data) {
                        if (data.meta.statusCode == "200") {
                            swal({
                                    title: "提示",
                                    text: data.meta.message,
                                    type: "success"
                                },
                                function (isConfirm) {
                                    var purl = '${preUrl}';
                                    if (purl == '/zfdx/zfdx-all') {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=4AE820E38F226585E055000000000001', null);
                                    } else if (purl == '/zfdx/zfdx-individual') {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=4AE820E38F246585E055000000000001', null);
                                    } else if (purl == '/zfdx/zfdx-individual-Three') {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=4AE820E38F256585E055000000000001', null);
                                    } else if (purl == '/zfdx/zfdx-natureReserve') {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=4AE820E38F266585E055000000000001', null);
                                    } else if (purl == '/zfdx/zfdx-stray') {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=4AE820E38F276585E055000000000001', null);
                                    } else {
                                        business.addMainContentParserHtml(
                                            WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + data.data.lawObjectId + '&menuId=' + '${menuId}', null);
                                    }

                                    return false;
                                });
                            return false;
                        } else if (data.meta.statusCode == "400") {
                            /* swal("提示", data.meta.message, "error"); */
                            swal({
                                title: "提示",
                                text: data.meta.message,
                                type: "error",
                                allowOutsideClick: true
                            });
                        } else {
                            /* swal("提示", "保存发起任务失败！", "error"); */
                            swal({
                                title: "提示",
                                text: "保存发起任务失败！",
                                type: "error",
                                allowOutsideClick: true
                            });
                        }
                    },
                    error: function () {
                        /* swal("操作失败", "保存发起任务失败!", "error"); */
                        swal({
                            title: "操作失败",
                            text: "保存发起任务失败！",
                            type: "error",
                            allowOutsideClick: true
                        });
                    }
                }
                $('#toChangeFormId').ajaxSubmit(options);
            } else if (validate == null) {
                //表单未填写
                $("#toChangeFormId").data('formValidation').validate();
                return false;
            }
        });
        //跳转list也
        $("#skipPersonagePage").click(
            function () {
                business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-all', null);
            })
        //保存 提交表单
        $("#submitformId").click(function () {

            //权属行政区
            var power_province = $("#power_province").val();
            var power_city = $("#power_city").val();
            var power_county = $("#power_county").val();
            if (power_county != null && power_county != '') {
                var county = power_county.split(",");
                $("#powerAreaId").val(county[0]);
                $("#powerAreaName").val(county[1]);
            } else if (power_county == ''
                && power_city != '') {
                //选择市
                var city = power_city.split(",");
                $("#powerAreaId").val(city[0]);
                $("#powerAreaName").val(city[1]);
            } else {
                $("#powerAreaId").val('35000000');
                $("#powerAreaName").val("福建省");
            }
            //执法对象所在行政区
            var law_object_province = $(
                "#law_object_province").val();
            var law_object_city = $("#law_object_city")
                .val();
            var law_object_county = $("#law_object_county")
                .val();
            if (law_object_county != null
                && law_object_county != '') {
                var county = law_object_county.split(",");
                $("#belongAreaId").val(county[0]);
                $("#belongAreaName").val(county[1]);
            } else if (law_object_county == ''
                && law_object_city != '') {
                //选择市
                var city = law_object_city.split(",");
                $("#belongAreaId").val(city[0]);
                $("#belongAreaName").val(city[1]);
            } else {
                $("#belongAreaId").val('35000000');
                $("#belongAreaName").val("福建省");
            }
            if (law_object_city != 'undefeated'
                && law_object_city != '') {
                var city = law_object_city.split(",");
                $("#belongAreaCityId").val(city[0]);
                $("#belongAreaCityName").val(city[1]);
            }
            loding('submitformId', '保存');
            $("#toChangeFormId").data('formValidation').validate();
            var validate = $("#toChangeFormId").data('formValidation').isValid();
            var menuId = '${menuId}';
            if (validate) {
                var options = {
                    url: WEBPATH + '/zfdx/zfdx-save-all?menuId=' + menuId,
                    type: 'post',
                    success: function (data) {
                        if (data.meta.statusCode == "200") {
                            swal(
                                {
                                    title: "保存成功",
                                    text: "",
                                    type: "success"
                                },
                                function (isConfirm) {
                                    var preUrl = '${preUrl}';
                                    if (preUrl != null && preUrl != '' && preUrl != 'undefined' && preUrl != '/sysIndex/indexHome') {
                                        if (preUrl == '/zfdx/zfdx-all') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=4AE820E38F226585E055000000000001", $("#searchForm").serialize());
                                        } else if (preUrl == '/zfdx/zfdx-individual') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=4AE820E38F246585E055000000000001", $("#searchForm").serialize());
                                        } else if (preUrl == '/zfdx/zfdx-individual-Three') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=4AE820E38F256585E055000000000001", $("#searchForm").serialize());
                                        } else if (preUrl == '/zfdx/zfdx-natureReserve') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=4AE820E38F266585E055000000000001", $("#searchForm").serialize());
                                        } else if (preUrl == '/zfdx/zfdx-stray') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=4AE820E38F276585E055000000000001", $("#searchForm").serialize());
                                        } else {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=" + '${menuId}', $("#searchForm").serialize());
                                        }

                                    } else {
                                        if (preUrl == '/zfdx/zfdx-all') {
                                            business.addMainContentParserHtml(WEBPATH + preUrl + "?menuId=4AE820E38F226585E055000000000001", null);
                                        } else {
                                            business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-enterprise?menuId=' + '${menuId}', null);
                                        }
                                    }
                                });
                        } else if (data.meta.statusCode == "400") {
                            /* swal("提示", data.meta.message, "error"); */
                            swal({
                                title: "提示",
                                text: data.meta.message,
                                type: "error",
                                allowOutsideClick: true
                            });
                        } else {
                            /* swal("提示", "保存失败！", "error"); */
                            swal({
                                title: "提示",
                                text: "保存失败！",
                                type: "error",
                                allowOutsideClick: true
                            });
                        }
                    },
                    error: function () {
                        /* swal("操作失败", "保存失败!", "error"); */
                        swal({
                            title: "操作失败",
                            text: "保存失败!",
                            type: "error",
                            allowOutsideClick: true
                        });
                    }
                }
                $('#toChangeFormId').ajaxSubmit(options);
            } else if (validate == null) {
                //表单未填写
                $("#toChangeFormId").data('formValidation').validate();
                return false;
            }
        });
    })

    function changeLawObjectType(lawObjectId) {
        $.ajax({
            type: "post",
            url: WEBPATH + "/zfdx/changeLawObjectType",
            data: {
                lawObjectId: lawObjectId
            },
            dataType: "json",
            success: function (data) {
                if (data.meta.code == "200") {
                    var options = {
                        remote: encodeURI(WEBPATH + '/zfdx/showLawObjType?lawObjType=' + data.data.typeCode + '&lawObjId=' + data.data.id)
                    };
                    $("#zfdxxg").modal(options);
                } else if (data.meta.code == "400001") {
                    swal("提示", "有未办结的任务,不可修改", "warning");
                } else if (data.meta.code == "400002") {
                    swal("提示", "有待下发的双随机任务,不可修改", "warning");
                } else if (data.meta.code == "400003") {
                    swal("提示", "有双随机属性标签,不可修改", "warning");
                }
            }
        });
        //business.addMainContentParserHtml(WEBPATH + '/zfdx/changeLawObjectType?lawObjectId='+lawObjectId, null);
    }

    function confirmLawEnforceType(orignalLawObjType, lawObjId) {
        var choosedLawObjType = $("#lawObjType").val();
        if (choosedLawObjType == "") {
            swal({
                title: "提示",
                text: "执法对象类型不能为空",
                type: "warning",
                allowOutsideClick: true
            });
        } else {
            $("#zfdxxg").modal('hide');
            $(".modal-backdrop").remove();//隐藏模态框
            $("body").removeClass('modal-open');
            var menuId = "";
            if (choosedLawObjType == 1) {
                menuId = "4AE820E38F236585E055000000000001";
            } else if (choosedLawObjType == 2) {
                menuId = "4AE820E38F246585E055000000000001";
            } else if (choosedLawObjType == 3) {
                menuId = "4AE820E38F256585E055000000000001";
            } else if (choosedLawObjType == 4) {
                menuId = "4AE820E38F266585E055000000000001";
            } else {
                menuId = "4AE820E38F276585E055000000000001";
            }

            business.addMainContentParserHtml(WEBPATH
                + '/zfdx/newLawObjectPage?type=' + choosedLawObjType + '&id=' + lawObjId + '&menuId=' + menuId, null);

        }
    }

    function setMapValue() {
        if ($("#new_map_txt").html() == "") {
            swal("提示", "你还没选择相应的坐标点。", "warning");
            return false;
        }
        var mapJW = $("#new_map_txt").html().split(",");
        $("#mapJD").val(mapJW[0]);
        $("#mapWD").val(mapJW[1]);
        $('#toChangeFormId').formValidation('revalidateField', 'gisCoordinateX');
        $('#toChangeFormId').formValidation('revalidateField', 'gisCoordinateY');
        $('#myModal').modal('hide')
    }

    // 百度 地图
    function createMapClick(address, gisCoordinateX, gisCoordinateY) {
        var urlBaidu = encodeURI(WEBPATH + '/taskManager/baidu-ditu?address=' + address + '&gisCoordinateX=' + gisCoordinateX + '&gisCoordinateY=' + gisCoordinateY);
        var options = {
            remote: urlBaidu
        };
        $('#myModal').modal(options);
    }

    //返回上一步主菜单
    function goBack(preUrl) {
        if (preUrl != null && preUrl != '' && preUrl != 'undefined') {
            business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=" + '${menuId}', $("#searchForm").serialize());
        } else {
            swal({
                title: "提示！",
                text: "返回信息错误，请刷新后重试。",
                type: "error"
            })
        }
    }
</script>
<script type="text/javascript">
    $(function () {
        //监听回退键
        business.listenBackSpace();
    });
</script>

<script>
    $(function () {
        $("#folder").popover('destroy').popover();
    });
    var flag;//定时发送请求任务
    var time;//时间戳
    var uuid = business.guid();//每个页面一个单独的uuid

    $("#folder").on('shown.bs.popover', function () {
        //开启遮罩，重新打开持续请求后台坐标数据
        business.openwait('1');//传入1，表示不要中间的转圈
        clearInterval(flag);
        //每隔1.5秒请求后台坐标数据
        flag = setInterval(function () {
            $.ajax({
                type: "POST",
                async: false,
                url: WEBPATH + '/taskManager/getPositionInfo',
                data: {uuid: uuid, time: time},
                error: function (request) {
                    //swal("错误!","获取位置信息失败！", "error");
                    //一次请求错误不做处理，等待30分钟后超时
                },
                success: function (data) {
                    if (data.meta.statusCode == '200') {
                        var position = data.data;
                        if (position != null && position != '' && position != 'undefined') {
                            var gisCoordinateX = position.split(',')[0];
                            var gisCoordinateY = position.split(',')[1];
                            $("[name='gisCoordinateX']").val(gisCoordinateX);
                            $("[name='gisCoordinateY']").val(gisCoordinateY);
                            clearInterval(flag);
                            time = '';
                            $('#appQrCode').empty();
                            $('#folder').popover('hide');
                            document.getElementById('getPositionSuccess').style.display = '';
                            setTimeout(function () {
                                document.getElementById('getPositionSuccess').style.display = 'none';
                            }, 5000)
                        }
                    } else {
                        //swal("错误!","获取位置信息失败！", "error");
                        //一次请求错误不做处理，等待30分钟后超时
                    }
                }
            });
        }, 1500);
    })

    $('#folder').on('show.bs.popover', function () {
        setTimeout(function () {
            //$('#folder').popover('hide');
            $('#appQrCode').empty();
            $('#appQrCode').html("正在生成二维码……");
            setSysTime();
            buildQR(time, uuid);
        }, 10);
    })
    $('#folder').on('hide.bs.popover', function () {
        //隐藏时关闭遮罩，关闭间隔请求，关闭超时提示，清空二维码
        business.closewait();
        clearInterval(flag);
        $('#appQrCode').empty();
    })

    //监听点击事件
    $('body').on('click', function (event) {
        var target = $(event.target);
        if (!target.hasClass('popover')
            && target.parent().attr('id') != 'appQrCode'
            && target.parent('.popover-content').length === 0
            && target.parent('.myPopover').length === 0
            && target.parent('.popover-title').length === 0
            && target.parent('.popover').length === 0 && target.attr("id") !== "folder") {
            $('#folder').popover('hide');
        }
        if (target.hasClass('close')) {
            $('#folder').popover('hide');
        }
    });

    //刷新二维码
    function refreshQR() {
        setSysTime();
        buildQR(time, uuid);
    }

    //设置time为系统时间
    function setSysTime() {
        $.ajax({
            type: "POST",
            async: false,//同步获取时间戳
            url: WEBPATH + '/api/auth/getCurTime',
            error: function (request) {
                swal("提示!", "获取系统时间失败，二维码生成失败！", "warning");
                $('#folder').popover('hide');
            },
            success: function (data) {
                if (data.meta.httpStatusCode == '200') {
                    time = data.data.curtime;
                } else {
                    swal("提示!", "获取系统时间失败，二维码生成失败！", "warning");
                    $('#folder').popover('hide');
                }
            }
        });
    }

    //生成二维码
    function buildQR(time, uuid) {
        $('#appQrCode').empty();
        var QRtext = {appLogo: 'CHN-FJ', type: '010', time: time, uuid: uuid, url: '/api/taskManager/appGISInfo'};
        $('#appQrCode').qrcode({
            render: 'canvas',
            text: JSON.stringify(QRtext),
            height: 225,
            cache: false,
            width: 225,
            foreground: "#23b7e5"
        });
    }
</script>
<%--新加字段 管理类型、发证--%>
<script type="text/javascript">
    $(function(){
        var ismanageCode = '${entity.managementType}';
        if(ismanageCode == '登记管理' || ismanageCode == ''){
            $("#manageShow").hide();
            $("#licenseNumber").val('');
            $("#certifyingAuthority").val('');
            $("#startDateStr").val('');
            $("#endDateStr").val('');
            $("#isCertification").val('');
        }else{
            $("#manageShow").show();
        }
        var isCertificationCode = '${entity.isCertification}';
        if(isCertificationCode == '已发证'){
            $(".isCertificationShow").show();
        }else{
            $(".isCertificationShow").hide();
            $("#licenseNumber").val('');
            $("#certifyingAuthority").val('');
            $("#startDateStr").val('');
            $("#endDateStr").val('');
        }
        $("#isCertification").change(function(){
            if($(this).val() == "已发证"){
                $(".isCertificationShow").show();
            }else{
                $(".isCertificationShow").hide();
                $("#licenseNumber").val('');
                $("#certifyingAuthority").val('');
                $("#startDateStr").val('');
                $("#endDateStr").val('');
            }
        })
    })
    function manageSelect(value) {
        if(value == '登记管理' || value == '请选择'){
            $("#manageShow").hide();
            $("#licenseNumber").val('');
            $("#certifyingAuthority").val('');
            $("#startDateStr").val('');
            $("#endDateStr").val('');
            $("#isCertification").val('');
        }else{
            $("#manageShow").show();
        }
    }
</script>
</body>
</html>
