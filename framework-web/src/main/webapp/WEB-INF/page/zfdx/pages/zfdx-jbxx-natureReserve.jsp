<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>


<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta charset="utf-8">
 
</head>
			<div class="main-container">
			<div class="padding-md">
				
                <div class="smart-widget widget-blue">
						<div class="smart-widget-header">
							<i class="fa fa-comment"></i> 福州市麻雀保护区
							<span class="smart-widget-option">
								<span class="refresh-icon-animated">
									<i class="fa fa-circle-o-notch fa-spin"></i>
								</span>
	                            <a href="#" class="widget-toggle-hidden-option">
	                                <i class="fa fa-cog"></i>
	                            </a>
	                            <a href="#" class="widget-collapse-option" data-toggle="collapse">
	                                <i class="fa fa-chevron-up"></i>
	                            </a>
	                            <a href="#" class="widget-refresh-option">
	                                <i class="fa fa-refresh"></i>
	                            </a>
	                            
	                        </span>
						</div>
						<div class="smart-widget-inner">
							<div class="smart-widget-hidden-section">
								<ul class="widget-color-list clearfix">
									<li style="background-color:#20232b;" data-color="widget-dark"></li>
									<li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
									<li style="background-color:#23b7e5;" data-color="widget-blue"></li>
									<li style="background-color:#2baab1;" data-color="widget-green"></li>
									<li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
									<li style="background-color:#fbc852;" data-color="widget-orange"></li>
									<li style="background-color:#e36159;" data-color="widget-red"></li>
									<li style="background-color:#7266ba;" data-color="widget-purple"></li>
									<li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
									<li style="background-color:#fff;" data-color="reset"></li>
								</ul>
							</div>
							<div class="widget-tab clearfix">
								<ul class="tab-bar">
									<li class="active"><a href="#jbxx" data-toggle="tab"><i class="fa fa-pencil"></i> 基本信息</a></li>
									<li class=""><a href="#hjjc" data-toggle="tab"><i class="fa fa-file-text-o"></i> 环境监察</a></li>
								</ul>
							</div>
							<div class="smart-widget-body">
								<div class="tab-content">
									<div class="tab-pane fade in active" id="jbxx">
                                    	<div class="form-horizontal">
                                                <div class="text-right m-top-md">
                                                	<button class="btn btn-info" onClick="javascript:window.location.href='../zfdx-natureReserve.html'" type="submit">返回列表</button>
                                                    <button class="btn btn-info" onClick="javascript:window.location.href='../rwfp.html'">发起现场执法</button>
                                                    <button class="btn btn-info" onClick="javascript:window.location.href='zfdx-jbxx-bhqxg.html'" type="submit">修改</button>
                                                    <button class="btn btn-info">删除</button>

                                                </div>
                                                
                                                    
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        对象类别
                                                    </label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">自然保护区</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputEmail3" class="col-md-3 control-label">
                                                        执法对象ID</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">913501006113030273</div>

                                                    </div>
                                                </div>		
                                                <div class="form-group">
                                                    <label for="inputEmail3" class="col-md-3 control-label">
                                                        污染源编码</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">913501006113030273</div>

                                                    </div>
                                                </div>	
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        名称
                                                    </label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">福州市麻雀保护区</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        执法对象所在行政区
                                                    </label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">福建省福州市晋安区</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        地址</label>
                                                    <div class="col-md-6">
                                                        <div style="margin-top: 8px;">
                                                            福州市晋安区连江路73号（办公地）福州市福清江阴工业集中区（生产地）</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        权属行政区
                                                    </label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">福州市</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        地理坐标</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">118.983372 E
                                                            26.2613841 N</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        保护区范围描述</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">50平方公里</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                    主要保护对象</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">天鹅</div>
                                                    </div>
                                                </div>                                                   
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        是否设立专门的管理机构</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">是</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        管理机构名称</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">福州市麻雀保护区管理委员会</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        管理机构组织机构代码</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">无</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        管理机构统一社会信用代码</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">1454545454</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        管理机构负责人</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">张三</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        管理机构负责人联系方式</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">无</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        联系人</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">无</div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label for="inputtext3" class="col-md-3 control-label">
                                                        联系方式</label>
                                                    <div class="col-md-3">
                                                        <div style="margin-top: 8px;">无</div>
                                                    </div>
                                                </div>
                                        </div>
									</div>
									<div class="tab-pane fade" id="hjjc">
                                    	<div class="form-horizontal">
                                            <div class="form-group">
                                                <label for="inputtext3" class="col-lg-1 col-md-2 control-label">
                                                    开始时间
                                                </label>
                                                <div class="col-lg-2 col-md-2">
                                                    <input type="text" placeholder="开始时间" class="form-control" data-parsley-required="true">
                                                </div>
                                                <label for="inputtext3" class="col-lg-1 col-md-2 control-label">
                                                    结束时间
                                                </label>
                                                <div class="col-lg-2 col-md-2">
                                                    <input type="text" placeholder="结束时间" class="form-control" data-parsley-required="true">
                                                </div>
                                                <div class="col-lg-2 col-md-2">
                                                    <button class="btn btn-info">查询</button>
                                                </div>
                                            </div>
                                            <hr>
                                            <div class="padding-xs">温馨提示：点击工单摘要可以查看任务详情。</div>
                                            <div class="row padding-md">
                                                <div class="col-lg-8 col-md-8">
                                                    <div class="timeline-wrapper clearfix">
                                                        <div class="timeline-year font-semi-bold">
                                                            2017
                                                        </div>
                                                        <div class="timeline-row alt">
                                                            <div class="timeline-item">
                                                                <div class="timeline-icon">
                                                                    3月
                                                                </div>
                                                                <div class="timeline-item-inner" onClick="javascript:window.location.href='../xczf.html'" style="cursor:pointer;">
                                                                    <div class="timeline-body">
                                                                        <div class="font-16 font-semi-bold padding-xs">任务编号:FJFZJA201XXXXXXX</a></div>
                                                                        <div class="font-14 padding-xs">任务来源：专项检查</div>
                                                                        <div class="font-14 padding-xs">检查开始时间: 2016年x月x日</div>
                                                                        <div class="font-14 padding-xs">检查结束时间: 2016年x月x日</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="timeline-row alt">
                                                            <div class="timeline-item">
                                                                <div class="timeline-icon">
                                                                    5月
                                                                </div>
                                                                <div class="timeline-item-inner">
                                                                    <div class="timeline-body">
                                                                        <div class="font-16 font-semi-bold padding-xs">任务编号:FJFZJA201XXXXXXX</div>
                                                                        <div class="font-14 padding-xs">任务来源：专项检查</div>
                                                                        <div class="font-14 padding-xs">检查开始时间: 2016年x月x日</div>
                                                                        <div class="font-14 padding-xs">检查结束时间: 2016年x月x日</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="timeline-row alt">
                                                            <div class="timeline-item">
                                                                <div class="timeline-icon">
                                                                    6月
                                                                </div>
                                                                <div class="timeline-item-inner">
                                                                    <div class="timeline-body">
                                                                        <div class="font-16 font-semi-bold padding-xs">任务编号:FJFZJA201XXXXXXX</div>
                                                                        <div class="font-14 padding-xs">任务来源：专项检查</div>
                                                                        <div class="font-14 padding-xs">检查开始时间: 2016年x月x日</div>
                                                                        <div class="font-14 padding-xs">检查结束时间: 2016年x月x日</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>                                                
                                        </div>
									</div>
								</div>
							</div>
						</div>
					</div>
                
			</div>
		</div>

		
		<script>

			  document.addEventListener('DOMContentLoaded', function () {
        var multiSlides = document.querySelector('.js_multislides');

        lory(multiSlides, {
            infinite: 4,
            slidesToScroll: 4
        });
    });
       
    </script>
    	   <script type="text/javascript">
		 $(function(){
				//监听回退键
				business.listenBackSpace();	  
			 });
	   </script>
</body>
</html>