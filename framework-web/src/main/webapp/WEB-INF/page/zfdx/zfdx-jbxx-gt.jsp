<%@ page language="java" import="java.util.*" pageEncoding="UTF-8" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil" %>
<%
    String doc = PropertiesHandlerUtil.getValue("STANDARDID_DOC_URL", "supervise_monitor_overproof");
%>
<c:set var="docUrl"><%=doc%>
</c:set>
<%
    String eImageUrl = PropertiesHandlerUtil.getValue("ENTERPRISE_IMAGE_URL", "supervise_monitor_overproof");
%>
<c:set var="eImageUrl"><%=eImageUrl%>
</c:set>
<%
    String fastdfs_server = PropertiesHandlerUtil.getValue("fastdfs.nginx.ip", "fastdfs");
%>
<c:set var="fastdfsUrl"><%=fastdfs_server%>
</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
</head>
<body>
<div class="main-container">
    <input type="hidden" value="${fastdfsUrl}" id="fastdfsServer">
    <div class="padding-md">
        <div class="smart-widget widget-blue">
            <div class="smart-widget-header font-16">
                <i class="fa fa-comment"></i> ${lawObject.objectName }
                <span class="smart-widget-option">
								<span class="refresh-icon-animated">
									<i class="fa fa-circle-o-notch fa-spin"></i>
								</span>	
	                       		<div style="margin:-6px 0 0 0;">
	                       			<c:if test="${not empty lawObject.standenterid}">
                                        <!-- 企业画像开始 -->
                                        <%-- <a href="${eImageUrl}=${lawObject.standenterid}" target="_blank"><img src="/static/img/zfdx-qyhx.png" />     --%>
                                        <!-- 企业画像结束 -->
                                        <a href="${docUrl}&tid=0&enterid=${lawObject.standenterid}" target="_blank"><img
                                                src="/static/img/lsjcsj-button.png"/>
								  		</a>
                                    </c:if>
                                	 <a href="#" onclick="goBack('${preUrl}')"><i class="fa fa-arrow-right"></i> 返回</a>     
                                </div> 
	                        </span>

                <!--<div class="padding-sm" style="float:right;">
								<button type="button" class="btn btn-default btn-block"  onclick="goBack('${preUrl}')">返回</button>
							</div>-->

            </div>
            <div class="smart-widget-inner">
                <div class="widget-tab clearfix">
                    <ul class="tab-bar">
                        <li class="active"><a href="#jbxx" data-toggle="tab"><i class="fa fa-pencil"></i> 基本信息</a></li>
                        <li class=""><a href="#xczf" id="spotInspectionBtn" data-toggle="tab"><i
                                class="fa fa-file-text-o"></i>执法信息</a></li>
                        <li class=""><a href="#ajxx" id="caseInfoBtn" data-toggle="tab"><i
                                class="fa fa-file-text-o"></i> 案件信息</a></li>
                        <!-- 这个在下面有一个加载引入isSuperviseChickModel.jsp，里面会有 loadDiscreditlist 方法  -->
                        <li class=""><a href="#isSuperviseModel" id="" onclick="loadDiscreditlist()"
                                        data-toggle="tab"><i class="fa fa-file-text-o"></i> 失信信息</a></li>
                        <li class=""><a href="#xcxc" data-toggle="tab" onclick="loadObjectCommentTree()"><i
                                class="fa fa-user-circle"></i> 现场巡查</a></li>
                    </ul>
                </div>
                <div class="smart-widget-body">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="jbxx">
                            <div class="form-horizontal">
                                <div class="text-right m-top-md">
                                    <!--<button class="btn btn-info" id="skipPersonagePage" type="submit">返回列表</button>-->
                                    <button class="btn btn-info" id="startTask">发起现场执法</button>
                                    <c:forEach items="${res}" var="res" varStatus="status">
                                        <c:if test="${res.resourceDesc=='SWUPDATE'}">
                                            <button class="btn btn-info" id="updateLawObjectgt">修改</button>
                                        </c:if>
                                        <c:if test="${res.resourceDesc=='SWSC2'}">
                                            <button class="btn btn-info" id="delObjectBtn">删除</button>
                                        </c:if>
                                    </c:forEach>
                                </div>


                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>对象类别
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">个体、三无、小三产</div>
                                    </div>
                                </div>
                                <input type="hidden" id="mainId" value="${lawObject.id }">
                                <%--   <div class="form-group">
                                      <label for="inputEmail3" class="col-md-3 control-label">
                                          执法对象ID</label>
                                      <div class="col-md-3">
                                          <div style="margin-top: 8px;">${lawObject.id}</div>

                                      </div>
                                  </div>	 --%>
                                <div class="form-group">
                                    <label for="inputEmail3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>污染源编码</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.objectNumber }</div>

                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>名称
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.objectName }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>执法对象所在行政区
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.belongAreaName }</div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>执法对象所属网格</label>
                                    <div class="col-md-6">
                                        <div style="margin-top: 8px;">
                                            <span v-if="lawObject.gridCounty!=null">${lawObject.gridCounty.split(",")[1]}</span>
                                            <span v-else-if="lawObject.gridStreet!=null">${lawObject.gridStreet.split(",")[1] }</span>
                                            <span v-else="lawObject.gridCommunity!=null">${lawObject.gridCommunity.split(",")[1]}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>执法对象所属网格编码</label>
                                    <div class="col-md-6">
                                        <div style="margin-top: 8px;">
                                            ${lawObject.gridUcode }
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>地址</label>
                                    <div class="col-md-6">
                                        <div style="margin-top: 8px;">
                                            ${lawObject.address }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        权属行政区
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.powerAreaName }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>地理坐标</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.gisCoordinateX } E
                                            ${lawObject.gisCoordinateY } N
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        统一社会信用代码</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.socialCreditCode }">无</c:if>
                                            ${lawObject.socialCreditCode }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        营业执照证件号</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.licenseNo }">无</c:if>
                                            ${lawObject.licenseNo }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>行业类型</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.industryTypeName }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        组织机构代码</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.orgCode }">无</c:if>
                                            ${lawObject.orgCode }</div>
                                    </div>
                                </div>


                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">税务登记号</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.taxationNumber }">无</c:if>
                                            ${lawObject.taxationNumber }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">事业单位证书号</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.companyNumber }">无</c:if>
                                            ${lawObject.companyNumber }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">社会组织登记证号</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty  lawObject.socialNumber }">无</c:if>
                                            ${lawObject.socialNumber }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        证件类型</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.cardTypeName }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        证件号码</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.cardNumber }</div>
                                    </div>
                                </div>


                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span> 联系人</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.legalPerson }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>联系方式</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.legalPhone }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>是否重点源</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:choose>
                                                <c:when test="${lawObject.isKeySource=='1'}">
                                                    是
                                                </c:when>
                                                <c:when test="${lawObject.isKeySource=='0'}">
                                                    否
                                                </c:when>
                                                <c:otherwise>
                                                    无
                                                </c:otherwise>
                                            </c:choose>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        双随机属性
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            <c:if test="${empty randomAttrNames }">无</c:if>
                                            ${randomAttrNames }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        所属流域代码
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            ${lawObject.WSCD }</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        所属流域名称
                                    </label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">
                                            ${lawObject.WSNM}</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>污染源类别</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.pollutionSourceTypeName}</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>适用排污许可行业技术规范</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.sypwxkhyjsgfName}</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>固定污染源排污许可分类</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.sewageClassifyName}</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>管理类型</label>
                                    <div class="col-md-3">
                                        <div style="margin-top: 8px;">${lawObject.managementType}</div>
                                    </div>
                                </div>
                                <div id="manageShow">
                                    <div class="form-group">
                                        <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>是否发证</label>
                                        <div class="col-md-3">
                                            <div style="margin-top: 8px;">
                                                <c:choose>
                                                    <c:when test="${lawObject.isCertification=='已发证'}">
                                                        已发证
                                                    </c:when>
                                                    <c:when test="${lawObject.isCertification=='未发证'}">
                                                        未发证
                                                    </c:when>
                                                    <c:otherwise>
                                                        无
                                                    </c:otherwise>
                                                </c:choose>
                                            </div>
                                        </div>
                                    </div>
                                    <c:if test="${lawObject.isCertification == '已发证'}">
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>许可证编号</label>
                                            <div class="col-md-3">
                                                <div style="margin-top: 8px;">${lawObject.licenseNumber}</div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>发证机构</label>
                                            <div class="col-md-3">
                                                <div style="margin-top: 8px;">
                                                    <c:choose>
                                                        <c:when test="${lawObject.certifyingAuthority=='1'}">
                                                            县级环保部门
                                                        </c:when>
                                                        <c:when test="${lawObject.certifyingAuthority=='0'}">
                                                            市级环保部门
                                                        </c:when>
                                                        <c:otherwise>
                                                            无
                                                        </c:otherwise>
                                                    </c:choose>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>发证时间</label>
                                            <div class="col-md-3">
                                                <div style="margin-top: 8px;">
                                                        ${lawObject.startDateStr}
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputtext3" class="col-md-3 control-label"><span style="color: red;">*</span>截止时间</label>
                                            <div class="col-md-3">
                                                <div style="margin-top: 8px;">
                                                        ${lawObject.endDateStr}
                                                </div>
                                            </div>
                                        </div>
                                    </c:if>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>是否属于“小散乱污”企业</label>
                                    <div class="col-md-3" style="margin-top: 8px;">
                                        <c:choose>
                                            <c:when test="${lawObject.xslw=='1'}">
                                                是
                                            </c:when>
                                            <c:when test="${lawObject.xslw=='0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                无
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>是否信用评价涉及单位</label>
                                    <div class="col-md-3" style="margin-top: 8px;">
                                        <c:choose>
                                            <c:when test="${lawObject.isSocialCreditUnit=='1'}">
                                                是
                                            </c:when>
                                            <c:when test="${lawObject.isSocialCreditUnit=='0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                无
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>


                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">
                                        <span style="color: red;">*</span>是否注销</label>
                                    <div class="col-md-3" style="margin-top: 8px;">
                                        <c:choose>
                                            <c:when test="${lawObject.isCancelled=='1'}">
                                                是
                                            </c:when>
                                            <c:when test="${lawObject.isCancelled=='0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                无
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="inputtext3" class="col-md-3 control-label">是否正面清单企业</label>
                                    <div class="col-md-3" style="margin-top: 8px;">
                                        <c:choose>
                                            <c:when test="${lawObject.ispositive=='1'}">
                                                是
                                            </c:when>
                                            <c:when test="${lawObject.ispositive=='0'}">
                                                否
                                            </c:when>
                                            <c:otherwise>
                                                无
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <jsp:include page="../model/environmentChickModel.jsp"></jsp:include>
                        <!--失信信息页面 -->
                        <jsp:include page="model/isSuperviseChickModel.jsp"></jsp:include>
                        <!--现场巡查页面 -->
                        <jsp:include page="model/localCheck.jsp"></jsp:include>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script>
    //返回上一步主菜单
    function goBack(preUrl) {
        if (preUrl != null && preUrl != '' && preUrl != 'undefined') {
            if (preUrl == '/zfdx/zfdx-all' || preUrl == '/zfdx/zfdx-supervise') {
                business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=" + '${menuIdAll}', $("#searchForm").serialize());
            } else {
                business.addMainContentParserHtml(WEBPATH + preUrl + "?back=1&menuId=" + '${menuId}', $("#searchForm").serialize());
            }

        } else {
            business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-all' + "?menuId=" + '4AE820E38F226585E055000000000001', $("#searchForm").serialize());
            // swal({
            //     title: "提示！",
            //     text: "返回信息错误，请刷新后重试。",
            //     type: "error"
            // })
        }
    }

    $("#updateLawObjectgt").click(function () {
        var id = $("#mainId").val();
        var preUrl = '${preUrl}';
        if (preUrl == '/zfdx/zfdx-supervise' || preUrl == '/zfdx/zfdx-all') {
            business.addMainContentParserHtml(WEBPATH + '/zfdx/newLawObjectPage?type=3&id=' + id + '&backAllMenuId=' + '${menuIdAll}', null);
        } else {
            business.addMainContentParserHtml(WEBPATH + '/zfdx/newLawObjectPage?type=3&id=' + id + '&menuId=' + '${menuId}', null);
        }

    });
    //跳转list也
    $("#skipPersonagePage").click(function () {
        business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-individual-Three', null);
    });
    //	lawObjectStatus对象条件查询
    $("#lawObjectSearchBtn").click(function () {
        $('#enterpriseObjectTable').bootstrapTable('refresh');
    });
    //查询对象细信息
    $("#checkObject").click(function () {
        var id = $("#mainId").val();
        business.addMainContentParserHtml(WEBPATH + '/zfdx/rwfp?lawObjectId=' + id, null);
    })
    //发起现场执法
    $("#startTask").click(function () {
        var id = $("#mainId").val();
        if ('${preUrl}' == '/zfdx/zfdx-all' || '${preUrl}' == '/zfdx/zfdx-supervise') {
            business.addMainContentParserHtml(WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + id + '&menuId=' + '${menuIdAll}', null);
        } else {
            business.addMainContentParserHtml(WEBPATH + '/jcbl/startLocaleChick?lawObjectId=' + id + '&menuId=' + '${menuId}', null);
        }
    })
    //删除执法对象操作
    $("#delObjectBtn").click(function () {
        var id = $("#mainId").val();
        swal({
            title: "提示 ",
            text: "确定要删除所选择的记录吗？",
            type: "warning",
            showCancelButton: true,
            confirmButtonColor: "#DD6B55",
            confirmButtonText: "是的，我要删除！",
            cancelButtonText: "让我再考虑一下",
            closeOnConfirm: false,
            closeOnCancel: false
        }, function (isConfirm) {
            if (isConfirm) {
                $.ajax({
                    type: "post",
                    url: WEBPATH + "/zfdx/delObjectByIds",
                    data: {
                        ids: id
                    },
                    dataType: "json",
                    success: function (data) {
                        if (data.meta.statusCode == "200") {
                            swal({
                                title: "提示",
                                text: data.meta.message,
                                type: "success"
                            }, function (isConfirm) {
                                var purl = '${preUrl}';
                                if (purl == '/zfdx/zfdx-all' || purl == '/zfdx/zfdx-supervise') {
                                    business.addMainContentParserHtml(WEBPATH + purl + "?menuId=" + '${menuIdAll}', null);
                                } else {
                                    business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-individual-Three?menuId=' + '${menuId}', null);
                                }

                            });
                        } else if (data.meta.statusCode == "406") {
                            swal({
                                title: "提示",
                                text: data.meta.message,
                                type: "info"
                            }, function (isConfirm) {
                                var purl = '${preUrl}';
                                if (purl == '/zfdx/zfdx-all' || purl == '/zfdx/zfdx-supervise') {
                                    business.addMainContentParserHtml(WEBPATH + purl + "?menuId=" + '${menuIdAll}', null);
                                } else {
                                    business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-individual-Three?menuId=' + '${menuId}', null);
                                }
                            });
                        } else {
                            swal({
                                title: "提示",
                                text: data.meta.message,
                                type: "error"
                            }, function (isConfirm) {
                                var purl = '${preUrl}';
                                if (purl == '/zfdx/zfdx-all' || purl == '/zfdx/zfdx-supervise') {
                                    business.addMainContentParserHtml(WEBPATH + purl + "?menuId=" + '${menuIdAll}', null);
                                } else {
                                    business.addMainContentParserHtml(WEBPATH + '/zfdx/zfdx-individual-Three?menuId=' + '${menuId}', null);
                                }

                            });
                        }
                    }
                });
            } else {
                swal({
                    title: "已取消",
                    text: "您取消了删除操作！",
                    type: "info"
                })
            }
        })

    })

    //现场检查
    $("#spotInspectionBtn").click(
        function () {
            var startTimes = "";
            var endTimes = "";
            var id = $("#id").val();

            $.ajax({
                type: "post",
                url: WEBPATH + "/zfdx/lawObjectByTaskJosn",
                data: {
                    startTimes: startTimes,
                    endTimes: endTimes,
                    id: id
                },
                dataType: "json",
                success: function (data) {
                    $("#contentDiv").children().remove();
                    $("#msgDiv").html("");
                    if (data.yearList.length > 0) {
                        for (var i = 0; i < data.yearList.length; i++) {
                            $("#contentDiv")
                                .append(
                                    "<div class='timeline-year font-semi-bold'>"
                                    + data.yearList[i]
                                    + "</div>");
                            for (var j = 0; j < data.mounthList.length; j++) {
                                var mounth = data.mounthList[j];
                                var year = data.yearList[i];
                                if (mounth.indexOf(year) == 0) {
                                    // if((data.mounthList[j]).indexOf(data.yearList[i])){
                                    $("#contentDiv")
                                        .append(
                                            " <div class='timeline-row alt'> <div class='timeline-item'>  <div class='timeline-icon'>"
                                            + (data.mounthList[j])
                                                .substring(4)
                                            + "</div>")
                                    //  }
                                    for (var z = 0; z < data.taskList.length; z++) {
                                        var yearMonth = data.taskList[z].yearMonth;
                                        if (data.taskList[z].yearMonth == data.mounthList[j]) {
                                            $("#contentDiv")
                                                .append("<div class='timeline-item-inner' style='border: 1px solid #CCC;padding:20px; margin:20px 50px;background-color:#f2f2f2;cursor:pointer;'><div onclick=switchHistoryTask('" + data.taskList[z].id + "','" + data.taskList[z].lawObjectType + "','" + data.taskList[z].nodeCode + "') class='timeline-body'><div class='font-16 font-semi-bold padding-xs'>任务编号:"
                                                    + data.taskList[z].taskId
                                                    + "</div><div class='font-14 padding-xs'>任务来源："
                                                    + data.taskList[z].taskFromName
                                                    + "</div> <div class='font-14 padding-xs'>检查开始时间:"
                                                    + data.taskList[z].startTime
                                                    + " </div><div class='font-14 padding-xs'>检查结束时间:"
                                                    + data.taskList[z].endTime
                                                    + " </div></div></div></div></div></div>");
                                        }
                                    }
                                }
                            }
                        }

                    }
                }
            });

        });
</script>
<script type="text/javascript">
    $(function () {
        //监听回退键
        business.listenBackSpace();
        var isManageCode = '${lawObject.managementType}';
        if(isManageCode == '登记管理' || isManageCode == ''){
            $('#manageShow').hide()
        }else{
            $('#manageShow').show()
        }
    });
</script>
</body>
</html>