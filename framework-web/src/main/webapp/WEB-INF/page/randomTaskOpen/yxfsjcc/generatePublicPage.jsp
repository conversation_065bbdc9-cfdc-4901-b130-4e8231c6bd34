<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@page
	import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<!DOCTYPE html>
<html lang="en">
<%
	String fastdfs_addr = PropertiesHandlerUtil.getValue("fastdfs.nginx.ip", "fastdfs");
%>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<c:set var="vEnter" value="\n" scope="request"/>
<% request.setAttribute("vEnter", "\n"); %>
<body class="overflow-hidden">
		<div class="main-container">
			<div class="padding-md">

				<div class="smart-widget">
					<div class="smart-widget-inner">
						<jsp:include page="../randomOpenBusiness.jsp"></jsp:include>
						<div class="smart-widget-body">
							<div class="tab-content">
								<div class="tab-pane fade in active" id="style1Tab2">
									<div class="smart-widget-inner table-responsive">
										<div class="smart-widget-body form-horizontal">
											<div class="form-group">
												<div class="col-lg-12 text-right">
													<button class="btn btn-info" type="button"
														onclick="goback('${preUrl}')">返回列表</button>
												</div>
											</div>
											<form id="taskInfoForm">
												<input type="hidden" id="id" name="id"  value="${openInfo.id}">
												<input type="hidden" id="option" name="option" value="${type }">
												<input type="hidden" id="openTaskItemList" name="openTaskItemList">
												<div class="form-group">
													<label class="col-lg-2 col-md-2 col-sm-2 control-label"><span style="color: red;">*</span>公开标题</label>
													<div class="col-lg-8 col-md-8 col-sm-8">
														<input type="text" class="form-control" placeholder="公开标题" id="publicTitle" name="publicTitle" value="${openInfo.publicTitle}" >
													</div>
												</div>
												<div class="form-group">
													<label class="col-lg-2 col-md-2 col-sm-2 control-label"><span style="color: red;">*</span>正文</label>
													<div class="col-lg-8 col-md-8 col-sm-8">
														<textarea rows="10" class="form-control" placeholder="正文" 	type="text" id="message" name="message" >${openInfo.message}</textarea>
													</div>
												</div>
												<div class="form-group">
													<label class="col-lg-2 col-md-2 col-sm-2 control-label"><span style="color: red;">*</span>附件标题</label>
													<div class="col-lg-8 col-md-8 col-sm-8">
														<input type="text" class="form-control"  placeholder="附件标题" id="fileName" name="fileName" value="${openInfo.fileName }">
													</div>
												</div>
											</form>
											<div class="form-group">
												<label class="col-lg-2 col-md-2 col-sm-2 control-label"><span style="color: red;">*</span>执法任务</label>
												<div class="col-lg-8 col-md-8 col-sm-8">
													<table id="openTaskTable"
														class="table table-striped table-hover table-bordered no-margin">
														<thead>
															<tr>
																<th class="text-center" style="width: 50px;">序号</th>
																<th class="text-center" style="width: 130px;">检查时间</th>
																<th class="text-center">检查对象</th>
																<th class="text-center">检查内容</th>
																<th class="text-center">检查情况及处理意见</th>
															</tr>
														</thead>
														<tbody>
															<tr v-for="(item, index) in items" :id="'tr'+index">
																<input type="hidden" class="hiddenIndex" :value="index">
																<td class="text-center" style="vertical-align: middle;">{{index+1}}</td>
																<td class="text-center" style="vertical-align: middle;">
																	<input type="text" v-model="item.checkDateStr"  class="form-control" :id="'checkoutDate'+index"   v-on:click="checkoutDate(index)"	placeholder="检查时间" ></td>
																<td class="text-center"   style="vertical-align: middle;">
																	<input type="text" v-model="item.checkObject" :id="'checkObject'+index"  class="form-control"  placeholder="检查对象" >
																</td>
																<td class="text-center"   style="vertical-align: middle;">
																	<input type="text"  v-model="item.checkObtion" :id="'checkObtion'+index" class="form-control"  placeholder="检查内容" >
																</td>
																<td class="text-center"   style="vertical-align: middle;">
																	<textarea  v-model="item.checkAdvice" :id="'checkAdvice'+index"  rows="10" @focus="focusFun()" @blur="blurFun()" class="form-control"  placeholder="检查情况及处理意见" ></textarea>
																</td>

															</tr>
														</tbody>
													</table>
												
										</div>
									</div>
										<div class="modal-footer">
											<!--<button type="button" class="btn btn-info" data-dismiss="modal" data-toggle="modal" data-target="#yulan">预览</button>-->
											<button type="button" class="btn btn-info"  onclick="taskInfoSave()">确认生成</button>
										</div>
								</div>
							</div>
						</div>
					</div>
				</div>

			</div>
		</div>
	<div class="modal fade" id="sczfxx" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden=true>
		<div class="modal-dialog modal-lg">
			<div class="modal-content"></div>
		</div>
	</div>
	<div class="modal fade in" id="sjdh" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content"></div>
		</div>
	</div>
	<!-- 附件预览  -->
	<div class="modal fade" id="inputImgModeler" tabindex="-1"
		role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<div class="modal-header">
					<div class="modal-footer">
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
					</div>
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">附件预览</h4>
				</div>

			</div>
		</div>
	</div>
	<div class="modal fade" id="fileShowModel" tabindex="-1" role="dialog"
		aria-labelledby="fileModalLabel" aria-hidden="true">
		<div class="modal-dialog  modal-lg">
			<div class="modal-content"></div>
			<!-- /.modal-content -->
		</div>
		<!-- /.modal-dialog -->
	</div>
	<script type="text/javascript">

		var dataArr = new Array();
		$(function() {
			 $("[name='message']").focus(function(){
			        business.listenTextAreaComeEnter();
			 })
			 $("[name='message']").blur(function(){
			        business.listenTextAreaGoEnter();
			 })
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/randomOpenManager/openTaskItemList',
					data : {
						ids : '${ids}',type:'${type}',openInfoId:'${openInfoId}'
					},//  form law object
					async : false,
					error : function(request) {
						swal({
							title : "错误!",
							text : "执法任务信息失败！",
							type : "error"
						});
					},
					success : function(data) {
						dataArr.splice(0, dataArr.length);
						for (var i = 0; i < data.length; i++) {
							dataArr.push(data[i]);
						}
					}
				});
		})
		var vue = new Vue({
			el : '#openTaskTable',
			data : {
				items : dataArr,
			},
			methods : {
				checkoutDate:function(index){
							 var checkoutDate ="#checkoutDate"+index;
							  $(checkoutDate).datetimepicker({
								  format:'yyyy-mm-dd',
									todayBtn : true,
									//clearBtn:true,
									language: 'cn',
									autoclose : true,
									weekStart: 1,  
								    startView: 2,  
								    minView: 2,  
								    forceParse: false,  
								}).on('changeDate',function(ev){
									var date = ev.date;
					                var y = date.getFullYear();
					                var m = date.getMonth() + 1;
					                var d = date.getDate();
					                vue.items[index].checkDateStr=y + '-' +m + '-' + d;
								});
							  $(checkoutDate).datetimepicker('show');
					  }, focusFun: function(){//Vue处理 textarea回车无反应的问题
					        	business.listenTextAreaComeEnter();
					 		
					  },blurFun:function(){//Vue处理 textarea回车无反应的问题
				        business.listenTextAreaGoEnter();
					  }
			}
		});
		function taskInfoSave() {
			$("#taskInfoForm").data('formValidation').validate();
			var validate = $("#taskInfoForm").data('formValidation').isValid();
			if (validate) {
				var text = vue.items;
				var chickItemList = new Array();
				var index =1;
				var finalVue1 = true;
				$(".hiddenIndex").each(function(){ 
					var i = $(this).val();
						//必填验证
						if(vue.items[i].checkDateStr == null || vue.items[i].checkDateStr == ''){
							swal({title:"提示",text:"检查时间为必填信息！",type:"info",allowOutsideClick :true})
							finalVue1 = false;
							return false;
						}
						if(vue.items[i].checkObject == null || vue.items[i].checkObject == ''){
							swal({title:"提示",text:"检查对象为必填信息！",type:"info",allowOutsideClick :true})
							finalVue1 = false;
							return false;
						}
						if(vue.items[i].checkObtion == null || vue.items[i].checkObtion == ''){
							swal({title:"提示",text:"检查内容为必填信息！",type:"info",allowOutsideClick :true})
							finalVue1 = false;
							return false;
						}
						if(vue.items[i].checkAdvice == null || vue.items[i].checkAdvice == ''){
							swal({title:"提示",text:"检查情况及处理意见为必填信息！",type:"info",allowOutsideClick :true})
							finalVue1 = false;
							return false;
						}
						var type = '${type}';
						var obj;
						if(type==1){
							obj ={	  checkDateStr:vue.items[i].checkDateStr,
									  checkObject:vue.items[i].checkObject,
									  /*loction:index++,*/
									  checkObtion:vue.items[i].checkObtion,
									  checkAdvice:vue.items[i].checkAdvice,
									  rTaskId:vue.items[i].rTaskId,
									  checkSubject:vue.items[i].checkSubject,
									  checkCode:vue.items[i].checkCode
									};
						}else{
							obj ={id:vue.items[i].id,
									  checkDateStr:vue.items[i].checkDateStr,
									  checkObject:vue.items[i].checkObject,
									  /*loction:index++,*/
									  checkObtion:vue.items[i].checkObtion,
									  checkAdvice:vue.items[i].checkAdvice,
									  rTaskId:vue.items[i].rTaskId
									};
						}
					chickItemList.push(obj);
					
				})
				$("#openTaskItemList").val(JSON.stringify(chickItemList));
				if(finalVue1 == false) return false;
				var data = $('#taskInfoForm').serialize();
				swal({
					title : "提示 ",
					text : "确定要生成公开信息？",
					type : "warning",
					showCancelButton : true,
					confirmButtonColor : "#DD6B55",
					confirmButtonText : "是的，我要生成！",
					cancelButtonText : "让我再考虑一下",
					closeOnConfirm : false,
					closeOnCancel : false
				}, function(isConfirm) {
					if (isConfirm) {
						$.ajax({
							type : "POST",
							url : WEBPATH
									+ '/randomOpenManager/addOrUpdateGeneratePublic',
							data : data,
							async : false,
							error : function(request) {
								swal({
									title : "操作失败",
									text : "",
									type : "info"
								});
							},
							success : function(data) {
								if (data.meta.statusCode == 200) {

									swal({
										title : "提示",
										text : "公开信息已生成",
										type : "success"
									}, function() {
										var type = '${type}';
										//跳转页面
										if(type==1){
											business.addMainContentParserHtml(WEBPATH + '/randomOpenManager/issuedRandomPage', $("#RandomTaskOpenFormTables").serialize());
										}else{
											business.addMainContentParserHtml(WEBPATH + '/taskGenerateOpen/OpenShowList', $("#RandomTaskOpenFormTables").serialize());
										}
									});
								} else {
									swal({
										title : "提示",
										text :"公开信息生成失败",
										type : "info"
									});
								}
							}
						});
					}else {
						swal({
							title : "已取消",
							text : "您取消了生成公开信息操作！",
							type : "info",
							allowOutsideClick : true
						})
					}
				})
			} else {
				swal({
					title : "请检查填报信息是否完整规范",
					text : "",
					type : "info",
					allowOutsideClick : true
				});
			}

		}
		$(function() {
			$("#taskInfoForm").formValidation({
				//excluded:[":hidden",":disabled",":not(visible)"] ,//不可见的，不可填的，隐藏域不做校验
				framework : 'bootstrap',
				message : 'This value is not valid',
				icon : {
					valid : 'glyphicon glyphicon-ok',
					invalid : 'glyphicon glyphicon-remove',
					validating : 'glyphicon glyphicon-refresh'
				},
				fields : {
					publicTitle : {
						message : '公开标题不能为空！',
						validators : {
							notEmpty : {
								message : '公开标题不能为空！'
							},
							stringLength : {
								max : 150,
								message : '公开标题最大不能超过150个字符'
							}
						}

					},
					message : {
						message : '正文不能为空！',
						validators : {
							notEmpty : {
								message : '正文不能为空！'
							},
							stringLength : {
								max : 4000,
								message : '正文最大不能超过4000个字符'
							}
						}

					},
					fileName : {
						message : '附件标题不能为空！',
						validators : {
							notEmpty : {
								message : '附件标题不能为空！'
							},
							stringLength : {
								max : 100,
								message : '附件标题最大不能超过100个字符'
							}
						}

					}
				}
			});
		})
		function goback(preUrl){
			if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
	  			if(preUrl=='/randomOpenManager/issuedRandomPage'){
	  				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", $("#RandomTaskOpenFormTables").serialize());
				}else if(preUrl=='/taskGenerateOpen/OpenShowList'){
					$("#selectToggle").val("4");
	  				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", $("#RandomTaskOpenFormTables").serialize());
				}else if(preUrl=='/openReview/open'){
					$("#selectToggle").val("3");
	  				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", $("#RandomTaskOpenFormTables").serialize());
				}
	  		} else {
	  			swal({
	  				title : "提示！",
	  				text : "返回信息错误，请刷新后重试。",
	  				type : "error",
	  				allowOutsideClick :true
	  			})
	  		}
		}
	</script>
</body>
</html>