<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="sec" uri="http://www.springframework.org/security/tags" %>
<sec:authentication property="principal" var="authentication"/>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<body class="overflow-hidden">
	<div class="main-container">
		<div class="padding-md">
			<!--第一层快速查询row-->
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget">
						<div class="smart-widget-inner">
							<div class="smart-widget-body form-horizontal">
								<div class="form-group">
									<label class="control-label col-lg-2">被修改人</label>
									<div class="col-lg-3">
										<input type="text" placeholder="污染源名称" class="form-control"
											data-parsley-required="true"  name="currentUserName"  >
									</div>
									<label class="control-label col-lg-2">修改人</label>
									<div class="col-lg-3">
										<input type="text" placeholder="修改人" class="form-control"
											data-parsley-required="true" name="updateUserName">
									</div>
								</div>
								<div class="form-group">
									<label class="control-label col-lg-2">所在行政区</label>
									<div class="col-lg-2">
										<select class="form-control" id='belongProvince' name="belongProvince">
											<option value="35000000">福建省</option>
										</select>
									</div>
									<div class="col-lg-3">
										<select class="form-control" id="belongCity" name="belongCity">
										</select>
									</div>
									<div class="col-lg-3">
										<select class="form-control" id="belongCountry" name="belongCountry">
										</select>
									</div>
								</div>
								
								<div class="form-group">
											<label class="control-label col-lg-2">开始时间</label>
											<div class="col-lg-3">
												<input type="text" placeholder="开始时间" class="form-control" name="taskReachTime" data-parsley-required="true">
											</div>
											<label class="control-label col-lg-2">结束时间</label>
											<div class="col-lg-3">
												<input type="text" placeholder="结束时间" class="form-control" name="processingTime" data-parsley-required="true">
											</div>
										</div>
								<div class="form-group text-right">
									<div class="col-lg-10">
										<button class="btn btn-info" type="submit" id="searchButton"
											style="width: 120px;" onclick="subForm()">查询</button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
	
			<!--./第一层快速查询row-->
	
			<!--第二层任务办理row-->
			<div class="row">
				<!--任务办理-->
				<div class="col-lg-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 用户更新日志 <span
								class="smart-widget-option"> <span
								class="refresh-icon-animated"> <i
									class="fa fa-circle-o-notch fa-spin"></i>
							</span> 
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">
							<table class="table table-striped table-hover no-margin table-no-bordered" id="logDataTable">
							</table>
						</div>
					</div>
				</div>
				<!--./待办任务-->
			</div>
			<!--./第二层任务办理row-->
		</div>
	</div>
</body>
<script type="text/javascript">
        //选择地区
        var htmlCity = "<option value=''>——市级——</option>"; 
		var htmlCounty = "<option value=''>——县级——</option>"; 
		var belongAreaCode = '${authentication.belongAreaId}';
		$.ajax({
            type:"post",
            url:WEBPATH+"/tArea/chickUserArea",
            dataType:"json",
            data:{},
            success:function(data){
            	if(data.cityStatus =='1'){
            		//省级用户
            		  $.ajax({
            		        type:"post",
            		        url:WEBPATH+"/tArea/cityList",
            		        dataType:"json",
            		        success:function(data){
                              	$("#belongCity").append("<option value=''>请选择</option>"); 
                            	$("#belongCountry").append("<option value=''>请选择</option>"); 
            			         $.each(data,function(i,item){
            			            	 $("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
            			         });
            		        }
            		    });
            	}else if(data.cityStatus =="2"){
            		//市级用户
            		 $("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
            	    $.ajax({
                        type:"post",
                        url:WEBPATH+"/tArea/countyListByCode",
                        dataType:"json",
                        data:{parentCode:data.cityCode},
                        success:function(data){
                        	 $("#belongCountry").append("<option value=''>请选择</option>"); 
                	         $.each(data,function(i,item){
                	          	$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
                	         });
                        }
                    });
            	}else{
            		//县级用户
            		 $("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
            		 $("#belongCountry").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
            	}
            }
        });
		//
		$("#belongCity").change(function(){
		        if ($(this).val() == ""){
		        	 $("#belongCountry option").remove();
		        	 $("#belongCountry").html(htmlCounty);
		        	return;
		        }
		        var parentCode = $(this).val();
		        $("#belongCountry option").remove();
		        $.ajax({
		            type:"post",
		            url:WEBPATH+"/tArea/countyListByCode",
		            dataType:"json",
		            data:{parentCode:parentCode},
		            success:function(data){
		            	 $("#belongCountry").html(htmlCounty);
		    	         $.each(data,function(i,item){
		    	          	$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
		    	         });
		            }
		        });
		  });
		
		 $.ajax({
		        type:"post",
		        url:WEBPATH+"/tArea/cityList",
		        dataType:"json",
		        success:function(data){
		        	 $("#powerCountry").html(htmlCity	);
			         $("#powerCity").append("<option value=''>请选择</option>");
			         $.each(data,function(i,item){
			            	 $("#powerCity").append("<option value="+item.code+">"+item.name+"</option>");
			         });
		        }
		    });
		 
		 $("#belongCity").change(function(){
		        if ($(this).val() == ""){
		        	 $("#belongCountry option").remove();
		        	 $("#belongCountry").html(htmlCounty);
		        	return;
		        }
		        var parentCode = $(this).val();
		        $("#belongCountry option").remove();
		        $.ajax({
		            type:"post",
		            url:WEBPATH+"/tArea/countyListByCode",
		            dataType:"json",
		            data:{parentCode:parentCode},
		            success:function(data){
		            	 $("#belongCountry").html(htmlCounty);
		    	         $.each(data,function(i,item){
		    	          	$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
		    	         });
		            }
		        });
		  });

</script>

<script type="text/javascript">
//使用Bootstrap
$(document).ready(function(){
	business.listenEnter("searchButton");
	$('#logDataTable').bootstrapTable({       
			 method: 'post',
			 dataType: "json", 
			 url:  WEBPATH+'/sysLog/user-log-list',
		     undefinedText : '-',  
		     pagination : true, // 分页  
		     striped : true, // 是否显示行间隔色  
		     cache : false, // 是否使用缓存  
		     pageSize:15, // 设置默认分页为 10
		     pageNumber: 1,
		     queryParamsType: "",
		     locale:'zh-CN',
		     pageList: [5, 15, 20,30,50], // 自定义分页列表
		     
		     singleSelect: false,
		     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
		     // showColumns : true, // 显示隐藏列  
		     sidePagination: "server", //服务端请求
		     queryParams:function (params) {
		      		
		      		var objectName = $("[name='currentUserName']").val();  
		      		var updateUserName = $("[name='updateUserName']").val();   
		      		//所属地区
		      		var belongProvince = $("#belongProvince").val();
			    	var belongCity =  $("#belongCity").val();
			    	var belongCountry = $("#belongCountry").val();
			    	
			    	var searchStartTime=$("[name='taskReachTime']").val();
		      		var searchEndTime=$("[name='processingTime']").val();
		            //判断所属地区
			    	var areaCode="";
		            if(belongCity=='' || belongCity=='undefined' || belongCity==null){
		            	//市级参数为空，以用户自身权限查询
		            	areaCode=belongAreaCode;
		            }else if(belongCountry=='' || belongCountry==null || belongCountry=='undefined'){
		            	//县级参数为空
		            	areaCode=belongCity;
		            }else{
		            	//县级参数不为空
		            	areaCode=belongCountry;
		            }
			    	
		            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
		        		 pageNum: params.pageNumber,
		                 pageSize: params.pageSize,
		                 currentUserName:objectName,
		                 updateUserName:updateUserName,
		                 searchStartTime:searchStartTime,
		                 searchEndTime:searchEndTime,
		                 areaCode:areaCode
		                 
		            };
		            return temp;
		          },//参数
                       uniqueId : "id", // 每一行的唯一标识  
	                         columns: [

                                          {
												field : "updateTime",
												title : "时间",
												align : 'center',
												formatter: function(value,row,index){
													var time = row.updateTime;
													if(time==null){
														return "";
													}else {
														var date = new Date(time);
										                var y = date.getFullYear();
										                var m = date.getMonth() + 1;
										                var d = date.getDate();
										                var h = date.getHours();  //时
										                var mm = date.getMinutes(); //分
										                var s = date.getSeconds();
										                return y + '-' +m + '-' + d + ' ' + h + ':' + mm + ':' + s;
													}
												}
											},
											{
												field : "currentUserName",
												title : "被修改人姓名",
												align : 'center'
											},
											{
												field : "currentAreaName",
												title : "所在行政区",
												align : 'center'
											},
											{
												field : "updateColumnName",
												title : "修改属性名称",
												align : 'center'
											},
											{
												field : "beforValue",
												title : "修改前",
												align : 'center',
												formatter: function(value,row,index){
									        	   	  var html="";
									        	   	  if(row.beforValue=='1'){
									        	   		html+="是";
									        	   	  }else if(row.beforValue=='0'){
									        	   		html+="否";
									        	   	  }else{
									        	   		html="";
									        	   	  }
										        	  return html;
										       }
											},
											{
												field : "afterValue",
												title : "修改后",
												align : 'center',
												formatter: function(value,row,index){
									        	   	  var html="";
									        	   	  if(row.afterValue=='1'){
									        	   		html+="是";
									        	   	  }else if(row.afterValue=='0'){
									        	   		html+="否";
									        	   	  }else{
									        	   		html="";
									        	   	  }
										        	  return html;
										       }
											},
											{
												field : "updateUserName",
												title : "修改人",
												align : 'center'
											},
											{
												field : "updateDepartmentName",
												title : "修改人所属部门",
												align : 'center'
											}
											 ],
									responseHandler : function(res) {
										return {
											total : res.total,
											rows : res.list
										};
									},
									onCheck : function(row,
											$element) {

									},//单击row事件
									onUncheck : function(row,
											$element) {

									},
									onUncheckAll : function(row,
											$element) {

									},
									onCheckAll : function(row,
											$element) {

									},
									onRefresh : function() {

									},
									formatLoadingMessage : function() {
										return "玩命加载中...";
									},
									formatNoMatches : function() { //没有匹配的结果
										return '无符合条件的记录';
									}
				});

				//绑定搜索按钮
				$('#searchButt').click(function() {
					//$('#logDataTable').bootstrapTable('refresh');
					$('#logDataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
				});
				
				// 搜索时间控件的控制 
		 		$("[name='taskReachTime']").datetimepicker({
		 		     format:'yyyy-mm-dd hh:ii',
		 		   	 language: 'cn',
		 		     //endDate:$("#askingEndDate").val(),
		 		     autoclose: true
		 		}).on('changeDate',function(ev){
		 			$("[name='processingTime']").datetimepicker('setStartDate',new Date(ev.date.valueOf()));
		 		});
		 		
		 		$("[name='processingTime']").datetimepicker({
		 		     format:'yyyy-mm-dd hh:ii',
		 		     language: 'cn',
		 		   	 //startDate:$("#askingStartDate").val(), 
		 		     autoclose: true
		 		}).on('changeDate',function(ev){
		 			$("[name='taskReachTime']").datetimepicker('setEndDate',new Date(ev.date.valueOf()));
		 		});  
			});
			
			//查询方法
			function subForm(){
				//$('#logDataTable').bootstrapTable('refresh');
				$('#logDataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
			}
</script>