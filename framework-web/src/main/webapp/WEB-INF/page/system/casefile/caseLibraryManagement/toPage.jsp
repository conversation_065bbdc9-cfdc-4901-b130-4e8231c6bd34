<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<div class="main-container">
	<div class="padding-md">
		<input id="groundBackParams" name="groundBackParams" type="hidden"
			value='${params }' />
		<!-- 返回所需的参数 -->

		<!--第一层快速查询row-->
		<div class="row">
			<div class="col-lg-12">
				<div class="smart-widget">
					<div class="smart-widget-inner">
						<div class="smart-widget-body form-horizontal">
							<div class="form-group">
								<label class="control-label col-lg-2">来源</label>
								<div class="col-lg-3">
									<select class="form-control" id="type"	name="type">
										<option value="">全部</option>
										<option value="0">执法</option>
										<option value="1">案件</option>
									</select>
								</div>
								<label class="control-label col-lg-2">文书类型名称</label>
								<div class="col-lg-3">
									<input type="text" placeholder="文书类型名称" class="form-control" name="codeNameSearch" id="codeNameSearch">
								</div>
							</div>
							<div class="form-group">
								<label class="control-label col-lg-2">状态</label>
								<div class="col-lg-3">
									<select class="form-control" id="isEnable"
										name="isEnable">
										<option value="">全部</option>
										<option value="1">启用</option>
										<option value="0">停用</option>
										
									</select>
								</div>
								<label class="control-label col-lg-2">文书共有属性</label>
									<div class="col-lg-3">
										<select class="form-control" id="isPublic"
											name="isPublic">
											<option value="">全部</option>
											<option value="1">所有小案件共用</option>
											<option value="0">按日计罚</option>
										</select>
									</div>
							</div>
							<div class="form-group">
								<div class="col-lg-10 text-right">
									<button class="btn btn-info" id="searchBtn"
										style="width: 80px;">查询</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!--./第一层快速查询row-->

		<!--第二层任务办理row-->
		<div class="row">
		<div class="form-group">
             <div class="col-lg-12 text-right">
                 	<button type="button" class="btn btn-info no-shadow" onclick="addOrUpdateModel('')" style="margin-right:10px;">新增文书类型</button>
             </div>
        </div>
			<!--任务办理-->
			<div class="col-lg-12">
			
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
						案件文书库管理<span
							class="smart-widget-option"> <span
							class="refresh-icon-animated"> <i
								class="fa fa-circle-o-notch fa-spin"></i>
						</span>

						</span>
					</div>
					<div class="smart-widget-inner table-responsive">

						<table class="table-no-bordered" id="dataTable">

						</table>
					</div>
				</div>
			</div>
			<!--./待办任务-->
		</div>
	</div>
</div>
<div class="modal fade" id="modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">

    
                </div>
            </div>
        </div>

<script type="text/javascript">
	$(document).ready(function() {
		//监听enter查询，内包含监听回退键
		business.listenEnter("searchBtn");
	})

</script>
<script type="text/javascript">
		var pageNumber = 1;
		var pageSize = 15;
		$(document).ready(function() {
			//执法对象选择模态框表单动态加载LoadingDataItems
			LoadingDataItems();
			$('#dataTable').bootstrapTable('hideColumn', 'id');

			//返回按钮之后参数和页码的回显
			//如果不设置延迟，在非常短的间隔时间内连续点返回会导致参数丢失
			//因为跳转页面是异步请求（如果用ajaxSetup设置成同步请求会造成全局的ajax请求都是同步的）
			setTimeout('setParams()', 100);
		});

		function setParams() {
			var params = $("#groundBackParams").val();
			if (params != null && params != '' && params != 'undefined') {
				var jsonParam = $.parseJSON(params);
				for ( var key in jsonParam) {
					//绑定设定条件
					$("[name=" + key + "]").val(jsonParam[key]);
				}
				$('#dataTable').bootstrapTable('refresh', {
					pageNumber : parseInt(jsonParam['pageNumber']),
					pageSize : parseInt(jsonParam['pageSize'])
				});
			}
		}

		function LoadingDataItems() {
			$('#dataTable')
					.bootstrapTable(
							{
								method : 'post',
								dataType : "json",
								url : WEBPATH
										+ '/CaseLibraryManagement/caseLibraryManagementList',
								undefinedText : '-',
								pagination : true, // 分页  
								striped : true, // 是否显示行间隔色  
								cache : false, // 是否使用缓存  
								pageSize : 15, // 设置默认分页为 20
								pageNumber : 1,
								queryParamsType : "",
								pageList : [ 5, 15, 30, 50, 100, 200 ], // 自定义分页列表
								singleSelect : false,
								contentType : "application/x-www-form-urlencoded;charset=UTF-8",
								// pageList : [ 5, 10, 20 ],  
								// showColumns : true, // 显示隐藏列  
								sidePagination : "server", //服务端请求
								queryParams : queryParams,//参数
								uniqueId : "id", // 每一行的唯一标识  
								//序号	名称	证件号	联系人	联系方式	权属
								columns : [
										{
											field : "id",
											title : "id",
											align : 'center',
										},
										{
											title : '序号',//标题  可不加  
											align : 'center',
											formatter : function(value, row,
													index) {
												return index + 1;
											}
										},
										{
											field : "type",
											title : "来源",
											align : 'center',
				 			                formatter: function (value, row, index) {
				 			                	var html="-";
				 			                	   if(value==0){
				 			                		   html = "执法";
				 			                	   }else if(value==1){
				 			                		  html = "案件";
				 			                	   }
				 			                       return html;  
				 			                }
										},
										{
											field : "codeName",
											title : "文书类型名称",
											align : 'center',
										},
										{
											field : "codeAlias",
											title : "默认案卷目录名称",
											align : 'center',
										},
										{
											field : "isPublic",
											title : "文书共有属性",
											align : 'center',
				 			                formatter: function (value, row, index) {
				 			                	var html="-";
				 			                	   if(value==0){
				 			                		   html = "按日计罚";
				 			                	   }else if(value==1){
				 			                		  html = "所有小案件共用";
				 			                	   }
				 			                       return html;  
				 			                }
										},
										{
											field : "isEnable",
											title : "状态",
											align : 'center',
											formatter: function (value, row, index) {
				 			                	var html="-";
				 			                	   if(value==0){
				 			                		   html = "停用";
				 			                	   }else if(value==1){
				 			                		  html = "启用";
				 			                	   }
				 			                       return html;  
				 			                }
										},
										{
											field : "operation",
											title : "操作",
											align : 'center',
											formatter : function(value, row,index) {
												var html = "";
												 html+="<a  onclick=\"addOrUpdateModel('"+ row.id+ "')\"   style='color:#43c1e9;cursor:pointer;'>修改</a>&nbsp;";
												 if(row.isEnable==0){
													 html+="<a  onclick=\"operation('"+ row.id+ "','"+ 2+ "')\"   style='color:#43c1e9;cursor:pointer;'>启用</a>&nbsp;";
												 }
												 if(row.isEnable==1){
												 	html+="<a  onclick=\"operation('"+ row.id+"','"+ 3+ "')\"   style='color:#43c1e9;cursor:pointer;'>停用</a>&nbsp;";
												 }
												 /* html+="<a  onclick=\"operation('"+ row.id+ "','"+ 4+ "')\"   style='color:#E36159;cursor:pointer;'>删除</a>&nbsp;"; */
												 return html;
											}
										} ],
								responseHandler : function(res) {
									return {
										total : res.data.total,
										rows : res.data.list
									};
								},
								onCheck : function(row, $element) {
								},//单击row事件
								onUncheck : function(row, $element) {
								},
								onRefresh : function() {
								},
								onPreBody : function() {
								},
								formatLoadingMessage : function() {
									return "数据加载中...";
								},
								formatNoMatches : function() { //没有匹配的结果
									return '无符合条件的记录';
								}
							})
		}
		function queryParams(params) {
			var type = $("#type").val();
			var codeNameSearch = $("#codeNameSearch").val();
			var isEnable = $("#isEnable").val();
			var isPublic = $("#isPublic").val();
			var temp = { //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
				pageNumber : params.pageNumber,
				pageSize : params.pageSize,
				codeName : codeNameSearch,
				isEnable : isEnable,
				isPublic : isPublic,
				type:type
			};
			return temp;
		}

		$("#searchBtn").click(function() {
			$('#dataTable').bootstrapTable('refreshOptions', {
				pageNumber : 1,
				pageSize : 15
			});
		});
		
		function addOrUpdateModel(id){
			var options = {
					remote:WEBPATH+'/CaseLibraryManagement/addCaseLibraryManagementModel?id='+id
				  };
				$('#modal').modal(options);
		}
		
		
		function operation(id,type){
			if(type==1){

			}else if(type==2 || type==3){
				var isEnable ="";
				var htmlSuccess = "";
				var htmlError = "";
				if(type==2){
					isEnable=1;//启用
					htmlSuccess='启用成功';
					htmlError='启用失败';
				}else{
					isEnable=0;//停用					
					htmlSuccess='停用成功';
					htmlError='停用失败';
				}
				$.ajax({
		            cache: true,
		            type: "POST",
		            url: WEBPATH+'/CaseLibraryManagement/enableOrDisable',
		            data:{id:id,isEnable:isEnable},
		            async: false,
		            error: function(request) {
		                swal({title: htmlError ,text: "",type:"info",allowOutsideClick :true});
		            },
		            success: function(data) {
		            	if(data.meta.statusCode == 200) {

							swal({
								title : htmlSuccess,
								text : "",
								type : "success"
							},function(){
								//business.addMainContentParserHtml(WEBPATH + '/HomeSpecialInlet/toTaskBatchAssociate', 'specialActionId='+specialActionId+"&specialActionName="+specialActionName+"&applyAreaCode="+applyAreaCode);
								$('#modal').modal('hide');
								$('#dataTable').bootstrapTable('refresh',{pageNumber:1,pageSize:15});
							});
						}else{
							swal({
								title : htmlError,
								text : data.meta.detailMessage,
								type : "info",
								allowOutsideClick :true 
							});
						}
		            }
		        });
			}else if(type==4){
				swal({
				  	title: "提示",
				  	text: "您确定要删除该条文书信息吗",
				  	type: "info",
				  	showCancelButton : true,
					confirmButtonColor : "#DD6B55",
					confirmButtonText : "是的，我要删除！",
					cancelButtonText : "让我再考虑一下",
					closeOnConfirm : false,
					closeOnCancel : false
			}, function(confirm){
					if(confirm){
						$.ajax({
				            cache: true,
				            type: "POST",
				            url: WEBPATH+'/CaseLibraryManagement/delCaseLibraryManagement',
				            data:{id:id},
				            async: false,
				            error: function(request) {
				                swal({title: "删除失败" ,text: "",type:"info",allowOutsideClick :true});
				            },
				            success: function(data) {
				            	if(data.meta.statusCode == 200) {
	
									swal({
										title : "删除成功",
										text : "",
										type : "success"
									},function(){
										//business.addMainContentParserHtml(WEBPATH + '/HomeSpecialInlet/toTaskBatchAssociate', 'specialActionId='+specialActionId+"&specialActionName="+specialActionName+"&applyAreaCode="+applyAreaCode);
										$('#modal').modal('hide');
										$('#dataTable').bootstrapTable('refresh',{pageNumber:1,pageSize:15});
									});
								}else{
									swal({
										title : "删除失败",
										text : data.meta.detailMessage,
										type : "info",
										allowOutsideClick :true 
									});
								}
				            }
				        });
					}else{
						swal({
							title : "已取消",
							text : "您取消了删除操作！",
							type : "info",
							allowOutsideClick :true
						})
					}
				})
			}else{
				swal({ title : "无效操作请重试", text : "", type : "info",allowOutsideClick :true  });
			}
		}
	</script>