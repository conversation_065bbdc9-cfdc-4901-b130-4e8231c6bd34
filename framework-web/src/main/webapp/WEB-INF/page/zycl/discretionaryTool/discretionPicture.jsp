<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
    <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="${webpath }/static/js/html2canvas.js"></script>
<script src="${webpath }/static/js/canvas2image.js"></script>
<script src="${webpath }/static/js/bluebird.js"></script>
<c:set var="vEnter" value="\n" scope="request"/>
<% request.setAttribute("vEnter", "\n"); %>
<title>Insert title here</title>
</style>
<style type="text/css">
	.zycl-box{
		border: solid 1px #ccc;border-radius: 5px;padding: 20px 0 20px 30px;height: auto;
	}
	.zycl-sctitle{
		text-align: center;color: #999;
	}
	.zycl-jg-box{
		position: absolute;left: 50%;margin: 10px 0 10px -75px;
	}
	.zycl-jg-bj{
		border-radius: 5px;background: #f3f3f3;padding: 10px;margin-top: 20px;
	}
	.zycl-jg-fk{
		font-size: 22px; font-weight: bold;color: red;position: relative;top: 5px;
	}
	.zycl-bfnr{
		text-align: center;color: #999;margin-top: 120px;
	}
	.zycl-time-sx{
		border-left: dotted 1px #999;margin: 0 50px;float: left;
	}
	.zycl-time-huan1{
		border-radius: 50%;border: solid 1px #79dffc;width: 110px;height: 110px;
	}
	.zycl-time-huan2{
		border-radius: 50%;border: solid 4px #fff;
	}
	.zycl-time-huan3{
		border-radius: 50%;background: #22b8e5; padding: 20px 0;text-align: center;color: #fff; width: 100px;height: 100px;
	}
	.zycl-content-box{
		border: solid 1px #dadada;margin: 10px 0 0 80px;font-size: 15px;width:80%px;
	}
	.zycl-content-title{
		background: #fff;margin-left: 20px; padding: 0 20px;color: #1b80a7;position: relative;top: -10px;width: 100px;font-weight: bold;
	}
	.zycl-content{
		padding:5px 20px 20px 20px;
	}
</style>
</head>
<body>
<div class="main-container"  >
				<div class="padding-md">
                   	<div class="smart-widget">
						<div class="smart-widget-inner">
							<ul class="nav tab-style1">
								<li style="text-align: center;">
							  		<img src="${webpath}/static/img/Step-3.png" />
							  	</li>
							
							<div class="smart-widget-body form-horizontal" style="margin-top: 20px;">
								<div class="tab-content">
									<div class="tab-pane fade in active" id="wszz">										
										<div class="col-lg-10 col-xs-offset-1 zycl-box" id="picture" >
											<div class="zycl-sctitle">本裁量结果于福建省环境监察执法平台生成于${time }时生成</div>
											<div class="zycl-jg-box">
												<div class="zycl-jg-bj">
													<span class="zycl-jg-fk"><img src="${webpath}/static/img/icon-rmb.png" />${generate.finalMoney }万元</span>
												</div>
											</div>
											<div class="zycl-bfnr">并罚内容：${fn:replace(details.stDetails,vEnter,"<br>")}</div>
<%-- 											<div class="zycl-bfnr">并罚内容：${details.stDetails }</div> --%>
											<div class="col-lg-12">
												<div class="zycl-time-sx">
													<div class="col-lg-12">
														<div style="position: absolute;left: -60px;">
															<div class="zycl-time-huan1">
																<div class="zycl-time-huan2">
																	<div class="zycl-time-huan3">最终处罚<br><span style="font-size: 16px;font-weight: bold;">${generate.finalMoney }万元</span></div>
																</div>
															</div>
														</div>
														<div class="zycl-content-box">
															<div class="zycl-content-title">确定原因</div>
															<div class="zycl-content">${fn:replace(generate.details,vEnter,"<br>")}</div>
<%-- 															<div class="zycl-content">${generate.details }</div> --%>
													</div>
													<div class="col-lg-12" style="margin-top: 30px;">
														<div style="position: absolute;left: -60px;">
															<div class="zycl-time-huan1">
																<div class="zycl-time-huan2">
																	<div class="zycl-time-huan3">裁量罚款<br><span style="font-size: 16px;font-weight: bold;">${generate.consultMoney }万元</span></div>
																</div>
															</div>
														</div>
														<div class="zycl-content-box">
															<div class="zycl-content-title">裁量因素</div>
															<div class="zycl-content" >
																<div ><span style="font-weight: bold;">情形分类：</span>${fn:replace(details.stClassify,vEnter,"<br>")}。</div>
																<div ><span style="font-weight: bold;">情节和后果：</span>${fn:replace(details.stResult,vEnter,"<br>")}。</div>
																<div  style="color: #999;font-size: 12px;">注：本裁量依据《${dtDiscretionInfo.fileName }》</div>
															</div>
														</div>
													</div>
													<div class="col-lg-12" style="margin-top: 30px;margin-bottom: 50px;">
														<div style="position: absolute;left: -60px;">
															<div class="zycl-time-huan1" style="margin-top: -40px;">
																<div class="zycl-time-huan2">
																	<div class="zycl-time-huan3">法定罚款<br><span style="font-size: 16px;font-weight: bold;">${Factor.minCondition }-${Factor.maxCondition }万元</span></div>
																</div>
															</div>
															<c:if test="${Factor.type == '1' }">
															<div class="zycl-time-huan1">
																<div class="zycl-time-huan2">
																	<div class="zycl-time-huan3">基础罚款<br><span style="font-size: 16px;font-weight: bold;">${generate.basicsMoney }万元</span></div>
																</div>
															</div>
															</c:if>
														</div>
														<div class="zycl-content-box">
															<div class="zycl-content-title">裁量因素</div>
															<div class="zycl-content">
																<p style="font-weight: bold;"><span>${Factor.law }</span>
																<c:if test="${Factor.lawChapter !='N'}"><span style="padding: 0 10px;">第${Factor.lawChapter }章</span></c:if>
																<c:if test="${Factor.lawArticle !='N'}"><span>第${Factor.lawArticle }条</span></c:if>
																<c:if test="${Factor.lawParagraph !='N'}"><span style="padding: 0 10px;">第${Factor.lawParagraph }款</span></c:if></p>
																<div ><span style="font-weight: bold;">条文内容：</span>${fn:replace(Factor.lawContent,vEnter,"<br>")}。</div>
																</div>
														</div>
													</div>
												</div>
											</div>
											
										</div>	
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
								<div class="col-lg-3 col-xs-offset-5" style="margin-top: 20px;margin-bottom: 20px">
								<button type="submit" class="btn btn-info" style="width:100px;" onClick="backPage()">上一步</button>
								<button type="button" class="btn btn-info" style="width:100px;" onClick="savePicture()">保存本地</button>
								</div>
						</div> 
					  </ul>
					</div>
				</div>
			</div>
		</div>
</body>
<script type="text/javascript">
	//上一步
	function backPage(){
		var id = '${id}'; 
		 business.addMainContentParserHtml(WEBPATH+'/discretionaryTool/backDiscretionMain','id='+id); 
	}
	
	//将页面转成图片  
	function savePicture(){
		location.href="${webpath}/waitingIssue/downloadPdfModal?id=${pictureId}";
    /* html2canvas(document.getElementById('picture'), {
         allowTaint: false,
         taintTest: true,
         useCORS:true,
         onrendered: function(canvas) {
        	var myDate = new Date();
			var StrTime = myDate.getFullYear()+'年'+(myDate.getMonth()+1)+'月'+myDate.getDate()+'日'+myDate.getHours()+'时';
			   //处理IE浏览器
			if(!!window.ActiveXObject || "ActiveXObject" in window){
			     var blob = canvas.msToBlob();
			     navigator.msSaveBlob(blob,StrTime+"生成裁量结果.png");
			     return;
			    }
        	var imgUri = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream"); 
             fileDownload(imgUri);   
         },
         background:"#fff",
     })*/ 
	}
	//唤起浏览器下载
	function fileDownload(downloadUrl){
		let aLink = document.createElement('a');
		aLink.style.display = 'none';
		aLink.href = downloadUrl;
		var myDate = new Date();
		var StrTime = myDate.getFullYear()+'年'+(myDate.getMonth()+1)+'月'+myDate.getDate()+'日'+myDate.getHours()+'时';
		aLink.download = StrTime+"生成裁量结果.png";
		document.body.appendChild(aLink);
		aLink.click();
		document.body.removeChild(aLink);
	}
	
</script>
</html>