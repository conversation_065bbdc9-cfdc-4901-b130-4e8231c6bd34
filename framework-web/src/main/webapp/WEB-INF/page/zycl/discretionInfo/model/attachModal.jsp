<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script type="text/javascript">
	$(function(){
		//监听回退键
		business.listenBackSpace();    
	});
</script>
<script type="text/javascript">
	var id = $("#id").val();
	var obj = {id:id};
	$("#file-case").fileinput({
		theme: "explorer",
		language : 'zh',
		uploadUrl : WEBPATH + '/discretionInfo/PDFUpload',
		allowedFileExtensions : ['pdf'],
		maxFileSize : 1024*50,
		maxFilePreviewSize:1024*50,
		maxFileCount: 1,
		uploadAsync:false,
	  	initialCaption: "附件格式支持pdf，每个文件不大于50M，数量为1",
	  	uploadExtraData: function(previewId, index) {   //额外参数
            return obj;
        },
		slugCallback : function(filename) {
			return filename.replace('(', '_').replace(']', '_');
		}
	}).on('filebatchuploadsuccess', function(event, data, previewId, index) {
		if(data.response.data.success == '200'){
			var responseData = data.response.data;
			afterUploaded(responseData);
		} else {
			swal({title:"上传失败", text:data.response.meta.detailMessage, type:"error",allowOutsideClick :true});
			$('#dataForm').formValidation('revalidateField', 'fileName');
		}
	}).on('fileuploaded',function(event,data){
		if(data.response.data.success == '200'){
			var responseData = data.response.data;
			afterUploaded(responseData);
		} else {
			swal({title:"上传失败", text:data.response.meta.detailMessage, type:"error",allowOutsideClick :true});
			$('#dataForm').formValidation('revalidateField', 'fileName');
		}
	}).on('filesuccessremove', function(event, id) {
	   //alert(id);
	});
	$('#accessory1').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");
	})
	
	function afterUploaded(responseData){
		//清空上传文件框并设置可用
		$("#file-case").fileinput('refresh').fileinput('enable');
		for(var key in responseData.item){
			item[key] = responseData.item[key];//item全局变量（itemcode,itemid）
		
				$("#file-case").fileinput('refresh', {
					theme: "explorer",
					uploadExtraData:{objectId:objectId,upOrDown:upOrDown,tableName:tableName,"fileId":fileId,"divId":divId}
				})//刷新文件上传框的参数
			
		}
		for(var i=0;i<responseData.attachments.length;i++){
			var attachment=responseData.attachments[i];
			$("#fileDiv div").remove();
			$("#fileDiv").append("<div class='col-md-3 col-sm-3' id='"+attachment.id+"'></div>");
			var filename;
			var src;
			if(attachment.fileName != null && attachment.fileName.length>8){
				filename = attachment.fileName.substring(0,5)+"..."+attachment.fileName.substring(attachment.fileName.length-3);
			} else if(attachment.fileName != null && attachment.fileName.length<=8){
				filename = attachment.fileName;
			}
			if(attachment.fileType==2){
				src = "${webpath}/static/img/pdf-thumb.jpg";
			} else if (attachment.fileType==1) {
				src = "${FASTDFS_ADDR}/"+attachment.fileUrl;
			}
			$("#"+attachment.id).append('<div class="pricing-widget clean-pricing" style="cursor: pointer;"> <div class="pricing-value">' +
				'<span class="value"><img style="height: 130px;" data-toggle="modal" data-remote="${webpath}/discretionInfo/showModal?id='+attachment.id+'"'+
					'data-target="#inputImgModeler" id="P'+attachment.id+'" src="'+src+'"/></span></div> ' + 
				'<ul class="text-left padding-sm" style="margin-top: -20px;"><li title="'+attachment.fileName+'">文件名：'+filename+'</li>' + 
				'<li>文件大小：'+attachment.fileSize+'</li></ul></div>');
		}
		//维护信息到主信息页面
		$("#fileId").val(attachment.id); 
		$("#fileName").val(attachment.fileName); 
		$('#dataForm').formValidation('revalidateField', 'fileName');
		swal({title:"上传成功", text:"附件上传成功", type:"success",allowOutsideClick :true});
	}
	
	function deleteFile(id, fileName){
		var itemCode = $("#itemCode").val();
		var caseId = $("#caseId").val();
		var objectId = $("#objectId").val();
		var isDayPenalty = $("#isDayPenalty").val();
		swal({
	        title: "您确定执行删除操作吗？",
	        type: "warning",
	        showCancelButton: true,
	        closeOnConfirm: false,
	        confirmButtonText: "是的，我要删除",
	        confirmButtonColor: "#ec6c62"
	    	}, function() {
	            $.ajax({
	              type: "post",
	   		      url: WEBPATH+"/attachment/fileinfoDelete",
	   		      data:{attachId:id,itemCode:itemCode,fileName:fileName,caseId:caseId,objectId:objectId,isDayPenalty:isDayPenalty},
	            }).done(function(data) {
	            	 if(data.meta.result==="success"){
	 		        	swal({
	 		        		title : "删除成功", 
	                        text : "附件删除成功",
	                        type : "success"
	 		        	},function(){
	 		        		$("#"+id).remove();
	 		        		var names = $("#attach"+itemCode + " input").val();
	 		        		if(names != null && names != ""){
		 		        		names = names.replace(fileName,'');
	 		        		}
	 		        		if(names.lastIndexOf() == ','){
	 		        			names=names.substring(0,names.length-1);
	 		        		}
	 		        		if(names.substring(0,1) == ','){
	 		        			names=names.substring(1,names.length);
	 		        		}
	 		        		if(names.indexOf(',,') != -1){
	 		        			names=names.replace(',,',',');
	 		        		}
	 		        		$("#attach"+itemCode + " input").val(names);
	                    })
	 		        }else{
	 		          swal({title:"删除失败", text:data.meta.detailMessage, type:"error",allowOutsideClick :true});
	 		        }
	            }).error(function(data) {
	                swal({title:"操作失败", text:"请求异常，删除失败！", type:"error",allowOutsideClick :true});
	            });
	    });
	}
	
</script>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="myModalLabel">附件管理</h4>
</div>
<div class="modal-body">
	<input type="hidden" id="id" value="${id }">
	<div class="smart-widget-inner table-responsive">
		<div class="smart-widget-body form-horizontal" style="overflow: hidden">
			<div class="form-group" id="fileDiv">
					<c:if test="${sysFile.fileType==2}">
						<div class="col-md-3 col-sm-3" id="${sysFile.id }">
							<div class="pricing-widget clean-pricing"
								style="cursor: pointer;">
								<div class="pricing-value">
									<span class="value"><img style="height: 130px;"
										data-toggle="modal" id="P${sysFile.id}"
										data-remote="${webpath}/discretionInfo/showModal?id=${sysFile.id}"
										data-target="#inputImgModeler"
										src="${webpath}/static/img/pdf-thumb.jpg" /></span>
								</div>
								<ul class="text-left padding-sm" style="margin-top: -20px;">
									
									<c:if test="${sysFile.fileName != null && fn:length(sysFile.fileName)>8 }">
										<li>文件名：${fn:substring(sysFile.fileName,0,5) }...${fn:substring(sysFile.fileName,fn:length(sysFile.fileName)-3,fn:length(sysFile.fileName))}</li>
									</c:if>
									<c:if test="${sysFile.fileName != null && fn:length(sysFile.fileName)<=8 }">
										<li>文件名：${sysFile.fileName }</li>
									</c:if>
									<li>文件大小：${sysFile.fileSize }</li>
									<%-- <li class="text-center" style="margin-top:10px;">
										<button type="button" class="btn btn-info btn-xs"
											onclick="deleteFile('${sysFile.id}','${sysFile.fileName }')">删除</button>
									</li> --%>
								</ul>
							</div>
						</div>
					</c:if>
			</div>
			<hr>
			<div class="form-group" style="padding-left: 50px">
				<h4>附件上传&nbsp;<a href="javascript:void(0)"><%-- <c:if test="${not empty objectId }"><img src="${webpath }/static/img/QRcode-aj.png" style="margin-top:-3px;" id="folderX"data-toggle="popover" data-placement="bottom" data-html="true" 
										data-content="请使用手机App的扫一扫功能<br />扫描以下二维码：<br /><div id='qrCodeX' style=' width:230px; height:235px;padding-top:7px;'></div>"></c:if> --%></a></h4>
				<div class="col-lg-12">
					<form enctype="multipart/form-data">
						<input id="file-case" name="file-es[]" type="file" multiple>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
</div>


	<!-- 照片编辑modeler 结束 -->
