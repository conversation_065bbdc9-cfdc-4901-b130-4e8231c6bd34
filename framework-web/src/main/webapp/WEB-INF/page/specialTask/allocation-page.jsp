<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta charset="utf-8">
</head>
<script type="text/javascript">

$(document).ready(function(){
	business.listenEnter("searchButt");
	$('#dataTable').bootstrapTable({       
		 method: 'post',
		 dataType: "json", 
		 url:  WEBPATH+'/dis-special/getDate',
	     undefinedText : '-',  
	     pagination : true, // 分页  
	     striped : true, // 是否显示行间隔色  
	     cache : false, // 是否使用缓存  
	     pageSize:15, // 设置默认分页为 20
	     pageNumber: 1,
	     queryParamsType: "",
	     locale:'zh-CN',
	     pageList: [5, 15, 20, 30, 50], // 自定义分页列表
	     singleSelect: false,
	     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
	     // showColumns : true, // 显示隐藏列  
	     sidePagination: "server", //服务端请求
	     queryParams:function (params) {
	      		var templateName=$("[name='templateName']").val();
	      		
	      		var speciallevel = $('#speciallevel option:selected').val();   
	      		var isSpecial = $('#isSpecial option:selected').val();   
	      		
	      		var applyAreaName = $("[name='applyAreaName']").val();
	      		var updateTimeStart = $("[name='updateTimeStart']").val();
	      		var updateTimeEnd = $("[name='updateTimeEnd']").val();
	            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
            		pageNum: params.pageNumber,
                    pageSize: params.pageSize,
                 	templateName:templateName,
                 	speciallevel:speciallevel,
                 	isSpecial:isSpecial,
                 	applyAreaName:applyAreaName,
                 	updateTimeStart:updateTimeStart,
                 	updateTimeEnd:updateTimeEnd
	            };
	            return temp;
	     },//参数
	     uniqueId : "id", // 每一行的唯一标识  操作、序号、专项名称、专项级别、创建单位、更新时间、适用地区、持续时间、是否指定检查表、描述、附件
		 columns: [
		           {
		        	   field: "id",
			           title: "操作",
			           align: 'center',
			           formatter: function(value,row,index){
			        	   	 var launchType = '${launchType}';
			        	   	 return "<a onclick=\"launchSpecialTask('"+launchType+"','"+row.isSpecial+"','"+row.templateType+"','"+row.id+"')\" style=\"cursor:pointer; color: #23b7e5;\" >选择</a>"
				       }
		           },
		           {
		        	   field: "",
			           title: "序号",
			           align: 'center',
			           formatter: function(value,row,index){
				        	  return index+1;
				       }
		           }, 
		           {
			           field: "templateName",
			           title: "专项名称",
			           align: 'center'
			       },
		           {
			           field: "speciallevelName",
			           title: "专项级别",
			           align: 'center'
		           },
		           {
			           field: "createDepartment",
			           title: "创建单位",
			           align: 'center'
		           },
		           {
			           field: "updateTime",
			           title: "更新时间",
			           align: 'center',
			   		   formatter : function(value){
							if(value==null || value==''){return '';}
			                var date = new Date(value);
			                var y = date.getFullYear();
			                var m = date.getMonth() + 1;
			                var d = date.getDate();
			                var h = date.getHours();  
			                var i = date.getMinutes(); //分
			                var s = date.getSeconds(); //秒
			                return y + '-' +m + '-' + d;
			            }
		           },
		           {
			           field: "applyAreaName",
			           title: "适用地区",
			           align: 'center'
		           },
		           {
			           field: "",
			           title: "持续时间",
			           align: 'center',
			           formatter: function (value, row, index) { 
			        	   var html = '-';
			        	   if(row.durationStartTime!=null && row.durationStartTime!='' && row.durationStartTime!= undefined && row.durationEndTime!=null && row.durationEndTime!='' && row.durationEndTime!= undefined){
									var date1 = new Date(row.durationStartTime);
									var y1 = date1.getFullYear();
									var m1 = date1.getMonth() + 1;
									var d1 = date1.getDate();
									var h1 = date1.getHours();
									var mm = date1.getMinutes(); //获取当前分钟数(0-59)
									var date2 = new Date(row.durationEndTime);
									var y2 = date2.getFullYear();
									var m2 = date2.getMonth() + 1;
									var d2 = date2.getDate();
									var h2 = date2.getHours();
									var mm = date2.getMinutes(); //获取当前分钟数(0-59)
									html =  y1 + '-' + m1 + '-' +d1+'-'+y2+'-'+m2+'-'+d2;
			        	   }
			        	   return html;
	                   }
		           },
		           {
			           field: "isSpecial",
			           title: "是否指定检查表",
			           align: 'center',
			   		   formatter : function(value){
							// 0：关联专项行动  1：特定专项行动			   			   
							if(value==1)
								return '是'
							 else
								return '否'
			            }
		           },
		           {
			           field: "describe",
			           title: "描述",
			           align: 'center',
			           formatter: function(value,row,index){
			        	   var allValue = '';
			        	   if(value!=null){
			        		   allValue = value;
			        	   }
			        	   var smallValue = '';
			        	   if(value!=null && value.length>30){
			        		   smallValue = value.substring(0,30)+'...';
			        	   }else if(value!=null){
			        		   smallValue = value;
			        	   }
			        	   return "<span title='"+allValue+"'> "+smallValue+" </span>";
				       }
		           },
		           {    
		        	   field: 'id',
			           title: "附件",
			           align: 'center',
				           formatter: function (value, row, index) { 					        	  
				        		   return "<a style='color:#43c1e9;cursor:pointer;' href='#' onclick=\"attachmentPreviewer('"+row.id+"')\" tabindex='-1'>预览</a>";
				           }
		           }
		 ],
		 responseHandler : function(res) {  
               return {  
                   total : res.total,  
                   rows : res.list  
               };  
         },
         onCheck: function(row, $element) {
    	   
         },//单击row事件
         onUncheck: function(row, $element) {
        		
	     },
	     onUncheckAll: function(row, $element) {
	       			
	     },
	     onCheckAll:function(row, $element) {
	        		
	     },
	     onRefresh: function () {
	        		
	     },
         formatLoadingMessage: function () {
        	   return "玩命加载中...";
         },
         formatNoMatches: function () { //没有匹配的结果
        		   return '无符合条件的记录';
         }
	});
	//绑定搜索按钮
	$('#searchButt').click(function() {
		$('#dataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
   	});
});
	function attachmentPreviewer(actionId){
			var options = {
				remote:encodeURI(WEBPATH+'/HomeSpecialInlet/attachmentPreviewer?actionId='+actionId)
			  };
			$('#fjyl').modal(options);
		}
</script>


<body class="overflow-hidden">
	<div class="main-container">
		<div class="padding-md">
			<!--第一层快速查询row-->
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget">
						<div class="smart-widget-inner">
							<div class="smart-widget-body form-horizontal">

 									<form id="searchForm" role="form">
										<input id="launchType" name="launchType" type="hidden" value='${launchType}'/>
                                    	<div class="form-group">
                                            <label class="control-label col-md-2">专项名称</label>
                                            <div class="col-md-2">
                                                <input type="text" placeholder="专项名称" class="form-control"  id="templateName" name="templateName">
                                            </div>
                                            <label class="control-label col-md-1">专项级别</label>
                                            <div class="col-md-2">
                                                <select class="form-control" id="speciallevel" name="speciallevel">
                                                    <option value="">—请选择—</option>
													<option value="0">部级</option>
													<option value="1">省级</option>
													<option value="2">市级</option>
													<option value="3">县级</option>
                                                </select>
                                            </div>
                                            <label class="col-lg-1 control-label">是否指定检查表</label>
											<div class="col-lg-2">
												<select class="form-control" id="isSpecial"  name="isSpecial">
													<option value="">请选择</option>
													<option value="1">是</option>
													<option value="0">否</option>
												</select>
											</div>                                            
                                        </div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2 col-sm-2">适用地区</label>
                                            <div class="col-md-2 col-sm-2">
                                                <input type="text" placeholder="适用地区" class="form-control"  id="applyAreaName" name="applyAreaName" >
                                            </div>  
                                            <label class="control-label col-md-1 col-sm-2">更新时间</label>
                                            <div class="col-md-2 col-sm-2" style="padding:0;">
                                            	<div class="col-md-6 col-sm-6">
                                                <input type="text" placeholder="开始时间" class="form-control" readonly="readonly" id="updateTimeStart" name="updateTimeStart">
                                                </div>
                                                <div class="col-md-6 col-sm-6">
                                                <input type="text" placeholder="结束时间" class="form-control" readonly="readonly" id="updateTimeEnd" name="updateTimeEnd">
                                                </div>
                                            </div>  
                                            <div class="col-md-3 text-right">
                                                  <button class="btn btn-info" type="button" id="searchButt" style="width:100px;">查询</button>
                                            </div>                                          
										</div>
								 </form>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--./第一层快速查询row-->

			<!--第二层任务办理row-->
			<div class="row">
				<!--任务办理-->
				<div class="col-lg-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 专项选择 <span
								class="smart-widget-option"> <span
								class="refresh-icon-animated"> <i
									class="fa fa-circle-o-notch fa-spin"></i>
							</span>

							</span>
						</div>
						<div class="smart-widget-inner table-responsive">

							<table class="table table-striped no-margin table-no-bordered"
								id="dataTable">

							</table>
						</div>
					</div>
				</div>
			</div>
		</div>
	<!-- 附件预览  -->
	<div class="modal fade" id="fjyl" tabindex="-1" role="dialog" aria-labelledby="fileModalLabel" aria-hidden="false">
		   <div class="modal-dialog  modal-lg">
	        <div class="modal-content">
			</div>
		</div>
	</div>
	<script type="text/javascript">
		$(function() {
			//监听回退键
			business.listenBackSpace();
			
			// 搜索时间控件的控制 
			$("#updateTimeStart").datetimepicker({
				 	format:'yyyy-mm-dd',
					language: 'cn',
					autoclose : true,
					todayBtn : true,
					clearBtn : true,
					minView : 'month',
					maxView : 'decade' 
			}).on('changeDate',function(ev){
				$("#updateTimeEnd").datetimepicker('setStartDate',new Date($("#updateTimeStart").val()));
			});
			
			$("#updateTimeEnd").datetimepicker({
				 	format:'yyyy-mm-dd',
					language: 'cn',
					autoclose : true,
					todayBtn : true,
					clearBtn : true,
					minView : 'month',
					maxView : 'decade' 
			}).on('changeDate',function(ev){
				$("#updateTimeStart").datetimepicker('setEndDate',new Date($("#updateTimeEnd").val()));
			});
			
		});
		
		/**
		 * 发起《关联专项》 和 《特定专项》
		 * launchType: 0:分配执法  1：直接现场执法
		 * isSpecial： 0：关联专项行动  1：特定专项行动
		 * sceneSysspecailId专项行动id
		 */
		function launchSpecialTask(launchType,isSpecial,templateType,sceneSysspecailId){
			 //alert(launchType+"-------"+isSpecial+"-------"+templateType+"---------"+sceneSysspecailId);
			 if(launchType==0){ // 0:分配执法
				 ClickTag();
				 if(isSpecial==0){ // 0：关联专项行动
					  macroMgr.onLevelTwoMenuClick(null, 'jcbl/rwfp',{taskFromType:'1','homeSpeciaId':sceneSysspecailId})
				 }else{// 1：特定专项行动
					  macroMgr.onLevelTwoMenuClick(null, 'jcbl/rwfp',{taskFromType:templateType,homeSpeciaId:sceneSysspecailId})
				 }
				 openClickTag("element-one-xtsy","element-one-jcbl","element-two-ywbl","element-three-rwfp");
			 }else if (launchType==1){
				 ClickTag();
				 if(isSpecial==0){ // 0：关联专项行动
					  macroMgr.onLevelTwoMenuClick(null, 'jcbl/startLocaleChick',{taskFromType:templateType,'homeSpeciaId':sceneSysspecailId})
				 }else{// 1：特定专项行动
					  macroMgr.onLevelTwoMenuClick(null, 'jcbl/startLocaleChick',{taskFromType:templateType,'homeSpeciaId':sceneSysspecailId})
				 }
				 openClickTag("element-one-xtsy","element-one-jcbl","element-two-ywbl","element-three-rwfp");	
			 }else{  
				 return ;
			 }
		}
		
	</script>
</body>
</html>