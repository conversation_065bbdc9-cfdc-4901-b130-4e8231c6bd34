<%@ page language="java" contentType="text/html; charset=utf-8" pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
 <% 
 	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
 	String wgServerIp = PropertiesHandlerUtil.getValue("wg.server.ip","fujianWG");
 	String wgFastdfsServerIp = PropertiesHandlerUtil.getValue("wg.fastdfs.server.ip","fujianWG");
 %>  
 <c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set> 
 <c:set var="WG_SERVER_IP"><%=wgServerIp%></c:set> 
 <c:set var="WG_FASTDFS_SERVER_IP"><%=wgFastdfsServerIp%></c:set> 
 <script type="text/javascript">
	var wGServerIp = '${WG_SERVER_IP}';
	var wGFastdfsServerIP='${WG_FASTDFS_SERVER_IP}';
</script>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta charset="utf-8">
<title></title>
<style type="text/css">
	 img{max-width:1500px;} /*百度地图标点放大*/
	  .jgdx-title{
        border-bottom: 3px solid #58fb58;
        width: 60px;
        margin:0 0 0 20px;
	    }
	    .jgdx-title111{
	        border-bottom: 3px solid #1418da;
	        width: 60px;
	        margin:10px 0 0 20px;
	    }
	    .jgdx-title1{
	        color: #58fb58;
	        margin: 0 0 4px 2px;
	        font-size: 19px;
	    }
	    .jgdx-title2{
	        color: #58fb58;       
	        font-size: 14px;
	        position: relative;
	        top: -30px;
	        left: 100px;
	    }
	    .jgdx-title11{
	        color: #1418da;
	        margin: 0 0 4px 2px;
	        font-size: 19px;
	    }
	    .jgdx-title21{
	        color: #1418da;       
	        font-size: 14px;
	        position: relative;
	        top: -30px;
	        left: 100px;
	    }
	    .yuan{
	        border-radius: 50%;
	        width: 12px;
	        height: 12px;
	        background: #505150;
	    }	
	    .back{
	        color: black;
	        margin-left: 20px;
	    }
	   .after{
	        margin-top: 10px;
	        color: gray
	    }
	    .fenge{
	        border-bottom: 1px dashed black;
	    }
</style>
</head>
<body>
<div class="main-container">
	<div class="padding-md">
      <div class="smart-widget widget-blue">
      <!-- <input type="hidden" value="http://*************:8099/"  id="fastdfsServer"> 网格巡查附件服务IP -->
       <input type="hidden"  class="form-control"  id="parkId" value=${park.id }>
       <input type="hidden"  class="form-control"  id="parkBelongAreaId" value=${park.belongAreaId }>
       <input type="hidden"  class="form-control"  id="lawPersonNum" value=${lawNum }>
       <p style="color:#23b7e5;font-size:18px;margin:20px 0 0 35px;font-weight: bold">${park.name }</p>
       <span style="position: relative;top:-26px;left:500px;">
       		<button class="btn" style="background-color:transparent;color:#23b7e5;outline: none;border: 0px;l" type="submit">
       			<c:if test="${park.parkLevel == 0 }">国家级</c:if>
       			<c:if test="${park.parkLevel == 1 }">省级</c:if>
       			<c:if test="${park.parkLevel == 2 }">市级</c:if>
       			<c:if test="${park.parkLevel == 3 }">县级</c:if>
      		</button>
       </span>
       <div class="smart-widget-body">								
        <div class="form-horizontal" style="margin:-20px 0 0 30px;">										
		  <div style="position: absolute;top: 20px;right: 20px;">
			<button class="btn btn-info" onClick="back()" >返回</button>
		  </div>
		<div class="form-group">
			<label for="inputtext3" class="col-md-1 control-label">园区类别：</label>
			<div class="col-md-7"><div style="margin-top: 8px;">${park.parkTypeName }</div></div>
		</div>
		<div class="form-group">
		    <label for="inputEmail3" class="col-md-1 control-label">主导行业：</label>
		    <div class="col-md-7"><div style="margin-top: 8px;">${park.leadingIndustries }</div></div>
		</div>		
		</div>																
	   </div>
       <div class="smart-widget-inner" style="margin-top:40px;">							
            <div class="widget-tab clearfix">
             <ul class="tab-bar font-18" style="height: 45px;">
                 <li class="active" style="font-size:16px;"> <a href="#jbxx" data-toggle="tab"><i class="fa fa-file-text-o"></i> 基本信息</li>
                 <li class="" style="font-size:16px;"><a href="#jgdw" id="gridAndLawTab" data-toggle="tab" style="margin-top: -20px;"><i class="fa fa-file-text-o"></i>队伍信息<span class="badge badge-info bounceIn animation-delay2 pull-right" id="dwxxNumber">0</span></a></li>
                 <li class="" style="font-size:16px;"><a href="#jgdx" id="jgdxTab" data-toggle="tab"><i class="fa fa-file-text-o"></i> 监管对象<span class="badge badge-info bounceIn animation-delay2 pull-right" id="jgdxNumber">${lawCount }</span></a></li>
                 <li class="" style="font-size:16px;"><a href="#wgxc" id="xunchaTab" data-toggle="tab"><i class="fa fa-file-text-o"></i> 网格巡查<span class="badge badge-info bounceIn animation-delay2 pull-right" id="wgxcNumber">0</span></a></li>
                 <li class="" style="font-size:16px;"><a href="#wgsj" id="shijianTab" data-toggle="tab"><i class="fa fa-file-text-o"></i> 网格事件<span class="badge badge-info bounceIn animation-delay2 pull-right" id="wgsjNumber">0</span></a></li>
                 <li class="" style="font-size:16px;"><a href="#xczf" id="taskTab" data-toggle="tab"><i class="fa fa-file-text-o"></i> 环境执法<span class="badge badge-info bounceIn animation-delay2 pull-right" id="hjzfNumber">${taskCount }</span></a></li>
                 <li class="" style="font-size:16px;"><a href="#ajcb" id="caseTab" data-toggle="tab"><i class="fa fa-file-text-o"></i> 案件查办<span class="badge badge-info bounceIn animation-delay2 pull-right" id="ajckNumber">${caseCount }</span></a></li>
             </ul>                       
            </div>
            <div class="smart-widget-body">
                 <div class="tab-content">
                 <!-- 基本信息 -->
				<div class="tab-pane fade in active" id="jbxx" >
				  <div class="form-horizontal">
                       <div style="position: absolute;right: 50px;top: 240px;">
                             <div id="map_main" style="width: 400px; height:300px;"></div>
                             <div style="text-align: center;font-size: 14px;">园区中心点位置</div>
                       </div> 
                     <c:if test="${not empty park.pointsParkName }">
                     <div class="form-group">
                             <label for="inputtext3" class="col-md-2 control-label"> 分园名称：</label>
                             <div class="col-md-7"><div style="margin-top: 8px;">${park.pointsParkName }</div></div>
                     </div>
                     </c:if>
					 <div class="form-group">
                             <label for="inputtext3" class="col-md-2 control-label"> 所属行政区：</label>
                             <div class="col-md-7"><div style="margin-top: 8px;">${park.belongAreaCityName }${park.belongAreaName }</div></div>
                     </div>
                     <!-- <div class="form-group">
                            <label for="inputtext3" class="col-md-2 control-label"> 涉及网格：</label>
                     	    <div class="col-md-5"><div style="margin-top: 8px;">${park.wgName }</div></div>
                     </div> -->
                     <div class="form-group">
                     		<label for="inputtext3" class="col-md-2 control-label">成立时间：</label>
                     		<div class="col-md-7"><div style="margin-top: 8px;">${park.buildtime }</div></div>
                     </div>
                     <div class="form-group">
                    		 <label for="inputEmail3" class="col-md-2 control-label">负责人：</label>
                     		<div class="col-md-7"><div style="margin-top: 8px;">${park.legalPerson }</div></div>
                     </div>		
                     <div class="form-group">
                               <label for="inputEmail3" class="col-md-2 control-label">联系人：</label>
                               <div class="col-md-7"><div style="margin-top: 8px;">${park.chargePerson }</div></div>
                     </div>	
                     <div class="form-group">
                               <label for="inputtext3" class="col-md-2 control-label"> 联系人邮箱：</label>
                               <div class="col-md-7"><div style="margin-top: 8px;">${park.chargeEmail }</div></div>
                     </div>
                     <div class="form-group">
                               <label for="inputtext3" class="col-md-2 control-label"> 联系人手机：</label>
                               <div class="col-md-7"><div style="margin-top: 8px;">${park.chargePhone }</div></div>
                     </div>
                     <div class="form-group">
                                <label for="inputtext3" class="col-md-2 control-label">联系人微信：</label>
                                <div class="col-md-7"><div style="margin-top: 8px;">${park.chargeWechat }</div></div>
                     </div>
                     <div class="form-group">
                     			<label for="inputtext3" class="col-md-2 control-label">范围描述：</label>
								<div class="col-md-5">
									<div style="margin-top: 8px;">
										<p>${park.scopeDescription }</p>
										<p style="color: #999;">——${park.scopeFileName }</p>
									</div>
								</div>
					</div>                            
				</div>
			</div>
			<!-- 队伍信息 -->
            <div class="tab-pane fade" id="jgdw">
                 <div class="form-horizontal">
			<div class="jgdx-title">
				<p class="jgdx-title1">网格员</p>
			</div>
			<span class="jgdx-title2" id="wgyNum"></span>
			<button class="btn btn-info" onclick="exportWgUserExcel()" style="width:80px;float: right;">导出Excel</button>
			<div style="height: 300px; overflow-y: auto;">
				<table class="jgdx-table" id="isGridsListTable">
					<tr  v-for="(item, index)  in isGridsList" >
						<td style="width: 45px;">
							<div class="yuan"></div>
						</td>
						<td style="font-size: 14px; color: black; width: 100px;">{{ item.loginName }} </td>
						<td style="font-size: 14px; width: 270px;">
							<p class="after">
								所属网格：<span class="back">{{ item.gridName }}</span>
							</p>
						</td>
						<td style="font-size: 14px; width: 230px;">
							<p class="after">
								手机号：<span class="back">{{ item.phone }}</span>
							</p>
						</td>
					</tr>
				</table>
			</div>
			<div class="fenge"></div>
			<div class="jgdx-title111">
				<p class="jgdx-title11">执法员</p>
			</div>
			<span class="jgdx-title21" id="zfyNum"></span>
			<button class="btn btn-info" onclick="exportZfUserExcel()" style="width:80px;float: right;">导出Excel</button>
			<div style="height: 300px; overflow-y: auto;">
				<table class="jgdx-table" id="isLawoffListTable">
					<tr v-for="(item, index)  in isLawoffList" >
						<td style="width: 45px;">
							<div class="yuan"></div>
						</td>
						<td style="font-size: 14px; color: black; width: 100px;">{{ item.loginname }}</td>
						<td style="font-size: 14px; width: 250px;">
							<p class="after">
								所属行政区：<span class="back">{{ item.belongAreaName }}</span>
							</p>
						</td>
						<td style="font-size: 14px; width: 350px;">
							<p class="after">
								所属部门：<span class="back">{{ item.belongDepartmentName }}</span>
							</p>
						<td>
						<td style="font-size: 14px; width: 200px;">
							<p class="after">
								职务：<span class="back">{{ item.jobName }}</span>
							</p>
						<td>
						<td style="font-size: 14px; width: 270px;">
							<p class="after">
								手机号：<span class="back">{{ item.phone }}</span>
							</p>
						</td>
					</tr>
				</table>
			</div>
		</div>
			</div>
			<!-- 监管对象 -->
             <div class="tab-pane fadein" id="jgdx">
				<div class="smart-widget-inner">									
					<div class="smart-widget-body form-horizontal">
				 		<div class="form-group">
							<label class="control-label col-lg-1">监管对象名称</label>
							<div class="col-lg-2">
							<input type="text" placeholder="监管对象名称" class="form-control" data-parsley-required="true" id="lawObjName">
							</div>
							<div class="col-lg-8">
								<button class="btn btn-info" id="jgdxBtn" style="width:80px;">查询</button>
								<button class="btn btn-info" onclick="exportParkExcel()" style="width:80px;float: right;">导出Excel</button>
							</div>
							<div class="col-lg-8"></div>                                      
						</div>
					</div>
				</div>
				<table class="table-no-bordered" id ="jgdxData"></table>
			</div>
		<!-- 网格巡查 -->
			<div class="tab-pane fade" id="wgxc">
					<div class="smart-widget-body form-horizontal">											
						<div class="form-group">
<!-- 								<label class="col-lg-1 control-label">对象名称</label> -->
<!-- 								<div class="col-lg-2"> -->
<!-- 									<input class="form-control" placeholder="对象名称" id ="lawNameInput"> -->
<!-- 								</div> -->
							<label class="col-lg-1 control-label">巡查内容</label>
							<div class="col-lg-2">
								<input class="form-control" placeholder="巡查内容" id="descInput">
							</div>
							<label class="col-lg-1 control-label">发布时间</label>
							<div class="col-lg-2 no-padding">
								<div class="col-lg-6">
									<input type="text" class="form-control"  readonly="readonly" placeholder="开始时间" id="creatDateBegin" name="creatDateBegin"/>
							 
								</div>
								<div class="col-lg-6">
									<input type="text" class="form-control"  readonly="readonly" placeholder="结束时间" id="creatDateEnd" name="creatDateEnd"/>
								</div>
							</div>
							<div class="col-lg-2 " style="padding-left: 20px">
									<button class="btn btn-info"  onclick="searchPatroDataByXunCha()">查询</button>
									<button class="btn btn-info" onclick="exportWgxcExcel()" style="width:80px;float: right;">导出Excel</button>
								</div>
						</div>	
					</div>
					<div class="form-horizontal"  >
					<div class="row padding-md">
						<div class="col-lg-10 col-md-10">
							 <div class="" id="objectComment"> 
							  <div class="timeline-item-inner" v-for="(item2,index2) in dataList">
							    <div >
									<div class="font-14 padding-xs">
										<span> {{ item2.creatDate | timestampToTime }} </span>
										<span style="padding: 0 50px 0 100px;">{{item2.creatUserName}}</span>
										<span>{{item2.creatDepartmentName}}</span>
										<span>{{item2.objName}}</span>
									</div>
									<div class="font-14 padding-xs">{{item2.title}}</div>
									<div  v-if="item2.fileList !=null && item2.fileList.length>0">
										<img class="padding-xs" v-for="(item3,index3) in item2.fileList" :src="item3.fileUrl,item3.fileType | getFileUrl"  v-on:click="showImgModal(item3.patroId,item3.originallyImgId)"
																style="width: 100px;height:80px; cursor: pointer;">
									</div>
								</div>
								<hr>
							  </div>
							  <div  id="wgxcjagd"style="text-align: center; color: red;margin-top: 10px" v-on:click="WGXCDataAppend()" >加载更多</div>
							 </div>
					   </div>
				   </div>                                                
				  </div>
			</div>
				
				<!-- 网格事件 -->
				<div class="tab-pane fade" id="wgsj">
					<div class="smart-widget-body form-horizontal">											
					<div class="form-group">
						<label class="col-lg-1 control-label">事件描述</label>
						<div class="col-lg-1 ">
							<input class="form-control" placeholder="事件描述" id="descInputSJ">
						</div>
						<label class="col-lg-1 control-label">事件发起时间</label>
						<div class="col-lg-2 no-padding">
							<div class="col-lg-6">
								<input type="text" class="form-control"  readonly="readonly" placeholder="开始时间" id="creatDateBeginSJ" name="creatDateBeginSJ"/>
						 
							</div>
							<div class="col-lg-6">
								<input type="text" class="form-control"  readonly="readonly" placeholder="结束时间" id="creatDateEndSJ" name="creatDateEndSJ"/>
							</div>
						</div>
						<label class="col-lg-1 control-label">事件状态</label>
						<div class="col-lg-1">
							<select class="form-control" id="nodeTimeoutStateSJ">
								<option value="">请选择</option>
								<option value="0">未处理</option>
								<option value="1">处理中</option>
								<option value="2">已办结</option>
								<option value="3">无需处理</option>
							</select>
						</div>
						<div class="col-lg-2" style="padding-left: 20px">
								<button class="btn btn-info" t onclick="searchPatroDataByIfSJ()">查询</button>
								<button class="btn btn-info" onclick="exportWgsjExcel()" style="width:80px;float: right;">导出Excel</button>
							</div>
					</div>	
				</div>
		<div class="form-horizontal"  >
				<div class="row padding-md">
					<div class="col-lg-10 col-md-10">
						 <div class="" id="objectCommentSJ">
						 <div class="timeline-item-inner" v-for="(item2,index2) in dataList">
						 <div >
							<div class="font-14 padding-xs">
								<span>事件编号：{{item2.taskNumber}}</span><span style="padding: 0 50px 0 100px;">{{ item2.creatDate | timestampToTime }} 发起</span>
								<!-- 0未处理；  1 处理中；  2 办结 ； 3 无需处理 -->
								<template  v-if="item2.taskStateCode!=null && item2.taskStateCode == 0" >
									<span style="border-radius: 15px;background: #23b7e5;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:-5px;">未处理</span>
								</template>
								<template  v-if="item2.taskStateCode!=null &&  item2.taskStateCode == 1" >
									<span style="border-radius: 15px;background: #23b7e5;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:-5px;">处理中</span>
								</template>
								<template  v-if="item2.taskStateCode!=null &&  item2.taskStateCode == 2" >
									<span style="border-radius: 15px;background: grey;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:-5px;">已办结</span>
								</template>
								<template  v-if="item2.taskStateCode!=null &&  item2.taskStateCode == 3" >
									<span style="border-radius: 15px;background: #23b7e5;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:-5px;"> 无需处理</span>
								</template>
										<a :href="'http://10.158.15.50:11027/zhjg/auth/goHome?redirectId='+item2.id+'&openPageType=xzcfsjxq'" target="_blank" style="margin:0 0 0 40px; color: blue"> 查看详情</a>
							</div>
							<div class="font-14 padding-xs">{{item2.taskDesc}}</div>
							<div class="font-14 padding-xs">污染类型：{{item2.polluteCodeName}}</div>
							<div  v-if="item2.fileList !=null && item2.fileList.length>0">
								<img class="padding-xs" v-for="(item3,index3) in item2.fileList" :src="item3.fileUrl,item3.fileType | getFileUrl" v-on:click="showImgModal(item3.taskId,item3.id)"
															style="width: 100px;height:80px; cursor: pointer;">
							</div>
						</div>
						<hr>
						</div>
						<div  id="wgsjjzgd"style="text-align: center; color: red;margin-top: 10px" v-on:click="WGSJDataAppend()" >加载更多</div>
						</div>
					</div>
				</div>                                                
		</div>
				</div>
					<!-- 现场执法 -->
					<div class="tab-pane fade" id="xczf">
						<div class="smart-widget-body form-horizontal">		
							<div class="form-group">
							<label class="col-lg-1 control-label">执法对象</label>
							<div class="col-lg-2">
								<input class="form-control" placeholder="执法对象" id="taskLawObjectName">
							</div>
							<label class="col-lg-1 control-label">执法人员</label>
							<div class="col-lg-2">
								<input class="form-control" placeholder="执法人员" id= "taskLawPerson">
							</div>
							<label class="col-lg-1 control-label">执法状态</label>
							<div class="col-lg-1">
								<select class="form-control" id="taskStatus">
										<option value="">请选择</option>
										<option value="任务分配">任务分配</option>
										<option value="办理中">办理中</option>
										<option value="已办结">已办结</option>
										<option value="已删除">已删除</option>
								</select>
							</div>
							<label class="col-lg-1 control-label">执法时间</label>
							<div class="col-lg-2 no-padding">
									<div class="col-lg-6">
										<input class="form-control" placeholder="开始时间" id="taskBeginTime">
									</div>
									<div class="col-lg-6">
										<input class="form-control" placeholder="结束时间" id="taskEndTime">
									</div>
							</div>                                                            
							</div>
							<div class="col-lg-12" style="padding-left: 85%">
								<button class="btn btn-info" id="taskSearchBtn">查询</button>
								<button class="btn btn-info" onclick="exportHjzfExcel()" style="width:80px;">导出Excel</button>
							</div>
							
						</div>                                                     
						<div class="form-horizontal" >
							<div class="row padding-md">
								<div class="col-lg-10 col-md-10">
									<div class="">
										<div id="taskDiv">
										<div class="timeline-item-inner" style="margin-left:25px" v-for="(item,index) in dataList">
										<div class="font-14 padding-xs">
											<span>执法编号：{{item.taskId}}</span>
											<span style="border-radius: 15px;background: #23b7e5;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:40px;">{{item.taskStateName}}</span>
											<a href="javascript:void(0);" style="margin:0 0 0 40px; color: blue"><span v-on:click="handle_Task(item.id,item.lawObjectType,item.taskStateCode)">查看详情</span></a>
										</div>                     
										<div class="font-14 padding-xs" style="color: blue">{{item.lawObjectName}}</div>
										<div class="font-14 padding-xs">执法人员及执法证件编号：{{item.checUserNames}}</div>
										<div class="font-14 padding-xs">执法时间：{{item.lawEnforcementStartTimeStr}} 到 {{item.lawEnforcementEndTimeStr}}</div>
										<div class="font-14 padding-xs">监察小结：{{item.checkSummary}}</div>
 										<div class="font-14 padding-xs"> 
 											<img  v-for="(item2,index2) in item.fileList":src="getItemUrl(item2.picUrl)" style="width:100px;height:80px;margin-left:25px;margin-top:5px" v-on:click="pictureYL(item.id,index2)">	 
										</div> 
										<hr>
										</div>
										<div  id="zczfjzgd"style="text-align: center; color: red;margin-top: 10px" v-on:click="taskDataAppend()" >加载更多</div>
									</div>
								</div>
							</div>                                                
						</div>
					</div>
					</div>
					<!-- 案件查办 -->
					<div class="tab-pane fade" id="ajcb">
						<div class="smart-widget-body form-horizontal">											
							<div class="form-group">
								<label class="col-lg-1 control-label">案件编号</label>
								<div class="col-lg-2"><input class="form-control" placeholder="案件编号" id="caseNumber"></div>
								<label class="col-lg-1 control-label">案件名称</label>
								<div class="col-lg-2"><input class="form-control" placeholder="案件名称" id="caseObject"></div>
								<label class="col-lg-1 control-label">创建时间</label>
								<div class="col-lg-2 no-padding">
									<div class="col-lg-6"><input class="form-control" placeholder="开始时间" id="caseStartTime"></div>
									<div class="col-lg-6"><input class="form-control" placeholder="结束时间" id="caseEndTime"></div>
								</div>
								<label class="col-lg-1 control-label">案件状态</label>
								<div class="col-lg-1">
									<select class="form-control" id="caseStatus">
										   <option value="">请选择</option>
										   <option value="0">进行中</option>
										   <option value="1">已办结</option>
										  
									</select>
								</div>
								<div class="col-lg-12"style="padding-left: 85%;padding-top: 10px">
									<button class="btn btn-info" id="caseSearchBtn" >查询</button>
									<button class="btn btn-info" onclick="exportAjckExcel()" style="width:80px;">导出Excel</button>
								</div>
							</div>	
						</div>                                                    
						<div class="form-horizontal" >
							<div class="row padding-md">
								<div class="col-lg-10 col-md-10">
									<div class="">
										<div id="caseDiv">
										<div class="timeline-item-inner" style="margin-left:25px" v-for="(item,index) in dataList">
											<div class="font-14 padding-xs">
												<span>案件编号:{{item.caseNumber}}</span>
												<span style="border-radius: 15px;background: #23b7e5;color:white;font-size: 12px;padding: 2px 5px 2px 5px;margin-left:40px;">{{item.caseStatusName}}</span>
												<a href="javascript:void(0);" target="_blank" style="margin:0 0 0 40px; color: blue"><span v-on:click="handle_case(item.id )">查看详情</span></a>
											</div>
											<div class="font-14 padding-xs" style="color: blue">{{item.caseName}}</div>
											<div class="font-14 padding-xs">处理主体：{{item.punishSubject}}</div>
											<div class="font-14 padding-xs">调查机构：{{item.researchOrgName}}</div>
											<div class="padding-xs">
												<img src="../../static/img/pdf1.jpg" style="width:100px; cursor: pointer;" data-toggle="modal" data-target="#img">
												<a href="javascript:void(0);" style="color:blue;margin-left:50px;" data-toggle="modal" data-target="#pdf">
													<span v-on:click="case_Click(item.id)">查看卷宗</span>
												</a>
											</div>
											<hr>
										</div>
										<div  id="ajckjzgd"style="text-align: center; color: red;margin-top: 10px" v-on:click="caseDataAppend()" >加载更多</div>
										</div>
							</div>                                                
						</div>
					</div>
                   </div>
                 </div>
               
             </div>
			</div>
        </div>
         <!-- 图片轮播 -->
        <div class="modal fade" id="pictureImg" tabindex="-1"role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-md">                                                                                            
				<div class="modal-content">
				</div>
			</div>
		</div>
		<!-- 网格巡查、事件附件预览modal -->
		<div class="modal fade" id="fileModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-md">
				<div class="modal-content" id="modalContent">
				</div>
			</div>
		</div>
</body>
<script type="text/javascript">
//网格数据调用参数使用
var wgParameter;
   //园区中心点位置    地图
	$(function(){
		var map = new BMap.Map("map_main");
		var mapJD = '${park.gisCoordinateX}';
		var mapWD = '${park.gisCoordinateY}';
		var old_map = "";//$('#map').val(); 
		if(mapJD!="" && mapWD!=""){
			old_map = mapJD+","+mapWD;
		}
		if (old_map.length > '5') { //打开的时候显示已设置的
			var oldMap = old_map.split(",");
			var point = new BMap.Point(oldMap[0], oldMap[1]);
			//map.centerAndZoom("福建省",12); 
			map.centerAndZoom(point, 12);
			var marker = new BMap.Marker(point); // 创建标注    
			map.addOverlay(marker);
			marker.disableDragging();   
			
		}
		getParameter();//获取参数
	})
   //给时间插件fu初值
	function GetDateStr(AddDayCount) {
	    var dd = new Date();
	    dd.setDate(dd.getDate()+AddDayCount);//获取AddDayCount天后的日期
	    var y = dd.getFullYear();
	    var m = dd.getMonth()+1;//获取当前月份的日期
	    var d = dd.getDate();
	    return y+"-"+m+"-"+d;
	}
	 function getParameter(){
		var parkId = $("#parkId").val();
		$.ajax({
			type : "post",
			url : WEBPATH +"/parkArchives/getParameterBySource",
			data : {parkId:parkId},
			dataType : "json",
			success : function(data) {
				wgParameter = data;
				getNumberWGdwxx();
				getNumberWGwgxc();
				getNumberWGsjxc();
			}
		  });
		}
	 
		function getNumberWGdwxx(){
			var visit = wgParameter;
			//console.log(a);
			$.ajax({
				type : "post",
	            url:wGServerIp+"/zhjg/publicVisit/getUserCount",
				data : {visitJson:visit},
				dataType : "json",
				success : function(data) {
					debugger
					$("#dwxxNumber").html("");
					var num = $("#lawPersonNum").val();
					if(data.data ==  null){
						$("#dwxxNumber").append(num);
					}else{
						$("#dwxxNumber").append(parseInt(num)+parseInt(data.data));
					}
				}
			});
		}
		function getNumberWGwgxc(){
			var visit = wgParameter;
			var lawName = '' ;
			var desc ='' ;
			var begDate = '';
			var endDate = '';
			desc = $.trim($("#descInput").val());
	   		begDate = $("#creatDateBegin").val();
	  		endDate = $("#creatDateEnd").val();
			$.ajax({
				type : "post",
	            url:wGServerIp+"/zhjg/publicVisit/getObjectCommentCount",
				data : {visitJson:visit,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate},
				dataType : "json",
				success : function(data) {
					$("#wgxcNumber").html("");
					if(data.data ==  null){
						$("#wgxcNumber").append("0");
					}else{
						$("#wgxcNumber").append(data.data);
					}
				}
			});
		}
		function getNumberWGsjxc(){
			var visit = wgParameter;
			var lawName = '' ;
			var desc ='' ;
			var begDate = '';
			var endDate = '';
			var nodeTimeoutState='';
			lawName = $.trim($("#lawNameInputSJ").val());
			desc = $.trim($("#descInputSJ").val());
	   		begDate = $("#creatDateBeginSJ").val();
	  		endDate = $("#creatDateEndSJ").val();
	  		nodeTimeoutState = $('#nodeTimeoutStateSJ option:selected').val();
			$.ajax({
				type : "post",
	            url:wGServerIp+"/zhjg/publicVisit/getEventDataCount",
				data : {visitJson:visit,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate,nodeTimeoutState:nodeTimeoutState},
				dataType : "json",
				success : function(data) {
					$("#wgsjNumber").html("");
					if(data.data ==  null){
						$("#wgsjNumber").append("0");
					}else{
						$("#wgsjNumber").append(data.data);
					}
				}
			});
	   }
	//监管对象  parkId
	var jgdxNum = 0;
	$("#jgdxTab").click(function(){
		LoadingDataListLawObjcts();
	});	
   function LoadingDataListLawObjcts() {
			$('#jgdxData').bootstrapTable({
				method : 'post',
				dataType : "json",
				url : WEBPATH + '/parkArchives/listParkBySearch',
				undefinedText : '-',
				pagination : true, // 分页  
				striped : true, // 是否显示行间隔色  
				cache : false, // 是否使用缓存  
				pageSize : 15, // 设置默认分页为 20
				pageNumber : 1,
				contentType : "application/x-www-form-urlencoded;charset=UTF-8",
				queryParamsType : "",
				pageList : [ 5, 15, 30, 50, 100, 200 ], // 自定义分页列表
				singleSelect : false,
				// pageList : [ 5, 10, 20 ],  
				// showColumns : true, // 显示隐藏列  
				sidePagination : "server", //服务端请求
				queryParams : function (params) {
			    	var parkId = $("#parkId").val() ;
			    	var lawObjName =$("#lawObjName").val() ;
		            var temp = {   
	            		 pageNumber: params.pageNumber,
	                     pageSize: params.pageSize,
	                     parkId:parkId,
	                     lawObjName:lawObjName
		            };
		            return temp;
			    	 
			     },//参数//参数
				uniqueId : "id", // 每一行的唯一标识  
				//序号	名称	证件号	联系人	联系方式	权属
				columns : [
						{
							field : "objectName",
							title : "名称",
							align : 'left',
							formatter : function(value, row,index) {
									return "<a href ='#' style ='color:#6CCCF1;' onclick=toLawObject(\'"+ row.id+ "\',\'"+row.typeCode+ "\') >"+value+"</a>";
							}
						},
						{
							field : "typeName",
							title : "类型",
							align : 'center',
						},
						{
							field : "socialCreditCode",
							title : "证照名称",
							align : 'center',
							
						},
						{
							field : "licenseNo",
							title : "证照号",
							align : 'center',
						},
						{
							field : "legalPerson",
							title : "联系人",
							align : 'center',
						},
						{
							field : "legalPhone",
							title : "联系人方式",
							align : 'center',
						}
						 ],
				responseHandler : function(res) {
					jgdxNum:res.data.total
					return {
						total : res.data.total,
						rows : res.data.list
					};
				},
				onCheck : function(row, $element) {
				},//单击row事件
				onUncheck : function(row, $element) {
				},
				formatLoadingMessage : function() {
					return "数据加载中...";
				},
				formatNoMatches : function() { //没有匹配的结果
					return '无符合条件的记录';
				}
			})				
		}
		$("#jgdxBtn").click(function() {
			$('#jgdxData').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
			getParkNum();
		});
		//跳转监管对象详情
		function toLawObject(id,code) {
			//business.addMainContentParserHtml(WEBPATH +'/zfdx/detailedInformationObject?id='+id+'&typeCode='+code, null);
			window.open(WEBPATH+"/auth/goHome?yyydjgdx=yes"+'&id='+id+'&typeCode='+code);
		}
		//根据查询条件获取气泡数
		function getParkNum(){
			var parkId = $("#parkId").val() ;
	    	var lawObjName =$("#lawObjName").val() ;
			$.ajax({
				type : "post",
				url : WEBPATH + "/parkArchives/getParkNum",
				data : {
					parkId:parkId,
                    lawObjName:lawObjName
				},
				dataType : "json",
				success : function(data) {
					$("#jgdxNumber").html("");
					$("#jgdxNumber").html(data.data);
				}
			})
		};
		//环境执法
		$("#taskBeginTime").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		});
		$("#taskEndTime").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		});
		var taskPageNum = 2;
		var wGFastdfsServerIP = '${WG_FASTDFS_SERVER_IP}';
		var taskVM = new Vue({
			el:'#taskDiv',
			data:{
				dataList:"",
				wGFastdfsServerIP:wGFastdfsServerIP
			},
			methods: {
				contains:function(year,month){
					var temp = month.substring(0,4);
					if(temp==year){
						return true;
					}
					return false;
				},
				taskDataAppend:function(){
					var fileIp = '${FASTDFS_ADDR}'+'/';
					var lawObjName = $("#taskLawObjectName").val();
					var person = $("#taskLawPerson").val();
					var status = $("#taskStatus").val();
					var startTime = $("#taskBeginTime").val();
					var endTime = $("#taskEndTime").val();
					var parkId = $("#parkId").val();
					$.ajax({
						type : "post",
						url : WEBPATH + "/parkArchives/getTaskListBean",
						data : {
							 lawObjName : lawObjName,
							 person : person,
							 status : status,
							 startTime : startTime,
							 endTime : endTime,
							 parkId : parkId,
							 pageNumber: taskPageNum,
							 pageSize: 20,
						},
						dataType : "json",
						success : function(data) {
							taskPageNum = data.pageNumber+1;
							for ( var i = 0; i <data.listTaskBean.length; i++){
								taskVM.$data.dataList.push(data.listTaskBean[i]);
							}
							if(data.listTaskBean.length < 19){
								$("#zczfjzgd").html("");zfdx-all
							}
						}
					})
				},getItemUrl:function(value){
					return WEBPATH+"/"+value;
				},pictureYL:function(id,num){
					//执法图片预览
						 var options = {
							remote:WEBPATH+"/parkArchives/picinfoPage?id="+id+"&num="+num,
							show: true,
						  };
						 $('#pictureImg').modal(options);
				}
			}
		});
		$("#taskTab").click(function(){
			task();
		});
		function task() {
			taskPageNum = 2;
			var fileIp = '${FASTDFS_ADDR}'+'/';
			var lawObjName = $("#taskLawObjectName").val();
			var person = $("#taskLawPerson").val();
			var status = $("#taskStatus").val();
			var startTime = $("#taskBeginTime").val();
			var endTime = $("#taskEndTime").val();
			var parkId = $("#parkId").val();
			$.ajax({
				type : "post",
				url : WEBPATH + "/parkArchives/getTaskListBean",
				data : {
					 lawObjName : lawObjName,
					 person : person,
					 status : status,
					 startTime : startTime,
					 endTime : endTime,
					 parkId : parkId,
					 pageNumber: 1,
					 pageSize: 20,
				},
				dataType : "json",
				success : function(data) {
					taskVM.$data.dataList = data.listTaskBean;
					$("#zczfjzgd").html("加载更多");
					if(data.listTaskBean.length < 19){
						$("#zczfjzgd").html("");
					}
				}
			})
		};
		$("#taskSearchBtn").click(function(){
			task();
			getHjzfNum();
		});
		//执法查看详情
		function handle_Task(taskId,lawObjectType,nodeCode){
			    window.open(WEBPATH+"/auth/goHome?yyydhjzf=yes"+'&taskId='+taskId+'&lawObjectType='+lawObjectType+'&nodeCode='+nodeCode);
		 }
		//根据查询条件获取气泡数
		function getHjzfNum(){
			var lawObjName = $("#taskLawObjectName").val();
			var person = $("#taskLawPerson").val();
			var status = $("#taskStatus").val();
			var startTime = $("#taskBeginTime").val();
			var endTime = $("#taskEndTime").val();
			var parkId = $("#parkId").val();
			$.ajax({
				type : "post",
				url : WEBPATH + "/parkArchives/getHjzfNum",
				data : {
					 lawObjName : lawObjName,
					 person : person,
					 status : status,
					 startTime : startTime,
					 endTime : endTime,
					 parkId : parkId,
				},
				dataType : "json",
				success : function(data) {
					$("#hjzfNumber").html("");
					$("#hjzfNumber").html(data.data);
				}
			})
		}
		//案件查看
		$("#caseStartTime").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		});
		$("#caseEndTime").datetimepicker({
			format : 'yyyy-mm-dd',
			todayBtn : true,
			autoclose : true,
			language: 'cn',
			minView : 'year',
			maxView : 'decade',
			clearBtn: true
		});
		var casePageNum = 2;
		var caseVM = new Vue({
			el:'#caseDiv',
			data:{
				dataList:""
			},
			methods: {
				contains:function(year,month){
					var temp = month.substring(0,4);
					if(temp==year){
						return true;
					}
					return false;
				},
				caseDataAppend:function(){
					var lawObjName = $("#caseObject").val();
					var status = $("#caseStatus").val();
					var startTime = $("#caseStartTime").val();
					var endTime = $("#caseEndTime").val();
					var parkId = $("#parkId").val();
					$.ajax({
						type : "post",
						url : WEBPATH + "/parkArchives/getCaseListBean",
						data : {
							 lawObjName : lawObjName,
							 status : status,
							 startTime : startTime,
							 endTime : endTime,
							 parkId : parkId,
							 pageNumber: casePageNum,
							 pageSize: 20,
						},
						dataType : "json",
						success : function(data) {
							casePageNum = data.pageNumber+1;
							for ( var i = 0; i <data.listCaseBean.length; i++){
								caseVM.$data.dataList.push(data.listCaseBean[i]);
							}
							if(data.listCaseBean.length < 19){
								$("#ajckjzgd").html("");
							}
						}
					})
				},
			}
		});
		$("#caseTab").click(function(){
				caseData();
			});
		
		function caseData() {
			casePageNum = 2;
			var lawObjName = $("#caseObject").val();
			var status = $("#caseStatus").val();
			var startTime = $("#caseStartTime").val();
			var endTime = $("#caseEndTime").val();
			var parkId = $("#parkId").val();
			var caseNumber = $("#caseNumber").val();
			$.ajax({
				type : "post",
				url : WEBPATH + "/parkArchives/getCaseListBean",
				data : {
					 lawObjName : lawObjName,
					 status : status,
					 startTime : startTime,
					 endTime : endTime,
					 parkId : parkId,
					 caseNumber:caseNumber,
					 parkId : parkId,
					 pageNumber: 1,
					 pageSize: 20,
				},
				dataType : "json",
				success : function(data) {
					caseVM.$data.dataList = data.listCaseBean;
					$("#ajckjzgd").html("加载更多");
					if(data.listCaseBean.length < 19){
						$("#ajckjzgd").html("");
					}
				}
			})
		};
		$("#caseSearchBtn").click(function(){
			caseData();
			getAjcbNum();
		});
		//案件查看详情
		function handle_case(caseId){
			window.open(WEBPATH+"/auth/goHome?yyydajck=yes"+'&caseId='+caseId);
		 }
		//案件  查看卷宗
		function case_Click(id){
		    window.open( WEBPATH+'/parkArchives/caseCheck?caseId='+id);
	     }
		//根据查询条件获取气泡数
		function getAjcbNum(){
			var lawObjName = $("#caseObject").val();
			var status = $("#caseStatus").val();
			var startTime = $("#caseStartTime").val();
			var endTime = $("#caseEndTime").val();
			var parkId = $("#parkId").val();
			var caseNumber = $("#caseNumber").val();
			$.ajax({
				type : "post",
				url : WEBPATH + "/parkArchives/getAjcbNum",
				data : {
					 lawObjName : lawObjName,
					 status : status,
					 startTime : startTime,
					 endTime : endTime,
					 parkId : parkId,
					 caseNumber:caseNumber,
				},
				dataType : "json",
				success : function(data) {
					$("#ajckNumber").html("");
					$("#ajckNumber").html(data.data);
				}
			})
		};
		
</script>
	<!-- 队伍信息 -->
	<script type="text/javascript">
	var vueIsGrids = new Vue({
		el : '#isGridsListTable',
		data : {
			isGridsList: []
		},
		methods : {
			 
		}
	});
	
	var vueIsLawoff = new Vue({
		el : '#isLawoffListTable',
		data : {
			isLawoffList : []
		},
		methods : {
			
		}
	});
	$("#gridAndLawTab").click(function(){
		initGrids();
		getNumberWGdwxx();//刷新总数（tab  旁的总数）
	})
	function initGrids(){
		var visit = wgParameter;
		$.ajax({
			type : "post",
            url:wGServerIp+"/zhjg/publicVisit/getUserIsGridsOrLawoffList",
			data : {visitJson:visit,type:'1'},
			dataType : "json",
			success : function(data) {
				$("#wgyNum").html("")
				$("#wgyNum").html(data.data.length+"名");
				vueIsGrids.$data.isGridsList = data.data ;
			}
		});
		
		$.ajax({
			type : "post",
            url: WEBPATH + "/parkArchives/getLawPerson",
			data : {belongAreaId:$("#parkBelongAreaId").val()},
			dataType : "json",
			success : function(data) {
				$("#zfyNum").html("")
				$("#zfyNum").html(data.data.length+"名");
				vueIsLawoff.$data.isLawoffList = data.data ;
			}
		});
	}
	
	</script>
 <!-- 网格巡查 -->
 <script type="text/javascript">
	 $("#creatDateBegin").datetimepicker({
	     format:'yyyy-mm-dd',
	     language: 'cn',
	     clearBtn: true,
	     autoclose: true,
	     minView:2,
	     maxView:4
	   })
	$("#creatDateEnd").datetimepicker({
	    format:'yyyy-mm-dd',
	     language: 'cn',
	      clearBtn: true,
	    autoclose: true,
	    minView:2,
	      maxView:4
	})
	//$("#creatDateEnd").val(GetDateStr(1));
	//$("#creatDateBegin").val(GetDateStr(-30));
	var imageServer = wGFastdfsServerIP;
	var WGxcPagemun = 2;
	var vm = new Vue({
		el:'#objectComment',
		data:{
			dataList:""
		},
		methods: {
			contains:function(year,month){
				var temp = month.substring(0,4);
				if(temp==year){
					return true;
				}
				return false;
			},
			WGXCDataAppend:function(){
				var lawObjectId = wgParameter;
				var lawName = '' ;
				var desc ='' ;
				var begDate = '';
				var endDate = '';
				desc = $.trim($("#descInput").val());
		   		begDate = $("#creatDateBegin").val();
		  		endDate = $("#creatDateEnd").val();
				$.ajax({
					type : "post",
					url : wGServerIp+"/zhjg/publicVisit/getObjectCommentTreeData",
					data : {
						visitJson:lawObjectId,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate,pageSize:20,pageNum:WGxcPagemun
					},
					dataType : "json",
					success : function(data) {
						WGxcPagemun = data.pageNum+1;
						for ( var i = 0; i <data.dataList.length; i++){
							vm.$data.dataList.push(data.dataList[i]);
						}
						if(data.dataList.length < 19){
							$("#wgxcjagd").html("");
						}
					}
				});
			},
			showImgModal:function(lawCircleId,orgImageId){
				  $.ajax({
	                 type:"post",
	                 url:wGServerIp+"/zhjg/publicVisit/showImageModal",
	                 dataType:"json",
	                 data:{
	                	 lawCircleId:lawCircleId,
	                	 currentImageOrgId:orgImageId
	                 },
	                 success:function(data){
	                 		$("#fileModal").modal('show');
	                 		$("#modalContent").empty();
	                 		var html = '';
	                 		html = '<div class="modal-header"><div style="float: right; margin-top: -5px;">'
	                 		+'<button type="button" class="btn btn-default" onclick="closeModal()">关闭</button>'
	                 		+'</div><h4 class="modal-title" id="myModalLabel">预览</h4></div>'
	                 		+'<div class="modal-body"><div class="col-sm-12"><div id="myCarousel" class="carousel slide">'
	                 		+'<ol class="carousel-indicators">';
	                 	
	                 		$.each(data,function(i,item){
	        					if(item.selected !=null &&item.selected=='1'){
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" class="active"></li>';
	        					}else{
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" ></li>';
	        					}
	        				});
	                 		html = html + '</ol><div class="carousel-inner">';
	                 		$.each(data,function(i,item){
	                 			if(item.selected !=null &&item.selected=='1'){
	        						if(item.fileType !=null &&item.fileType=='2'){
	        							html = html + '<div class="item active"><img src="${webpath}/static/img/pdf-thumb.jpg" style="width: 100%;height:558px; "'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}else{
	        							html = html + '<div class="item active"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%;height:558px; "'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}
	        					}else{
	        						if(item.fileType !=null &&item.fileType=='2'){
	        							html = html + '<div class="item"><img src="${webpath}/static/img/pdf-thumb.jpg" style="width: 100%; height:558px;"'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}else{
	        							html = html + '<div class="item"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%; height:558px;"'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}
	        					}
	        				});
	                 		html = html + '</div><a class="left carousel-control" href="#myCarousel" role="button"'
	         				+'data-slide="prev"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>'
	         				+'<span class="sr-only">Previous</span></a> <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">'
	         				+'<span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>'+'<span class="sr-only">Next</span></a>'
	         				+'</div></div></div><div class="modal-footer"></div>';
	                 		$("#modalContent").append(html);
	                 		
	                 		
	                 }
	             });    
			}
		},
		filters:{
			getMonth:function(value){
				var month = value.substring(4,5);
				if(month=='0'){
					return value.substring(5);
				}else{
					return value.substring(4);
				}
			},
			getFileUrl:function(url,type){
				if(type!=null && type =='2'){
					return '${webpath}/static/img/pdf-thumb.jpg';
				}else{
					return  imageServer + url;
				}
			},
			timestampToTime:function(cjsj) {
		        var date = new Date(cjsj) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
		        var Y = date.getFullYear() + '-'
		        var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-'
		        var D = date.getDate()< 10 ? '0'+date.getDate()+ ' ': date.getDate()+ ' '
		        var h = date.getHours() + ':'
		        var m = date.getMinutes() + ':'
		        var s = date.getSeconds()
		        return Y+M+D+h+m+s;
		    },
		}
	});
	$("#xunchaTab").click(function(){
		searchPatroDataByXunCha();
	});
	function searchPatroDataByXunCha(){
		getNumberWGwgxc();//点击查询获取气泡数；
		WGxcPagemun = 2;
		var lawObjectId = wgParameter;
		var lawName = '' ;
		var desc ='' ;
		var begDate = '';
		var endDate = '';
		desc = $.trim($("#descInput").val());
   		begDate = $("#creatDateBegin").val();
  		endDate = $("#creatDateEnd").val();
		$.ajax({
			type : "post",
			url : wGServerIp+"/zhjg/publicVisit/getObjectCommentTreeData",
			data : {
				visitJson:lawObjectId,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate,pageSize:20,pageNum:1
			},
			dataType : "json",
			success : function(data) {
				vm.$data.dataList = data.dataList;
				$("#wgxcjagd").html("加载更多");
				if(data.dataList.length < 19){
					$("#wgxcjagd").html("");
				}
			}
		});
	}
	function closeModal(){
		$("#fileModal").modal('hide');
	}
	//下载文件
	function downFile(filename, url) {
		  window.location.href =wGServerIp+"/zhjg/publicVisit/downloadFile?url="+ url + "&filename=" + filename;
	}
</script>
 <!-- 事件 -->
<script type="text/javascript">
	$("#creatDateBeginSJ").datetimepicker({
	    format:'yyyy-mm-dd',
	    language: 'cn',
	    clearBtn: true,
	    autoclose: true,
	    minView:2,
	    maxView:4
	  })
	$("#creatDateEndSJ").datetimepicker({
	   format:'yyyy-mm-dd',
	    language: 'cn',
	    clearBtn: true,
	    autoclose: true,
	    minView:2,
	    maxView:4
	})
	//$("#creatDateEndSJ").val(GetDateStr(1));
	//$("#creatDateBeginSJ").val(GetDateStr(-30));
	var imageServer = wGFastdfsServerIP;
	var WGSJPagemun = 2;
	var vmsj = new Vue({
		el:'#objectCommentSJ',
		data:{
			dataList:""
		},
		methods: {
			contains:function(year,month){
				var temp = month.substring(0,4);
				if(temp==year){
					return true;
				}
				return false;
			},
			WGSJDataAppend:function(){
				var lawObjectId = wgParameter;
				var lawName = '' ;
				var desc ='' ;
				var begDate = '';
				var endDate = '';
				var nodeTimeoutState='';
				lawName = $.trim($("#lawNameInputSJ").val());
				desc = $.trim($("#descInputSJ").val());
		   		begDate = $("#creatDateBeginSJ").val();
		  		endDate = $("#creatDateEndSJ").val();
		  		nodeTimeoutState = $('#nodeTimeoutStateSJ option:selected').val(); 
				$.ajax({
					type : "post",
					url : wGServerIp+"/zhjg/publicVisit/getEventData",
					data : {
						visitJson:lawObjectId,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate,nodeTimeoutState:nodeTimeoutState,pageSize:20,pageNum:WGSJPagemun
					},
					
					success : function(data) {
						WGSJPagemun = data.pageNum+1;
						for ( var i = 0; i <data.dataList.length; i++){
							vm.$data.dataList.push(data.dataList[i]);
						}
						if(data.dataList.length < 19){
							$("#wgsjjzgd").html("");
						}
						
					}
				});
			},
			showImgModal:function(lawCircleId,orgImageId){
				  $.ajax({
	                 type:"post",
	                 url:wGServerIp+"/zhjg/publicVisit/showTaskFiles",
	                 dataType:"json",
	                 data:{
	                	 lawCircleId:lawCircleId,
	                	 currentImageOrgId:orgImageId
	                 },
	                 success:function(data){
	                 		$("#fileModal").modal('show');
	                 		$("#modalContent").empty();
	                 		var html = '';
	                 		html = '<div class="modal-header"><div style="float: right; margin-top: -5px;">'
	                 		+'<button type="button" class="btn btn-default" onclick="closeModal()">关闭</button>'
	                 		+'</div><h4 class="modal-title" id="myModalLabel">预览</h4></div>'
	                 		+'<div class="modal-body"><div class="col-sm-12"><div id="myCarousel" class="carousel slide">'
	                 		+'<ol class="carousel-indicators">';
	                 	
	                 		$.each(data,function(i,item){
	        					if(item.selected !=null &&item.selected=='1'){
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" class="active"></li>';
	        					}else{
	        						html = html + '<li data-target="#myCarousel" data-slide-to="'+i+'" ></li>';
	        					}
	        				});
	                 		html = html + '</ol><div class="carousel-inner">';
	                 		$.each(data,function(i,item){
	                 			if(item.selected !=null &&item.selected=='1'){
	        						if(item.fileType !=null &&item.fileType=='2'){
	        							html = html + '<div class="item active"><img src="${webpath}/static/img/pdf-thumb.jpg" style="width: 100%;height:558px; "'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}else{
	        							html = html + '<div class="item active"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%;height:558px; "'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}
	        					}else{
	        						if(item.fileType !=null &&item.fileType=='2'){
	        							html = html + '<div class="item"><img src="${webpath}/static/img/pdf-thumb.jpg" style="width: 100%; height:558px;"'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}else{
	        							html = html + '<div class="item"><img src="'+imageServer+''+item.fileUrl+'" style="width: 100%; height:558px;"'
	            						+'alt="'+i+' slide">'
	            						+'<div style="position: absolute;bottom: 5px;left:50%;margin-left: -20px; z-index: 999;">'
	                                    +'<button class="btn btn-info btn-xs" onclick="downFile(\''+item.fileName+'\',\''+item.fileUrl+'\')" >下载</button>'
	                                    +'</div>' 
	            						+'</div>';
	        						}
	        					}
	        				});
	                 		html = html + '</div><a class="left carousel-control" href="#myCarousel" role="button"'
	         				+'data-slide="prev"><span class="glyphicon glyphicon-chevron-left" aria-hidden="true"></span>'
	         				+'<span class="sr-only">Previous</span></a> <a class="right carousel-control" href="#myCarousel" role="button" data-slide="next">'
	         				+'<span class="glyphicon glyphicon-chevron-right" aria-hidden="true"></span>'+'<span class="sr-only">Next</span></a>'
	         				+'</div></div></div><div class="modal-footer"></div>';
	                 		$("#modalContent").append(html);
	                 		
	                 		
	                 }
	             });    
			}
		},
		filters:{
			getMonth:function(value){
				var month = value.substring(4,5);
				if(month=='0'){
					return value.substring(5);
				}else{
					return value.substring(4);
				}
				
			},
			getFileUrl:function(url,type){
				if(type!=null && type =='2'){
					return '${webpath}/static/img/pdf-thumb.jpg';
				}else{
					return  imageServer + url;
				}
			},
			timestampToTime:function(cjsj) {
		        var date = new Date(cjsj) //时间戳为10位需*1000，时间戳为13位的话不需乘1000
		        var Y = date.getFullYear() + '-'
		        var M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-'
		        var D = date.getDate()< 10 ? '0'+date.getDate()+ ' ': date.getDate()+ ' '
		        var h = date.getHours() + ':'
		        var m = date.getMinutes() + ':'
		        var s = date.getSeconds()
		        return Y+M+D+h+m+s;
		    },
		}
	});
	$("#shijianTab").click(function(){
		searchPatroDataByIfSJ();
	});
	function searchPatroDataByIfSJ(){
		getNumberWGsjxc();//点击查询获取气泡数；
		WGSJPagemun = 2;
		var lawObjectId = wgParameter;
		var lawName = '' ;
		var desc ='' ;
		var begDate = '';
		var endDate = '';
		var nodeTimeoutState='';
		lawName = $.trim($("#lawNameInputSJ").val());
		desc = $.trim($("#descInputSJ").val());
   		begDate = $("#creatDateBeginSJ").val();
  		endDate = $("#creatDateEndSJ").val();
  		nodeTimeoutState = $('#nodeTimeoutStateSJ option:selected').val(); 
		$.ajax({
			type : "post",
			url : wGServerIp+"/zhjg/publicVisit/getEventData",
			data : {
				visitJson:lawObjectId,lawName:lawName,desc:desc,begDate:begDate,endDate:endDate,nodeTimeoutState:nodeTimeoutState,pageSize:20,pageNum:1
			},
			
			success : function(data) {
				vmsj.$data.dataList = data.dataList;
				$("#wgsjjzgd").html("加载更多");
				if(data.dataList.length < 19){
					$("#wgsjjzgd").html("");
				}
			}
		});
	}
	function closeModal(){
		$("#fileModal").modal('hide');
	}
	//返回
	function back(){
		business.addMainContentParserHtml(WEBPATH+"/parkArchives/toMainPage", null);
	}
	//下载文件
	function downFile2(filename, url) {
		 window.location.href =wGServerIp+"/zhjg/publicVisit/downloadFile?url="+ url + "&filename=" + filename;
	}
	//2019-7-3  给所有小模块添加导出Excel功能
	//网格员信息导出
	function exportWgUserExcel(){
		var visit = wgParameter;
		window.location.href=  wGServerIp+'/zhjg/publicVisit/exportWgyExcel?visitJson='+visit+'&type=1';
	}
	//执法员信息导出
	function exportZfUserExcel(){
		var belongAreaId = $("#parkBelongAreaId").val()
		window.location.href= WEBPATH+'/parkArchives/exportZfUserExcel?belongAreaId='+belongAreaId;
	}
	//监管对象信息导出
	function exportParkExcel(){
		var parkId = $("#parkId").val() ;
    	var lawObjName =$("#lawObjName").val() ;
    	window.location.href= WEBPATH+'/parkArchives/exportParkExcel?parkId='+parkId+'&lawObjName='+lawObjName;
	}
	//网格巡查信息导出
	function exportWgxcExcel(){
		var lawObjectId = wgParameter;
		var lawName = '' ;
		var desc ='' ;
		var begDate = '';
		var endDate = '';
		desc = $.trim($("#descInput").val());
   		begDate = $("#creatDateBegin").val();
  		endDate = $("#creatDateEnd").val();
  		window.location.href= wGServerIp+'/zhjg/publicVisit/exportWgxcExcel?visitJson='+lawObjectId+'&lawName='+lawName+
		'&desc='+desc+'&begDate='+begDate+'&endDate='+endDate;
	}
	//网格事件信息导出
	function exportWgsjExcel(){
		var lawObjectId = wgParameter;
		var lawName = '' ;
		var desc ='' ;
		var begDate = '';
		var endDate = '';
		var nodeTimeoutState='';
		lawName = $.trim($("#lawNameInputSJ").val());
		desc = $.trim($("#descInputSJ").val());
   		begDate = $("#creatDateBeginSJ").val();
  		endDate = $("#creatDateEndSJ").val();
  		nodeTimeoutState = $('#nodeTimeoutStateSJ option:selected').val();
  		window.location.href= wGServerIp+'/zhjg/publicVisit/exportAjckExcel?visitJson='+lawObjectId+'&lawName='+lawName+
		'&desc='+desc+'&begDate='+begDate+'&endDate='+endDate+'&nodeTimeoutState='+nodeTimeoutState;
	}
	//环境执法信息导出
	function exportHjzfExcel(){
		var lawObjName = $("#taskLawObjectName").val();
		var person = $("#taskLawPerson").val();
		var status = $("#taskStatus").val();
		var startTime = $("#taskBeginTime").val();
		var endTime = $("#taskEndTime").val();
		var parkId = $("#parkId").val();
		window.location.href= WEBPATH+'/parkArchives/exportHjzfExcel?parkId='+parkId+'&lawObjName='+lawObjName+
		'&person='+person+'&status='+status+'&startTime='+startTime+'&endTime='+endTime;
	}
	//案件查看信息导出
	function exportAjckExcel(){
		var lawObjName = $("#caseObject").val();
		var status = $("#caseStatus").val();
		var startTime = $("#caseStartTime").val();
		var endTime = $("#caseEndTime").val();
		var parkId = $("#parkId").val();
		var caseNumber = $("#caseNumber").val();
		window.location.href= WEBPATH+'/parkArchives/exportAjckExcel?parkId='+parkId+'&lawObjName='+lawObjName+
		'&caseNumber='+caseNumber+'&status='+status+'&startTime='+startTime+'&endTime='+endTime;
	}
</script>
</html>