<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="myModalLabel">选择输入cron表达式</h4>
</div>
<div class="modal-body">
	<div>
		<span>执行周期：</span> 
		<select class="form-control" id="selectType" name="selectType" 
			onchange="changeType()">
			<option value="">请选择</option>
			<option value="1">每隔n秒</option>
			<option value="2">每隔n分钟</option>
			<option value="3">每隔n小时</option>
			<option value="4">每天的n点(m分）</option>
			<option value="5">每周x的n点(m分)</option>
			<option value="6">自定义corn表达式</option>
		</select>
		
		<select class="form-control" id="selectWeek" name="selectWeek" style="display: none;"
			onchange="changeWeek()">
			<option value="MON">周一</option>
			<option value="TUE">周二</option>
			<option value="WED">周三</option>
			<option value="THU">周四</option>
			<option value="FRI">周五</option>
			<option value="SAT">周六</option>
			<option value="SUN">周日</option>
		</select>
		
		<select class="form-control" id="selectHour" name="selectHour" style="display: none;" onchange="changeHour()">
		</select>
		
		<select class="form-control" id="selectMin" name="selectMin" style="display: none;">
		</select>
		
		<select class="form-control" id="selectSec" name="selectSec" style="display: none;">
		</select>
		 
		<input id="customCron" name="customCron" type="text" style="display: none;"
			style="width: 200px;" class="form-control"/>
		
		<button type="button" class="btn btn-info" onclick="generateCron()">生成表达式</button>
		<br/>
		<div id="cronDiv" style="display:none;">
			表达式：
			<input id="cornvalue" class="form-control" name="cornvalue" readonly="readonly" />
		</div>
	</div>
</div>
<div class="modal-footer">
	<button type="button" class="btn btn-info" data-dismiss="modal" onclick="confirmCron()">确定</button>
	<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
</div>
<script>

	$(function(){
		$('#InputCronModaler').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
	})
	
	function confirmCron(){
		var cronVal = $("#cornvalue").val();
		//把值写入到父页面的输入框
		//...
		$("#cronExpression").val(cronVal);
		$('#jobInfo').formValidation('revalidateField', 'cronExpression');
	}
	
	function generateCron(){
		var selectType = $("#selectType").val();
		var cornValue;
		if (selectType == '1') {
			//按照秒 */5 * * * * ?
			var secondval = $("#selectSec").val();
			if (secondval != null) {
				cornvalue = "0/" + secondval + " * * * * ?";
			}
		} else if (selectType == '2') {
			//按分钟0 */1 * * * ?
			var minuteVal = $("#selectMin").val();
			if (minuteVal != null) {
				cornvalue = "0 0/" + minuteVal + " * * * ?"
			}
		} else if (selectType == '3') {
			//0 0 0/2 * * ?
			var hourVal = $("#selectHour").val();
			if (hourVal != null) {
				cornvalue = "0 0 0/" + hourVal + " * * ?"
			}
		} else if (selectType == '4') {
			//0 15 10 * * ?
			var hourValue = $("#selectHour").val();
			var minuteValue = $("#selectMin").val();
			if (minuteValue == null || minuteValue == '') {
				minuteValue = 0;
			}
			if (hourValue != null) {
				cornvalue = "0 " + minuteValue + " " + hourValue + " * * ?";
			}
		} else if (selectType == '5') {
			var weekValue = $("#selectWeek").val();
			var hourValue = $("#selectHour").val();
			var minuteValue = $("#selectMin").val();
			//0 15 10 ? * MON-FRI
			if (minuteValue == null || minuteValue == '') {
				minuteValue = 0;
			}
			if (weekValue != null && hourValue != null) {
				cornvalue = "0 " + minuteValue + " " + hourValue + " ? * " + weekValue;
			}
		} else if (selectType == '6') {
			cornvalue = $("#customCron").val();
		}
		document.getElementById('cronDiv').style.display='';
		$("#cornvalue").attr("value", cornvalue);
	}
	
	/*初始化小时*/
	function initHours() {
		var selectHour = $("#selectHour");
		for (var i = 0; i < 24; i++) {
			var hourHtml = "<option value='"+i+"'> "+i+" h </option>";
			selectHour.append(hourHtml);
		}
	}
	
	/*初始化分钟*/
	function initMinutes() {
		var selectMin = $("#selectMin");
		for (var i = 0; i < 60; i++) {
			var minHtml = "<option value='"+i+"'> "+i+" m </option>";
			selectMin.append(minHtml);
		}
	}

	/*初始化秒*/
	function initSeconds() {
		var selectSec = $("#selectSec");
		for (var i = 0; i < 60; i++) {
			var secHtml = "<option value='"+i+"'> "+i+" s </option>";
			selectSec.append(secHtml);
		}
	}
	
	function changeType() {
		var selectType = $("#selectType").val();
		if(selectType == ''){
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='none';
			document.getElementById('selectMin').style.display='none';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='none';
		} else if (selectType == '1'){
			document.getElementById('selectSec').style.display='';
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='none';
			document.getElementById('selectMin').style.display='none';
			document.getElementById('customCron').style.display='none';
			initSeconds();
		} else if (selectType == '2'){
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='none';
			document.getElementById('selectMin').style.display='';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='none';
			initMinutes();
		} else if (selectType == '3'){
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='';
			document.getElementById('selectMin').style.display='none';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='none';
			initHours();
		} else if (selectType == '4'){
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='';
			initHours();
			document.getElementById('selectMin').style.display='none';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='none';
		} else if (selectType == '5'){
			document.getElementById('selectWeek').style.display='';
			document.getElementById('selectHour').style.display='none';
			document.getElementById('selectMin').style.display='none';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='none';
		} else if (selectType == '6'){
			document.getElementById('selectWeek').style.display='none';
			document.getElementById('selectHour').style.display='none';
			document.getElementById('selectMin').style.display='none';
			document.getElementById('selectSec').style.display='none';
			document.getElementById('customCron').style.display='';
		}
	}
	
	function changeWeek(){
		var selectType = $("#selectType").val();
		var selectWeek = $("#selectWeek").val();
		if(selectType == '5' && selectWeek != null && selectWeek != ''){
			document.getElementById('selectHour').style.display='';
			initHours();
		}
	}
	
	function changeHour(){
		var selectType = $("#selectType").val();
		var selectHour = $("#selectHour").val();
		if((selectType == '4' || selectType == '5') && selectHour != null && selectHour != '') {
			document.getElementById('selectMin').style.display='';
			initMinutes();
		}
	}
</script>
