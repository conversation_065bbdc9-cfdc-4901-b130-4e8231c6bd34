<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<style>
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<div class="modal-header">
	<!-- <button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button> -->
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="fileModalLabel">预览附件</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-body">
		<c:if test="${fileType==2}">
			<div class="pricing-value">
				<div id="B${fileInfoModel.id}"></div>
			</div>
		</c:if>
		<c:if test="${fileType==1}">
			 <div class="pricing-value">
				<span class="value"><img style="height:100%;" src="${FASTDFS_ADDR}/${fileInfoModel.fileUrl}" /></span>
			</div> 
		</c:if>
	</div>
</div>

<div class="modal-footer" > 
	
	 
</div>
<script type="text/javascript">
$(document).ready(function(){
		$('#fyjg').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		$('#fileShowModel').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		$('#inputImgModeler').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		$('#yl').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		$("#closeModel").click(function(){
			$("#yl").modal('hide');
		})
		if('${fileInfo}'!=null && '${fileInfo}'!='' && '${fileInfo}'!= undefined){
			
			var obj=$.parseJSON('${fileInfo}');
			if(obj != null) {
				var select="#B"+obj.id;
				if(obj.fileType==2){
					if(isIEWhether){
						if(PDFObject.supportsPDFs){
							PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select);
						} else {
							 swal({ 
							        title: "提示",  
							        text: "您的浏览器不支持pdf预览功能，请安装pdf阅读器后重试！",  
							        type: "warning", 
							        showCancelButton: true, 
							        closeOnConfirm: true, 
							        confirmButtonText: "下载并安装", 
							        confirmButtonColor: "#ec6c62" 
							    }, function() { 
							        window.location.href=WEBPATH+"/sysUser/downloadPdfReader"
							    }); 
						}
					}else{
						var options = {
								/* pdfOpenParams: {
									navpanes: 0,
									toolbar: 0,
									statusbar: 0,
									page: 1
								}, */
								forcePDFJS: false,
								PDFJS_URL: WEBPATH+"/static/pdfjs/web/viewer.html"
						};
						PDFObject.embed(FASTDFS_ADDR+obj.fileUrl,select, options);
					}
				}
			}
		}
		
})

$(document).ready(function(){
	var img = document.getElementById("image");
	if(img!=null&& img!='' && typeof(img)!='undefined'){
		var dx = img.width;
	    var dy = img.heigth;
	    if(dx>dy){
		    img.style.width="100%";
	    }else{
		    img.style.height="100%";
	    }
	}
})
function closeModal(){
	$("#yl").modal('hide');
}
function rotateRight(){
	   var x = document.getElementById("image");
	  // var p = document.getElementById("szh");
	   n=parseInt($("#jiaoduR").val());
	   n = n + 90;
	   
	   x.style.transform = "rotate(" + n + "deg)";
	   x.style.webkitTransform = "rotate(" + n + "deg)";
	   x.style.OTransform = "rotate(" + n + "deg)";
	   x.style.MozTransform = "rotate(" + n + "deg)";
	   var dx = x.width;
	   var dy = x.heigth;
	   if(dx>dy){
		   x.style.width="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }else{
		   x.style.height="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }
	   if (n >= 360){
	   		n = 0;
	   }
	   $("#jiaoduR").val(n);
	   $("#degree").val(n/90);
}   
function rightClick(){
	rotateRight();
	
}

function leftClick(){
	rotateLeft();
}

function rotateLeft(){
	   var x = document.getElementById("image");
	   //var p = document.getElementById("szh");
	   n=parseInt($("#jiaoduL").val());
	   n = n - 90;
	  
	   x.style.transform = "rotate(" + n + "deg)";
	   x.style.webkitTransform = "rotate(" + n + "deg)";
	   x.style.OTransform = "rotate(" + n + "deg)";
	   x.style.MozTransform = "rotate(" + n + "deg)";
	   var dx = x.width;
	   var dy = x.heigth;
	   if(dx>dy){
		   x.style.width="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }else{
		   x.style.height="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }
	   if (n <= 0){
	   		n = 360;
	   }
	   $("#jiaoduL").val(n);
	   $("#degree").val(n/90);
} 
$(function(){
	// 注释 模态框清理 
 	$('#fjyl').on('hide.bs.modal', function () {
		   $("#fjyl").removeData("bs.modal"); 
	})
	$('.modal').on('hidden.bs.modal', function () {
       if ($('.modal.in').size() >= 1) {
          $('body').addClass('modal-open')
       }
    })
})
</script>

<style>
   .changneng {
    width: 100%;
    height: 500px;
    overflow: auto;
    position: relative;
    margin-top: 10px;
}

.changneng > p {
    position: absolute;
    cursor: move;
    transform-origin: center;
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;
    -o-transform-origin: center;
    width: 100%;
    height: 100%;
    padding: 0;
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
    cursor: move;
    left: 0;
    top: 0;
}

.changneng > p > img {
    display: inline-block;
    vertical-align: middle;
    cursor: move;
   }
</style>