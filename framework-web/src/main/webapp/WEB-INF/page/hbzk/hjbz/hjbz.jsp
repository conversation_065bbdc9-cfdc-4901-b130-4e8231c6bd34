 <%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<title>环境标准</title>
<script type="text/javascript">
		$(document).ready(function(){
			//设置查询enter键无效
			business.listenEnter('gpcSearch');
			$('#dataTable').bootstrapTable({       
				 method: 'post',
				 dataType: "json", 
				 url:"https://std.12369.com/GPCStandard/fjzf/getStandardMainListForFjzf.do",
			     undefinedText : '-',  
			     pagination : true, // 分页  
			     striped : true, // 是否显示行间隔色  
			     cache : false, // 是否使用缓存  
			     pageSize:15, // 设置默认分页为 20
			     pageNumber: 1,
			     queryParamsType: "",
			     locale:'zh-CN',
			     pageList: [5,10, 20, 30,50], // 自定义分页列表
			     singleSelect: true,
			     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			     sidePagination: "server", //服务端请求
			     icons:{
			    	  paginationSwitchDown: 'glyphicon-collapse-down icon-chevron-down',
			    	  paginationSwitchUp: 'glyphicon-collapse-up icon-chevron-up',
			    	  refresh: 'glyphicon-refresh icon-refresh',
			    	  toggle: 'glyphicon-list-alt icon-list-alt',
			    	  columns: 'glyphicon-th icon-th',
			    	  detailOpen: 'glyphicon-plus icon-plus',
			    	  detailClose: 'glyphicon-minus icon-minus'
			     },
			     queryParams:function (params) {
			            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
			                 page: params.pageNumber,
			                 rows: params.pageSize,
			                 sort:"standardCode",
			                 order:"asc",
			               	 keyword:$("#keyword").val(), //关键词
			               	 standardLevel:$("#standardLevel").val(),//标准级别
			               	 mediumAttribute:$("#mediumAttribute").val(), //介质属性
			               	 applicableRegion:$("#applicableRegion").val()//适用地区
			            };
			            return temp;
			     },//参数
			     uniqueId : "id", // 每一行的唯一标识  
				 columns: [
				           {
					           field: "standardName",
					           title: "标准名称",
					           formatter: function(value,row,index){
						        	  return "<a href ='javascript:void(0)' style ='color:#6CCCF1;' onclick=detailInfoPage('"+row.id+"')>"
										+value+"</a>";
						       }
					       },
				           {
					           field: "applicableRegion",
					           title: "适用地区",
					           align: 'center'
				           },
				           {
					           field: "standardCode",
					           title: "标准号",
					           align: 'center'
				           },
				           {
					           field: "availability",
					           title: "标准有效性",
					           align: 'center'
				           }
				 ],
				 responseHandler : function(res) {  
		               return {  
		                   total : res.total,  
		                   rows : res.rows  
		               };  
		         },
	             onCheck: function(row, $element) {
	        	   
	             },//单击row事件
		         onUncheck: function(row, $element) {
		        		
			     },
			     onUncheckAll: function(row, $element) {
			       			
			     },
			     onCheckAll:function(row, $element) {
			        		
			     },
			     onRefresh: function () {
			        		
			     },
		         formatLoadingMessage: function () {
		        	   return "请稍等，正在加载中...";
		         },
		         formatNoMatches: function () { //没有匹配的结果
		        		   return '无符合条件的记录';
		         }
			});
			
			
			//获取省份信息异步查询
			$.ajax({
				type:"post",
				url:"https://std.12369.com/GPCStandard/fjzf/getProviceDataForFjzf.do",
				dataType:"json",
				success:function(data){
			       new Vue({
			  		  el: '#provinceDiv',
					  data: {
						  provinceObj:data
					  }
				   });
				}
		    });
			
			//介质属性
			$.ajax({
				type:"post",
				url:"https://std.12369.com/GPCStandard/fjzf/getMediumAttributeListForFjzf.do",
				dataType:"json",
				success:function(data){
			       new Vue({
			  		  el: '#mediumAttributeDiv',
					  data: {
						  mediumAttributeDivObj:data
					  }
				   });
				}
		    });
			
			//绑定搜索按钮
			$('#gpcSearch').click(function() {
				$('#dataTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
		   	});
       		
	    });
		
		function detailInfoPage(id){
			business.addMainContentParserHtml(WEBPATH+'/GPCStandard/StandardMainByIdForPage','id='+id);
		}
</script>
</head>
<body>
<div class="main-container">
				<div class="padding-md">
                	<div class="row">  
                    <div class="col-lg-12"> 
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 环境标准
                                </div>
                              <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div>
                                  <div class="smart-widget-body form-horizontal">
                                      <div class="form-group">
                                            <label class="control-label col-md-2">关键字</label>
                                            <div class="col-md-3">
                                                <input type="text" id="keyword" placeholder="请输入要查询的关键字" class="form-control" data-parsley-required="true">
                                            </div>
                                            <label class="control-label col-md-2">介质属性</label>
                                            <div id="mediumAttributeDiv" class="col-md-3">
                                                <select id="mediumAttribute" class="form-control">
                                                    <option value="">——请选择——</option>
                                                    <option v-for="(obj, index) in mediumAttributeDivObj" :value='obj.name'>{{obj.name}}</option>
                                                </select>
                                            </div>
										</div>
                                        <div class="form-group">
                                            <label class="control-label col-md-2">标准级别</label>
                                            <div class="col-md-3">
                                                <select id="standardLevel" class="form-control">
                                                    <option value="">——请选择——</option>
                                                      <option value="国家标准">国家标准</option>
                                                      <option value="地方标准">地方标准</option>
                                                      <option value="行业标准">行业标准</option>
                                                </select>
                                            </div>
                                            <label class="control-label col-md-2">适用地区</label>
                                            <div id="provinceDiv" class="col-md-3">
                                                <select id="applicableRegion" class="form-control">
                                                      <option value="">——请选择——</option>
                                                      <option  v-for="(obj, index) in provinceObj" :value='obj.name'>{{obj.name}}</option>
                                                </select>
                                            </div>
                                            <button class="btn btn-info" type="button" id="gpcSearch" style="width:120px;">查询</button>
										</div>
                                  </div>
                                </div>
                            </div>
                        </div>
                    </div>
					<!--环保智库row-->
                    <div class="row">                        
                        <div class="col-md-12"> 
                            <div class="smart-widget widget-blue">
                                <div class="smart-widget-header font-16">
                                    <i class="fa fa-arrow-right"></i> 环境标准 - 搜索结果
                                </div>
                              <div class="smart-widget-inner table-responsive">
                                <div class="smart-widget-hidden-section">
                                        <ul class="widget-color-list clearfix">
                                            <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                            <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                            <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                            <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                            <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                            <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                            <li style="background-color:#e36159;" data-color="widget-red"></li>
                                            <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                            <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                            <li style="background-color:#fff;" data-color="reset"></li>
                                        </ul>
                                  </div>
                                  <div class="form-horizontal">
                                     <table class="table table-striped no-margin table-no-bordered" id="dataTable">
										</table>
                                  </div>
                                </div>
                            </div>
                   </div>
            </div>
     </div>
</div>
</body>
</html>