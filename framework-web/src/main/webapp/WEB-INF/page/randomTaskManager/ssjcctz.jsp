<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
<style type="text/css">

	.fixed-table-Container tbody td , .fixed-table-container, .bootstrap-table .table, .table>tbody>tr>th, .table>tfoot>tr>th,  .table>thead>tr>th{
		border:0px;
	}
	.table>tbody>tr>td,  .table>tfoot>tr>td,.table>thead>tr>td,{
		border-bottom:1px;
	}
</style>
</head>
<script type="text/javascript">
	$(document).ready(function(){
		business.listenEnter("searchButtIssue");
        $.ajax({
            type:"post",
            url:WEBPATH+"/tArea/cityList",
            dataType:"json",
            async:false,
            success:function(data){
                $("#belongCountry").append("<option value=''>——县级——</option>");
                $("#belongCity").append("<option value=''>——市级——</option>");
                $.each(data,function(i,item){
                    $("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
                });
                dxly();
            }
        });


        //textpdf
        $("#belongCity").change(function(){
            if ($(this).val() == ""){
                $("#belongCountry option").remove();
                $("#belongCountry").append("<option value=''>——县级——</option>");
                dxly();
                return;
            }
            var parentCode = $(this).val();
            $("#belongCountry option").remove();
            $.ajax({
                type:"post",
                url:WEBPATH+"/tArea/countyListByCode",
                async:false,
                dataType:"json",
                data:{parentCode:parentCode},
                success:function(data){
                    $("#belongCountry").append("<option value=''>——县级——</option>");
                    $.each(data,function(i,item){
                        $("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>");
                    });
                }
            });
            dxly();
        });
        $("#belongCountry").change(function(){
            dxly();
        });
        function dxly() {
            $("#doubleRandomAttr option").remove();
            $.ajax({
                type:"post",
                url:WEBPATH+"/randomTaskManager/attr",
                async:false,
                dataType:"json",
                data:{
                    belongProvince:$("#belongProvince").val(),
                    belongCity:$("#belongCity").val(),
                    belongCountry:$("#belongCountry").val()
                },
                success:function(data){
                    $("#doubleRandomAttr").append("<option value=''>——请选择——</option>");
                    $.each(data,function(i,item){
                        $("#doubleRandomAttr").append("<option value="+item.id+"  >"+item.attrName+"</option>");
                    });
                }
            });
        }
		function LoadingDataListLawObjctItems(){
		$('#TaskIssueListTable').bootstrapTable({       
			 method: 'post',
			 dataType: "json", 
			 url:  WEBPATH+'/randomTaskManager/task-list',
		     undefinedText : '-',  
		     pagination : true, // 分页  
		     striped : true, // 是否显示行间隔色  
		     cache : false, // 是否使用缓存  
		     pageSize:15, 
		     pageNumber: 1,
		     queryParamsType: "",
		     locale:'zh-CN',
		     pageList: [5, 10, 20,30,50], // 自定义分页列表
		     singleSelect: false,
		     contentType: "application/x-www-form-urlencoded;charset=UTF-8",
		     // showColumns : true, // 显示隐藏列  
		     useCurrentPage: true,
		     sidePagination: "server", //服务端请求
		     queryParams:function (params) {
		    		//var launchUserName=$("[name='launchUserName']").val(); 
		    		var extractedPersonNames=$("[name='extractedPersonNames']").val(); 
		    		var belongYear=$("[name='belongYear']").val(); 
		    		var quarter=$("[name='quarter']").val(); 
		    		var databaseId=$("[name='databaseId']").val(); 
		    		var taskStateCode=$("[name='taskStateCode']").val();
		            var objectName = $("#objectName").val();
                    var belongProvince=$("#belongProvince").val();
                    var belongCity=$("#belongCity").val();
                    var belongCountry=$("#belongCountry").val();
		            var temp = {    
		            		//launchUserName:launchUserName,
                            belongProvince:belongProvince,
                            belongCity:belongCity,
                            belongCountry:belongCountry,
		            		extractedPersonNames:extractedPersonNames,
		            		belongYear:belongYear,
		            		quarter:quarter,
		            		databaseId:databaseId,
		            		taskStateCode:taskStateCode,
		            		objectName:objectName,
		            		pageNum: params.pageNumber,
	                    	pageSize: params.pageSize
		            };
		            return temp;
		     },//参数
		     uniqueId : "id", // 每一行的唯一标识  
			 columns: [
			           {
			        	   field: "",
				           title: "序号",
				           align: 'center',
				           formatter: function(value,row,index){
					        	  return index+1;
					       }
			           }, 
			           {
				           field: "",
				           title: "双随机抽查年度",
				           align: 'center',
				           formatter: function(value,row,index){
					        	  return row.belongYear+'年';
					       }
				       },
			           {
				           field: "",
				           title: "双随机抽查季度",
				           align: 'center',
				           formatter: function(value,row,index){
				        	   var html = "";
				        	   if(row.quarter==1){
				        		   html = "第一季度";
				        	   }else if(row.quarter==2){
				        		   html = "第二季度";
				        	   }else if(row.quarter==3){
				        		   html = "第三季度";
				        	   }else if(row.quarter==4){
				        		   html = "第四季度";
				        	   }else {
				        		   // 若出现空，其实是不对的
				        		   html = "";
				        	   }
					           return html;
					       }
			           },
			           {
				           field: "attrName",
				           title: "对象来源双随机库",
				           align: 'center'
			           },
			           {
				           field: "objectName",
				           title: "执法对象名称",
				           align: 'center'
			           },
			           {
				           field: "taskStateName",
				           title: "任务状态",
				           align: 'center'
			           },
			           {
			        	   filed: "taskCheckDate",
			        	   title: "现场检查时间",
			        	   align: 'center',
			        	   formatter: function(value,row,index){
								var time = row.taskCheckDate;
								if(time==null){
									return "";
								}else {
									var date = new Date(time);
					                var y = date.getFullYear();
					                var m = date.getMonth() + 1;
					                var d = date.getDate();
					                var h = date.getHours();  //时
					                var mm = date.getMinutes(); //分
					                var s = date.getSeconds();
					                return y + '-' +m + '-' + d + ' ' + h + ':' + mm + ':' + s;
								}
							}
			           },
			           {
				           field: "extractedPersonNames",
				           title: "双随机执法人员",
				           align: 'center'
			           },
                     {
                         field: "ispositive",
                         title: "是否正面清单企业",
                         align: 'center',
                         formatter: function(value,row,index){
                             return value == null ? "-" : value == 0 ? "否":"是";
                         }
                     },
			           {
				           field: "",
				           title: "操作",	
				           align: 'center',
				           formatter: function(value,row,index){
				        	   var html="";
				        	   if(row.taskId!=null && row.taskId !=''){
				        		   html="<a onclick=\"selectTask('"+row.taskId+"','"+row.lawObjectType+"',0,'"+row.nodeCode+"')\" style=\"cursor:pointer;\"><i class=\"fa fa-search\" style=\"color: #23b7e5;\">详情</i></a>"
				        	   }else{
				        		   html="<a ><i class=\"fa fa-search\" style=\"color: #AAAAAA;\">详情</i></a>"
				        	   }
					           return html;
				           }
			           } 
			 ],
			 responseHandler : function(res) {  
	               return {  
	                   total : res.total,  
	                   rows : res.list  
	               };  
	         },
	         onCheck: function(row, $element) {
	    	   
	         },//单击row事件
	         onUncheck: function(row, $element) {
	        		
		     },
		     onUncheckAll: function(row, $element) {
		       			
		     },
		     onCheckAll:function(row, $element) {
		        		
		     },
		     onRefresh: function () {
		        		
		     },
		     onLoadSuccess:function(params){
		     },
	         formatLoadingMessage: function () {
	        	   return "玩命加载中...";
	         },
	         formatNoMatches: function () { //没有匹配的结果
	        		   return '无符合条件的记录';
	         }
		});
		}
		
		//返回按钮之后参数和页码的回显
		var params = $("#groundBackParams").val();
		var pageNumber=1 ;
		var pageSize=15;
		//console.log("返回参数："+params);
		if(params != null && params != '' && params != 'undefined'){
			var jsonParam = $.parseJSON(params);
			pageNumber = parseInt(jsonParam['pageNum']);
			pageSize = parseInt(jsonParam['pageSize']);
				
			for(var key in jsonParam){
				//绑定设定条件
				/* if(key=='belongCity' || key == 'powerCity' || key == 'randomCity'){
					$("#"+key).find("option[value='"+jsonParam[key]+"']").attr("selected","selected");
					//$("#"+key).val(jsonParam[key]);
					$("#"+key).trigger('change');
					continue;
				} */
				$("[name='"+key+"']").val(jsonParam[key]);
			}
		}
		//执法对象选择模态框表单动态加载enterpriseObjectTable
		LoadingDataListLawObjctItems();
		$('#TaskIssueListTable').bootstrapTable('hideColumn', 'id');
		$('#TaskIssueListTable').bootstrapTable('refresh',{pageNumber:pageNumber,pageSize:pageSize});
		
     		//绑定搜索按钮
	$('#searchButtIssue').click(function() {
		//$('#TaskIssueListTable').bootstrapTable('refresh');
		$('#TaskIssueListTable').bootstrapTable('refreshOptions',{pageNumber:1,pageSize:15});
   	});
    // 日期控件  		
	$("#belongYear").datetimepicker({
	     format:'yyyy',
	   	 language: 'cn',
	   	 weekStart: 1,  
         autoclose: true,  
         startView: 4,  
         minView: 4,  
         forceParse: false,
         endDate:new Date().getFullYear()+1
	});
	
 });
	// 跳转查看页面
	function selectTask(taskId,lawObjectType,sourcePlace,nodeCode){
	    var obj={taskId:taskId,lawObjectType:lawObjectType,parentUrl:sourcePlace,nodeCode:nodeCode};
	    if(nodeCode =='0'){
	    	business.addMainContentParserHtml(WEBPATH+'/taskNodeManager/history-rwfp',obj); 
	    }else  if(nodeCode=='1'){
	    	 business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType='+0,obj);
	    }else{
	    	swal({title: "该状态下不能查看任务" ,text: "",type:"error"});
	    }
		   
	 }
	function downloadExcelRandomTask(){
        var extractedPersonNames=$("[name='extractedPersonNames']").val();
        var belongYear=$("[name='belongYear']").val();
        var quarter=$("[name='quarter']").val();
        var databaseId=$("[name='databaseId']").val();
        var taskStateCode=$("[name='taskStateCode']").val();
        var objectName = $("#objectName").val();
        var belongProvince=$("#belongProvince").val();
        var belongCity=$("#belongCity").val();
        var belongCountry=$("#belongCountry").val();
		var path = WEBPATH+'/randomTaskManager/download-random-execl?extractedPersonNames='+extractedPersonNames+'&belongYear='+belongYear+'&quarter='
		 +quarter+'&databaseId='+databaseId+'&taskStateCode='+taskStateCode+'&belongProvince='+belongProvince+"&belongCity="+belongCity+"&belongCountry="+belongCountry+"&objectName="+objectName;
	    window.location.href=path;
	   
	}
 	 
</script>
<body>
<div class="main-container">
	<input id="groundBackParams" name="groundBackParams" type="hidden" value='${params }'/><!-- 返回所需的参数 -->
		<div class="padding-md">
                <div class="smart-widget">
						<div class="smart-widget-inner">
							<jsp:include page="RandomBusiness.jsp"></jsp:include>
							<div class="smart-widget-body">
								<div class="tab-content">
									<div class="tab-pane fade in active" id="style1Tab2">
										<div class="smart-widget-inner table-responsive">
                                        	<div class="smart-widget-body form-horizontal">
                                                <!-- <div class="form-group">
                                                    <label class="control-label col-lg-2">发起人</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="launchUserName"  placeholder="发起人" class="form-control" data-parsley-required="true">
                                                    </div>
                                                    <label class="control-label col-lg-2">抽取人员</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="extractedPersonNames" placeholder="抽取人员" class="form-control" data-parsley-required="true">
                                                    </div>
                                                </div> -->
                                                <div class="form-group">
                                                    <label for="行政区" class="col-lg-2 control-label">行政区</label>
                                                    <div class="col-md-2 col-lg-2 col-sm-2">
                                                        <select
                                                                class="form-control" id ="belongProvince" name ="belongProvince">
                                                            <option value="35000000">福建省</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                                        <select
                                                                class="form-control" id ="belongCity" name ="belongCity">
                                                            <!--  <option value="">请选择</option> -->
                                                        </select>
                                                    </div>
                                                    <div class="col-md-3 col-lg-3 col-sm-3">
                                                        <select
                                                                class="form-control" id ="belongCountry" name ="belongCountry">
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="control-label col-lg-2">双随机抽查年度</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" id="belongYear"  readonly="readonly"   name="belongYear" placeholder="双随机抽查年度" class="form-control" data-parsley-required="true">
                                                    </div>
                                                    <label class="control-label col-lg-2">双随机抽查季度</label>
                                                    <div class="col-lg-3">
                                                        <select class="form-control" name="quarter">
                                                            <option value="">——请选择——</option>
                                                            <option value="1">第一季度</option>
                                                            <option value="2">第二季度</option>
                                                            <option value="3">第三季度</option>
                                                            <option value="4">第四季度</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="control-label col-lg-2">对象来源双随机库</label>
                                                    <div class="col-lg-3">
                                                        <select class="form-control" name="databaseId" id="doubleRandomAttr">
                                                            <option value="">——请选择——</option>
                                                            <c:forEach items="${doubleRandomAttrList}" var="item" varStatus="status">
												 				<option value='${item.id }'>${item.attrName }</option>
															</c:forEach>
                                                        </select>
                                                    </div>
                                                    <label class="control-label col-lg-2">任务状态</label>
                                                    <div class="col-lg-3">
                                                       <select class="form-control" name="taskStateCode">
                                                            <option value="">——请选择——</option>
                                                            <option value="1">办理中</option>
                                                            <option value="2">已办结</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <!--<div class="form-group">
                                                    <label class="control-label col-lg-2">现场检查时间</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="checkTimeStart"  placeholder="开始时间" class="form-control" data-parsley-required="true" id="checkTimeStart">
                                                    </div>
                                                    <label class="control-label col-lg-2">现场检查时间</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="checkTimeEnd" placeholder="结束时间" class="form-control" data-parsley-required="true" id="checkTimeEnd">
                                                    </div>
                                                </div>-->
                                                <div class="form-group">
                                                <label class="control-label col-lg-2">双随机执法人员</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="extractedPersonNames" placeholder="双随机执法人员" class="form-control" data-parsley-required="true">
                                                    </div>
                                                   <label class="control-label col-lg-2">执法对象名称</label>
                                                    <div class="col-lg-3">
                                                        <input type="text" name="objectName" placeholder="执法对象名称" class="form-control" data-parsley-required="true" id="objectName">
                                                    </div> 
                                                    
                                                </div>
                                                <div class="col-lg-7"></div>
                                                <div class="col-lg-3 text-right">
                                                        <button class="btn btn-info" type="button" style="width:120px;" id="searchButtIssue" >查询</button>
                                                    </div>
                                                <div class="form-group">
				                                    <div class="col-lg-12 text-right">
				                                        <button class="btn btn-info" type="button" id ="downloadExcelTask" onclick="downloadExcelRandomTask()">导出excel</button>
				                                    </div>
				                                 </div>
                                            </div>
                                              <table class="table table-striped table-hover no-margin table-no-bordered"  id="TaskIssueListTable">
                                              		
                                              </table>
                                        </div>
									</div>
								</div>
							</div>
						</div>
					</div>          
                    
				</div>
			</div>
</body>
</html>