<%@ page language="java" contentType="text/html; charset=UTF-8"
    pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
    <meta charset="utf-8">
    <title>福建省环境监察执法平台</title>
	<meta name="renderer" content="webkit">
	<meta http-equiv="pragma" content="no-cache">
	<meta http-equiv="cache-control" content="no-cache">
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0" />
	<script src="${webpath }/static/js/tableExport.min.js"  type="text/javascript"></script>
	<style type="text/css">
		.fixed-table-container thead th .th-inner {
			white-space : normal;
		}
		table.table-expandable > tbody > tr div.table-expandable-arrow.up {
		    background-position:0px 0px;
		}
	</style>
</head>
	<script type="text/javascript">
		$(function(){
			//监听回退键
			business.listenBackSpace();
		});
	</script>
<body>
<div class="main-container">
	<div class="padding-md">
        <div class="row">
            <div class="col-lg-12 text-right form-inline no-margin">
            	<div class="smart-widget">
					<div class="smart-widget-inner">
						<div class="smart-widget-body">
							<div class="form-group">
								<div class="input-group">
									<!-- <input type="text" value="2017.01.01-2017.04.12" class="datepicker-input form-control"> -->
									<input type="text" readonly="readonly" value="" id="startTime" name="startTime"
										   placeholder="起始时间" class="datepicker-input form-control" data-parsley-required="true">
								</div>
								<div class="input-group">
									<input type="text" readonly="readonly" value="" id="endTime" name="endTime"
										   placeholder="结束时间" class="datepicker-input form-control" data-parsley-required="true">
									<!-- <span class="input-group-addon"><i class="fa fa-calendar"></i></span> -->
								</div>
							</div>
							<div class="form-group">
								<select class="form-control"  id ='belongProvince' name ="belongProvince">
									<option value="35000000">福建省</option>
								</select>
							</div>
							<div class="form-group">
								<select class="form-control"  id ="belongCity" name="belongCity">

								</select>
							</div>
							<div class="form-group">
								<select class="form-control"  id ="belongCountry" name ="belongCountry">

								</select>
							</div>
							<div class="form-group">
								<div class="padding-sm" style="float: left;">
									<button type="button" class="btn btn-info btn-block" onclick="selectCount()">查询统计</button>
								</div>
							</div>
							<div class="form-group">
								<div class="padding-sm" style="float: left;">
									<button onclick ="exportTable()" type="button" class="btn btn-info no-shadow" style="margin-right: 10px;">导出EXCEL</button>
								</div>
							</div>
                		</div>
                	</div>
                </div>
            </div>
        </div>
		<div class="row">
			<div class="col-lg-12">
				<div class="smart-widget widget-blue">
					<div class="smart-widget-header font-16">
						<i class="fa fa-commenting"></i> 现场检查数据统计
					</div>
					<div class="smart-widget-inner">
						<div class="smart-widget-body no-padding">
							<div class="padding-md table-responsive">
								<table id="qsltdlbTable" class =" table table-bordered">
								</table>
							</div>
						</div>
					</div>
					<div id ="exportNoTable" class="font-16" style="float: right;display: none" >
						<button     onclick ="exportNoTable()" type="button" class="btn btn-info no-shadow" style="margin-right: 10px;">导出未执法人员EXCEL</button>
					</div>
					<div class="smart-widget-inner" >
						<div class="smart-widget-body no-padding">

							<div class="padding-md table-responsive">
								<table id="NozfSysusersTable" class =" table table-bordered">
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<script type="text/javascript">
	var endDate=getBeforeDate(1);
	var startDate=getBeforeDate(7);
	$("#startTime").val(startDate);
	$("#endTime").val(endDate);
	$(function() {
		$.ajax({
			type:"post",
			url:WEBPATH+"/tArea/chickUserArea",
			dataType:"json",
			data:{},
			async:false,
			success:function(list){
				if(list.cityStatus =='1'){
					//省级用户
					$.ajax({
						type:"post",
						url:WEBPATH+"/tArea/cityList",
						dataType:"json",
						async:false,
						success:function(data){
							$("#belongCity").append("<option value=''>请选择</option>");
							$("#belongCountry").append("<option value=''>请选择</option>");
							$.each(data,function(i,item){
								$("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
							});
						}
					});
				}else if(list.cityStatus =="2"){
					//市级用户
					$("#belongCity").append("<option selected value="+list.cityCode+">"+list.cityName+"</option>");
					$.ajax({
						type:"post",
						url:WEBPATH+"/tArea/countyListByCode",
						dataType:"json",
						async:false,
						data:{parentCode:list.cityCode},
						success:function(data){
							$("#belongCountry").append("<option value=''>请选择</option>");
							$.each(data,function(i,item){
								$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>");
							});
						}
					});
				}else{
					//县级用户
					$("#belongCity").append("<option selected value="+list.cityCode+">"+list.cityName+"</option>");
					$("#belongCountry").append("<option selected value="+list.countyCode+"  >"+list.countyName+"</option>");
				}
			}
		});

		$("#belongCity").change(function(){
			if ($(this).val() == ""){
				$("#belongCountry option").remove();
				$("#belongCountry").html("<option value=''>请选择</option>");
				return;
			}
			var parentCode = $(this).val();
			$("#belongCountry option").remove();
			$.ajax({
				type:"post",
				url:WEBPATH+"/tArea/countyListByCode",
				dataType:"json",
				async:false,
				data:{parentCode:parentCode},
				success:function(data){
					$("#belongCountry").html("<option value=''>请选择</option>");
					$.each(data,function(i,item){
						$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>");
					});
				}
			});
		});
		//自定义时间段
		$("#startTime").datetimepicker({
			language:'cn',
			format:'yyyy-mm-dd',
			todayBtn: true,
			autoclose: true,
			minView:'year',
			maxView:'decade',
			endDate:new Date(getBeforeDate(1))
		}).on('changeDate', function(ev){
			startDate = ev.date;
			$('#endTime').datetimepicker('setStartDate', startDate);

		});
		$("#endTime").datetimepicker({
			language:'cn',
			format:'yyyy-mm-dd',
			todayBtn: true,
			autoclose: true,
			minView:'year',
			maxView:'decade',
			endDate:new Date(getBeforeDate(1))
		}).on('changeDate', function(ev){
			endDate = ev.date;
			$('#startTime').datetimepicker('setEndDate', endDate);

		});
		loadingDetailTable();
	});
	function loadingDetailTable(){
		$("#qsltdlbTable").bootstrapTable({
			method: 'post',
			dataType: "json",
			url:  WEBPATH+'/jckh/siteInspectionStatistics',
			undefinedText : '-',
			async:true,
			// pagination : true, // 分页
			striped : true, // 是否显示行间隔色
			cache : false, // 是否使用缓存
			// pageSize: 15, // 设置默认分页为 20
			// pageNumber: 1,
			queryParamsType: "",
			// pageList: [15, 25, 50, 100, 200], // 自定义分页列表
			// singleSelect: false,
			contentType: "application/x-www-form-urlencoded;charset=UTF-8",
			// showColumns : true, // 显示隐藏列
			sidePagination: "server", //服务端请求
			queryParams:function (params) {
	            var temp = {
					belongCity:$("#belongCity").val(),
					belongCountry:$("#belongCountry").val(),
					startTime:$("#startTime").val(),
					endTime:$("#endTime").val(),
	            };
	            return temp;
	     	},//参数
	     	uniqueId : "areaCode", // 每一行的唯一标识
			columns: [
				{
					field: "areaName",
					title: "行政区划",
					align: "center"

				},{
					field: "areaCode",
					title: "行政区划代码",
					align: 'center'
				},{
					field: "baseOrgNumber",
					title: "执法机构数",
					align: 'center'
				},{
                    field: "lawOrgNumber",
                    title: "上报执法机构数",
                    align: 'center'
                },{
					field: "basePersonNumber",
					title: "执法人员数",
					align: 'center'
				},
				{
					field: "orgPercentage",
					title: "填报检查数据机构占比",
					align: "center",
					formatter : function(value, row,index) {
						return value+'%';
					}
				},{
					field: "taskCount",
					title: "填报执法检查记录",
					align: 'center'

				},{
                    field: "taskCount24",
                    title: "24小时内填报执法记录",
                    align: 'center'

                },{
                    field: "appCheckData",
                    title: "填报巡查执法记录",
                    align: 'center'

                },{
					field: "perCapita",
					title: "人均填报数量",
					align: 'center'
				},
                {
                    field: "lawPersonNumber",
                    title: "出动人数",
                    align: 'center'

                },
                {
                    field: "noLawPersonNumber",
                    title: "未执法人数",
                    align: 'center',
                    formatter:function(value,row,index){

                            istr="";
							   istr +="<a href='#' onclick=\"linkCase('" + row.areaCode +"')\">"
                                    +"<span style='color:#23b7e5;'>" + value
                            return  istr;
                    }

                },
				{
					field: "frequency",
					title: "出动人次",
					align: 'center'

				},

				{
					field: "activeRate",
					title: "执法人员活跃率",
					align: "center",
					formatter : function(value, row,index) {
						return value+'%';
					}
				},
				{
					field: "lawProblemNumber",
					title: "发现问题数量",
					align: 'center'

				},{
					field: "problemPercentage",
					title: "问题占比",
					align: 'center',
					formatter : function(value, row,index) {
						return value+'%';
					}

				},{
					field: "problemPerCapita",
					title: "人均发现问题数",
					align: 'center'

				}
			],
			responseHandler : function(res) {
				return {
				    //total : res.size,
				    rows : res.data
            	};
			},
			onCheck: function(row, $element) {
			},//单击row事件
			onUncheck: function(row, $element) {
			},
			rowStyle:function(row, index){
				var style = {};
				/* if(parseFloat(row.lawUserDayCount)<4 || parseFloat(row.lawTimesUserCount)<4){
					style={css:{'color':'red'}};//“人均执法天数”小于4天的，整行字体标为红色
				} */
				if(row.onJobRandomUserCount == 0){
					style={classes:'hideRow'};//如果在编在岗且纳入双随机人数为0，隐藏一行
				}
				if(row.belongCode != null && row.belongCode.length != 8) {
					style={classes:'childRow'}
				}
				return style;
			},
			formatLoadingMessage: function () {
				return "数据加载中...";
			},
			formatNoMatches: function () { //没有匹配的结果
				return '无符合条件的记录';
			},
			onLoadSuccess:function(data){
			//	$("#qsltdlbTable").bootstrapTable('hideColumn','belongCode');
				$(".hideRow").hide();//如果在编在岗且纳入双随机人数为0，隐藏一行
			}
		});

	}
	//查询
	function selectCount() {
		$("#qsltdlbTable").bootstrapTable('refresh');
	}

    function linkCase(caseId){
	if(caseId==="null"){
		caseId = "";
	}
        $("#NozfSysusersTable").bootstrapTable('destroy');
        debugger
        var div = document.getElementById("exportNoTable");

        // 切换显示和隐藏
        if (div.style.display === "none") {
            div.style.display = "block";
        }

        $("#NozfSysusersTable").bootstrapTable({
            method: 'post',
            dataType: "json",
            url:  WEBPATH+'/jckh/exportNozfSysusers',
            undefinedText : '-',
            async:false,
            // pagination : true, // 分页
            striped : true, // 是否显示行间隔色
            cache : false, // 是否使用缓存
            // pageSize: 1000, // 设置默认分页为 20
            // pageNumber: 1,
            queryParamsType: "",
            // pageList: [15, 25, 50, 100, 200], // 自定义分页列表
            // singleSelect: false,
            contentType: "application/x-www-form-urlencoded;charset=UTF-8",
            // showColumns : true, // 显示隐藏列
            sidePagination: "server", //服务端请求
            queryParams:function(params){
                var temp = {
                    areaCode :caseId ,
                    belongCity:$("#belongCity").val(),
                    belongCountry:$("#belongCountry").val(),
                    startTime:$("#startTime").val(),
                    endTime:$("#endTime").val(),
                };
                return temp;
			},

            columns: [
                {
                    field: "province",
                    title: "省",
                    align: "center"

                },{
                    field: "city",
                    title: "市",
                    align: 'center'
                },{
                    field: "country",
                    title: "县",
                    align: 'center'
                },{
                    field: "loginname",
                    title: "登录账号",
                    align: 'center'
                },{
                    field: "username",
                    title: "姓名",
                    align: 'center'
                }


            ],
            responseHandler : function(res) {
                return {
                    //total : res.size,
                    rows : res.data
                };
            },
            onCheck: function(row, $element) {
            },//单击row事件
            onUncheck: function(row, $element) {
            },
            formatLoadingMessage: function () {
                return "数据加载中...";
            },
            formatNoMatches: function () { //没有匹配的结果
                return '无符合条件的记录';
            },
            onLoadSuccess:function(data){
                //	$("#qsltdlbTable").bootstrapTable('hideColumn','belongCode');
                //如果在编在岗且纳入双随机人数为0，隐藏一行
            }
        });
    }


    //导出未执法
    function exportNoTable(){
        $('#NozfSysusersTable').tableExport({
            type:'excel',
            escape:'false',
            fileName:fileName(),
            ignoreColumn: []
        });
    }

	//导出页面表单
	function exportTable(){
		$('#qsltdlbTable').tableExport({
			type:'excel',
			escape:'false',
			fileName:fileName(),
			ignoreColumn: []
		});
	}

	function fileName(){
		 if ($('#belongCity').val()!=null && $('#belongCity').val()!=''){
			return '福建省'+$('#belongCity option:selected').text()+"现场检查数据统计情况";
		} else {
			return '福建省现场检查数据统计情况';
		}
	}
	/**
	 * 	前数几天
	 * @param val
	 * @returns {string}
	 */
	function getBeforeDate(val) {
		var curDate = new Date();
		var beforeDate = new Date(curDate.getTime() - 24*60*60*1000*val); //前一天
		var y=beforeDate.getFullYear();    //获取完整的年份(4位,1970-????)
		var m=1+ beforeDate.getMonth();       //获取当前月份(0-11,0代表1月)
		var d=beforeDate.getDate();
		if(d<10) {
			return y + "-" + m + "-0" + d;
		}
		return y + "-" + m + "-" + d;
	}
</script>
</body>
</html>
