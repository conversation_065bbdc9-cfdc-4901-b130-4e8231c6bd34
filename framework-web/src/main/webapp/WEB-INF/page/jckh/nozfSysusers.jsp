<%--
  Created by IntelliJ IDEA.
  User: wangyi
  Date: 2023/7/25
  Time: 15:18
  To change this template use File | Settings | File Templates.
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <meta charset="utf-8">
    <title>福建省环境监察执法平台</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <script src="${webpath }/static/js/tableExport.min.js"  type="text/javascript"></script>
    <style type="text/css">
        .fixed-table-container thead th .th-inner {
            white-space : normal;
        }
        table.table-expandable > tbody > tr div.table-expandable-arrow.up {
            background-position:0px 0px;
        }
    </style>
</head>
<body>
<div class="main-container">
    <div class="padding-md">
        <div class="row">
            <div class="col-lg-12 text-right form-inline no-margin">
                <div class="smart-widget">
                    <div class="smart-widget-inner">
                        <div class="smart-widget-body">

                            <div class="form-group">
                                <div class="padding-sm" style="float: left;">
                                    <button onclick ="exportTable()" type="button" class="btn btn-info no-shadow" style="margin-right: 10px;">导出EXCEL</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="smart-widget widget-blue">
                    <div class="smart-widget-header font-16">
                        <i class="fa fa-commenting"></i> 未执法人员
                    </div>
                    <div class="smart-widget-inner">
                        <div class="smart-widget-body no-padding">
                            <div class="padding-md table-responsive">
                                <table id="table" data-toggle="table" data-pagination="true" data-search="true" data-show-export="true" data-export-options='{"fileName": "table"}'>
                                    <thead>
                                    <tr>
                                        <th data-field="province">省</th>
                                        <th data-field="city">市</th>
                                        <th data-field="county">县</th>
                                        <th data-field="name">姓名</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <!-- 表格数据会通过 JavaScript 动态生成 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function() {
        $.ajax({
            type: "post",
            url: WEBPATH + "/tArea/chickUserArea",
            dataType: "json",
            data: {},
            async: false,
            success: function (list) {

                    //省级用户
                    $.ajax({
                        type: "post",
                        url: WEBPATH + "/tArea/cityList",
                        dataType: "json",
                        async: false,
                        success: function (data) {
                            $("#belongCity").append("<option value=''>请选择</option>");
                            $("#belongCountry").append("<option value=''>请选择</option>");
                            $.each(data, function (i, item) {
                                $("#belongCity").append("<option value=" + item.code + ">" + item.name + "</option>");
                            });
                        }
                    });

            }
        });
    });
</script>
</body>
</html>
