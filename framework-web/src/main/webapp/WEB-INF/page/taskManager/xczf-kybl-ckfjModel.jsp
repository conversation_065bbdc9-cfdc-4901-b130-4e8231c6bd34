<%@ page language="java" contentType="text/html; charset=utf-8"
	pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<script type="text/javascript">
	/* function downloadFile(id){
		"${webpath}/inquestRec/downloadFile?id=${obj.id}"
		$.ajax({
			type:"post",
			url:WEBPATH+"/inquestRec/downloadFile",
			data:{id:id},
		});
	} */
	$('#ckfj').on('hide.bs.modal', function () {
		   $(this).removeData("bs.modal");  
	})
	function deleteFile(id){
		swal({
	        title: "您确定执行删除操作吗？",
	        type: "warning",
	        showCancelButton: true,
	        closeOnConfirm: false,
	        confirmButtonText: "是的，我要删除",
	        confirmButtonColor: "#ec6c62"
	    	}, function() {
	            $.ajax({
	              type: "post",
	   		      url: WEBPATH+"/inquestRec/fileinfoDelete",
	   		      data:{id:id},
	            }).done(function(data) {
	            	 if(data.meta.result==="success"){
	            		 swal({
		 		        		title : "删除成功", 
		                        text : "附件删除成功",
		                        type : "success",
		                        allowOutsideClick :true
		 		        	})
		 		        	$("#"+id).remove();
	 		        }else{
	 		          swal({title:"删除失败", text:"附件删除失败", type:"error",allowOutsideClick :true});
	 		        }
	            }).error(function(data) {
	                swal({title:"操作失败", text:"删除操作失败了!", type:"error",allowOutsideClick :true});
	            });
	    });
	}
</script>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="myModalLabel">查看附件</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-inner table-responsive">
		<div class="smart-widget-body form-horizontal" style="overflow: hidden">
			<c:if test="${not empty fileList}">
				<c:forEach items="${fileList}" var="obj" varStatus="status">
					<c:if test="${status.count eq 1 || (status.count-1) % 6 eq 0}">
					</c:if>
					<c:if test="${obj.picType==0}">
						<div class="col-md-3 col-sm-3" id="${obj.id }">
							<div class="pricing-widget clean-pricing"
								style="cursor: pointer;">
								<div class="pricing-value">
									<span class="value"><img style="height: 150px;"
										data-toggle="modal"
										data-remote="${webpath}/inquestRec/showFileModal?id=${obj.id}"
										data-target="#inputImgModeler"
										src="${webpath}/static/img/pdf-thumb.jpg" /></span>
								</div>
								<ul class="text-left padding-sm" style="margin-top: -20px;">
									<li>文件类型：${obj.fileTypeName}</li>
									<c:if test="${obj.picName != null && obj.picName.length()>8 }">
										<li>文件名：${obj.picName.substring(0,5) }...${obj.picName.substring(obj.picName.length()-3)}</li>
									</c:if>
									<c:if test="${obj.picName != null && obj.picName.length()<=8 }">
										<li>文件名：${obj.picName }</li>
									</c:if>
									<li class="text-center" style="margin-top:10px;">
										<a href="${webpath}/inquestRec/downloadFile?id=${obj.id}">
										<button type="button" class="btn btn-info btn-xs"
											>下载</button>
										</a>
										<c:if test="${lsFlag==1 }"> <%-- 1表示正常待办任务进来的，0表示历史任务进来的，不显示删除按钮 --%>
											<button type="button" class="btn btn-info btn-xs"
												onclick="deleteFile('${obj.id}')">删除</button>
										</c:if>
									</li>
								</ul>
							</div>
						</div>
					</c:if>
					<c:if test="${obj.picType==1}">
						<div class="col-md-3 col-sm-3" id="${obj.id }">
							<div class="pricing-widget clean-pricing" style="cursor: pointer;">
								<div class="pricing-value">
									<span class="value"><img style="height: 150px;" id="P${obj.id }"
										data-toggle="modal"
										data-remote="${webpath}/inquestRec/showFileModal?id=${obj.id}"
										data-target="#inputImgModeler"
										src="${FASTDFS_ADDR}/${obj.picUrl}" /></span>
								</div>
								<ul class="text-left padding-sm" style="margin-top: -20px;">

									<li>文件类型：${obj.fileTypeName}</li>
									<c:if test="${obj.picName != null && obj.picName.length()>8 }">
										<li>文件名：${obj.picName.substring(0,5) }...${obj.picName.substring(obj.picName.length()-3) }</li>
									</c:if>
									<c:if test="${obj.picName != null && obj.picName.length()<=8 }">
										<li>文件名：${obj.picName }</li>
									</c:if>
									<li class="text-center" style="margin-top:10px;">
										<a href="${webpath}/inquestRec/downloadFile?id=${obj.id}">
										<button type="button" class="btn btn-info btn-xs"
											>下载</button>
										</a>
										<c:if test="${lsFlag==1 }"> <%-- 1表示正常待办任务进来的，0表示历史任务进来的，不显示删除按钮 --%>
											<button type="button" class="btn btn-info btn-xs"
												onclick="deleteFile('${obj.id}')">删除</button>
										</c:if>
									</li>
								</ul>
							</div>
						</div>
					</c:if>
					<c:if test="${status.count % 6 eq 0 || status.count eq 6}">
					</c:if>
				</c:forEach>
			</c:if>
		</div>
	</div>
</div>
<div class="modal-footer">
	<!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
</div>

	<!-- 照片编辑modeler 结束 -->
