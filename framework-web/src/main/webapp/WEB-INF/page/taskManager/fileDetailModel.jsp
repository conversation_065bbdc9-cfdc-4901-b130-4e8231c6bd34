<%@ page language="java" contentType="text/html; charset=utf-8"
    pageEncoding="utf-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<div class="modal-header">
	<button type="button" class="close" data-dismiss="modal"
		aria-hidden="true">&times;</button>
	<h4 class="modal-title" id="fileModalLabel">预览附件</h4>
</div>
<div class="modal-body">
	<div class="smart-widget-body">
		<c:if test="${fileInfoModel.picType==0}">
			<div class="pricing-value">
				<div id="B${fileInfoModel.id}"></div>
			</div>
		</c:if>
		<c:if test="${fileInfoModel.picType==1}">
			<%-- <div class="pricing-value">
				<span class="value"><img style="height:100%;" src="${FASTDFS_ADDR}/${fileInfoModel.picUrl}" /></span>
			</div> --%>
			<div class="col-md-12 col-sm-12"  id="img2017">
				<div class="pricing-value">
					<div class="changneng" style="text-align: center;">
					    <img id="image"  src="${FASTDFS_ADDR}/${fileInfoModel.picUrl}" />
					</div>
				</div>
				<input type="button" value="向←旋转" onclick="leftClick()" id="rotationLeft"/>
	            <input type="button" value="向→旋转" id="rotationRight" onclick="rightClick()"/>
				<input type="hidden" id="jiaoduR" value="0">
				<input type="hidden" id="jiaoduL" value="360">
				<input type="hidden" id="degree">
			</div>
			<div><button onclick="savePic('${fileInfoModel.id}','${fileInfoModel.picUrl }','${tableName }','PIC_URL','${fileInfoModel.fileId }')" class="btn btn-info" style="float: right;">保存</button></div>
		</c:if>
	</div>
</div>

<div class="modal-footer"> 
	<button type="button" class="btn btn-default"   data-dismiss="modal">关闭</button>
	 
</div>
<script type="text/javascript">
$(document).ready(function(){
		$('#inputImgModeler').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");  
		})
		var obj=$.parseJSON('${fileInfo}');
		if(obj != null) {
			var select="#B"+obj.id;
			if(obj.picType==0){
				if(isIEWhether){
					if(PDFObject.supportsPDFs){
						PDFObject.embed(FASTDFS_ADDR+obj.picUrl,select);
					} else {
						 swal({ 
						        title: "提示",  
						        text: "您的浏览器不支持pdf预览功能，请安装pdf阅读器后重试！",  
						        type: "warning", 
						        showCancelButton: true, 
						        closeOnConfirm: true, 
						        confirmButtonText: "下载并安装", 
						        confirmButtonColor: "#ec6c62" 
						    }, function() { 
						        window.location.href=WEBPATH+"/sysUser/downloadPdfReader"
						    }); 
					}
				}else{
					var options = {
							/* pdfOpenParams: {
								navpanes: 0,
								toolbar: 0,
								statusbar: 0,
								page: 1
							}, */
							forcePDFJS: false,
							PDFJS_URL: WEBPATH+"/static/pdfjs/web/viewer.html"
					};
					PDFObject.embed(FASTDFS_ADDR+obj.picUrl,select, options);
				}
			}
		}
		
})

$(document).ready(function(){
	var img = document.getElementById("image");
	if(img!=null&& img!='' && typeof(img)!='undefined'){
		var dx = img.width;
	    var dy = img.heigth;
	    if(dx>dy){
		    img.style.width="100%";
	    }else{
		    img.style.height="100%";
	    }
	}
})
function rotateRight(){
	   var x = document.getElementById("image");
	  // var p = document.getElementById("szh");
	   n=parseInt($("#jiaoduR").val());
	   n = n + 90;
	   
	   x.style.transform = "rotate(" + n + "deg)";
	   x.style.webkitTransform = "rotate(" + n + "deg)";
	   x.style.OTransform = "rotate(" + n + "deg)";
	   x.style.MozTransform = "rotate(" + n + "deg)";
	   var dx = x.width;
	   var dy = x.heigth;
	   if(dx>dy){
		   x.style.width="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }else{
		   x.style.height="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }
	   if (n >= 360){
	   		n = 0;
	   }
	   $("#jiaoduR").val(n);
	   $("#degree").val(n/90);
}   
function rightClick(){
	rotateRight();
	
}

function leftClick(){
	rotateLeft();
}

function rotateLeft(){
	   var x = document.getElementById("image");
	   //var p = document.getElementById("szh");
	   n=parseInt($("#jiaoduL").val());
	   n = n - 90;
	  
	   x.style.transform = "rotate(" + n + "deg)";
	   x.style.webkitTransform = "rotate(" + n + "deg)";
	   x.style.OTransform = "rotate(" + n + "deg)";
	   x.style.MozTransform = "rotate(" + n + "deg)";
	   var dx = x.width;
	   var dy = x.heigth;
	   if(dx>dy){
		   x.style.width="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }else{
		   x.style.height="100%";
		   if(n!=0 && n!=180 && n!=360){
			   x.style.marginTop="190px";
		   }else{
			   x.style.marginTop="0px";
		   }
	   }
	   if (n <= 0){
	   		n = 360;
	   }
	   $("#jiaoduL").val(n);
	   $("#degree").val(n/90);
} 

/* function savePic(id,url,tableName,column,fileId){
	var degree = $("#degree").val();
	business.openwait();
	$.ajax({
		method:'POST',
		url:WEBPATH+'/askingManager/rotateImage',
		data:{
			id:id,
			url:url,
			tableName:tableName,
			column:column,
			fileId:fileId,
			degree:degree
		}, error: function(request) {
        	business.closewait();//关闭遮罩层
        	swal("错误!","系统错误", "error");
        },
		success:function(data){
			business.closewait();//关闭遮罩层
			if(data.meta.code==200){
				swal({
					title : "提交成功！",
					text : "",
					type : "success"
				}, function() {
					//刷新当前页面
				})
			}else{
				swal({
					title : "操作失败！",
					text : data.data,
					type : "error"
				});
			}
		}
	});
} */
</script>

<style>
   .changneng {
    width: 100%;
    height: 500px;
    overflow: auto;
    position: relative;
    margin-top: 10px;
}

.changneng > p {
    position: absolute;
    cursor: move;
    transform-origin: center;
    -webkit-transform-origin: center;
    -moz-transform-origin: center;
    -ms-transform-origin: center;
    -o-transform-origin: center;
    width: 100%;
    height: 100%;
    padding: 0;
    -webkit-margin-before: 0;
    -webkit-margin-after: 0;
    cursor: move;
    left: 0;
    top: 0;
}

.changneng > p > img {
    display: inline-block;
    vertical-align: middle;
    cursor: move;
   }
</style>