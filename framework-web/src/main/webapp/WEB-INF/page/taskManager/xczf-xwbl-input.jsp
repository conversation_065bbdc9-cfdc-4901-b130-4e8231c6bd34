<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>

<sec:authentication property="principal" var="authentication"/>
<%  
  String server_addr=PropertiesHandlerUtil.getValue("server.addr","fastdfs");
%>  
<c:set var="SERVER_ADDR"><%=server_addr%></c:set>

<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
.redTest{
	background-color:red;
}
</style>
<script src='${webpath }/static/js/model/chickUserModel_ask.js'></script>
<script src='${webpath }/static/js/model/chickUserModel_Record.js'></script>
<title>Insert title here</title>
<SCRIPT type="text/javascript">
	var setting = {
		data: {
			key: {
				title:"t"
			},
			simpleData: {
				enable: true
			}
		},
		callback: {
			onClick: onClick
		}
	};
	var zNodes;
	function onClick(event, treeId, treeNode, clickFlag) {
		$("#askingUnitId").val(treeNode.id);
		$("#askingUnitName").val(treeNode.name);
		$('#askingRecordForm').formValidation('revalidateField', 'askingUnitName'); 
	}		

	$(document).ready(function(){
		//business.listenEnter('templateName');
		var areacode='${authentication.belongAreaId}';
		
		$.ajax({
			type: "Post",
			url: WEBPATH+"/sysDept/getDepts",
			data:{areaCode:areacode},
			success: function (strReult) {
				//alert(strReult.type);
				if(strReult.type=="success"){
					zNodes=eval(strReult.data);
					$.fn.zTree.init($("#treeDept"), setting, zNodes);
					
					//默认选中第一个节点
	                var treeObj = $.fn.zTree.getZTreeObj("treeDept");
					var selDeptID = '${askingRecord.askingUnitId }';
					if(selDeptID==""){
						selDeptID = '${authentication.belongDepartmentId}';
					}
	                var selNode = treeObj.getNodeByParam("id", selDeptID, null);
	                treeObj.selectNode(selNode);//选择点 
	                //treeObj.expandNode(selNode,true);
				}else{
					swal("服务异常，部门数据请求失败!", "", "warning");
				}
			},
			error: function(){
		   		swal("网络异常，请求数据失败!", "", "error");
			}
		});
		
	});
</SCRIPT>
</head>
<body>
	<div class="main-container">
		<div class="padding-md">
			<!-- 必要信息字段 -->
			<jsp:include page="BusinessFlow.jsp"></jsp:include>
			<!--第二层询问笔录row-->
			<div class="row">
				<!--任务办理表单-->
				<div class="col-lg-12">
					<div class="smart-widget widget-light-grey">
						<div class="smart-widget-header font-16">
								<i class="fa fa-comment"></i> 询问笔录制作  <c:if test="${askingRecord.id==null || askingRecord.id =='' }"><span id="askModelerType"></span></c:if>
                               <span class="smart-widget-option" style="margin-top:-3px;">
                                    <a onclick="addAskingRrecord()"><button type="button" class="btn btn-info btn-sm">新增笔录</button></a>
                                    <a name="bllb"><button type="button" onclick="locationDiv()" class="btn btn-info btn-sm">查看列表</button></a>
                                    <c:if test="${askingRecord.id==null || askingRecord.id =='' }">
                                   	  <a >
                                   	  <!-- <button type="button"  class="btn btn-info btn-sm" onclick="sysModeler()">系统推荐模板</button> -->
                                   	  	<button type="button" class="btn btn-info no-shadow"
											tabindex="-1" data-toggle="modal" 
											data-remote="${webpath}/inquiryRecordTemplate/xwbl-sys-refineTemplate-model?onDblClickRow=3"
											data-target="#xttjmb">系统推荐模版</button>
                                   	  </a>
                                   	  <a  ><button type="button"  class="btn btn-info btn-sm"   data-remote="${webpath}/inquiryRecordTemplate/xwbl-custom-refineTemplate-model?modelType=2&status=1&onDblClickRow=3"  data-toggle="modal" data-target="#askCustomModel" >自定义模板</button></a>
                                   	  <a  ><button type="button"  class="btn btn-info btn-sm"  data-remote="${webpath}/askingManager/ask-exist-page?taskId=${lawObj.taskId}"  data-toggle="modal" data-target="#askExistModel" >历史记录模板</button></a>
                                    </c:if>
                                    <span class="refresh-icon-animated"><i class="fa fa-circle-o-notch fa-spin"></i></span>
								</span>
							</div>
						<div class="smart-widget-inner table-responsive">
						<!-- 该状态控制记录人model关闭 -->
						<input type="hidden" id="userRecordModelClose" value="0">
							<div class="smart-widget-body form-horizontal tab-bigfont">
								<form  id= "askingRecordForm">
								<input  name="token" value="${tokenReport }" type="hidden">
								<!-- 打印比例 -->
								<input  id ="multiple" name="multiple"  type="hidden" value="${askingRecord.multiple}">
								 <c:choose>
									<c:when test="${askingRecord.id!=null  &&  askingRecord.id !='' }">  
										<c:if test="${askingRecord.askType==0}">
											<div class="form-group">
											<label for="询问笔录类型" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span>制作方式</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
											<p>系统录入</p>
											</div>
											</div>
										</c:if>
										<c:if test="${askingRecord.askType==1}">
											<div class="form-group">
											<label for="询问笔录类型" class="col-lg-2 col-sm-2 col-xs-5 control-label">制作方式</label>
											<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
											<p>模板导入</p>
											</div>
											</div>
										</c:if>
									</c:when>
									<c:otherwise>
										<!-- 转台不对这里直接不给显示信息 -->
									</c:otherwise>
								</c:choose>
                                <input type="hidden" id="recordName" value="${askingRecord.recordName  }">
 								<div class="form-group"  style="display:none">
										<label for="询问笔录名称" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
										style="color: red;">*</span> 文书制作单位</label>
										<div class="col-lg-8 col-sm-8 col-xs-12">
											<input  readonly="readonly" class="form-control" id="makeUnitName"  name="makeUnitName" placeholder="文书制作单位" value="${askingRecord.makeUnitName }">
										</div>
								</div>
								<!-- 隐藏主要信息 -->
								<input type="hidden" id="askId"  name="id" value="${askingRecord.id}"> 
								<input type="hidden" id="" name="taskId" value="${lawObj.taskId}"></input>
							    <input type="hidden" id="contributionName" name="contributionName" value="${askingRecord.contributionName}"></input>
								
								<div class="form-group">
									<label for="时间" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span> 时间</label>
									<div class="col-lg-3 col-sm-3 col-xs-12"> 
										<input class="form-control" id="askingStartDate"  readonly="readonly" name="askingStartDate" placeholder="开始时间" value="<fmt:formatDate value='${askingRecord.askingStartDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
									</div>
									<label for="至" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>至</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input class="form-control" id="askingEndDate" readonly name="askingEndDate" placeholder="结束时间" value="<fmt:formatDate value='${askingRecord.askingEndDate }' pattern='yyyy-MM-dd HH:mm:ss'></fmt:formatDate>">
									</div>
								</div>
								<div class="form-group">
									<label for="地点" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>地点</label>
									<div class="col-lg-8 col-sm-8 col-xs-12">
										<input  class="form-control" id="place"  name="place" placeholder="地点" value="${askingRecord.place }">
									</div>
								</div>
								<div class="form-group">
									<label for="被调查询问人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span
										style="color: red;">*</span> 被调查询问人</label>
									<div class="col-lg-8 col-sm-8 col-xs-12">
										<input  class="form-control" id="askedUserName" name="askedUserName" placeholder="被调查询问人" value="${askingRecord.askedUserName }">
									</div>
								</div>
								<div class="form-group">
									<label for="性别" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>性别</label>
									<div id="sexDiv" class="col-lg-3 col-sm-3 col-xs-12">
										<select class="form-control" name="sex" id="sex_ask">
											<option value="" <c:if test="${askingRecord.sex=='' }">selected</c:if>  >——请选择——</option>
											<option value="0" <c:if test="${askingRecord.sex=='0' }">selected</c:if>  >女</option>
											<option value="1" <c:if test="${askingRecord.sex=='1' }">selected</c:if>  >男</option>
										</select>
									</div>
									<label for="年龄" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>年龄</label>
									<div id="ageDiv" class="col-lg-3 col-sm-3 col-xs-12">
										<input class="form-control" id="" name="age" placeholder="年龄" value="${askingRecord.age }" >
									</div>
								</div>
								<div class="form-group">
									<label for="身份证号码" class="col-lg-2 col-sm-2 col-xs-5 control-label">身份证号码</label>
									<div class="col-lg-8 col-sm-8 col-xs-12">
										<input class="form-control" id="" name="cardId" placeholder="身份证号码" value="${askingRecord.cardId }">
									</div>
								</div>
								<div class="form-group">
									<label for="工作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label">工作单位</label>
									<div class="col-lg-8 col-sm-8 col-xs-12">
										<input class="form-control" id=""  name="unitName" placeholder="工作单位" value="${askingRecord.unitName }">
									</div>
								</div>
								<div class="form-group">
									<label for="职务" class="col-lg-2 col-sm-2 col-xs-5 control-label">职务</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input class="form-control" id="" name="job"  placeholder="职务" value="${askingRecord.job }">
									</div>
									<label for="电话" class="col-lg-2 col-sm-2 col-xs-5 control-label">电话</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input class="form-control" id="" name="phone" placeholder="电话" value="${askingRecord.phone }">
									</div>
								</div>
								<div class="form-group">
									<label for="地址" class="col-lg-2 col-sm-2 col-xs-5 control-label">地址</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input  class="form-control" id="" name="address"  placeholder="地址" value="${askingRecord.address }">
									</div>
									<label for="邮编" class="col-lg-2 col-sm-2 col-xs-5 control-label">邮编</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input class="form-control" id="" name="postcode"  placeholder="邮编" value="${askingRecord.postcode }">
									</div>
								</div>
								<div class="form-group">
                                        <label for="与本案关系" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color: red;">*</span>与本案关系</label>
                                        <div class="col-lg-8 col-sm-8 col-xs-12">
                                        <select class="form-control" id="relationCaseCode" name="relationCaseCode">
                                            <option value=""  <c:if test="${askingRecord.relationCaseCode=='' }">selected</c:if> >——请选择——</option>
                                            <option value="0" <c:if test="${askingRecord.relationCaseCode=='0' }">selected</c:if> >违法嫌疑单位的工作人员</option>
                                            <option value="1" <c:if test="${askingRecord.relationCaseCode=='1' }">selected</c:if> >污染受害人</option>
                                            <option value="2" <c:if test="${askingRecord.relationCaseCode=='2' }">selected</c:if> >证人</option>
                                            <option value="3" <c:if test="${askingRecord.relationCaseCode=='3' }">selected</c:if> >违法嫌疑人</option>
                                        </select>
                                        <input type="hidden" id="relationCaseName" name="relationCaseName" value="${askingRecord.relationCaseName}"> 
                                        </div>
                                </div>
                               <div class="form-group">
                                        <label for="调查询问人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 调查询问人</label>
                                        <div class="col-lg-3 col-sm-3 col-xs-12">
                                            <div class="input-group">
                                                <input type="text"  id="checkUserNames_ask"  name="askingUserNames" readonly class="form-control" placeholder="调查询问人"  value="${askingRecord.askingUserNames}">
												<input type="hidden" id="checkUserIds_ask" name="askingUserIds" value="${askingRecord.askingUserIds }"> 
                                                <div class="input-group-btn">
                                                    <button type="button"  id="CheckUserChooseBtn_ask" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#jcr_addask">添加</button>
                                                </div>
                                            </div>
                                        </div>
                                        <label for="执法证号" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 执法证号</label>
                                        <div class="col-lg-3 col-sm-3 col-xs-12">
                                        	<input class="form-control" id="lawEnforcIds"  name="lawEnforcIds" readonly placeholder="执法证号"  value="${askingRecord.lawEnforcIds }">
                                        </div>
                                </div>
                                <div class="form-group">
                                        <label for="记录人" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 记录人</label>
                                        <div class="col-lg-8 col-sm-8 col-xs-12">
                                            <div class="input-group">
                                            	 <input class="form-control" id="recordUserName"  readonly="readonly"  name="recordUserName" value="${askingRecord.recordUserName }"  placeholder="记录人" ></input>
												<input type="hidden" id="recordUser" name="recordUser" value="${askingRecord.recordUser }"> 
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-info no-shadow" tabindex="-1" data-toggle="modal" data-target="#askRecordUser">记录人添加</button>
                                                </div>
                                            </div>
                                        </div>
                                 </div>
                                <div class="form-group">
									<label for="参与人员及其工作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label">参与人员及其工作单位</label>
									<div class="col-lg-8">
										<%-- <input class="form-control" id=""  name="participant" placeholder="参与人员及其工作单位" value="${askingRecord.participant }"> --%>
										<textarea rows="6" class="form-control" id="participant" name="participant" placeholder="参与人员及其工作单位" >${askingRecord.participant }</textarea>
									</div>
								</div>
                                <div class="form-group">
                                        <label for="工作单位" class="col-lg-2 col-sm-2 col-xs-5 control-label"><span style="color:red;">*</span> 工作单位</label>
                                        <div class="col-lg-8 col-sm-8 col-xs-12">
                                            <div class="input-group">
                                            	 <input class="form-control" id="askingUnitName" name="askingUnitName"   readonly="readonly"  placeholder="工作单位" value="${askingRecord.askingUnitName }">
                                            	<input type="hidden" id="askingUnitId" name="askingUnitId" value="${askingRecord.askingUnitId }">   
                                                <div class="input-group-btn">
                                                    <button type="button" class="btn btn-info no-shadow"
                                                        tabindex="-1" data-toggle="modal" data-target="#gzdw">工作单位</button>
                                                </div>
                                            </div>
                                        </div>
                                 </div>
								<div class="form-group">
                                        <label for="告知事项" class="col-lg-2 col-sm-2 col-xs-5 control-label">执法人员表明身份、出示证件及被调查询问人确认的记录</label>
                                        <div class="col-lg-8 col-sm-8 col-xs-12" style="line-height:2em;">
                                          		  我们是<input class="from_gaozhi" id=""  name= "askDepartmentName" value="${askingRecord.askDepartmentName }"  placeholder="默认登录用户所在环保局">的行政执法人员，这是我们的执法证件（
                                          		  执法证编号：<input class="from_gaozhi" style="width:300px;" id="lawEnforcNumber" name="lawEnforcNumber"  value="${askingRecord.lawEnforcNumber }"  placeholder="默认检查人执法证号">）。
                                          		  请过目确认：<input  readonly="readonly"  class="from_gaozhi" id="">
                                          		  今天我们依法进行检查并了解有关情况，你应当配合调查，如实提供材料，不得拒绝、阻碍、隐瞒或者提供虚假情况。如果你认为检查人与本案有利害关系，可能影响公正办案，可以申请回避，并说明理由。
                                          		  你有权对本次调查询问提出陈述、申辩。
                                          		  请确认： <input readonly  class="from_gaozhi">
                                        </div>
                                </div>
                                	<input type="hidden" id="jsonContent"  name="jsonContent" value="">
                                </form>
                                
                               <!-- 问答项  询问内容 信息-->
								<div class="container">
									<h3 class="title">
										<span>询问内容</span>
									</h3>
								</div>
								<div class="row">
									<div class="col-lg-1"></div>
									<div class="col-lg-10" style="word-break: break-all; work-wrap: break-word;">
									
									
									
		 					 	 		<!-- <table class="table table-striped no-margin" id="addTabContent">
											<tbody>
												<tr  v-for="(item, index) in items" :id ="'content'+index">
													
													<td class="text-left" style="background-color: #F7F7F7;" :id ="'contentTr'+index" >
															<span class="col-lg-12 padding-xs"><div class="wen">问</div><p :id ="'askingContent'+index" > {{ item.askingContent }} </p></span>
															<span class="col-lg-12 padding-xs"><div class="da">答</div><p :id ="'answerContent'+index" >{{item.answerContent }}</p></span>
													</td>
													<td class="text-center" style="width: 40px; background-color: #ddd;  padding:3% 0;">
															<i class="fa fa-times" title="删除" style="color: #F00; font-size: 20px; cursor:pointer;"  v-on:click="deleteContent(item.id,index)"></i>
													</td>
													<td class="text-center" style="width:40px; background-color:#23b7e5; padding:3% 0;">
																<i class="fa fa-ellipsis-h" title="编辑" style="color: #FFF; font-size: 20px; cursor:pointer;"  data-toggle="modal" data-target="#xzxwnr"
																 v-on:click="changeContent(item.id,index)"></i>
													</td>
													<td class="text-center" style="width:45px; background-color:#efefef;">
                                                        <div  v-if="index == 0 ">
                                                            <i v-if="(index+1) == items.length "></i>
                                                            <i v-else-if="(index+1) != items.length " title="下移" class="fa fa-caret-down" style="color: #bbb; font-size: 20px;  padding-top:25px;cursor:pointer;" v-on:click="chevronDown(item.id,items[index+1].id,index)" ></i>
                                                        </div>
                                                        <div v-else-if="(index+1) == items.length ">
                                                            <i class="fa fa-caret-up" title="上移" style="color: #bbb; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronUp(item.id,items[index-1].id,index)" ></i>
                                                        </div>
                                                        <div v-else>
                                                             <i class="fa fa-caret-up" title="上移" style="color: #bbb; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronUp(item.id,items[index-1].id,index)" ></i>
                                                             <i class="fa fa-caret-down" title="下移" style="color: #bbb; font-size: 20px; padding-top:25px;cursor:pointer;" v-on:click="chevronDown(item.id,items[index+1].id,index)" ></i>
                                                        </div>
																
													</td>
												</tr>
											</tbody>
										</table> -->   
										<form id="vueAskFormData">
	                                  		<ul id="addTabContent" class="Sortable_xwbl_list">
	                                           <li  v-for="(item, index) in items" :id ="'content'+index" style="position:relative;">
	                                                <div :id ="'content'+index" style="width:90%;">
	                                                		<input  name="id"  type="hidden" :value='item.id' > </input>
	                                                		<input  name="askingItemDatabaseId"  type="hidden" :value='item.askingItemDatabaseId' > </input>
	                                                		<input  name="askingContent"  :id ="'askingContentHidden'+index"  type="hidden" :value='item.askingContent' > </input>
	                                                		<input  name="answerContent"  :id ="'answerContentHidden'+index"  type="hidden" :value='item.answerContent' > </input>
	                                                        <div class="wen">问</div><p name="askingContent" :id ="'askingContent'+index" > {{ item.askingContent }} </p>
	                                                        <div class="da">答</div><p  name="answerContent" :id ="'answerContent'+index" >{{item.answerContent }}</p>
	                                                </div>
	                                                <span style="position:absolute;top:50%;right:10px; margin-top:-10px;">
	                                                    <span>
	                                                        <i class="fa fa-ellipsis-h" title="编辑" style="color:#23b7e5; font-size: 20px; cursor:pointer;"
	                                                              data-toggle="modal" data-target="#xzxwnr" v-on:click="changeContent(item.id,index)"></i>
	                                                    </span>
	                                                    <span style="padding:0 10px;">
	                                                        <i class="fa fa-times" title="删除" style="color: #F00; font-size: 20px; cursor:pointer;"  v-on:click="deleteContent(item.id,index)"></i>
	                                                    </span>
	                                                    <span>
	                                                        <i  title="拖动" class="fa fa-mouse-pointer" style="cursor:pointer;"></i>
	                                                    </span>
	                                                </span>
	                                            </li>  
	                                          </ul> 			
										</form>
										<div class="padding-sm">
											<button type="button" class="btn btn-info" data-toggle="modal" data-target="#xzxwnr" onclick="addAskingContent()">新增询问内容</button>
										</div>
									</div>
									<div class="col-lg-1"></div>
								</div>
						 
								
							</div>
							<div class="modal-footer">
                                <input type="hidden" name="taskId" id="mainTaskId" value="${lawObj.taskId}"/>
                                <input type="hidden" name="id" id="mainRecordId" value="${askingRecord.id}"/>
                                <input type="hidden" name="id" id="itemType" value="askingRecord"/>
							   	<button  id="askingRrecordSub"   type="button" class="btn btn-info" onclick="saveAskingRrecord()">保存</button>
							   	<c:if test="${askingRecord.id==null  or askingRecord.id =='' }">
							   		  <a href="${webpath }/askingManager/exportRecord"><button type="button" class="btn btn-info">导出离线模板</button></a>
							  		  <button type="button" class="btn btn-info" data-toggle="modal" data-target="#UploadAskXls"  >导入离线数据</button>
							   	</c:if>
					   			<c:if test="${askingRecord.id!=null  and askingRecord.id !='' }">
					   			 	  <button type="button" class="btn btn-info" id="printBtn1"  onclick="printBtn()"  title="手机端不可用">打印</button>
                                      <a href="${webpath }/askingManager/download-ask-doc?askId=${askingRecord.id}"><button type="button" class="btn btn-info" >下载文书</button></a>
                                      <button type="button" onclick="shareBooksBtn('${askingRecord.id}')" class="btn btn-info">分享文书</button>
                                      <a href="${webpath }/askingManager/exportRecord"><button type="button" class="btn btn-info">导出离线模板</button></a>
                                      <button type="button" class="btn btn-info" data-toggle="modal" data-target="#cksmj"  data-remote="${webpath}/askingManager/cksmj?askingId=${askingRecord.id}&lsFlag=1">查看扫描件</button>
                                      <button type="button" class="btn btn-info" data-toggle="modal" data-target="#scsmj" data-remote="${webpath}/askingManager/scsmj">上传扫描件</button>
                                      <button type="button" class="btn btn-info" data-toggle="modal" data-target="#cwzdymb" onclick="selectLawObjType('${lawObj.taskId}')" >存为自定义模板</button>
					   			</c:if>
                                       
							</div>
						</div>
					</div>
				</div>
				<!--./任务办理表单-->
			</div>
			<!--./第二层询问笔录row-->

		</div>
	</div>
	
	<!-- 查看扫描件（Modal） -->
	<div class="modal fade" id="cksmj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
	<!-- ./查看扫描件（Modal） -->
	
	<!-- 打印（Modal） -->
	<div class="modal fade" id="fileShowModel" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
	<!-- ./打印（Modal） -->
	
	<!-- 二级预览模态框（Modal） -->
		<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog" 
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		        <!-- /.modal-content -->
		      </div>
		     <!-- /.modal-dialog -->
		</div>
	<!-- ./二级预览模态框（Modal） -->
	
	<!-- 上传扫描件（modal） -->
	<div class="modal fade" id="scsmj" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			
			</div>
		</div>
	</div>
	<!-- /上传扫描件（modal） -->
	
	<!-- 新增询问内容（Modal） -->
    <div class="modal fade" id="xzxwnr" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <div style="float:right; margin-top:-5px;">
                        <button type="button"  onClick="addClickItem()" class="btn btn-info">确定</button>
                        <button type="button" class="btn btn-default"  onclick="clearModeler()" data-dismiss="modal">关闭</button>
                        </div>
                        <h4 class="modal-title" id="myModalLabel">新增询问内容</h4>
                    </div>
                    <div class="modal-body">
                        <div class="smart-widget-body">
                            <form class="form-horizontal"  id="AskingContentForm" >
                            	<input type="hidden" id="modelerState"   name="" value="0"> 
                            	<input type="hidden" id="itemIndexContent"  name="" value=""> 
                            	
                            	<input type="hidden" id="contentId"   name="id" value=""> 
                            	<input type="hidden" id="taskIdContent"   name="taskId" value="${lawObj.taskId}"> 
                            	<input type="hidden" id="askingIdContent"  name="askingId" value="${askingRecord.id}"> 
                            	<input type="hidden" id="askingItemDatabaseId"  name="askingItemDatabaseId" value="">  
                                <div class="form-group">
                                	<span class="col-lg-12 padding-xs"> 
                                		<div class="col-md-1"><div class="wen" style="margin-left:10px;">问</div></div>
                                	 <div class="col-lg-11 col-sm-11 col-xs-12">
                                     <textarea id="askingContent" maxlength="1000"  name ="askingContent" onkeydown="checknum()" onkeyup="checknum()" class="form-control" rows="6" placeholder="询问问题"></textarea>
                                     <input id="in" type="text" disabled="disabled" style="border:0;" />               
                                    </div>
                                	</span>      
                                </div>
                                 <div class="form-group">
                                   	<span class="col-lg-12 padding-xs">
                                   	<div class="col-md-1"> <div class="da" style="margin-left:10px;">答</div></div>
                                	 <div class="col-lg-11 col-sm-11 col-xs-12">
                                	 <textarea id="answerContent" maxlength="1000" name ="answerContent" onkeydown="checknum2()" onkeyup="checknum2()"  class="form-control" rows="6" placeholder="回答问题"></textarea>
                                	 <input id="in2" type="text" disabled="disabled" style="border:0;" />                            
                                    </div>
                                	</span>	
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <!--<button type="button"  onClick="addClickItem()" class="btn btn-info">确定</button>
                        <button type="button" class="btn btn-default"  onclick="clearModeler()" data-dismiss="modal">关闭</button>-->
                    </div>
                    
                </div>
            </div>
        </div>
        <!-- ./新增询问内容（Modal） -->
        <!-- 选择检察人 -->
			<div class="modal fade" id="jcr_addask" tabindex="-1" role="dialog"
							aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">&times;</button>
						<h4 class="modal-title" id="myModalLabel">调查询问人</h4>
					</div>
					<div class="modal-body">
						<div class="smart-widget-body form-horizontal">
								<div class="form-group">
									<label for="姓名" class="col-lg-2 col-sm-2 col-xs-5 control-label">姓名</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input type="text" name="userName" id="userName_addask" class="form-control"  placeholder="姓名">
									</div>
								</div>
								<div class="form-group">
									<label for="机构" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政区</label>
									<div class="col-lg-2 col-sm-2 col-xs-5">
										<select class="form-control" id ="user_power_province_addask">
											<option value="35000000">福建省</option>
										</select>
									</div>
								
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<select class="form-control" id="user_power_city_addask">
											<option value="">——市级——</option> 
										</select>
									</div>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<select class="form-control" id="user_power_county_addask">
											<option value="">——县级——</option> 
										</select>
									</div>
								</div>
							<div class="form-group">
								<div class="col-lg-offset-2 col-lg-10">
								<button id="checkPersonBtn_addask" class="btn btn-info"
										style="width: 120px;">查 询</button>
 								</div>
							</div>
								<!-- checkPersonStatus -->
								<div class="form-group">
									<label for="已选择"  class="col-lg-2 col-sm-2 col-xs-5 control-label">已选择</label>
									 <div><input type="hidden" id ="checkPersonStatus_addask" name ="checkPersonStatus" value ="0"></div>
								 	<div class="col-lg-8 col-sm-8 col-xs-12" id ="chickUserAction_addask"></div>
								</div>
						<table id="checkUserChooseTable_addask" class ="table table-no-bordered">
                        <div style="position:absolute; right:60px; margin-top:11px;">
                        <button type="button" id ="chickUserBtn_addask"  data-dismiss="modal" class="btn btn-info btn-sm" style="width:80px; height:32px;">确定</button>
                     	<button style="margin:0 6px 0 8px;" type="button" id="cleanChickuserBtn_addask" class="btn btn-default" >清空</button>
                        </div>
                        </table>
						</div>
					</div>
					<!-- <div class="modal-footer">
						<button type="button" class="btn btn-info" id ="chickUserBtn_addask" data-dismiss="modal">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
						<button type="button" id="cleanChickuserBtn_addask" class="btn btn-default" >清空</button>
					</div> -->
				</div>
			</div>
		</div>
		<!-- 选择检察人 -->
        <!-- 记录人 modeler -->
        	<div class="modal fade" id="askRecordUser" tabindex="-1" role="dialog"
							aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">&times;</button>
						<h4 class="modal-title" id="myModalLabel">记录人</h4>
						<div style="color:red; float:left; margin:-20px 0 0 80px;">模板导入方式，则无法关联记录人</div> 
					</div>
					<div class="modal-body">
						<div class="smart-widget-body form-horizontal">
								<div class="form-group">
									<label for="姓名" class="col-lg-2 col-sm-2 col-xs-5 control-label">姓名</label>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<input type="text" name="userName" id="userName_ask"
											class="form-control" id="xm" placeholder="姓名">
									</div>
								</div>
								<div class="form-group">
									<label for="机构" class="col-lg-2 col-sm-2 col-xs-5 control-label">行政区</label>
									<div class="col-lg-2 col-sm-2 col-xs-5">
										<select class="form-control" id ="user_power_province">
											<option value="35000000">福建省</option>
										</select>
									</div>
								
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<select class="form-control" id="user_power_city_ask">
											<option value="">——市级——</option> 
										</select>
									</div>
									<div class="col-lg-3 col-sm-3 col-xs-12">
										<select class="form-control" id="user_power_county_ask">
											<option value="">——县级——</option> 
										</select>
									</div>
								</div>
							<div class="form-group">
								<div class="col-lg-offset-2 col-lg-10">
								<button id="checkPersonBtn_ask" class="btn btn-info" style="width: 120px;">查 询</button>
								</div>
							</div>
								<!-- checkPersonStatus -->
								<div class="form-group">
									<!-- <label for="已选择"  class="col-lg-2 col-sm-2 col-xs-5 control-label">已选择</label> -->
									 <div><input type="hidden" id ="checkPersonStatus_ask" name ="checkPersonStatus" value ="0"></div>
								 	<div class="col-lg-8 col-sm-8 col-xs-12" id ="chickUserAction_ask"></div>
								</div>
						</div>
						<hr>
						<table id="askRecordUserTables" class ="table-no-bordered">
							
						</table>
					</div>
					<div class="modal-footer">
						<!-- <button type="button" class="btn btn-info" data-dismiss="modal">确定</button> -->
						<!--<button type="button" onclick="chickUserCleanBtn()" class="btn btn-default" data-dismiss="modal">关闭</button>-->
					</div>
				</div>
			</div>
		</div>
        <!-- 记录人 结束 -->
        <!-- 工作单位设置（Modal） -->
		<div class="modal fade" id="gzdw" tabindex="-1" role="dialog"
			aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog">
				<div class="modal-content">
					<div class="modal-header">
						<div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info" data-dismiss="modal">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                        </div>
						<h4 class="modal-title" id="myModalLabel">部门列表</h4>
						<div style="color:red; float:left; margin:-20px 0 0 80px;">模板导入方式，则无法关联部门列表</div> 
					</div>
					<div class="modal-body">
						<div class="smart-widget-body form-horizontal">
								<div class="form-group">
										<div class="col-lg-6">
											<div >
		                                           <ul id="treeDept" class="ztree"></ul>
											</div>
										</div>
								</div>
						</div>
					</div>
					<div class="modal-footer">
						<!--<button type="button" class="btn btn-info" data-dismiss="modal">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
					</div>
		
				</div>
			</div>
		</div>
        
        <!-- 导入离线模板 开始-->
        <div class="modal fade" id="UploadAskXls" tabindex="-1"
			role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">                                                                                            
				<div class="modal-content">
					<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal"
							aria-hidden="true">&times;</button>
						<h4 class="modal-title" >导入离线模板</h4>
					</div>
					<div class="modal-body">
						<div class="smart-widget-body form-horizontal">
							<div class="form-group">
								<label for="任务要求附件" class="col-lg-2 col-sm-2 col-xs-5 control-label">模板</label>
								<div class="col-lg-9">
									<input id="file-modeler-xls" type="file" multiple>
									<p>附件格式支持xls，每个文件不大于1M，数量一个</p>
								</div>
							</div>
	
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-default"   data-dismiss="modal">关闭</button>
					</div>
	
				</div>
			</div>
		</div>
        <!-- 导入离线模板 结束 -->
        
       <!-- 分享文书模态框start -->
		 <div class="modal fade" id="shareBookModel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
		  <div class="modal-dialog" role="document">
		    <div class="modal-content">
		    	<div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal"
                        aria-hidden="true">&times;</button>
                    <h4 class="modal-title" >分享文书</h4>
                </div>
		       <div class="modal-body text-center" >
               	<div class="pricing-value value" data-toggle="modal" data-target="#inputImgModeler" style="background-image:url(${webpath }/static/img/QRcode_backgroup.png); width:300px; height:345px; padding:0 auto; margin:0 auto;" align="center">
		    	 <div  align="center" id="qrcode" ></div>
      			 <!-- <img id="getval"/>  --> 
      			 <div id="getval" align="center" style=" margin:0; padding:110px 0 0 30px;"></div>
      			 <div style="width: 80px; height: 80px; position:absolute; margin:-120px 0 0 125px;"><img id="qrCodeIco" src="${webpath }/static/img/qrLogo.png" /></div>
                 </div>
                 <h4>请微信扫码分享</h4>
		       </div>
		      <div class="modal-footer">
		        <!--<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>-->
		      </div>
		    </div>
		  </div>
		</div>  
        <!-- 系定义模板，模态框（Modal） ASKING_CUSTOM_MODEL -->
		<div class="modal fade" id="askCustomModel" tabindex="-1" role="dialog" 
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
		<!-- 现有模板 -->
		<div class="modal fade" id="askExistModel" tabindex="-1" role="dialog" 
		   aria-labelledby="fileModalLabel" aria-hidden="true">
			   <div class="modal-dialog  modal-lg">
		        <div class="modal-content">
		        </div>
		      </div>
		</div>
		
	<!-- 系统推荐模版（Modal） -->
		<div class="modal fade" id="xttjmb" tabindex="-1" role="dialog"
			aria-labelledby="myModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
				 </div>
			</div> 
		</div>
	<!-- 系统推荐模版（Modal） -->
	
		 <!-- 行业类型选择（Modal） 模态窗中的模态窗-->
			<div class="modal fade" id="Industrytype" tabindex="-1" role="dialog"
				aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
					</div>
				</div>
			</div>
			<div class="modal fade" id="IndustrytypeModel" tabindex="-1" role="dialog"
				aria-labelledby="myModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
					</div>
				</div>
			</div>
	 <!-- 行业类型选择（Modal） 模态窗中的模态窗-->
		
		<!-- 存为自定义模板（Modal） -->
    <div class="modal fade" id="cwzdymb" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <div style="float:right; margin-top:-5px;">
                    <button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    </div>
                    <h4 class="modal-title" id="myModalLabel">存为自定义模板</h4>
                </div>
                <form action="#" method="post" id= "templeteForm">
		                <input id="templateAreaname" name="templateAreaname" type="hidden" ></input> 
						<input id="templateArea" name="templateArea" type="hidden" ></input>
						<input type ="hidden" id="vueJson" name ="vueJson" value =""> 
						<input id="templateObjectType" type="hidden" name ="templateObjectType" value ="${lawObj.lawObjectType}">
					    <input type ="hidden" id="templateContributionName" name ="contributionName" value ="${askingRecord.contributionName}">
		                <div class="modal-body">
		                    <div class="smart-widget-body form-horizontal">
		                        <div class="form-group">
		                            <label class="col-lg-3 control-label"><span
										style="color: red;">*</span>名称</label>
		                            <div class="col-lg-8">
		                            <input type="text"  class="form-control" id="templateName" onkeydown="if(event.keyCode==13){return false;}" name ="templateName" placeholder="名称">
		                            </div>
		                        </div>
		                        <div class="form-group">
		                            <label class="col-lg-3 control-label">适用地区</label>
		                            <div class="col-lg-2">
		                                <select class="form-control" id ='belongProvince' name ="belongProvince" >
		                                    <option value="35000000" >福建省</option>
		                                </select>
		                            </div>
		                            <div class="col-lg-3">
		                                <select class="form-control" id ="belongCity" name="belongCity">
		                                </select>
		                             </div>
		                              <div class="col-lg-3">
		                                <select class="form-control" id ="belongCountry" name ="belongCountry">
		                                </select>
		                            </div>
		                         </div>
		                         <div class="form-group">
		                            <label class="col-lg-3 control-label">适用对象类型</label>
		                            <div class="col-lg-8" style="margin-top:7px;">
		                           		<c:choose>
													<c:when test="${lawObj.lawObjectType=='1'}"> 
													   		企事业单位
														   </c:when>
													<c:when test="${lawObj.lawObjectType=='2'}">   
													       	个人
													</c:when>
													<c:when test="${lawObj.lawObjectType=='3'}">   
													       个体、三无、小三产
													</c:when>
													<c:when test="${lawObj.lawObjectType=='4'}">   
													       	自然保护区
													</c:when>
													<c:when test="${lawObj.lawObjectType=='6'}">
													       	水源地
													</c:when>
													<c:otherwise>
													      	 无主
												 </c:otherwise>
										</c:choose>
		                            </div>
		                        </div>
		                         <c:if test = "${lawObj.lawObjectType=='1'}">
			                         <div class="form-group">
			                            <label for="行业类型" class="col-lg-3 col-md-3 control-label">适用行业</label>
			                            <div class="col-lg-8" style="margin-top:7px;" >
			                                <input type="hidden" name ="templateIndustry" id ="templateIndustry">
			                            	<input type="hidden" name ="templateIndustryName" id ="templateIndustryName">
			                            	
			                         		<span id ="templateIndustryNameTemp"></span>
			                                <div class="checkbox inline-block" style="padding-right:10px; float:right;">
			                                    <div class="custom-checkbox">
			                                        <input type="checkbox" id="templateIndustryStatus" name ="templateIndustryStatus">
			                                        <label for="templateIndustryStatus" class="checkbox-blue" ></label>
			                                    </div>
			                                    <div class="inline-block vertical-top">
			                                        	所有行业
			                                    </div>
			                                </div>
			                            </div>
			                        </div>
		                        </c:if>
		                        <input type="hidden" id ="chickItemListTemp" name ="chickItemListTemp" >
		                         <div class="form-group">
		                            <label class="col-lg-3 control-label">是否设为常用</label>
		                            <div class="col-lg-8">
		                                <select class="form-control" id ="usuallyStatus" name ="usuallyStatus">
		                                    <option value="">请选择</option>
		                                    <option value="1">是</option>
		                                    <option value="0">否</option>
		                                </select>
		                            </div>
		                         </div>
		                         <div class="form-group">
		                            <label class="col-lg-3 control-label">是否设为默认加载模板</label>
		                            <div class="col-lg-8">
		                                <select class="form-control" name ="defaultStatus">
		                                    <option value="">请选择</option>
		                                    <option value="1">是</option>
		                                    <option value="0">否</option>
		                                </select>
		                            </div>
		                         </div>
		                    </div>
		                </div>
                </form>
                <div class="modal-footer">
                    <!--<button type="button" class="btn btn-info" id ="saveSceneBtn"  data-dismiss="modal">保存</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>-->
                </div>
                
            </div>
        </div>
    </div>
    <!-- ./存为自定义模板（Modal） -->
    	<!-- 模板预览（Modal） -->
        <div class="modal fade" id="mbyl" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                </div>
            </div>
        </div>
		<!-- 模板预览（Modal） -->
		
		
	<!-- 主干信息逻辑操作 -->	
	<script type="text/javascript">
	 	$(function (){
	 		$("[name='askingContent']").focus(function(){
				business.listenTextAreaComeEnter();
			})
			$("[name='askingContent']").blur(function(){
				business.listenTextAreaGoEnter();
			})
	 		$("[name='answerContent']").focus(function(){
				business.listenTextAreaComeEnter();
			})
			$("[name='answerContent']").blur(function(){
				business.listenTextAreaGoEnter();
			})
	 		// 日期控件控制
	 		$("#askingStartDate").datetimepicker({
	 		     format:'yyyy-mm-dd hh:ii:ss',
	 		   	 language: 'cn',
	 		     endDate:$("#askingEndDate").val(),
	 		     autoclose: true
	 		}).on('changeDate',function(ev){
	 		    $('#askingRecordForm').formValidation('revalidateField', 'askingStartDate');
	 			$("#askingEndDate").datetimepicker('setStartDate',new Date(ev.date.valueOf()));
	 		});
	 		$("#askingEndDate").datetimepicker({
	 		     format:'yyyy-mm-dd hh:ii:ss',
	 		     language: 'cn',
	 		   	 startDate:$("#askingStartDate").val(), 
	 		     autoclose: true
	 		}).on('changeDate',function(ev){
	 		   $('#askingRecordForm').formValidation('revalidateField', 'askingEndDate');
	 			$("#askingStartDate").datetimepicker('setEndDate',new Date(ev.date.valueOf()));
	 		});
	 		
	 	   $("#relationCaseCode").change(function(){
		    	$("#relationCaseName").val($("#relationCaseCode option:selected").text()); 
		   });
	 	   var taskid='${lawObj.taskId}';
	      //初始化fileinput控件（第一次初始化） 附件限制
          $('#file-modeler-xls').fileinput({
        	  language : 'zh', //设置语言
              uploadUrl: WEBPATH+'/askingManager/up-modeler-xls?taskId='+taskid, //上传的地址
              allowedFileExtensions : ['xls'],//接收的文件后缀,
              maxFileCount: 1,
              enctype: 'multipart/form-data',
              showUpload: true, //是否显示上传按钮
              showPreview:false,
              previewFileIcon: "<i class='glyphicon glyphicon-king'></i>", 
              msgFilesTooMany: "选择上传的文件数量({n}) 超过允许的最大数值{m}！",
          }).on("fileuploaded", function(event, data) {
        	  if(data.response.result == 'success'){
        		  $('#UploadAskXls').modal('hide');
            		swal({ 
        				title : "保存成功",
        				text : "离线数据导入成功",
        				type : "success",
        				closeOnConfirm: true,
       			        confirmButtonText: "确定",
       			        confirmButtonColor: "#A7D5EA"
        			}, function() {
        				business.addMainContentParserHtml(WEBPATH + '/askingManager/xwbl',
            					$("#taskObjectForm").serialize() + "&selectType=" + 3);
        			})
        	  }else if(data.response.result == 'error'){
        		  swal({title:data.response.message ,text: "",type:"error",allowOutsideClick :true});
        	  }else{
        		  swal({title:"返回状态错误" ,text: "",type:"error",allowOutsideClick :true});
        	  }
    	  });
	 	  // form 表单验证  
          $("#askingRecordForm").formValidation({
	  	        framework: 'bootstrap',
	  	        message: 'This value is not valid',
	  	        icon:{
	  		            valid: 'glyphicon glyphicon-ok',
	  		            invalid: 'glyphicon glyphicon-remove',
	  		            validating: 'glyphicon glyphicon-refresh'
	                     },
	  	        fields: {
	  	        makeUnitName: {
	  	                validators: {
	  	                    notEmpty: {
	  	                        message: '制作单位不能为空'
	  	                    },
	  	      				stringLength: {
	  	                        max: 100,
	  	                        message: '制作单位最大100个字符'
  	                   	    }
	  	            	}
	  	            
	  	        },
	  	      participant : {
					validators : {
						stringLength : {
							max : 500,
							message : '参与人员及其工作单位最大500个字符'
						}
					}
				},
 	  	       place: {
	  	                validators: {
	  	                  notEmpty: {
	  	                        message: '地点不能为空'
	  	                    },
	  	          			stringLength: {
	  	                        max: 100,
	  	                        message: '地点最大100个字符'
	  	                    }
	  	                }
	  	        }, 
	  	        askedUserName: {
	  	                validators: {
	  	                    notEmpty: {
	  	                        message: '被调查询问人不能为空'
	  	                    },
	  	      				stringLength: {
	  	                        max: 50,
	  	                        message: '被调查询问人最大50个字符'
		                   	}
	  	            	}
	  	          },
	  	        sex: {
  	                row:'#sexDiv',
	  	        	validators: {
  	                    notEmpty: {
  	                        message: '性别不能为空'
  	                    }
  	            	}
  	          	},
  	          askingStartDate: {
	                validators: {
	                    notEmpty: {
	                        message: '开始时间不能为空'
	                    }
	            	}
	          	},
	          	askingEndDate: {
	  	                validators: {
	  	                    notEmpty: {
	  	                        message: '结束时间不能为空'
	  	                    }
	  	            	}
	  	          	},
  	          relationCaseCode: {
	    	            validators: {
	                    notEmpty: {
	                        message: '与本案关系不能为空'
	                    }
	            	}
	        	  },
  	  	         age: {
  	  	         	row:'#ageDiv',
  	  	        	 validators: {  
  	                	 notEmpty: {
	  	                        message: '年龄不能为空'
	  	                    },
  	                 	 regexp: {
	                        regexp: /^[0-9]\d?$|^1[0-4]\d$|^0$|^150$|^0[0-9]\d$/,
	                        message: '年龄0-150岁'
	                    }
  	            	}
  	              },  
	  	         cardId: {
	  	                validators: {
	  	          			stringLength: {
	  	                        min: 8,
	  	                        max: 18,
	  	                        message: '身份证号位8-18个字符'
	  	                    } 
	  	                }
	  	           },
	  	         unitName: {
	  	                validators: {
	  	          			stringLength: {
	  	                        max: 100,
	  	                        message: '工作单位最大100个字符'
	  	                    } 
	  	                }
	  	           },
		  	   job: {
  	                validators: {
  	          			stringLength: {
  	                        max: 50,
  	                        message: '职务最大50个字符'
  	                    } 
  	            	}
  	            },
  	          phone: {
 	                validators: {
 	                	// ([0-9]{3,4}-)?[0-9]{7,8} /^0?1[3|4|5|8][0-9]\d{8}$/
 	                	regexp: {
	                        regexp: /^(((\d{3,4}-){0,1}\d{7,8})|(1\d{10}))$/,
	                        message:'请输入正确的联系号码',
	                    },
 	    				stringLength: {
	                        max: 13,
	                        message: '联系号码最大13个字符'
	                    } 
 	            	}
 	            },
 	           address: {
 	                validators: {
 	          			stringLength: {
 	                        max: 500,
 	                        message: '地址最大500个字符'
 	                    } 
 	            	}
 	            },
 	           postcode: {
	                validators: {
 	                 	 regexp: {
 	                        regexp: /^[0-9][0-9]{5}$/,
 	                        message: '请输入正确邮编'
 	                    }
	            	}
	            },
	  	        askingUserNames: {
	  	                validators: {
	  	                	 notEmpty: {
	  		                        message: '调查询问人不能为空'
	  		                 },
	  	                    callback: {
	  	                      message: "检查人至少选择在2个，最多选择20个！",
	  	                      callback: function(value, validator, $field) {
		  	                      if(value!=""){
			  	                        var arr = value.split(",");
			  	                        if(arr.length>=2 && arr.length<=20){
			  	                            	return true;
			  	                          }else{
			  	                            	return false;
			  	                          }
		  	                        } else{
		  	                 			 return false;
		  	                        }
		  	                      }
	  	                 	},
				            stringLength: {
			                     max: 2000,
			                     message: '调查询问人过多'
			                 } 
	  	                } 
	  	           },
	  	         lawEnforcIds : {
						validators : {
							notEmpty : {
								message : '执法证号不能为空'
							}
						} 
					},
				 recordUserName : {
						validators : {
							notEmpty : {
								message : '记录人人不能为空'
							}
						} 
					},
				askingUnitName : {
							validators : {
								notEmpty : {
									message : '工作单位不能为空'
								}
							} 
				},
				askDepartmentName : {
					validators : {
						stringLength: {
 	                        max: 500,
 	                        message: '表明身份最大100个字符'
 	                    } 
					} 
				},
				lawEnforcNumber : {
					validators : {
						stringLength: {
 	                        max: 2000,
 	                        message: '执法证编号最大2000个字符'
 	                    } 
					} 
				}
					 
				}
			});
		});
	 	
	 	
	 	
		// 新增 询问笔录信息 
		function addAskingRrecord() {
			business.addMainContentParserHtml(WEBPATH + '/askingManager/xwbl',$("#taskObjectForm").serialize()+ "&askingRecordId=add&selectType=3");
		}
		// 保存 询问笔录主干信息新增 
		function saveAskingRrecord() {
			var askingRecordId = '${askingRecord.id}';
			var validate = false;
			$("#askingRecordForm").data('formValidation').validate();
			validate = $("#askingRecordForm").data('formValidation').isValid();
			if (validate) {
				
				
				// 加载等待3秒
		/* 		if(askingRecordId==null || askingRecordId==""){ // 若主干信息为空，则说明是第一次保存，则入库VUE 
					var  jsonContentList= new Array();
					for(var i=0;i < vueContentTables.items.length;i= i+4){
						var obj ={askingItemDatabaseId:vueContentTables.items[i].askingItemDatabaseId,askingContent:vueContentTables.items[i].askingContent, answerContent:vueContentTables.items[i].answerContent};
						jsonContentList.push(obj);
					}
					$("#jsonContent").val(JSON.stringify(jsonContentList)); 
				}
				 */
		 
				var formData=$("#vueAskFormData").serializeArray();//[Object, Object, Object] 
				var  jsonContentList= new Array();
		 		for(var i=0;i < formData.length;i=i+4){
					var obj ={id:formData[i].value,name:formData[i+1].value};
					
					var obj ={id:formData[i].value,askingItemDatabaseId:formData[i+1].value,askingContent:formData[i+2].value, answerContent:formData[i+3].value};
					// JSON.stringify(jsonContentList)
					jsonContentList.push(obj);
					$("#jsonContent").val(JSON.stringify(jsonContentList)); 
				}  
				
				
				
				
				loding('askingRrecordSub', '保存');
				business.openwait();
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/askingManager/save-askrecord',
					data : $('#askingRecordForm').serialize(),//  form law object
					async: false,
					error : function(request) {
						swal("错误!","系统错误", "error");
						business.closewait();
					},
					success : function(data) {
						business.closewait();
						if (data.result == 'success') {// 成功
							swal({ title : "保存成功", text : "", type : "success" });
							business.addMainContentParserHtml(WEBPATH + '/askingManager/xwbl', $("#taskObjectForm").serialize() + "&selectType="+ 3+ "&askingRecordId=" + data.code);
						} else if (data.result == 'handle') { // 失败  
							swal({ title : data.message, text : "", type : "error",allowOutsideClick :true });
						} else if (data.result == 'error') { // 失败  
							swal({ title : data.message, text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
					}
				});
			 } else if (validate == null) {
				business.closewait();//关闭遮罩层
				//表单未填写
				$("#askingRecordForm").data('formValidation').validate();
			}else if(validate == false){
				swal({ title : "有必填项未填，请检查", text : "", type : "info",allowOutsideClick :true });
			}
		}
		// 定位div 
		function locationDiv() {
			business.addMainContentParserHtml(WEBPATH + '/taskManager/xczf', $("#taskObjectForm").serialize()+ "&selectType=0&locationName=lockingDiv");
		}
	</script>
	
	<!-- 询问笔录动态项，加 vue 数据绑定 -->
	<script type="text/javascript">
		// 初始化vue信息，绑定动态项 
		$(document).ready(function() {
			// 后端请求过来询问项（json）绑定 VUE
			var lawObjectType  = '${lawObj.lawObjectType}';
			var contentId = '${askingRecord.id}';
			var code = 0; // 新增 
			if(contentId !=null && contentId !=''){
				 code = 2; // 历史/ 或者当前id
			}
			var obj={lawObjectType:lawObjectType,code:code,contentId:contentId};
			// 请求数据 
			$.ajax({
                cache: true,
                type: "POST",
                url: WEBPATH+'/askingManager/ask-content-vue',
                data:obj, 
                async: false,
                error: function(request) {
                	swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
					business.closewait();
                },
                success: function(data) {
                	dataArr = data.contentList; // 绑定 VUE （json）
                }
            });
		     vueContentTables = new Vue({ // 不要用var 声明，定义为全局 
				  el: '#addTabContent',
				  data: {
				    items:dataArr,
				  },
				  methods: {
					  changeContent:  function (contentId,index) { // 编辑
						  changeContent(contentId,index);
					  }, 
					  deleteContent: function (contentId,index){ //删除 
						  deleteContent(contentId, index);
					  },
					  chevronDown:function (contentIdA,contentIdB,index){ // 下移
						  chevronDown(contentIdA,contentIdB,index);
					  
					  },
					  chevronUp:function (contentIdA,contentIdB,index){ // 上移 
						  chevronUp(contentIdA,contentIdB,index);
					  }
				  }
			});
		});
		// 设置系统模板
		function sysModeler(){
	    	 var obj={code:3};
	    	 $.ajax({
				cache : true,
				type : "POST",
				url : WEBPATH + '/askingManager/ask-content-vue',
				data:obj, 
				async : false,
				error : function(request) {
					swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
				},
				success : function(data) {
					dataArr.splice(0,dataArr.length);
					for(var i =0;i<data.contentList.length;i++){
						 dataArr.push(data.contentList[i]);
					}
					$("#contributionName").val(data.recordName);  
					$("#templateContributionName").val(data.recordName);  
				}
			 });
		}
		// 编辑
		function changeContent(contentId,index) {
			// 先清空信息 
			clearModeler();
			// 设置是编辑状态标识
			$("#modelerState").val(0);
			// from 表单内容id值，用于后端判断是新增还是编辑
			$("#itemIndexContent").val(index); 
			$("#contentId").val(contentId);
			$("#askingContent").val($("#askingContent" + index).html());
			$("#answerContent").val($("#answerContent" + index).html());
		}
		// 删除
		function deleteContent(contentId, index) {
			swal({
				title : "您确定要删除这条信息吗",
				text : "删除后将无法恢复，请谨慎操作！",
				type : "warning",
				showCancelButton : true,
				confirmButtonColor : "#DD6B55",
				confirmButtonText : "是的，我要删除！",
				cancelButtonText : "让我再考虑一下",
				closeOnConfirm : false,
				closeOnCancel : false
			}, function(isConfirm) {
				if (isConfirm) {
					business.openwait();
					if(contentId !=null && contentId !=''){
						$.ajax({
							cache : true,
							type : "POST",
							url : WEBPATH + '/askingManager/del-askcontent',
							data : {
								contentId : contentId
							}, 
							async : false,
							error : function(request) {
								swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
								business.closewait();
							},
							success : function(data) {
								business.closewait();
								if (data.result == 'success') {// 成功
									swal({ title : "删除成功！", text : "您已经永久删除了这条信息。", type : "success" }, function() {
										 dataArr.splice(index,1);
									 })
								} else if (data.result == 'error') { // 失败  
									swal({ title : "删除失败", text : "", type : "error",allowOutsideClick :true });
								} else {
									swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
								}
							}
						});
						business.closewait();
					}else{
						swal({ title : "删除成功！", text : "", type : "success",allowOutsideClick :true });
						dataArr.splice(index,1); // 下标开始 往后删除几位
						business.closewait();
					}
				} else {
					swal({
						title : "已取消",
						text : "您取消了删除操作！",
						type : "error",
						allowOutsideClick :true
					})
				}
			})
		}
		// 新增
		function addAskingContent() {
			// 强制清空模态框内容信息 
			clearModeler();
			// 新增
			$("#modelerState").val(1);
		}
		// 确定       新增 / 修改 
		function addClickItem() {
			// modelerState :0 标示新增状态  其他 表示编辑状态   
			var itemIndex =  $("#itemIndexContent").val(); 
			var modelerState = $("#modelerState").val();
			var askingHtml = $("#askingContent").val();
			var answerHtml = $("#answerContent").val();
			if (askingHtml == null || askingHtml == '') {
				swal({title:"错误!", text:"询问问题内容不能为空!", type:"error",allowOutsideClick :true});
				return;
			}
			if (answerHtml == null || answerHtml == '') {
				swal({title:"错误!", text:"问题回答内容不能为空!", type:"error",allowOutsideClick :true});
				return;
			}
			if(askingHtml.length>1000){
				swal({title:"错误!", text:"询问问题内容最大1000字符!", type:"error",allowOutsideClick :true});
				return;
			}
			if(answerHtml.length>1000){
				swal({title:"错误!", text:"问题回答内容最大1000字符!", type:"error",allowOutsideClick :true});
				return;
			}
        	if(askingHtml.indexOf('<') != -1 || askingHtml.indexOf('>') != -1 || askingHtml.indexOf('&') != -1){
        		swal({title:"提示!",text:"询问问题内容不能包含特殊字符：> , < , &", type:"info",allowOutsideClick :true});
        		return;
        	}
        	if(answerHtml.indexOf('<') != -1 || answerHtml.indexOf('>') != -1 || answerHtml.indexOf('&') != -1){
        		swal({title:"提示!",text:"问题回答内容不能包含特殊字符：> , < , &", type:"info",allowOutsideClick :true});
        		return;
        	}
			var askingRecordId = '${askingRecord.id}';
			if( modelerState==0 ){  // 修改 
				if(askingRecordId!=null && askingRecordId!="" ){ // 存在主干id 
					//alert("有主干-修改业务")
					$.ajax({
						cache : true,
						type : "POST",
						url : WEBPATH + '/askingManager/save-askcontent',
						data : $('#AskingContentForm').serialize(), 
						async : false,
						error : function(request) {
							business.closewait();
							swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
						},
						success : function(data) {
							if (data.result == 'success') {// 成功
								swal({ title : "成功", text : "", type : "success" }, function() {
									vueContentTables.items[itemIndex].askingContent = askingHtml;
									vueContentTables.items[itemIndex].answerContent = answerHtml;
									// 没有主干信息的修改，也把askingItemDatabaseId 清空
									vueContentTables.items[itemIndex].askingItemDatabaseId = "";
									})
							} else if (data.result == 'error') { // 失败  
								swal({ title : "失败", text : "", type : "error",allowOutsideClick :true });
							} else {
								swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
							}
						}
					});
				}else{
					//alert("没主干-修改VUE")
					vueContentTables.items[itemIndex].askingContent = askingHtml;
					vueContentTables.items[itemIndex].answerContent = answerHtml;
					// 没有主干信息的修改，也把askingItemDatabaseId 清空
					vueContentTables.items[itemIndex].askingItemDatabaseId = "";
				}
			}else{
				if(askingRecordId!=null && askingRecordId!="" ){ // 存在主干id 
					//alert("有主干-新增业务")
					$.ajax({
						cache : true,
						type : "POST",
						url : WEBPATH + '/askingManager/save-askcontent',
						data : $('#AskingContentForm').serialize(), 
						async : false,
						error : function(request) {
							business.closewait();
							swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
						},
						success : function(data) {
							if (data.result == 'success') {// 成功
								swal({ title : "成功", text : "", type : "success" }, function() {
									var obj ={id:data.code,askingContent:askingHtml,answerContent:answerHtml};
										dataArr.push(obj);
									})
							} else if (data.result == 'error') { // 失败  
								swal({ title : "失败", text : "", type : "error",allowOutsideClick :true });
							} else {
								swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
							}
						}
					});
				}else{
					//alert("没主干-只新增VUE")
					var obj ={askingContent:askingHtml,answerContent:answerHtml};
					dataArr.push(obj);
				}
			}
			// 强制清空模态框内容信息 
			clearModeler();
			$('#xzxwnr').modal('hide')
		}
		
		// 下移
		function chevronDown(contentIdA,contentIdB,index){
			var vueTop  = vueContentTables.items[index];
			var vueDown = vueContentTables.items[index+1];
			var askingRecordId = '${askingRecord.id}';
			if(askingRecordId!=null && askingRecordId!="" ){ // 存在主干id 
				//  type 0：上移 1 ：下移
				var obj={contentIdA:contentIdA,contentIdB:contentIdB,index:index,type:1};
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/askingManager/ask-save-loction',
					data :obj, 
					async : false,
					error : function(request) {
						business.closewait();
						swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
					},
					success : function(data) {
						if (data.result == 'success') {// 成功
							Vue.set(vueContentTables.items, index, vueDown);
							Vue.set(vueContentTables.items, index+1, vueTop);
							var trId = $("#contentTr"+(index+1));
							trId.fadeOut("fast");
							setTimeout(function(){
								trId.fadeIn("fast");
							},100)
						} else if (data.result == 'error') { // 失败  
							swal({ title : "移动失败", text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
					}
				});
			}else{
				Vue.set(vueContentTables.items, index, vueDown);
				Vue.set(vueContentTables.items, index+1, vueTop);
				var trId = $("#contentTr"+(index+1));
				trId.fadeOut("fast");
				setTimeout(function(){
					trId.fadeIn("fast");
				},100)
			} 
		}
		// 上移
		function chevronUp(contentIdA,contentIdB,index){
			var vueDown  = vueContentTables.items[index];
			var vueTop = vueContentTables.items[index-1];
			var askingRecordId = '${askingRecord.id}';
			if(askingRecordId!=null && askingRecordId!="" ){ // 存在主干id 
			//  type 0：上移 1 ：下移
				var obj={contentIdA:contentIdA,contentIdB:contentIdB,index:index,type:0};
				$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/askingManager/ask-save-loction',
					data :obj, 
					async : false,
					error : function(request) {
						business.closewait();
						swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
					},
					success : function(data) {
						if (data.result == 'success') {// 成功
							Vue.set(vueContentTables.items, index, vueTop);
							Vue.set(vueContentTables.items, index-1, vueDown);
							var trId = $("#contentTr"+(index-1));
							trId.fadeOut("fast");
							setTimeout(function(){
								trId.fadeIn("fast");
							},100)
						} else if (data.result == 'error') { // 失败  
							swal({ title : "移动失败", text : "", type : "error",allowOutsideClick :true });
						} else {
							swal({ title : "返回信息错误", text : "", type : "error",allowOutsideClick :true });
						}
					}
				});
				
			}else{
				Vue.set(vueContentTables.items, index, vueTop);
				Vue.set(vueContentTables.items, index-1, vueDown);
				var trId = $("#contentTr"+(index-1));
				trId.fadeOut("fast");
				setTimeout(function(){
					trId.fadeIn("fast");
				},100)
			}
		}
	 
		
		// 关闭
		function clearModeler() {
			// 强制清空模态框内容信息 
			$("#itemIndexContent").val("");
			$("#contentId").val("");
			$("#askingContent").val("");
			$("#answerContent").val("");
		}
	</script>	
	
	<!-- 存为自定义模板js -->
	<script type="text/javascript">
		$(document).ready(function() {
			$.ajax({
				type:"post",
				url:WEBPATH+"/tArea/chickUserArea",
				dataType:"json",
				async:false,
				data:{},
				success:function(data){
					if(data.cityStatus =='1'){
						//省级用户
						$.ajax({
							type:"post",
							url:WEBPATH+"/tArea/cityList",
							async:false,
							dataType:"json",
							success:function(data){
								$("#belongCity").append("<option value=''>请选择</option>"); 
								$("#belongCountry").append("<option value=''>请选择</option>"); 
								$.each(data,function(i,item){
									$("#belongCity").append("<option value="+item.code+">"+item.name+"</option>");
								});
							}
						});
					}else if(data.cityStatus =="2"){
						//市级用户
						$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
						$.ajax({
							type:"post",
							url:WEBPATH+"/tArea/countyListByCode",
							dataType:"json",
							data:{parentCode:data.cityCode},
							success:function(data){
								$("#belongCountry").append("<option value=''>请选择</option>"); 
								$.each(data,function(i,item){
									$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
								});
							}
						});
					}else{
						//县级用户
						$("#belongCity").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
						$("#belongCountry").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
					}
				}
			});
			
			
			$("#belongCity").change(function(){
				if ($(this).val() == ""){
					$("#belongCountry option").remove();
					$("#belongCountry").append("<option value=''>请选择</option>"); 
					return;
				}
				var parentCode = $(this).val();
				$("#belongCountry option").remove();
				$.ajax({
					type:"post",
					url:WEBPATH+"/tArea/countyListByCode",
					async:false,
					dataType:"json",
					data:{parentCode:parentCode},
					success:function(data){
						$("#belongCountry").append("<option value=''>请选择</option>"); 
						$.each(data,function(i,item){
							$("#belongCountry").append("<option value="+item.code+"  >"+item.name+"</option>"); 
						});
					}
				});
			});
				
			//保存自定义模板 
			$("#saveSceneBtn").click(function(){
				var asktype = '${askingRecord.askType}';
				if(asktype == 1){
					swal({title:"提示 ", text:"模板导入数据不能存为自定义模板", type:"error",allowOutsideClick :true});
					return false;
				}
				if(vueContentTables.items == null || vueContentTables.items.length==0){
					swal({title:"提示 ", text:"询问内容不能为空！至少含有一条询问内容！", type:"error",allowOutsideClick :true});
					return false;
				}
				var belongProvince = $("#belongProvince").val();
				var belongCity =$("#belongCity").val();
				var belongCountry =$("#belongCountry").val();
				if(belongCountry!= null && belongCountry !=''){
					$("#templateArea").val(belongCountry);
					$("#templateAreaname").val($('#belongCountry option:selected').text());
				}else if(belongCountry =='' && belongCity != '' ){
					$("#templateArea").val(belongCity);
					$("#templateAreaname").val($('#belongCity option:selected').text());
				}else{
					$("#templateArea").val('35000000');
					$("#templateAreaname").val("福建省");
				}
				//判断模板的名称不能为空
				var templateName  =$("#templateName").val();
				if(templateName == '' || templateName == null){
					swal({title:"提示 ", text:"模板的名称不能为空!", type:"error",allowOutsideClick :true});
					return false;
				}
				var lawObjType = '${lawObj.lawObjectType}';
				
				var formData=$("#vueAskFormData").serializeArray();//[Object, Object, Object] 
				var  jsonContentList= new Array();
		 		for(var i=0;i < formData.length;i=i+4){
					var obj ={askContentId:formData[i].value,id:formData[i+1].value,askingContent:formData[i+2].value, answerContent:formData[i+3].value,templateObjectType:lawObjType,};
					// JSON.stringify(jsonContentList)
					jsonContentList.push(obj);
				} 
		 		
				/* var  jsonContentList= new Array();
				for(var i=0;i < vueContentTables.items.length;i++){
					var obj ={id:vueContentTables.items[i].askingItemDatabaseId,
							askingContent:vueContentTables.items[i].askingContent, 
							answerContent:vueContentTables.items[i].answerContent,
							templateObjectType:lawObjType,
							askContentId:vueContentTables.items[i].id};
					jsonContentList.push(obj);
				} */
				
				
				
				
				$("#vueJson").val(JSON.stringify(jsonContentList));
				$.ajax({
	                cache: true,
	                type: "POST",
	                url: WEBPATH+'/askingManager/ask-save-template',
	                data:$('#templeteForm').serialize(), 
	                async: false,
	                error: function(request) {
	                	swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
	                },
	                success: function(data) {
	                	if(data.result =='success'){
	                		swal({ title : data.message, text : "", type : data.result,allowOutsideClick :true});
	        				var contentId = '${askingRecord.id}';
	        				var code = 2; // 历史
	        				var obj={lawObjectType:lawObjType,code:code,contentId:contentId};
	        				// 请求数据 
	        				$.ajax({
	        	                cache: true,
	        	                type: "POST",
	        	                url: WEBPATH+'/askingManager/ask-content-vue',
	        	                data:obj, 
	        	                async: false,
	        	                error: function(request) {
	        	                    swal({title: request.message ,text: "",type:"error",allowOutsideClick :true});
	        	                },
	        	                success: function(data) {
	        	                	dataArr.splice(0,dataArr.length);
	        						for(var i =0;i<data.contentList.length;i++){
	        							 dataArr.push(data.contentList[i]);
	        						}
	        						$("#contributionName").val(data.recordName);  
	        						$("#templateContributionName").val(data.recordName);  
	        	                }
	        	            });
	                	}else{
	                		 swal({title: data.message ,text: "",type:"error",allowOutsideClick :true});
	                	}
	                }
	            });
		 
			});
			var industryTypeCode;
			var industryTypeName;
			 // 是否选中试用所有行业
		 	$("#templateIndustryStatus").change(function() { 
				if( $("#templateIndustryStatus").is(':checked')==true ){
					industryTypeCode = $("#templateIndustry").val();  
					industryTypeName =  $("#templateIndustryName").val();  
					$("#templateIndustry").val("all");  
	                $("#templateIndustryName").val("所有行业");  
					$("#templateIndustryNameTemp").html("所有行业");  
				}else{
					$("#templateIndustry").val(industryTypeCode);  
	                $("#templateIndustryName").val(industryTypeName);  
					$("#templateIndustryNameTemp").html(industryTypeName);  
				}
			});
		});
		// 点击 保存自定义模板时，若是企事业单位查询出企事业单位的类型
		function selectLawObjType(taskId){
				$.ajax({
	                cache: true,
	                type: "POST",
	                url: WEBPATH+'/askingManager/ask-save-page',
	                data:{taskId:taskId}, 
	                async: false,
	                error: function(request) {
	                	swal("错误!","网络异常", "error");
	                },
	                success: function(data) {
	                	$("#templateIndustry").val(data.industryTypeCode);  
	                	$("#templateIndustryName").val(data.industryTypeName);  
						$("#templateIndustryNameTemp").html(data.industryTypeName);  
	                }
	           });
		}
	</script>
	
	<!-- 二维码分享 -->
	<script type="text/javascript" src ='${webpath }/static/js/qrcode.min.js'></script>
	<script type="text/javascript">
	function shareBooksBtn(askingRecordId){
		var SERVER_ADDR = '${SERVER_ADDR}';	
		var FASTDFS_ADDR = '${FASTDFS_ADDR}';
		var recordName = $("#recordName").val();
		if(askingRecordId == null || askingRecordId == ""){
				swal({
				title : "提示！",
				text : "询问笔录保存后在分享文书！",
				type : "error",
				allowOutsideClick :true
				})
				return;
			}
			$.ajax({
	            cache: true,
	            type: "POST",
	            url: WEBPATH+'/askingManager/ask-doc-url',
	            data:{askId:askingRecordId},//  form law object
	            async: false,
	            error: function(request) {
	            	swal({title:"错误!",text:"网络异常", type:"error",allowOutsideClick :true});
	            },
	            success: function(data) {
	             	if (data.result == 'success') {// 成功
	         
	             		//var url = "HTTP://*************:8080${webpath}/qrCodeSharing/sharePage?url="+data.url;
	             		var base = new Base64(); 
						var result = base.encode(FASTDFS_ADDR+data.url); 
	             		 var url = SERVER_ADDR+"qrCodeSharing/sharePage?url="+result+"&filename="+recordName;
	             		
	            		if(data.url != ''&& data.url != null){
	            			var aurl = utf16to8(url);
	            			$('#qrcode').empty();
	            			$('#qrcode').qrcode({
	                            render: 'canvas',
	                            text: aurl,
	                            height: 210,
	                            width: 210,
	                        }).hide();
	            			$("#shareBookModel").modal('show');
						}else{
								//没有url
								swal({
										title : "错误",
										text : "文书未找到！",
										type : "error",
										allowOutsideClick :true
									})
						} 
	            		
	            		//从 canvas 提取图片 image 
	                    function convertCanvasToImage(canvas) { 
	                        //新Image对象，可以理解为DOM 
	                        var image = new Image(); 
	                        // canvas.toDataURL 返回的是一串Base64编码的URL，当然,浏览器自己肯定支持 
	                        // 指定格式 PNG 
	                        image.src = canvas.toDataURL("image/png"); 
	                        return image; 
	                    } 
	                    $('#getval').empty();
	                    //获取网页中的canvas对象 
	                    var mycanvas1=document.getElementsByTagName('canvas')[0]; 
	                    //将转换后的img标签插入到html中 
	                    var img=convertCanvasToImage(mycanvas1);
	                    $('#getval').append(img);//imagQrDiv表示你要插入的容器id
					}else if(data.result == 'error'){
						swal({
							title : "错误",
							text : "文书未找到！",
							type : "error",
							allowOutsideClick :true
						})
					}else{
						swal({
							title : "错误",
							text : "返回状态错误",
							type : "error",
							allowOutsideClick :true
						})
					}
	            }
			});
		}
	
	//
	function savePic(id,url,tableName,column,fileId){
			var degree = $("#degree").val();
			if(degree!=null && degree!=''&& typeof(degree)!='undefined'){
				if(degree!=0){
					business.openwait();
					$.ajax({
						method:'POST',
						url:WEBPATH+'/askingManager/rotateImage',
						data:{
							id:id,
							url:url,
							tableName:tableName,
							column:column,
							fileId:fileId,
							degree:degree
						}, error: function(request) {
				        	business.closewait();//关闭遮罩层
				        	swal({title:"错误!",text:"系统错误", type:"error",allowOutsideClick :true});
				        },
						success:function(data){
							business.closewait();//关闭遮罩层
							if(data.meta.code==200){
								swal({
									title : "操作成功！",
									text : "",
									type : "success"
								}, function() {
									//刷新当前页面
									//关闭模态框
									$("#inputImgModeler").modal('hide'); 
									var d = document.getElementById('P'+id);
									d.src = '${FASTDFS_ADDR}'+data.data;
								})
							}else{
								swal({
									title : "操作失败！",
									text : data.meta.message,
									type : "error",
									allowOutsideClick :true
								});
							}
						}
					});
					
				
				}else{
					swal({
						title : "操作成功！",
						text : "",
						type : "success",
						allowOutsideClick :true
					});
				}
			}else{
				swal({
					title : "操作成功！",
					text : "",
					type : "success",
					allowOutsideClick :true
				});
				$("#inputImgModeler").modal('hide'); 
			}
			
		}
 	$( function() {
		$( "#addTabContent" ).sortable({
			placeholder: "ui-state-highlight"
		});
		$( "#addTabContent" ).disableSelection();
	} );
 	function printBtn(){
 		var multiple = $("#multiple").val();
		 var options = {
					remote:WEBPATH+'/askingManager/print-ask-file?askId=${askingRecord.id}&multiple='+multiple
				  };
				$('#fileShowModel').modal(options);
 	}
    function checknum(){
        var nMax = 1000;
        var textDom =  document.getElementById("askingContent");
        var len =textDom.value.length;    
        if(len>nMax){
            textDom.value = textDom.value.substring(0,nMax);
            return;
        }
        document.getElementById("in").value="还可以输入"+(nMax-len)+"个字";
    }
    checknum();
    function checknum2(){
        var nMax = 1000;
        var textDom =  document.getElementById("answerContent");
        var len =textDom.value.length;    
        if(len>nMax){
            textDom.value = textDom.value.substring(0,nMax);
            return;
        }
        document.getElementById("in2").value="还可以输入"+(nMax-len)+"个字";
    }
    checknum2();
	</script>
</body>
</html>
