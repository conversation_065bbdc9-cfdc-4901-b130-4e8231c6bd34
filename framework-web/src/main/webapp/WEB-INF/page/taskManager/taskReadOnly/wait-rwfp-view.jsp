<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script>
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
</head>
<body>
	<div class="main-container">
		<div class="padding-md">
			<div style="display:none;">
				<form  id= "taskObjectForm">
					<!-- 主干不可缺少信息 -->
					<input id="taskId" name="taskId" value="${taskId}"></input>
					<input id="taskFlowId"  name="taskFlowId" value="${taskFlowId}"></input>
					<input id="lawObjectType" name="lawObjectType" value="${lawObjectType}"></input>
					<!-- 辅助信息 -->	
					<input id="taskNodeId" name="taskNodeId" value=""></input>
					<input id="parentUrl" name="parentUrl" value="${parentUrl}"></input>
					<input id="sourcePlace" name="sourcePlace" value="1"></input>
					<input id="nodeCode" name="nodeCode" value="${nodeCode}"></input>
					<input id="taskFolwRemark" name="taskFolwRemark" value=""></input>
					<input type="hidden" value="${preUrl}" id="preUrl"/>
				</form>
			</div>
			<div class="row">
				<!--任务分配-->
				<div class="col-lg-12">
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 分配执法信息
							<span class="smart-widget-option" style="margin-top:-3px;">
								<span class="refresh-icon-animated">
									<i class="fa fa-circle-o-notch fa-spin"></i>
								</span>
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">
							<div class="smart-widget-body form-horizontal">  
					             <div class="form-group">
                                            <div class="col-lg-offset-2 col-lg-10 text-right">
												 <a href="#" onclick="handleTask('${taskFlowId}', '${editTaskId }','${lawObjectType }','${parentUrl }','${nodeCode }')"> 
                                                 <button type="submit" class="btn btn-info">领办 </button></a>
                                                 <a href="#" onclick="goBack('${taskUrl}', '${taskParamSession }','${caseUrl }','${caseParamSession }')"> 
                                                 <button type="submit" class="btn btn-info">返回 </button></a>
                                            </div>
                                  </div>
								<c:if test="${hide== 1}">
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 投诉对象</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
												${complaintReportInfo.tsdxmc }
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 所属行政区域</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
												${complaintReportInfo.xzqyname }
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 反应问题所在地</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
												${complaintReportInfo.fywtszd }
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 投诉时间</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
												${complaintReportInfo.tssj }
										</div>
									</div>
									<div class="form-group">
										<label class="col-lg-2 col-sm-2 col-xs-5 control-label"> 投诉内容</label>
										<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
												${complaintReportInfo.tsnr }
										</div>
									</div>
								</c:if>
								<div class="form-group">
									<label for="执法对象名称" class="col-lg-2 col-sm-2 col-xs-5 control-label"> 执法对象名称</label>
									<div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
										${historyTask.lawObjectName }
									</div>
								</div>
                                <div class="form-group">
                                    <label for="详细地址" class="col-lg-2 col-sm-2 col-xs-5 control-label">详细地址</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
										${historyTask.address} 
                                    </div>
								</div>
                                <div class="form-group"  id="taskFromNameGroupOne">
                                    <label for="来源" class="col-lg-2 col-sm-2 col-xs-5 control-label">来源</label>
									<div id="taskFromNameDiv" class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
											${historyTask.taskFromName} 
                                    </div>
								</div>
								<div class="form-group" id="taskFromNameGroupTwo"  style="display:none" >
									<label for="来源" class="col-lg-2 col-sm-2 col-xs-5 control-label">来源</label>
										<div class="ol-lg-8 col-sm-8 col-xs-12">
									<select class="form-control" id="taskFromName" name="taskFromName">	
										<option value="">请选择</option>
										
										<c:forEach items="${taskSourceList}"  var="menu" varStatus="status" >
												<c:choose>
													<c:when test="${menu.askQuestion==1 and not empty menu.childrenList}"> 
														 <optgroup label="${menu.name}">
											 	              <c:forEach items="${menu.childrenList}" var="secondChild" varStatus="status">
						                                       	 <option value="${secondChild.name }#${secondChild.code}" <c:if test="${secondChild.code  ==task.taskFromCode }">selected</c:if>>${secondChild.name}</option>
						                                      </c:forEach>
														</optgroup>  
													</c:when>
													<c:otherwise>
														<c:if test="${menu.name =='测试信息' }">
															<option style="color:red;"  value="${menu.name }#${menu.code}" <c:if test="${menu.code  ==task.taskFromCode }">selected</c:if>>${menu.name}</option>
														</c:if>
														<c:if test="${menu.name !='测试信息' }">
															<option value="${menu.name }#${menu.code}" <c:if test="${menu.code  ==task.taskFromCode }">selected</c:if>>${menu.name}</option>
														</c:if>
													</c:otherwise>
												</c:choose> 
											</c:forEach>
									</select>
									</div>
							    </div>
                                <div class="form-group" id="monitorTypeNameGroupOne">
                                    <label for="监察类型" class="col-lg-2 col-sm-2 col-xs-5 control-label">监察类型</label>
                                    <div id="monitorTypeNameDiv" class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                        	${historyTask.monitorTypeName} 
                                    </div>
                                </div>
                             <div class="form-group" id="monitorTypeNameGroupTwo"  style="display:none" >
                            	<input id="monitorTypeCode" value="${task.monitorTypeCode}" name="monitorTypeCode" type="hidden">
								<label for="监察类型" class="col-lg-2 col-sm-2 col-xs-5 control-label">监察类型</label>
								<div class="ol-lg-8 col-sm-8 col-xs-12">
									<div class="input-group">
										<input type="text" id="monitorTypeName" name="monitorTypeName" value ="${task.monitorTypeName }" readonly = "readonly" 
											 class="form-control" placeholder="监察类型">
										<div class="input-group-btn">
											<button type="button" class="btn btn-info no-shadow"
												tabindex="-1" data-toggle="modal" data-target="#jclx">监察类型选择</button>
										</div>
									</div>
								</div>
							</div>
                                <div class="form-group">
                                    <label for="检查人" class="col-lg-2 col-sm-2 col-xs-5 control-label"> 检查人</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                        	${historyTask.checUserNames} 
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="执法证号" class="col-lg-2 col-sm-2 col-xs-5 control-label"> 执法证号</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
									 	${historyTask.lawEnforcIds} 
                                    </div>
								</div>
                                <div class="form-group">
                                    <label for="限办时间" class="col-lg-2 col-sm-2 col-xs-5 control-label">限办时间</label>
                                    <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">  
									 <fmt:formatDate value="${historyTask.limitTime}" pattern="yyyy-MM-dd"></fmt:formatDate>
                                    </div>
								</div>
                                <div class="form-group" id="specialActionNamesGroupOne">
                                    <label for="关联专项行动" class="col-lg-2 col-sm-2 col-xs-5 control-label">关联专项行动</label>
                                    <div  id="specialActionNamesDiv" class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                        	${historyTask.specialActionNames} 
                                    </div>
								</div>
								<input id="specialActionIds" value="${task.specialActionIds}" name="specialActionIds" type="hidden">
								<div class="form-group" id="specialActionNamesGroupTwo"  style="display:none" >
									<label for="关联专项行动" class="col-lg-2 col-sm-2 col-xs-5 control-label">关联专项行动</label>
									<div class="ol-lg-8 col-sm-8 col-xs-12">
										<div class="input-group">
											<input type="text"  id="specialActionNames"   name="specialActionNames" value ="${task.specialActionNames }" readonly = "readonly" 
												class="form-control" placeholder="关联专项行动">
											<div class="input-group-btn">
												<button type="button" class="btn btn-info no-shadow"
													id="glzxxdBtn" tabindex="-1" data-toggle="modal"
													data-remote="${webpath}/jcbl/special-action-page"
													data-target="#glfrw">关联专项行动 </button>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group">
                                    <label for="任务描述" class="col-lg-2 col-sm-2 col-xs-5 control-label">任务描述</label>
                                    <div class="col-md-6 text-left" style="margin-top: 7px;word-wrap:break-word;word-break:break-all;">
                                        	${historyTask.taskDesc} 
                                    </div>
								</div>
							</div>
					</div>
                    </div>
                </div>
                <!--任务要求附件-->
                <div class="col-lg-12">                                    
					<div class="smart-widget widget-blue">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 要求附件
							<span class="smart-widget-option">
								<span class="refresh-icon-animated">
									<i class="fa fa-circle-o-notch fa-spin"></i>
								</span>
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">
							<div class="smart-widget-body form-horizontal">                                      
								
								<div class="form-group">
									<!-- <label for="任务要求附件" class="col-lg-2 control-label">任务要求附件</label> -->
									<div class="smart-widget-inner table-responsive">
									<div class="smart-widget-body form-horizontal">
										<c:if test="${not empty historyTask.filesList}">
											<c:forEach items="${historyTask.filesList}" var="obj" varStatus="status">
												<c:if test="${status.count eq 1 || (status.count-1) % 6 eq 0}">
													
												</c:if>
												<c:if test="${obj.fileType==0}">
													<div class="col-md-3 col-sm-3">
														<div class="pricing-widget clean-pricing" style="cursor: pointer;">
															<div class="pricing-value">
																<span class="value"><img style="height: 150px;"
																	data-toggle="modal"
																	data-remote="${webpath}/taskNodeManager/showFileModal?id=${obj.id}"
																	data-target="#inputImgModeler"
																	src="${webpath}/static/img/pdf-thumb.jpg" /></span>
															</div>
															<ul class="text-center padding-sm" style="margin-top: -20px;">
																<c:if test="${obj.fileName != null && obj.fileName.length()>8 }">
																	<li>文件名：${obj.fileName.substring(0,5) }...${obj.fileName.substring(obj.fileName.length()-3)}</li>
																</c:if>
																<c:if test="${obj.fileName != null && obj.fileName.length()<=8 }">
																	<li>文件名：${obj.fileName }</li>
																</c:if>
																<li class="text-center" style="margin-top: 5px;">
																	<a href="${webpath}/taskNodeManager/downloadFile?id=${obj.id}">
																	<button type="button" class="btn btn-info btn-sm"
																		>下载</button>
																	</a>
																</li>
															</ul>
														</div>
													</div>
												</c:if>
												<c:if test="${obj.fileType==1}">
													<div class="col-md-3 col-sm-3">
														<div class="pricing-widget clean-pricing" style="cursor: pointer;">
															<div class="pricing-value">
																<span class="value"><img style="height: 150px;"
																	data-toggle="modal"
																	data-remote="${webpath}/taskNodeManager/showFileModal?id=${obj.id}"
																	data-target="#inputImgModeler"
																	src="${FASTDFS_ADDR}/${obj.fileUrl}" /></span>
															</div>
															<ul class="text-center padding-sm" style="margin-top: -20px;">
							
																<c:if test="${obj.fileName != null && obj.fileName.length()>8 }">
																	<li>文件名：${obj.fileName.substring(0,5) }...${obj.fileName.substring(obj.fileName.length()-3) }</li>
																</c:if>
																<c:if test="${obj.fileName != null && obj.fileName.length()<=8 }">
																	<li>文件名：${obj.fileName }</li>
																</c:if>
																<li class="text-center" style="margin-top: 5px;">
																	<a href="${webpath}/taskNodeManager/downloadFile?id=${obj.id}">
																	<button type="button" class="btn btn-info btn-sm"
																		>下载</button>
																	</a>
																</li>
															</ul>
														</div>
													</div>
												</c:if>
												<c:if test="${status.count eq 1 || (status.count-1) % 6 eq 0}">
												</c:if>
											</c:forEach>
										</c:if>
									</div>
									</div>
								</div>
							</div>
					</div>
                    </div>
                </div>    
                                      
	</div>
</div>
	<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog" 
	   aria-labelledby="fileModalLabel" aria-hidden="true">
		   <div class="modal-dialog  modal-lg">
	        <div class="modal-content">
	        </div>
	        <!-- /.modal-content -->
	      </div>
	     <!-- /.modal-dialog -->
	</div>
    <!-- 监察类型选择（Modal） -->
	<div class="modal fade" id="jclx" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true" style="height:580px;">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal"
						aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel">监察类型</h4>
				</div>
				<div class="modal-body">
					<div class="smart-widget-body">
						<form class="form-horizontal">
							<div class="form-group">
								<div class="col-lg-12">
									<c:forEach items="${monitorTypeList}" var="monitorTypeList">
										<div class="checkbox inline-block"
											style="padding-right: 30px;">
											<div class="custom-checkbox">
												<input type="checkbox" name="monitorTypeCodeList"  value="${monitorTypeList.code}"id="monitorTypeCode${monitorTypeList.code}"> <label
													for="monitorTypeCode${monitorTypeList.code}"
													class="checkbox-blue"> </label>
											</div>
											<div class="inline-block vertical-top" id ="monitorTypeName${monitorTypeList.code }">	
												${monitorTypeList.name}
											</div>
										</div>

									</c:forEach>
								</div>
							</div>
						</form>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" id="jclxxzBtn"  class="btn btn-info">确定</button>
					<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
				</div>

			</div>
		</div>
	</div>
	
	<!-- 专项行动选择（Modal） -->
	<div class="modal fade" id="glfrw" tabindex="-1" role="dialog" aria-labelledby="fileModalLabel" aria-hidden="true">
			<div class="modal-dialog  modal-lg">
				<div class="modal-content"></div>
			</div>
	</div>
	<script type="text/javascript">
		$(function(){
			var monitorTypeCode = $("#monitorTypeCode").val();
			if(monitorTypeCode != null && monitorTypeCode != ''){
				var monitorTypeCodeArr = monitorTypeCode.split(",");
				var obj = document.getElementsByName('monitorTypeCodeList');
				var names = "";
				for(var i=0; i<obj.length; i++){
					//不能用attrIdsStr.indexOf，因为如果有13，则会把1和3也选中。。。。
					for(var j = 0; j<monitorTypeCodeArr.length; j++){
						if(monitorTypeCodeArr[j]==obj[i].value){
							obj[i].checked=true;
						}
					}
				}
			}
		})
		
		$("#taskFromName").change(function(){ 
			var valSelect   = $("#taskFromName").val();
			var inChar = valSelect.indexOf('#');
			valSelect = valSelect.substring(0,inChar) 
			$("#taskFromNameDiv").html(valSelect);
		}) 
		
		$("#jclxxzBtn").click(function() {
			var arr = new Array();
			var obj = document.getElementsByName('monitorTypeCodeList');
			var ids = "";
			var names = "";
			for(var i=0; i<obj.length; i++){    
				if(obj[i].checked) ids+=obj[i].value+',';
				if(obj[i].checked) names+=$.trim($("#monitorTypeName"+obj[i].value).html())+',';
			}
			ids = ids.substring(0,ids.length-1);//选中的所有的id
			names = names.substring(0,names.length-1);//选中的所有的name
			$("#monitorTypeName").val(names);//在输入框显示选中的属性名
			$("#monitorTypeCode").val(ids);//在隐藏框存上选中的属性id，方便点击保存时获取
			//隐藏模态框
			$('#jclx').modal('hide');
			$("#monitorTypeNameDiv").html(names);//在隐藏框存上选中的属性id，方便点击保存时获取
		})
			
	/*  //设置传入参数
      function queryParams(params) {
            var temp = {  //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
                pageNum: params.pageNumber,
                pageSize: params.pageSize,
            };
            return temp;
        } */
	</script>
	
	<script type="text/javascript">
	//返回上一步主菜单
	function goBack(taskUrl,params,caseUrl,caseParam) {
		//console.log(params);//{parentUrl=1, lawObjectType=1, nodeCode=1, selectType=0, taskFlowId=54198D8E8134CCF2E055000000000001, taskId=54198D8E8132CCF2E055000000000001}
		var paramsJson;
		if(params != null && params != '' && params != 'undefined'){
			params = params.replaceAll("{", "{\"");
			params = params.replaceAll("=", "\":\"");
			params = params.replaceAll(", ", "\",\"");
			params = params.replaceAll("}", "\"}");
			paramsJson = $.parseJSON(params);
		}
		var caseParamJson;
		if(caseParam != null && caseParam != '' && caseParam != 'undefined'){
			caseParam = caseParam.replaceAll("{", "{\"");
			caseParam = caseParam.replaceAll("=", "\":\"");
			caseParam = caseParam.replaceAll(", ", "\",\"");
			caseParam = caseParam.replaceAll("}", "\"}");
			caseParamJson = $.parseJSON(caseParam);
		}
		//{"parentUrl":"1","lawObjectType":"1","nodeCode":"1","selectType":"0","taskFlowId":"54198D8E8134CCF2E055000000000001","taskId":"54198D8E8132CCF2E055000000000001"}
		//拼接json
		var preUrl = $("#preUrl").val();
		if(taskUrl != null && taskUrl != '' && taskUrl != 'undefined'){
			business.addMainContentParserHtml(WEBPATH+taskUrl, paramsJson);
		} else if(caseUrl != null && caseUrl != '' && caseUrl != 'undefined'){
			business.addMainContentParserHtml(WEBPATH+caseUrl, caseParamJson);
		} else {
			if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
				business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", $("#searchForm").serialize());
			} else {
				swal({
					title : "提示！",
					text : "返回信息错误，请刷新后重试。",
					type : "error"
				})
			}
		}
	}
	 // 办理任务（非分配状态的办理），coordinate坐标，用来找到页面指定form表单提交
	 function handleTask(id,taskId,lawObjectType,sourcePlace,nodeCode,taskLawObjectStatus){
		    var obj={taskFlowId:id,taskId:taskId,lawObjectType:lawObjectType,parentUrl:sourcePlace,nodeCode:nodeCode};
		    if(nodeCode =='1'){
				$.ajax({
	                cache: true,
	                type: "POST",
	                url: WEBPATH+'/taskManager/select-task-start',
	                data:obj,//  form law object
	                async: false,
	                error: function(request) {
	                    swal({title: request.message ,text: "",type:"error"});
	                },
	                success: function(data) {
	                  	if(data.result=='success'){ // 领取成功
		                    if(taskLawObjectStatus!=null && "2"== taskLawObjectStatus){ // 直接线程执法 
		                    	swal({
		        							title : "领取双随机任务",
		        							text : "双随机任务领取后，无法转送和退回",
		        							type : "warning",
		        							showCancelButton : true,
		        							confirmButtonColor : "#DD6B55",
		        							confirmButtonText : "是的，我要领取！",
		        							cancelButtonText : "让我再考虑一下",
		        							closeOnConfirm : false,
		        							closeOnCancel : false
		        						},
		        						function(isConfirm) {
		        							if (isConfirm) {
		        								swal({title: data.message ,text: "",type:data.result});
		        								var objForm={taskFlowId:id,taskId:taskId,lawObjectType:lawObjectType,parentUrl:sourcePlace,nodeCode:nodeCode,parentUrl:1,selectType:0,taskLawObjectStatus:taskLawObjectStatus};
		        								business.addMainContentParserHtml(WEBPATH+ '/taskManager/start-handle-task', objForm);
		        							} else {
		        								swal({
		        									title : "已取消",
		        									text : "您取消领取任务！",
		        									type : "error"
		        								})
		        						}
		        				})
		                    }else{
		                      	swal({title: data.message ,text: "",type:data.result});
			                    // 任务领取成功跳转新页面 
			                  	business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType='+0,obj);
		                    }
		                    
	                  	}else if(data.result=='handle'){ // 已经被当前用户领取
       					if(taskLawObjectStatus!=null && "2"== taskLawObjectStatus){ // 直接线程执法 
       						var objForm={taskFlowId:id,taskId:taskId,lawObjectType:lawObjectType,parentUrl:sourcePlace,nodeCode:nodeCode,parentUrl:1,selectType:0,taskLawObjectStatus:taskLawObjectStatus};
       						business.addMainContentParserHtml(WEBPATH+ '/taskManager/start-handle-task', objForm);
		                    }else{
			                  	business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType='+0,obj);
		                    }
	                  	}else if(data.result=='occupy'){// 已经被别的用户领取
	                		swal({title: data.message ,text: "",type:"error"});
	                	    business.addMainContentParserHtml(WEBPATH+"/taskManager/list",$("#searchForm").serialize());
	                  	}else{ // 错误 
	                  		swal({title: data.message ,text: "",type:data.result});
	                	    business.addMainContentParserHtml(WEBPATH+"/taskManager/list",$("#searchForm").serialize());
	                  	}
	                }
	            });
		    }else{
		   		 //  后续会在多个状态下进行领取任务，目前就只会在现场办理领取任务 
				swal({title: "该状态下不能办理任务" ,text: "",type:"error"});
		    }

	 }
	 $(function(){
			//监听回退键
			business.listenBackSpace();	  
		 });
	</script>
</body>
</html>