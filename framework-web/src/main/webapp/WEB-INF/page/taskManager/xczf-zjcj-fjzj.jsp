<!DOCTYPE html>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<%	
	String fastdfs_addr=PropertiesHandlerUtil.getValue("fastdfs.nginx.ip","fastdfs");
%>  
<c:set var="FASTDFS_ADDR"><%=fastdfs_addr%></c:set>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1" />
 <meta http-equiv="X-UA-Compatible" content="IE=edge" charset="utf-8" />
<meta charset="UTF-8" />
<style>
/*
PDFObject appends the classname "pdfobject-container" to the target element.
This enables you to style the element differently depending on whether the embed was successful.
In this example, a successful embed will result in a large box.
A failed embed will not have dimensions specified, so you don't see an oddly large empty box.
*/
.pdfobject-container {
	width: 100%;
	height:580px;
	margin: 2em 0;
}
</style>
<%-- <script src="${webpath }/static/bootstrap-fileinput-4.3.9/js/locales/zh.js"  type="text/javascript"></script> --%>
<script type="text/javascript">

/**
* 封装vue动态数据值	
*/
var dataArray = new Array();
var generateVue = new Vue({ // 不要用var 声明，定义为全局 
	  el: "#sortable",
	  data: {
		    items:dataArray,
		  },
	  methods: {
		  changeContent:  function (contentId) { // 更改
			  changeGenerate(contentId);
		  }, 
		  deleteContent: function (contentId,index){ //删除 
			  deleteGenerate(contentId, index);
		  },
		  chevronDownOrUp:function (contentIdA,contentIdB,index,type){ // 上移  下移
			  chevronGenerateDownOrUp(contentIdA,contentIdB,index,type);
		  },
		  itemVueOnBlur:function(index){
			  itemVueOnBlur(index);
		  }
	  }
}); 
	/* var taskId = ${lawObj.taskId}; */
	var taskId = "";
	taskId = '${lawObj.taskId}';
	 $(document).ready(function() {
			$.ajax({
		        cache: true,
		        type: "POST",
		        url: WEBPATH+'/tmEvidenceCollect/cFileDictionaryList',
		        data:{taskId:taskId},
		        async: false,
		        error: function(request) {
		        	swal("错误!","网络异常", "error");
					business.closewait();
		        },
		        success: function(data) {
		        	/* generateVue.$data.items = dt.data; // 绑定 VUE （json） */
		        	for(var  i=0,len=data.data['cFileDictionaryList'].length;i<len;i++){
		           		dataArray.push(data.data['cFileDictionaryList'][i]);
		        		
		           }
		        	$("#numberSize").html('当前附件：'+data.data['number']+'个文书，共'+data.data['totalSize']+'M。');
		        }
		    });
		
		 
	 });



function deleteFile(id){
	swal({
        title: "您确定执行删除操作吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的，我要删除",
        confirmButtonColor: "#ec6c62"
    	}, function() {
            $.ajax({
              type: "post",
   		      url: WEBPATH+"/tmEvidenceCollect/fileinfoDelete",
   		      data:{id:id},
            }).done(function(data) {
            	 if(data.meta.result==="success"){
           		 swal({
                       title : "删除成功！",
                       text : "附件删除成功",
                       type : "success",
                       allowOutsideClick :true
                     })
                    evidenceCollectFiles();
 		        }else{
 		          swal({title:"删除失败", text:"附件删除失败", type:"error",allowOutsideClick :true});
 		        }
            }).error(function(data) {
                swal({title:"操作失败", text:"删除操作失败了!", type:"error",allowOutsideClick :true});
            });
    });
}

function extractFile(){
	var taskId = '${lawObj.taskId}';
	var code = 93;
	swal({
        title: "您确定执行抽取操作吗？",
        type: "warning",
        showCancelButton: true,
        closeOnConfirm: false,
        confirmButtonText: "是的",
        confirmButtonColor: "#ec6c62"
    	}, function() {
            $.ajax({
              type: "post",
   		      url: WEBPATH+"/tmEvidenceCollect/fileinfoExtract",
   		      data:{taskId:taskId,code:code},
            }).done(function(data) {
            	 if(data.meta.result==="success"){
           		 swal({
                       title : "抽取成功！",
                       text : "附件抽取成功",
                       type : "success",
                       allowOutsideClick :true
                     })
                    evidenceCollectFiles();
 		        }else{
 		          swal({title:"抽取失败", text:"附件抽取失败", type:"error",allowOutsideClick :true});
 		        }
            }).error(function(data) {
                swal({title:"操作失败", text:"抽取操作失败了!", type:"error",allowOutsideClick :true});
            });
    });
}

/* $(function(){
	$( "#sortable" ).sortable({
		placeholder: "ui-state-highlight"
	});
}) */
</script>
</head>
<body>
	<div class="main-container">
		<div class="padding-md">
			<!-- 必要信息字段 -->
			<jsp:include page="BusinessFlow.jsp"></jsp:include>

			<!--第三层证据采集row-->
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget widget-light-grey">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 上传证据 <span
								class="smart-widget-option"> 
							</span>
						</div>
						<div class="smart-widget-inner table-responsive">

							<div class="modal-body">
								<div class="smart-widget-body form-horizontal">
									<div class="col-lg-2 col-sm-2 col-xs-5"
										onclick="evidenceCollect()">
										<div class="statistic-box m-bottom-md imgbox"
											style="background-image: url(${webpath }/static/img/photo.png); background-size: 100% 100%; cursor: pointer;">
											<div class="statistic-title">
												<h4
													style="color: #FFF; background-color: #23b7e5; margin: 80px -20px 0 -20px; padding: 5px 0;">照片</h4>
											</div>
										</div>
									</div>
									<div class="col-lg-2 col-sm-2 col-xs-5"
										onclick="evidenceCollectFiles()" id="fileClick">
										<div class="statistic-box m-bottom-md imgbox"
											style="background-image: url(${webpath }/static/img/file.png); background-size: 100% 100%; cursor: pointer;">
											<div class="statistic-title">
												<h4
													style="color: #000; background-color: #ccc; margin: 80px -20px 0 -20px; padding: 5px 0;">附件</h4>
											</div>
										</div>
									</div>
									<div class="col-lg-2 col-sm-2 col-xs-5"	onclick="evidenceCollectVideo()">
										<div class="statistic-box m-bottom-md imgbox"
											style="background-image: url(${webpath }/static/img/video.png); background-size: 100% 100%; cursor: pointer;">
											<div class="statistic-title">
												<h4
													style="color: #FFF; background-color: #23b7e5; margin: 80px -20px 0 -20px; padding: 5px 0;">录像</h4>
											</div>
										</div>
									</div>
									<div class="col-lg-2 col-sm-2 col-xs-5"	onclick="evidenceCollectRecord()">
										<div class="statistic-box m-bottom-md imgbox"
											style="background-image: url(${webpath }/static/img/sound.png); background-size: 100% 100%; cursor: pointer;">
											<div class="statistic-title">
												<h4
													style="color: #FFF; background-color: #23b7e5; margin: 80px -20px 0 -20px; padding: 5px 0;">录音</h4>
											</div>
										</div>
									</div>
								</div>
							</div>

						</div>
					</div>
				</div>
			</div>
			<!--./第三层任务办理row-->

			<!--第二层附件证据 row-->
			<div class="row">
				<div class="col-lg-12">
					<div class="smart-widget widget-light-grey">
						<div class="smart-widget-header font-16">
							<i class="fa fa-comment"></i> 附件证据 
							<%-- <span
								class="smart-widget-option" style="margin-top: -3px;">
								<button type="button" class="btn btn-info btn-sm" data-toggle="modal"
									data-target="#uploadFile" data-remote="${webpath}/tmEvidenceCollect/fileUploadPage?taskId=${lawObj.taskId}">抽取</button> <span
								class="refresh-icon-animated"><i
									class="fa fa-circle-o-notch fa-spin"></i></span> 
							</span> --%>
						</div>
						<div class="smart-widget-inner table-responsive">
								<div class="smart-widget-hidden-section">
									<ul class="widget-color-list clearfix">
										<li style="background-color: #20232b;"
											data-color="widget-dark"></li>
										<li style="background-color: #4c5f70;"
											data-color="widget-dark-blue"></li>
										<li style="background-color: #23b7e5;"
											data-color="widget-blue"></li>
										<li style="background-color: #2baab1;"
											data-color="widget-green"></li>
										<li style="background-color: #edbc6c;"
											data-color="widget-yellow"></li>
										<li style="background-color: #fbc852;"
											data-color="widget-orange"></li>
										<li style="background-color: #e36159;" data-color="widget-red"></li>
										<li style="background-color: #7266ba;"
											data-color="widget-purple"></li>
										<li style="background-color: #f5f5f5;"
											data-color="widget-light-grey"></li>
										<li style="background-color: #fff;" data-color="reset"></li>
									</ul>
								</div>
								<div class="smart-widget-body form-horizontal no-padding no-margin col-md-10 col-xs-offset-1">
									<div style="color: red;padding: 10px;"><span id="numberSize"></span></div>
									<ul id="sortable" class="Sortable_block__list Sortable_block__list_words no-padding no-margin">
										<li  v-for="(item, index) in items"  :id ="'content'+index" style="cursor:default" >
											<span class="Sortable_zfwsgl_right" style="width: 10%; display: inline-block;"></span>
											<span class="Sortable_zfwsgl_left" style="width: 50%; display: inline-block;">类型：{{item.codeName}}</span>
											
											<template v-if="item.code == '93'">
												 <span class="Sortable_zfwsgl_right" style="width: 50px; cursor:pointer"><button type="button" class="btn btn-info btn-xs" onclick="extractFile()">抽取</button> </span>
											</template>
											<template v-if="item.code != '93'">
												 <span class="Sortable_zfwsgl_right" style="width: 36px; display: inline-block;">&nbsp;</span>
											</template>
											<span class="Sortable_zfwsgl_right" style="width: 10px; display: inline-block;"></span>
											<span class="Sortable_zfwsgl_right" style="width: 30px; display: inline-block; cursor:pointer"><button type="button" class="btn btn-info btn-xs" v-on:click="showAttach(item.code,item.codeName)">附件管理</button></span>
											<span class="Sortable_zfwsgl_right" style="width: 50px; display: inline-block;"></span>
											<span class="Sortable_zfwsgl_right" style="width: 90px; display: inline-block;">{{item.size}}M</span>
										</li>			
									</ul>
								</div>
						<%-- <div class="smart-widget-inner table-responsive">
							<div class="smart-widget-body form-horizontal">
                              <div class="form-group">
								<c:if test="${not empty attachmentList}">  
									<c:forEach items="${attachmentList}" var="obj"  varStatus="status">  
										 <c:if test="${status.count eq 1 || (status.count-1) % 6 eq 0}">    
									     </c:if>
									     <c:if test="${obj.attachmentType==0}">
									     <div class="col-lg-3 col-md-3 col-sm-3">
											<div class="pricing-widget clean-pricing"  style="cursor: pointer;">
												<div class="pricing-value">
															<span class="value"><img style="height:150px;" data-toggle="modal"  data-remote="${webpath}/tmEvidenceCollect/showFileModal?id=${obj.id}" data-target="#fileShowModel"  src="${webpath}/static/img/pdf-thumb.jpg" /></span>
												</div>
												<ul class="padding-sm" style="margin-top: -20px;">
													<li class="text-center">文件类型：${obj.evidenceTypeName}</li>
													<li class="text-center" style="margin-top:10px;">
														<a href="${webpath}/tmEvidenceCollect/fileDownLoad?id=${obj.id}"><button type="button" class="btn btn-info btn-sm"
															data-dismiss="modal">下载</button></a>
														<button type="button" class="btn btn-info btn-sm"
															onclick="deleteFile('${obj.id}')">删除</button>
													</li>
												</ul>
											</div>
										 </div>
									     </c:if>
									     <c:if test="${obj.attachmentType==1}">
										     <div class="col-lg-3 col-md-3 col-sm-3">
												<div class="pricing-widget clean-pricing" style="cursor: pointer;">
													<div class="pricing-value">
														<span class="value"><img style="height:150px;" data-toggle="modal"  data-remote="${webpath}/tmEvidenceCollect/showFileModal?id=${obj.id}" data-target="#fileShowModel"  src="${FASTDFS_ADDR}/${obj.picUrl}" /></span>
													</div>
													<ul class="padding-sm" style="margin-top: -20px;">
		
														<li class="text-center">文件类型：${obj.evidenceTypeName}</li>
														<li class="text-center" style="margin-top:10px;">
															<a href="${webpath}/tmEvidenceCollect/fileDownLoad?id=${obj.id}"><button type="button" class="btn btn-info btn-sm"
																data-dismiss="modal">下载</button></a>
															<button type="button" class="btn btn-info btn-sm"
																onclick="deleteFile('${obj.id}')">删除</button>
														</li>
													</ul>
												</div>
											</div>
									     </c:if>
										 <c:if test="${status.count % 6 eq 0 || status.count eq 6}">    
								         </c:if>   
									</c:forEach>
								</c:if>
                                </div>
							</div>
						</div> --%>
					</div>
				</div>
			</div>
			<!--./第二层任务办理row-->
		</div>
<!-- 附件上传 modeler 开始 -->
	<div class="modal fade" id="uploadFile" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
	<div class="modal fade" id="inputImgModeler" tabindex="-1" role="dialog"
		aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
			</div>
		</div>
	</div>
 	<!-- 模态框（Modal）
	<div class="modal fade" id="fileShowModel" tabindex="-1" role="dialog" 
	   aria-labelledby="fileModalLabel" aria-hidden="true">
		   <div class="modal-dialog  modal-lg">
	        <div class="modal-content">
	        </div>
	        /.modal-content
	      </div>
	     /.modal-dialog
	</div> -->
	
	<script type="text/javascript">
		//点击附件管理
		function showAttach(code,codeName){
			var taskId = "";
			taskId = '${lawObj.taskId}';
			
			var options = {
				remote:encodeURI(WEBPATH+'/tmEvidenceCollect/seeFiles?taskId='+taskId+'&code='+code+'&codeName='+codeName)
			  };
		$('#uploadFile').modal(options);
		}
	</script>
	
	
	
	
	<!-- 照片编辑modeler 结束 -->
	
</body>
</html>
