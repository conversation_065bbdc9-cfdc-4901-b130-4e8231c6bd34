<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="sec"
	uri="http://www.springframework.org/security/tags"%>
<html>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<sec:authentication property="principal" var="authentication" />
<body>


	<div class="modal-header">
		<div style="float: right; margin-top: -5px;">
			<!--<button type="button" class="btn btn-info" data-dismiss="modal">确定</button>-->
			<button type="button" class="btn btn-default" data-dismiss="modal"
				id='closeBtn'>关闭</button>
		</div>
		<h4 class="modal-title" id="myModalLabel">系统推荐模板</h4>
	</div>
	<div class="modal-body">
		<div class="smart-widget-body form-horizontal">
			<div class="form-group">
			<input type="hidden" id="isStatus" value="${status }">
				<label class="control-label col-lg-2">名称</label>
				<div class="col-lg-3">
					<input type="text" placeholder="名称" id="templateName"
						name="templateName" class="form-control"
						data-parsley-required="true">
				</div>
				<label class="control-label col-lg-2">模板编号</label>
				<div class="col-lg-3">
					<input type="text" placeholder="模板编号" id="templateNumber"
						name="templateNumber" class="form-control"
						data-parsley-required="true">
				</div>
			</div>
			<div class="form-group">
				<label class="control-label col-lg-2">创建日期</label>
				<div class="col-lg-3" style="padding: 0px;">
					<div class="col-md-6">
						<input type="text" placeholder="开始时间" id="createStartTime"
							name="createStartTime" class="form-control" readonly="readonly">
					</div>
					<div class="col-md-6">
						<input type="text" placeholder="结束时间" id="createEndTime"
							name="createEndTime" class="form-control" readonly="readonly">
					</div>
				</div>
				<label class="control-label col-lg-2">适用对象类型</label>
				<div class="col-lg-3">
					<select class="form-control" id="templateObjectType"
						name="templateObjectType">
						<option value="">请选择</option>
                        <option value="0">通用</option>
						<option value="1">企事业单位</option>
                        <option value="2">个人</option>
                        <option value="3">个人三无小三产</option>
                        <option value="4">自然保护区</option>
						<option value="6">水源地</option>
						<option value="5">无主</option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label for="行业类型" class="col-lg-2 col-md-2 control-label">适用行业</label>
				<div class="col-lg-5">
					<div class="input-group" style="width: 215px; float: left;">
						<input type="hidden" class="form-control" readonly="readonly"
							id="templateIndustry" name="templateIndustry"> 
							<input type="text" class="form-control" readonly="readonly"
							id="templateIndustryName" name="templateIndustryName">
						<div class="input-group-btn">
							<button type="button" class="btn btn-info no-shadow" id ="industryModelBtn">选择</button>
						</div>
					</div>
					<label class="col-lg-2 col-md-2 control-label"></label>
					<div class="custom-checkbox" style="margin: 7px 0px 0 10px;">
					<input type="checkbox" id='allIndustry' name="allIndustry" value='1'> <label
						for="allIndustry" class="checkbox-blue"></label>
					</div>
					所有行业
				</div>
				 <div class="col-lg-3 text-right">
					<button class="btn btn-info" type="button" style="width: 80px;"
						id="refineTemplateSearch">查询</button>

				</div>
			</div>

			
			<div style="margin: 10px 20px; color: red;">提示：双击模板列表行进行选择</div>
			<table class="table table-no-bordered table-hover table-striped" id="sysRefineTempModelTable">
			</table>
		</div>
	</div>
	<div class="modal-footer"></div>


	<!-- ./自定义模板（Modal） -->
	<script type="text/javascript">
	
	$("#industryModelBtn").click(function(){
		 var options  = {
					remote:WEBPATH+'/refineTemplate/enterprises-industry-type-page?code=templateIndustry&name=templateIndustryName&all=allIndustry'
				  };
	    $('#Industrytype').modal(options);
	})
	var pageNumber=1 ;
	var pageSize=15;
		$(document).ready(function() {
			$('#xttjmb').on('hide.bs.modal', function () {
			   $(this).removeData("bs.modal");
			});
			LoadingRefineTemplateItems();
			$('#sysRefineTempModelTable').bootstrapTable('hideColumn', 'id');
			$('#sysRefineTempModelTable').bootstrapTable('hideColumn', 'isDefault');
		});
		//绑定搜索按钮
		$('#refineTemplateSearch').click(function() {
			$('#sysRefineTempModelTable').bootstrapTable('refreshOptions',{pageNumber:pageNumber,pageSize:pageSize});
		});
		function LoadingRefineTemplateItems() {
			$('#sysRefineTempModelTable').bootstrapTable({
				method : 'post',
				dataType : "json",
				url : WEBPATH + '/refineTemplate/sysRefineTemplateList',
				undefinedText : '-',
				pagination : true, // 分页  
				striped : true, // 是否显示行间隔色  
				pageSize : 15, // 设置默认分页为 15
				pageNumber : 1,
				queryParamsType : "",
				clickToSelect:true,
				checkboxHeader:false, //关闭表头全选
				locale : 'zh-CN',
				//clickToSelect : true,
				pageList : [ 5, 10, 20, 30, 50 ], // 自定义分页列表
				singleSelect : false,
				contentType : "application/x-www-form-urlencoded",
				// showColumns : true, // 显示隐藏列  
				sidePagination : "server", //服务端请求
				queryParams:function (params) {
		    	 	var templateNumber=$("#templateNumber").val();
			    	var templateName=$("#templateName").val();
			    	var createStartTime=$("#createStartTime").val();
			    	var createEndTime=$("#createEndTime").val();
			    	var templateObjectType=$("#templateObjectType").val();
			    	var templateIndustry=$("#templateIndustry").val();
		            var temp = {   //这里的键的名字和控制器的变量名必须一直，这边改动，控制器也需要改成一样的
		        		 pageNumber: params.pageNumber,
		                 pageSize: params.pageSize,
		                 templateNumber:templateNumber,
		                 templateName:templateName,
		                 createStartTime:createStartTime,
		                 createEndTime:createEndTime,
		                 templateObjectType:templateObjectType,
		                 templateIndustry:templateIndustry
		            };
		            return temp;
		     },//参数
				uniqueId : "id", // 每一行的唯一标识  
				columns : [{
					field: "id",
					title: "id",
					align: 'center',
				},{  
					field: "templateNumber",
				    title: "模板编号",
				    align: 'center', 
				}, {
					field : "templateName",
					title : "名称",
					align : 'center'
				}, {
					field : "templateObjectType",
					title : "适用对象类型",
					align : 'center',
					formatter : function(value, row, index) {
						var html = '-';
						if(value==0){
							html = '通用';
						}
						if(value==1){
							html='企事业单位';
						}
						if(value==2){
							html='个人';
						}
						if(value==3){
							html='个人三无小三产';
						}
						if(value==4){
							html='自然保护区';
						}
						if(value==4){
							html='水源地';
						}
						if(value==5){
							html='无主';
						}
						return html;
					}
				}, {
					field : "templateIndustryName",
					title : "适用行业",
					align : 'center'
				},{
					field : "createTime",
					title : "创建日期",
					align : 'center',
					formatter : function(value, row, index) {
						var time = value;
						if (time == null) {
							return "";
						} else {
							var date = new Date(time);
							var y = date.getFullYear();
							var m = date.getMonth() + 1;
							var d = date.getDate();
							var h = date.getHours();
							var mm = date.getMinutes(); //获取当前分钟数(0-59)
							return y + '-' + m + '-' +d;
						}
					}
				},{
					field : "operation",
					title : "操作",
					align : 'center',
					formatter: function(value,row,index){
			        	  var html="";
			        	  //alert(row.stsiId);
			        	  var special = '${special}';
			        	  var userId = '${authentication.id}';
			        	  var id = row.id;
			        	 	 //id为主键ID modelType： 1.为系统模板 2.为自定义模板。 0为根据检查项项id查询
			        	 	 html+="<a tabindex='-1' data-toggle='modal' data-remote='${webpath}/refineTemplate/sysRefineTemplate-view-model?id="+id+"&modelType=1&titleName=1"+"' data-target='#mbyl'  style=\"cursor:pointer;\"><i class=\"fa fa-search\" style=\"color: #23b7e5;\">预览</i></a>&nbsp";
			        	 	if(special==null || special == '' || special == undefined){
				        	 	if(row.admUserId == userId ){// 默认
					        		  // 已经设置为默认
					        		  html+="<i class=\"fa fa-file-word-o\" style=\"color:#23b7e5;cursor:pointer;\" onclick=\"defaultModeler('"+row.id+"','1','"+row.templateObjectType+"','1')\"  > 默认加载模板</i>";
					        	  }else{
					        		  html+="<i class=\"fa fa-file-word-o\" style=\"color:#ccc;cursor:pointer;\" onclick=\"defaultModeler('"+row.id+"','0','"+row.templateObjectType+"','1')\"  > 默认加载模板</i>";
					        	}
			        	 	}
			        	  return html;
			           }
				}, {
					field : "isDefault",
					title : "是否为超级模板",
					align : 'center'
				}],
				responseHandler : function(res) {
					return {
						total : res.data.total,
						rows : res.data.list
					};
				},
				onCheck : function(row, $element) {
					//alert(row);
				},//单击row事件
				onUncheck : function(row, $element) {

				},
				onUncheckAll : function(row, $element) {

				},
				onCheckAll : function(row, $element) {

				},
				onRefresh : function() {

				},
				 //双击事件 
	   	        onDblClickCell:function(field,value,row, $element){
	   	        	//获取模板库的id
	   	       	var id=  row.id;
	        	$.ajax({
					cache : true,
					type : "POST",
					url : WEBPATH + '/refineTemplate/sysCheckItemList',
					data : {
						sysModelerId:id,
					},//  form law object
					async : false,
					error : function(request) {
						swal("错误!","获取模板项失败！", "error");
					},
					success : function(data) {
						var status = $("#isStatus").val();
						$("#contributionName").val(row.contributionName);
						$("#templetType").html(data.type);
						$("#templateContributionName").val(row.contributionName);
						if(status =='0'){
							//精细化模板系统
							chickItemData.splice(0,chickItemData.length);
							for(var i =0;i<data.list.length;i++){
								chickItemData.push(data.list[i])
							}
						}else if(status =='1'){
							//自定义
							chickItemData.splice(0,chickItemData.length);
							for(var i =0;i<data.list.length;i++){
								chickItemData.push(data.list[i])
							}
						}else if(status =='2'){
							//现场检查表 系统模板
							dataArr.splice(0,dataArr.length);
							for(var i =0;i<data.list.length;i++){
								 dataArr.push(data.list[i])
							}
						}
						/* if(status =='0'){
							//精细化模板系统
						 addSysChickItemVue.sysChickItem =data.list;
						}else if(status =='1'){
							//自定义
						 addCustorChickItemVue.custorChickItem =data.list;
						}else if(status =='2'){
							//现场检查表 系统模板
							dataArr.splice(0,dataArr.length);
							for(var i =0;i<data.list.length;i++){
								 dataArr.push(data.list[i])
							}
						} */
					}
				});
	        	 $('#xttjmb').modal('hide');
	   	        },
				formatLoadingMessage : function() {
					return "请稍等，正在加载中...";
				},
				formatNoMatches : function() { //没有匹配的结果
					return '无符合条件的记录';
				}
			});
			}
	</script>
	<script type="text/javascript">
	$(function(){
		//当勾选所有行业时，清除行业搜索的值。
		$("#allIndustry").click(function(){
	          if($('input[name="allIndustry"]').prop("checked"))
	          {
	              $("#templateIndustryName").val("所有行业");
	              $("#templateIndustry").val("all");
	          }else{
	             $("#templateIndustryName").val("");
	             $("#templateIndustry").val("");
	             $("input[name=chosseIndustry]").removeAttr('checked');
	          }
	      })
		

	})
	$(document).ready(function(){
		loadArea();
		$("#belong_city").change(function(){
			if ($(this).val() == "") {
				$("#belong_county option").remove();
				$("#belong_county").append("<option value=''>——请选择——</option>"); 
					return;
			}
			var parentCode = $(this).val();
			$.ajax({
				type:"post",
				url:WEBPATH+"/tArea/countyListByCode",
				dataType:"json",
				async:false,
				data:{parentCode:parentCode},
				success:function(data){
					$("#belong_county option").remove();
					$("#belong_county").append("<option value=''>——请选择——</option>"); 
					$.each(data,function(i,item){
						$("#belong_county").append("<option value="+item.code+"  >"+item.name+"</option>"); 
					});
				}
			});
		});
	})
	
				//区划加载 
				function loadArea(){
					var htmlCity = "<option value=''>——请选择——</option>"; 
			  		var htmlCounty = "<option value=''>——请选择——</option>"; 
			  		var swingTagSnapInId = $("#swingTagSnapInId").val();
			  			$.ajax({
			  	            type:"post",
			  	            url:WEBPATH+"/tArea/chickUserArea",
			  	            async:false,
			  	            dataType:"json",
			  	            data:{},
			  	            success:function(data){
			  	            	/*if(data.cityStatus =='1'){*/
			  						//省级用户
			  						$.ajax({
			  							type:"post",
			  							url:WEBPATH+"/tArea/cityList",
			  							dataType:"json",
			  							async:false,
			  							success:function(data){
			  								$("#belong_city").append("<option value=''>——请选择——</option>"); 
			  								$("#belong_county").append("<option value=''>——请选择——</option>"); 
			  								$.each(data,function(i,item){
			  									$("#belong_city").append("<option value="+item.code+">"+item.name+"</option>");
			  								});
			  							}
			  						});
			  	            	/*}else if(data.cityStatus =="2"){
			  	            		//市级用户
			  						$("#belong_city").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
			  	            	    $.ajax({
			  	                        type:"post",
			  	                        url:WEBPATH+"/tArea/countyListByCode",
			  	                        dataType:"json",
			  	                        data:{parentCode:data.cityCode},
			  	                        success:function(data){
			  								$("#belong_county").append("<option value=''>——请选择——</option>"); 
			  								$.each(data,function(i,item){
			  									$("#belong_county").append("<option value="+item.code+"  >"+item.name+"</option>"); 
			  								});
			  	                        }
			  	                    });
			  	            	}else{
			  	            		//县级用户
			  						$("#belong_city").append("<option selected value="+data.cityCode+">"+data.cityName+"</option>");
			  						$("#belong_county").append("<option selected value="+data.countyCode+"  >"+data.countyName+"</option>"); 
			  	            	}*/
			  	            }
			  	        });
				}
	
	//限办日期 插件加载
		$("[name='createStartTime']").datetimepicker({
			     format:'yyyy-mm-dd',
			     clearBtn:true,
			  	 language: 'cn',
				 autoclose : true,
				 minView : 'year',
				 maxView : 'decade',
				 
			}).on('changeDate',function(ev){
				$("[name='createEndTime']").datetimepicker('setStartDate',new Date($("[name='createStartTime']").val()));
				
				$('#datetimepicker').datetimepicker('setEndDate', null);
			});
			
		
		$("[name='createEndTime']").datetimepicker({
			    format:'yyyy-mm-dd',
			    clearBtn:true,
				language: 'cn',
				autoclose : true,
				minView : 'year',
				maxView : 'decade',
				endDate:new Date()
		}).on('changeDate',function(ev){
				$("[name='createStartTime']").datetimepicker('setEndDate',new Date($("[name='createEndTime']").val()));
		});
		
		$(function(){
			//当勾选所有行业时，清除行业搜索的值。
			$("#onlyMeBox").change(function(){
				//当所有行业选中时 进行情况 并清除行业模态窗中的选中
				if($("#onlyMeBox").is(':checked')){
					$("#onlyMe").val("1");
				}else{
					$("#onlyMe").val("");
				}
				//alert($("#onlyMe").val());	
			});
		})	
		
	function refresh(){
			 
			business.addMainContentParserHtml(WEBPATH + '/sys-refineTemplate-model',"");
		}
		// 设置和解除默认 modelerID:模板id    code：1->当前已经是常用 0->当前不是常用      type: 行业类型      usuallyOrDefaultOrDelete:1默认 
		function defaultModeler(modelerID,code,type,operationType){
			if(type ==null || type=='null'){
				type= '';
			}
			 var obj={modelerId:modelerID,code:code,type:type,operationType:operationType};
			 var titleHtml = "";
			 var textHtml = "";
			 var confirmButtonTextHtml = "设置";
			 if (operationType == 1){
				 titleHtml ="设置默认模板";
				 if(code==1){
					 textHtml ="您是否要取消该默认模板？";
				 }else{
					 textHtml ="设定新默认加载模板后原设定模板将不再默认加载！";
				 }
			 }else{
				 swal({title: "操作项不明确" ,text: "",type:"error",allowOutsideClick :true});
				 return ;
			 }
			 swal({
					title : titleHtml,
					text : textHtml,
					type : "warning",
					showCancelButton : true,
					confirmButtonColor : "#DD6B55",
					confirmButtonText : "是的，我要"+confirmButtonTextHtml+"!",
					cancelButtonText : "让我再考虑一下",
					closeOnConfirm : false,
					closeOnCancel : false
				},
				function(isConfirm) {
					if (isConfirm) {
						$.ajax({
			                cache: true,
			                type: "POST",
			                url: WEBPATH+'/refineTemplate/set-default',
			                data:obj,
			                async: false,
			                error: function(request) {
			                    swal({title: "设置失败" ,text: "",type:"error",allowOutsideClick :true});
			                },
			                success: function(data) {
			                  	if(data.result=='success'){
			                  		swal({title: "设置成功" ,text: "",type:data.result,allowOutsideClick :true});
			                  	}else{
			                  		swal({title: data.message ,text: "",type:data.result,allowOutsideClick :true});
			                  	}
			                }
			            });
						// 刷新列表
						$('#sysRefineTempModelTable').bootstrapTable('refresh');
					}else {
						swal({
							title : "已取消",
							text : "您已取消当前操作！",
							type : "error",
							allowOutsideClick :true
						})
				}
			})
		}	
	</script>
</body>
</html> 