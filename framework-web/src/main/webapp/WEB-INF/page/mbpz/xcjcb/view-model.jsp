<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<html>
<body>
	<input type="hidden" id="pageLayer" name="pageLayer" value='${pageLayer }'>
	<div class="modal-header">
		<div style="float: right; margin-top: -5px;">
			<button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
		</div>
		<h4 class="modal-title" id="myModalLabel">模板预览</h4>
	</div>
	<div class="modal-body" id="app">
		<div class="smart-widget-body form-horizontal">
			<div class="container">
				<h3 class="title">
					<c:if test="${titleName==1 }">
							<span>现场检查表</span>
					</c:if>
					<c:if test="${titleName==2 }">
						<span>专项检查表</span>
					</c:if>
				</h3>
			</div>
			<div class="smart-widget-body text-center">
				<div class="row">
					<div class="col-lg-12">
						<table class="table table-striped  no-margin" id="itemTable">
							<c:forEach items="${refineTemplateList}" var="item" varStatus="idx">
								<!--0 单选 -->
								<c:if test="${item.checkItemStatus==0 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12 text-right">
                                                <div class="radio inline-block">
                                                    <div class="custom-radio m-right-xs">
			                                         	<input type="radio" name="item${idx.index }"    value="是" >
	                                     			</div>
	                                     			<div class="inline-block vertical-top">是</div>
	                                     			<div class="custom-radio m-right-xs">
			                                         	<input type="radio" name="item${idx.index }"   value="否" >
			                                        </div>
			                                        <div class="inline-block vertical-top">否</div>
			                                    </div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 1 多选 -->
								<c:if test="${item.checkItemStatus==1 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12 text-right">
												<!-- 存储每次循环时多选数据的key -->
													<c:set var="checkbox" value="checkBox${item.id }"></c:set>
													<!-- 获取key中的数据 -->
													<c:forEach items="${checkBox[checkbox]}" var="checkBox">
										 				<div class="checkbox inline-block">
                                                     		<div class="custom-checkbox">
				                                         		<input type="checkbox" name="item${idx.index }" >
				                                         		<label for="item${idx.index }"  class="checkbox-blue" ></label>
				                                     		</div>
				                                         	<div class="inline-block vertical-top">
				                                         		${checkBox.checkEntryName }
				                                         	</div>
				                                		</div>
				                                    </c:forEach>
	                                     	</div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 2 输入 长文本 -->
								<c:if test="${item.checkItemStatus==2 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;">
										 <c:if test="${fn:length(item.checkItemName)>20 }">  
                         					<c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${fn:substring(item.checkItemName, 0, 20)}...  
                   						 </c:if>  
                  						 <c:if test="${fn:length(item.checkItemName)<=20 }">  
                         					<c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }
                   						 </c:if>  
										 </td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
	                                         	<textarea class="form-control" rows="8" ></textarea>
	                                     	</div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 3 下拉 -->
								<c:if test="${item.checkItemStatus==3 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12 text-right">
	                                         	<!-- 存储每次循环时多选数据的key -->
													<c:set var="combobox" value="comboBox${item.id }"></c:set>
													<!-- 获取key中的数据 -->
													<div class="col-lg-8">
                                                            <div class="custom-radio m-right-xs">
				                                         		<select class="form-control" style="width: 150px;">	
																	<option value="null" >请选择</option>
																		<c:forEach items="${comboBox[combobox]}" var="comboBox">
																		<option value="">${comboBox.checkEntryName }</option>
																		</c:forEach>
																</select> 
				                                		</div>
				                                	</div>
	                                     	</div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 4 输入 单行文本 -->
								<c:if test="${item.checkItemStatus==4 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
	                                         	<input type="text" class="form-control" >
	                                     	</div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 5 经纬度 -->
								<c:if test="${item.checkItemStatus==5 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
	                                         	<div class="col-lg-2" style="float:right; width:150px;">
                                                     <button  class="btn btn-info" type="button" data-toggle="modal" data-target="#myModal" id="createMap" >定位</button>
                                                     <button type="button" class="btn btn-info" id="folder" data-container="body" tabindex="-1" data-toggle="popover" data-placement="bottom" data-html="true" data-content="请使用手机App的扫一扫功能<br>扫描以下二维码：<br><img src='../img/1499139993.png' style=' width:200px; height:200px;'><br>已成功读取手机定位信息 <button type='button' class='btn btn-danger btn-xs'>刷新</button>" data-original-title="" title="" aria-describedby="popover70288">手机定位</button>
    											</div>
                                                <div class="col-lg-3" style="float:right;">
                                                     <input type="text" placeholder="纬度" class="form-control" data-parsley-required="true" >
                                                </div>
    											<div class="col-lg-3" style="float:right;">
                                                     <input type="text" placeholder="经度" class="form-control" data-parsley-required="true" >
                                                </div> 
	                                     	</div>
	                                     </td>
                                 	</tr>
								</c:if>
								<!-- 6 单一时间 -->
								<c:if test="${item.checkItemStatus==6 }">
									<c:if test="${item.dateType!=1 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
										 		<div class="col-lg-5" style="float:right;">
										 			<input type="text" placeholder="2017-10-10" class="form-control" data-parsley-required="true" >
										 		</div>
	                                     	</div>
	                                     </td>
                                 	</tr>
                                 	</c:if>
                                 	<c:if test="${item.dateType==1 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
										 		<div class="col-lg-5" style="float:right;">
										 			<input type="text" placeholder="2017-10-10-22:20" class="form-control" data-parsley-required="true" >
										 		</div>
	                                     	</div>
	                                     </td>
                                 	</tr>
                                 	</c:if>
								</c:if>
								<!-- 7 时间段 -->
								<c:if test="${item.checkItemStatus==7 }">
									<c:if test="${item.dateType!=1 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
                                            	<div class="col-lg-5" style="float:right;">
                                                     <input type="text" placeholder="2017-10-11" class="form-control" data-parsley-required="true">
                                                </div>
										 		<div class="col-lg-5" style="float:right;">
                                                     <input type="text" placeholder="2017-10-10" class="form-control" data-parsley-required="true">
                                            	</div> 
	                                     	</div>
	                                     </td>
                                 	</tr>
                                 	</c:if>
                                 	<c:if test="${item.dateType==1 }">
									<tr>
										 <td class="text-left" style="background-color:#f7f7f7; vertical-align:middle;"><c:if test="${item.isMust ==1}"><span style="color: red;">*</span></c:if>${item.checkItemName }</td>
										 <td class="text-center" style="width:50%; background-color:#f7f7f7;">
										 	<div class="col-lg-12">
										 		<div class="col-lg-5" style="float:right;">
                                                     <input type="text" placeholder="2017-10-10-22:20" class="form-control" data-parsley-required="true">
                                            	</div> 
                                            	<div class="col-lg-5" style="float:right;">
                                                     <input type="text" placeholder="2017-10-10-22:20" class="form-control" data-parsley-required="true">
                                                </div>
	                                     	</div>
	                                     </td>
                                 	</tr>
                                 	</c:if>
								</c:if>
							</c:forEach>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="modal-footer"></div>
</body>
<script type="text/javascript">
$(function(){
	$('#mbyl').on('hidden.bs.modal', function () {
		   $(this).removeData("bs.modal");
		   var pageLayer = $("#pageLayer").val();
		   if(pageLayer!=1){
		  		document.getElementsByTagName('body')[0].className = 'modal-open';
		   }
	})
})
</script>
</html> 