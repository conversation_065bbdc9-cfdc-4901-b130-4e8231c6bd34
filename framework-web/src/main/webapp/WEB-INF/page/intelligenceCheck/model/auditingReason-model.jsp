<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html lang="en">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Insert title here</title>
</head>
<body>
	<div class="modal-header">
		<div style="float: right; margin-top: -5px;">
		   <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
		</div>
		<h4 class="modal-title" id="myModalLabel">稽查原因详情</h4>
	</div>
	<div class="modal-body" style="height: 200px; padding-top: 50px;">
		<div class="smart-widget-body form-horizontal">
			<div class="form-group">
				<div class="col-lg-12 font-16 text-center">
					<div id="showinfo" style="font-size: 15px;color: red;">
						<c:choose>
							<c:when test="${not empty reasonDetail  and reasonDetail !=null and reasonDetail !='' and reasonDetail !='null'}">
								 	${reasonDetail }
							</c:when>
							<c:otherwise>
								  抱歉！ 提示信息走丢了！
							</c:otherwise>
						</c:choose>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="modal-footer"></div>
</body>
<script type="text/javascript">
$(function(){
 
})
</script>
</html>