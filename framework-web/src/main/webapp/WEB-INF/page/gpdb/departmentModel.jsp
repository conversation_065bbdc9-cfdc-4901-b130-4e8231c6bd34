<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<html>
	<body>
					<div class="modal-header">
						<div style="float:right; margin-top:-5px;">
                        <button type="button" class="btn btn-info" data-dismiss="modal" onclick="confirmHandlingUnit()">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal" onclick="clearHandlingUnit()">清空</button>
                        </div>
						<h4 class="modal-title" id="">办理单位</h4>
					</div>
					<div class="modal-body">
						<div class="smart-widget-body form-horizontal">
								<div class="form-group">
										<div class="col-lg-6">
											<div >
		                                           <ul id="treeDept" class="ztree"></ul>
											</div>
										</div>
								</div>
						</div>
					</div>
					<div class="modal-footer">
						<!--<button type="button" class="btn btn-info" data-dismiss="modal" onclick="confirmHandlingUnit()">确定</button>
						<button type="button" class="btn btn-default" data-dismiss="modal" onclick="clearHandlingUnit()">清空</button>-->
					</div>
		<script type="text/javascript">
		//初始化部门
		var settingUnit = {
			data: {
				key: {
					title:"t"
				},
				simpleData: {
					enable: true
				}
			},
			callback: {
				onClick: onClickUnit
			}
		};
		var zNodesUnit;
		var depObj;
		function onClickUnit(event, treeId, treeNode, clickFlag) {
			depObj={id:null,swingTagId:swingTagId,manageUnitName:treeNode.name,manageUnitId:treeNode.id,
					limitTime:null,manageAreaCode:treeNode.belongAreaCode,manageAreaName:treeNode.belongAreaName,delmark:null,swingTagSubjectList:null}
			//$("#researchOrgId").val(treeNode.id);
			//$("#researchOrgName").val(treeNode.name);
		}
		$.ajax({
			type: "Post",
			url: WEBPATH+"/swingtagmanage/getSwingTagDepts",
			data:{
			},
			/* data:{queryType:1}, */
			success: function (strReult) {
				//alert(strReult.type);
				if(strReult.type=="success"){
					zNodesUnit=eval(strReult.data);
					$.fn.zTree.init($("#treeDept"), settingUnit, zNodesUnit);
					//默认选中第一个节点
	                var treeObj = $.fn.zTree.getZTreeObj("treeDept");
					
				//	selDeptID = '${authentication.belongDepartmentId}';
					
	              //  var selNode = treeObj.getNodeByParam("id", selDeptID, null);
	                
	              //  treeObj.selectNode(selNode);//选择点 
	              //  treeObj.expandNode(selNode,true);
				}else{
					swal("服务异常，部门数据请求失败!", "", "warning");
				}
			},
			error: function(){
		   		swal("网络异常，请求数据失败!", "", "error");
			}
		});
		//确认按钮
		function confirmHandlingUnit(){
			var swingTagSnapInList = swingTagVue.swingTagSnapInList;
			if(swingTagSnapInList != null){
				for(var i=0;i<swingTagSnapInList.length;i++){
					if(swingTagSnapInList[i].manageUnitId ==depObj.manageUnitId){
						swal("提示信息!", depObj.manageUnitName+" 办理单位已经选择，请选择其他办理单位！", "info");
						return fasle;
					}
				}
			}
			var limitTime =null;
			if(swingTagSnapInList !=null && swingTagSnapInList.length>0){
				limitTime =swingTagVue.swingTagSnapInList[swingTagSnapInList.length-1].limitTime
				depObj.limitTime =limitTime;
			}
			swingTagSnapInList.push(depObj);
			console.log(swingTagSnapInList);
		}
		</script>
	</body>
</html>
