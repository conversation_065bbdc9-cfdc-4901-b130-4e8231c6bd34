<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@page import="org.changneng.framework.frameworkcore.utils.PropertiesHandlerUtil"%>
<c:set var="webpath">${pageContext.request.contextPath}</c:set>
<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script src="${webpath }/static/TaskManager/ReadOnlyBusinessFlow.js"></script>
</head>
<body>
<!--执法任务列表-->
<div class="main-container">
    <div id="listCon" class="padding-md">
        <div class="row">
            <div class="col-lg-12">
                <div class="smart-widget widget-blue">
                    <div class="smart-widget-header font-16">
                        <i class="fa fa-arrow-right"></i> 关联执法及案件
                        <span class="smart-widget-option">
                            <i class="fa fa-chevron-left"></i>
                            <a href="#" onclick="goBack('${preUrl}')" class="font-16">返回</a>
                        </span>
                    </div>
                    <div class="smart-widget-inner table-responsive">
                        <div class="smart-widget-hidden-section">
                            <ul class="widget-color-list clearfix">
                                <li style="background-color:#20232b;" data-color="widget-dark"></li>
                                <li style="background-color:#4c5f70;" data-color="widget-dark-blue"></li>
                                <li style="background-color:#23b7e5;" data-color="widget-blue"></li>
                                <li style="background-color:#2baab1;" data-color="widget-green"></li>
                                <li style="background-color:#edbc6c;" data-color="widget-yellow"></li>
                                <li style="background-color:#fbc852;" data-color="widget-orange"></li>
                                <li style="background-color:#e36159;" data-color="widget-red"></li>
                                <li style="background-color:#7266ba;" data-color="widget-purple"></li>
                                <li style="background-color:#f5f5f5;" data-color="widget-light-grey"></li>
                                <li style="background-color:#fff;" data-color="reset"></li>
                            </ul>
                        </div>
                        <div class="smart-widget-body form-horizontal">
                            <div class="col-lg-10 col-lg-offset-1 padding-xs">
                                <h4 style="float:left;">关联执法列表</h4>
                                <div style="float:left; margin-left:20px;">
                                    <button onclick="linkLawModeler()" style="margin-top:5px;" type="button" class="btn btn-info no-shadow">添加关联</button>
                                </div>
                                <div style="float:left; margin-left:20px;">
                                    <button onclick="addLawModeler()" style="margin-top:5px;" type="button" class="btn btn-info no-shadow" >分配执法</button>
                                </div>
                            </div>

                            <%--vue 分配执法列表--%>
                            <div id="taskVueList" >
                                <div v-for="(zfglList, index) in zfglList">
                                    <div class="cfjd_panel col-lg-10 col-lg-offset-1">
                                        <div class="cfjd_titlt">
                                            <div style="margin-left: -50px;">{{zfglList.taskid}}</div>
                                            <div style="float:right; margin-top:-30px;margin-left: 30px;margin-right:-50px "><a v-on:click="linkToTask(zfglList.xftaskid)" style="margin-top:5px;" type="button" class="btn btn-info btn-sm no-shadow" tabindex="-1">详情</a></div>
                                        </div>
                                        <div style="float:right; margin-top:-30px;">
                                            <button style="margin-top:5px;" type="button" v-on:click="reset(zfglList.xftaskid,'0')" class="btn btn-danger no-shadow" tabindex="-1">取消该关联</button>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 control-label">现场执法时间</label>
                                            <div class="col-lg-8" style="margin-top:7px;">
                                                {{zfglList.lawenforcementstarttime}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">执法对象</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{zfglList.handlingunitname}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">执法单位</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{zfglList.lawobjectname}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-for="(tasklist, index) in taskList">
                                    <div class="cfjd_panel col-lg-10 col-lg-offset-1">
                                        <div class="cfjd_titlt">
                                            <div style="margin-left: -50px;"> {{tasklist.taskid}}</div>
                                            <div style="float:right; margin-top:-30px;margin-right: -50px;"><a v-on:click="linkToTask(tasklist.xftaskid)" style="margin-top:5px;" type="button" class="btn btn-info btn-sm no-shadow" tabindex="-1">详情</a></div>
                                        </div>
                                        <%--<div style="float:right; margin-top:-30px;">--%>
                                            <%--<button style="margin-top:5px;" type="button" v-on:click="reset(tasklist.taskid,'0')" class="btn btn-danger no-shadow" tabindex="-1">取消该关联</button>--%>
                                        <%--</div>--%>
                                        <div class="form-group">
                                            <label class="col-lg-2 control-label">现场执法时间</label>
                                            <div class="col-lg-8" style="margin-top:7px;">
                                                <!--  {{tasklist.taskCreateTime |time}} -->
                                                {{tasklist.lawenforcementstarttime}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">执法对象</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{tasklist.handlingunitname}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">执法单位</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{tasklist.lawobjectname}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- vue执法列表结束 -->
                            <div class="col-lg-10 col-lg-offset-1 padding-xs"><hr></div>
                            <div class="col-lg-10 col-lg-offset-1 padding-xs">
                                <h4 style="float:left;">关联案件列表</h4>
                                <div style="float:left; margin-left:20px;">
                                    <button onclick="linkCaseModeler()" style="margin-top:5px;" type="button" class="btn btn-info no-shadow">添加关联</button>
                                </div>
                            </div>
                            <!-- vue关联列表 -->
                            <div id="caseVueList">
                                <div v-for="(caselist, index) in caseList">
                                    <div class="cfjd_panel col-lg-10 col-lg-offset-1">
                                        <div class="cfjd_titlt">
                                            <div style="margin-left: -50px;">{{caselist.caseNamber}}</div>
                                            <div style="float:right; margin-top:-30px;margin-left: 30px;margin-right:-50px "><a v-on:click="linkToCase(caselist.caseid)" style="margin-top:5px;" type="button" class="btn btn-info btn-sm no-shadow" tabindex="-1">详情</a></div>
                                        </div>
                                        <div style="float:right; margin-top:-30px;">
                                            <button style="margin-top:5px;" type="button" v-on:click="delCase(caselist.caseid)" class="btn btn-danger no-shadow" tabindex="-1">取消该关联</button>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 control-label">创建日期</label>
                                            <div class="col-lg-8" style="margin-top:7px;">
                                                {{caselist.createTime}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">案件当事人</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{caselist.lawobjectname}}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-2 col-sm-3 col-xs-5 control-label">处理主体</label>
                                            <div class="col-lg-8 col-sm-8 col-xs-12" style="margin-top:7px;">
                                                {{caselist.punishSubject}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div><!-- vue结束 -->
                            <div class="col-lg-10 col-lg-offset-1 padding-xs">&nbsp;</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- //模态框 -->
<div class="modal fade" id="linkLawModeler"  role="dialog"
     aria-labelledby="fileModalLabel" aria-hidden="true">
    <div class="modal-dialog  modal-lg">
        <div class="modal-content"></div>
    </div>
</div>
<div class="modal fade" id="linkCaseModeler"  role="dialog"
     aria-labelledby="fileModalLabel" aria-hidden="true">
    <div class="modal-dialog  modal-lg">
        <div class="modal-content"></div>
    </div>
</div>
</body>

<script type="text/javascript">
var webpath = '${webpath}';
var lawObjectId = '${lawObjectId}';
var tsdxmc='${tsdxmc}';
var ajid = '${ajid}';
var listCon;
//监测日期
var pageNum = '${pageNum}';
//分配任务
function addLawModeler(){
	// 	var options1= {
	// 		    remote:WEBPATH+'/taskGeneral/task-monitor-page?lawObjectId='+lawObjectId+"&lawID="+lawID
	// };
    business.addMainContentParserHtml(WEBPATH+'/xfzf/lowerTask',null);
    //sessionStorage.setItem('rowCon',JSON.stringify(row))
	//$('#linkLawModeler').modal(options1);
}
//关联执法
function linkLawModeler(){
    business.addMainContentParserHtml(WEBPATH+'/xfzf/linkLaw?tsdxmc='+tsdxmc, null);

    //sessionStorage.setItem('Ajid',Ajid)
	// 	var options2= {
	// 		    remote:WEBPATH+'/ajtz/case-monitor-page?lawObjectId='+lawObjectId+"&lawID="+lawID
	// };
	// $('#linkCaseModeler').modal(options2);
}
function linkCaseModeler(){
    var options2= {
        remote:WEBPATH+'/xfzf/case-monitor-page?tsdxmc='+tsdxmc+'&ajid='+ajid
    };
    $('#linkCaseModeler').modal(options2);
}
//返回上一步主菜单
function goBack(preUrl) {
    debugger
	if(preUrl != null && preUrl != '' && preUrl != 'undefined'){
		business.addMainContentParserHtml(WEBPATH+preUrl+"?back=1", null);
	} else {
		swal({
			title : "提示！",
			text : "返回信息错误，请刷新后重试。",
			type : "error",
			allowOutsideClick :true
		})
	}
	sessionStorage.clear();
}
$(document).ready(function(){
	//vue list
     listCon = new Vue({
        el : '#listCon',
        data : {
            taskList:[],
            zfglList:[],
            caseList:[]
        },
        created:function () {
            this.getFormCon();
        },
        methods : {
            getFormCon() {
                let vm = this;
                let data = JSON.parse(sessionStorage.getItem('objData'));
                vm.ajid = sessionStorage.getItem('Ajid');
                $.ajax({
                    dataType: "json",
                    type: "POST",
                    url: webpath + "/xfzf/zfrwdataList",
                    data: {
                        ajid: vm.ajid
                    },
                    success: function (data) {
                        console.log(JSON.parse(data.data.guanlianjson))
                        if (data.meta.statusCode == '200') {
                            vm.caseList = JSON.parse(data.data.casejson);
                            vm.zfglList = JSON.parse(data.data.guanlianjson);
                            vm.taskList = JSON.parse(data.data.zhifajson);
                        } else {
                            swal({title: "加载失败", text: data.meta.message, type: "error", allowOutsideClick: true});
                        }
                    }
                })
            },
            reset(id, type) {
                let vm = this;
                swal({
                    title: "您确定执行此操作吗？",
                    text: "您确定要取消当前关联吗？",
                    type: "warning",
                    showCancelButton: true,
                    closeOnConfirm: false,
                    confirmButtonText: "是的，我要取消",
                    confirmButtonColor: "#ec6c62"
                }, function () {
                    $.ajax({
                        url: webpath + "/xfzf/linkRelation",
                        type: "POST",
                        data: JSON.stringify({taskid: id, insert: type, ajid: vm.ajid}),
                        dataType: "json"
                    }).done(function (data) {
                        if (data.meta.statusCode == "200") {
                            swal({title: "操作成功!", text: "已成功取消！", type: "success", allowOutsideClick: true});
                            //business.addMainContentParserHtml(WEBPATH+'overlaw/linkCaseAndLaw?lawObjectId='+lawObjectId+"&lawID="+lawID, null);
                            vm.getFormCon();
                        } else if (data.meta.statusCode == "500") {
                            swal({title: "发生错误！", text: "", type: "error", allowOutsideClick: true});
                        } else {
                            swal({title: "发生错误！", text: "", type: "error", allowOutsideClick: true});
                        }

                    }).error(function (data) {
                        swal({title: "OMG", text: "取消操作失败了!", type: "error", allowOutsideClick: true});
                    });
                });
            },
            delCase(caseid) {
                let vm = this;
                vm.ajid = sessionStorage.getItem('Ajid');
                console.log(vm.ajid,caseid);
                swal({
                    title: "您确定执行此操作吗？",
                    text: "您确定要取消当前关联吗？",
                    type: "warning",
                    showCancelButton: true,
                    closeOnConfirm: false,
                    confirmButtonText: "是的，我要取消",
                    confirmButtonColor: "#ec6c62"
                }, function () {
                    $.ajax({
                        url: webpath + "/xfzf/delCaseLink",
                        type: "POST",
                        data: {caseid: caseid, ajid: vm.ajid},
                        dataType: "json"
                    }).done(function (data) {
                        if (data.code == "001") {
                            swal({title: "操作成功!", text: "已成功取消！", type: "success", allowOutsideClick: true});
                          //  business.addMainContentParserHtml(WEBPATH+'xfzf/zfrwList?tsdxmc='+tsdxmc+"&ajid="+ajid, null);
                            vm.getFormCon();
                        } else if (data.meta.statusCode == "500") {
                            swal({title: "发生错误！", text: "", type: "error", allowOutsideClick: true});
                        } else {
                            swal({title: "发生错误！", text: "", type: "error", allowOutsideClick: true});
                        }

                    }).error(function (data) {
                        swal({title: "OMG", text: "取消操作失败了!", type: "error", allowOutsideClick: true});
                    });
                });
            }
        }
    })
})
function startTask(){
	$.ajax({
		data:{lawID:lawID,sign:'1'},
		dataType:"json",
		type:"POST",
		url:webpath+"/overlaw/judgeCaseTask",
		success:function(data){
			if(data.code == '500'){
				swal({title:'提示',text:data.message,type:'info',allowOutsideClick :true});
			}else{
				 business.addMainContentParserHtml(WEBPATH+'/jcbl/startLocaleChick?lawObjectId='+lawObjectId+'&menuId=4AE820E38F226585E055000000000001&taskFromType=1&status=5',null);
			}
		}
	})
	
}
function startCase(lawObjectId){
	$.ajax({
		data:{lawID:lawID,sign:'2'},
		dataType:"json",
		type:"POST",
		url:webpath+"/overlaw/judgeCaseTask",
		success:function(data){
			if(data.code == '500'){
				swal({title:'提示',text:data.message,type:'info',allowOutsideClick :true});
			}else{
				//macroMgr.onLevelTwoMenuClick(null, 'overlaw/startCase?lawObjectId='+lawObjectId+'&selectType=1&parentUrl=1');
				 business.addMainContentParserHtml(WEBPATH+'/overlaw/startCase?lawObjectId='+lawObjectId+'&selectType=1&parentUrl=1',null);
			}
		}
	})
}
function linkToTask(taskId){
    var obj={taskId:taskId,lawObjectType:'1',parentUrl:'0',nodeCode:'0'};
    business.addMainContentParserHtml(WEBPATH+'/taskManager/xczf?selectType=0',obj);
}
function linkToCase(caseId){
   // console.log(caseId);
    business.addMainContentParserHtml(WEBPATH+'/caseInfo/baseInfoCasePage?caseId='+caseId+"&selectType=1", null);
}
</script>
</html>