

<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>福建环境监察全过程业务智能办理系统</title>
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<meta name="description" content="">
<meta name="author" content="">

<!-- Bootstrap core CSS -->
<link rel="stylesheet" type="text/css"
	href="../libs/bootstrap/3.3.4/css/bootstrap.css">

<!-- Font Awesome -->
<link rel="stylesheet" type="text/css"
	href="../font-awesome/4.7.0/css/font-awesome.min.css">

<!-- ionicons -->


<!-- Morris -->
<link href="../css/morris.css" rel="stylesheet" />

<!-- Datepicker -->
<link href="../css/datepicker.css" rel="stylesheet" />

<!-- Animate -->
<link href="../css/animate.min.css" rel="stylesheet">

<!-- Owl Carousel -->
<link href="../css/owl.carousel.min.css" rel="stylesheet">
<link href="../css/owl.theme.default.min.css" rel="stylesheet">

<!-- Simplify -->
<link href="../css/simplify.min.css" rel="stylesheet">

<link href="../css/style.css" rel="stylesheet">

</head>

<body class="overflow-hidden">
	<div class="wrapper preload">
		<header class="top-nav">
			<div class="top-nav-inner">
				<!--手机端呼出左侧菜单-->
				<div class="nav-header">
					<button type="button"
						class="navbar-toggle pull-left sidebar-toggle"
						id="sidebarToggleSM">
						<span class="icon-bar"></span> <span class="icon-bar"></span> <span
							class="icon-bar"></span>
					</button>
					<!--手机端个人信息-->
					<ul class="nav-notification pull-right">
						<li><a href="#" class="dropdown-toggle"
							data-toggle="dropdown"><i class="fa fa-cog fa-lg"></i></a> <span
							class="badge badge-danger bounceIn">1</span>
							<ul class="dropdown-menu dropdown-sm pull-right user-dropdown">
								<li class="user-avatar"><img src="img/mep.png" alt=""
									class="img-circle">
									<div class="user-content">
										<h5 class="no-m-bottom">管理员</h5>
										<div class="m-top-xs">
											<a href="profile.html" class="m-right-sm">个人空间</a> <a
												href="signin.html">退出系统</a>
										</div>
									</div></li>
								<li><a href="inbox.html"> 我的邮件 <span
										class="badge badge-danger bounceIn animation-delay2 pull-right">1</span>
								</a></li>
								<li><a href="#"> 站内信息 <span
										class="badge badge-purple bounceIn animation-delay3 pull-right">2</span>
								</a></li>
								<li class="divider"></li>
								<li><a href="#">设置</a></li>
							</ul></li>
					</ul>
					<!--logo-->
					<a href="../home.html"> <span><img src="../img/logo.png"
							style="margin: 5px 0 0 0;" /></span>
					</a>
				</div>
				<!--./手机端呼出左侧菜单-->

				<!-- 框架top -->
				<div class="nav-container">
					<button type="button"
						class="navbar-toggle pull-left sidebar-toggle"
						id="sidebarToggleLG">
						<span class="icon-bar"></span> <span class="icon-bar"></span> <span
							class="icon-bar"></span>
					</button>
					<ul class="nav-notification">
						<li class="search-list">
							<div class="search-input-wrapper">
								<div class="search-input">
									<input type="text" class="form-control input-sm inline-block">
									<a href="#" class="input-icon text-normal"><i
										class="ion-ios7-search-strong"></i></a>
								</div>
							</div>
						</li>
					</ul>
					<div class="pull-right m-right-sm">
						<!--用户信息-->
						<div class="user-block hidden-xs">
							<a href="#" id="userToggle" data-toggle="dropdown"> <i
								class="fa fa-user-circle fa-lg"></i>
								<div class="user-detail inline-block">
									管理员您好！ <i class="fa fa-angle-down"></i>
								</div>
							</a>
							<div class="panel border dropdown-menu user-panel">
								<div class="panel-body paddingTB-sm">
									<ul>
										<li><a href="profile.html"> <i
												class="fa fa-edit fa-lg"></i><span class="m-left-xs">个人空间</span>
												<span class="badge badge-danger bounceIn animation-delay3">2</span>
										</a></li>
										<li><a href="signin.html"> <i
												class="fa fa-power-off fa-lg"></i><span class="m-left-xs">退出系统</span>
										</a></li>
									</ul>
								</div>
							</div>
						</div>
						<ul class="nav-notification">
							<!--站内信息-->
							<li><a href="#" data-toggle="dropdown"><i
									class="fa fa-envelope fa-lg"></i></a> <span
								class="badge badge-purple bounceIn animation-delay5 active">2</span>
								<ul class="dropdown-menu message pull-right">
									<li><a>你有4个新的未读邮件</a></li>
									<li><a class="clearfix" href="#"> <img
											src="img/mep.png" alt="User Avatar">
											<div class="detail">
												<strong>环保厅</strong>
												<p class="no-margin">关于待办任务的执行流程与规则...</p>
												<small class="text-muted"><i
													class="fa fa-check text-success"></i> 2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> <img
											src="img/mep.png" alt="User Avatar">
											<div class="detail">
												<strong>环保厅</strong>
												<p class="no-margin">关于待办任务的执行流程与规则...</p>
												<small class="text-muted"><i
													class="fa fa-check text-success"></i> 2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> <img
											src="img/mep.png" alt="User Avatar">
											<div class="detail m-left-sm">
												<strong>环保厅</strong>
												<p class="no-margin">关于待办任务的执行流程与规则...</p>
												<small class="text-muted"><i class="fa fa-reply"></i>
													2017-3-1</small>
											</div>
									</a></li>
									<li><a class="clearfix" href="#"> <img
											src="img/mep.png" alt="User Avatar">
											<div class="detail">
												<strong>环保厅</strong>
												<p class="no-margin">关于待办任务的执行流程与规则...</p>
												<small class="text-muted"><i class="fa fa-reply"></i>
													2017-3-1</small>
											</div>
									</a></li>
									<li><a href="#">查看所有消息</a></li>
								</ul></li>
							<!--信息提醒-->
							<li><a href="#" data-toggle="dropdown"><i
									class="fa fa-bell fa-lg"></i></a> <span
								class="badge badge-info bounceIn animation-delay6 active">4</span>
								<ul class="dropdown-menu notification dropdown-3 pull-right">
									<li><a href="#">你有5个新的通知</a></li>
									<li><a href="#"> <span
											class="notification-icon bg-warning"> <i
												class="fa fa-warning"></i>
										</span> <span class="m-left-xs">#2服务器没有响应。</span> <span
											class="time text-muted">2分钟前</span>
									</a></li>
									<li><a href="#"> <span
											class="notification-icon bg-success"> <i
												class="fa fa-plus"></i>
										</span> <span class="m-left-xs">新用户注册。</span> <span
											class="time text-muted">30分钟前</span>
									</a></li>
									<li><a href="#"> <span
											class="notification-icon bg-danger"> <i
												class="fa fa-bolt"></i>
										</span> <span class="m-left-xs">应用程序错误。</span> <span
											class="time text-muted">1小时前</span>
									</a></li>
									<li><a href="#"> <span
											class="notification-icon bg-success"> <i
												class="fa fa-usd"></i>
										</span> <span class="m-left-xs">应用程序错误。</span> <span
											class="time text-muted">2小时前</span>
									</a></li>
									<li><a href="#"> <span
											class="notification-icon bg-success"> <i
												class="fa fa-plus"></i>
										</span> <span class="m-left-xs">新用户注册。</span> <span
											class="time text-muted">5小时前</span>
									</a></li>
									<li><a href="#">查看所有的通知</a></li>
								</ul></li>
						</ul>
					</div>
				</div>
			</div>
		</header>
		<!-- 框架左侧菜单 -->
		<aside class="sidebar-menu fixed">
			<div class="sidebar-inner scrollable-sidebar">
				<div class="main-menu">
					<ul class="accordion">
						<li class="menu-header">Main Menu</li>
						<li class="bg-palette2 active"><a href="../home.html"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-home fa-lg"></i></span> <span
									class="text m-left-sm">系统首页</span>
							</span> <span class="menu-content-hover block"> 系统首页 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-edit fa-lg"></i></span> <span
									class="text m-left-sm">监察办理</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 监察办理 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-label">业务办理</span>
								</a>
									<ul class="submenu third-level">
										<li class="active"><a href="rwbl.html"><span
												class="submenu-label">任务办理</span></a></li>
										<li><a href="rwfp.html"><span class="submenu-label">任务分配</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-label">任务管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">任务计划管理</span></a></li>
										<li><a href="#"><span class="submenu-label">任务综合台账</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-label">智能稽查</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">稽查对象智能筛选</span></a></li>
										<li><a href="#"><span class="submenu-label">执法与处罚规范性分析</span></a></li>
										<li><a href="#"><span class="submenu-label">电子稽查规则配置</span></a></li>
									</ul></li>
							</ul></li>
						<li class="openable bg-palette2 open"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-commenting-o fa-lg"></i></span> <span
									class="text m-left-sm">执法对象</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 执法对象 </span>
						</a>
							<ul class="submenu">
								<li><a href="../zfdx-all.html"><span
										class="submenu-label">所有对象管理</span></a></li>
								<li><a href="../zfdx-enterprise.html"><span
										class="submenu-label">企业事业单位</span></a></li>
								<li><a href="../zfdx-individual.html"><span
										class="submenu-label">个人</span></a></li>
								<li><a href="../zfdx-individual-Three.html"><span
										class="submenu-label">个人、三无、小三产</span></a></li>
								<li><a href="../zfdx-natureReserve.html"><span
										class="submenu-label">自然保护区</span></a></li>
								<li><a href="../zfdx-stray.html"><span
										class="submenu-label">无主对象</span></a></li>

							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-commenting-o fa-lg"></i></span> <span
									class="text m-left-sm">异常线索</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 异常线索 </span>
						</a>
							<ul class="submenu">
								<li><a href="#"><span class="submenu-label">多源排放数据对比分析</span></a></li>
								<li><a href="#"><span class="submenu-label">自动监控与自行监测数据相关性分析</span></a></li>
							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-globe fa-lg"></i></span> <span
									class="text m-left-sm">GIS应用</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> GIS应用 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-label">GIS应用</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">污染源分步</span></a></li>
										<li><a href="#"><span class="submenu-label">任务统计</span></a></li>
										<li><a href="#"><span class="submenu-label">代办统计</span></a></li>
									</ul></li>
							</ul></li>
						<li class="bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-check-square-o fa-lg"></i></span> <span
									class="text m-left-sm">监察考核</span>
							</span> <span class="menu-content-hover block"> 监察考核 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-bar-chart fa-lg"></i></span> <span
									class="text m-left-sm">统计分析</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 统计分析 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">5</small>
										<span class="submenu-label">统计分析</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">综合统计</span></a></li>
										<li><a href="#"><span class="submenu-label">日历展示</span></a></li>
										<li><a href="#"><span class="submenu-label">决策支持</span></a></li>
										<li><a href="#"><span class="submenu-label">规范性分析</span></a></li>
										<li><a href="#"><span class="submenu-label">超标处置</span></a></li>
									</ul></li>
							</ul></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-users fa-lg"></i></span> <span
									class="text m-left-sm">监察队伍</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 监察队伍 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">1</small>
										<span class="submenu-label">机构查询</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">机构查询</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-label">人员查询</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">人员查询</span></a></li>
										<li><a href="#"><span class="submenu-label">执法证件</span></a></li>
										<li><a href="#"><span class="submenu-label">培训记录</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">1</small>
										<span class="submenu-label">本地人员管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">人员管理</span></a></li>
									</ul></li>
							</ul></li>
						<li class="bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-book fa-lg"></i></span> <span
									class="text m-left-sm">环保智库</span>
							</span> <span class="menu-content-hover block"> 环保智库 </span>
						</a></li>
						<li class="openable bg-palette2"><a href="#"> <span
								class="menu-content block"> <span class="menu-icon"><i
										class="block fa fa-cog fa-lg"></i></span> <span
									class="text m-left-sm">系统设置</span> <span class="submenu-icon"></span>
							</span> <span class="menu-content-hover block"> 系统设置 </span>
						</a>
							<ul class="submenu">
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-label">用户管理</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">部门管理</span></a></li>
										<li><a href="#"><span class="submenu-label">系统用户</span></a></li>
										<li><a href="#"><span class="submenu-label">角色管理</span></a></li>
									</ul></li>
								<li><a href="form_element.html"><span
										class="submenu-label">类型表维护</span></a></li>
								<li><a href="form_validation.html"><span
										class="submenu-label">预警设置</span></a></li>
								<li><a href="form_wizard.html"><span
										class="submenu-label">流程设置</span></a></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">4</small>
										<span class="submenu-label">现场检查表设置</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">检查表管理</span></a></li>
										<li><a href="#"><span class="submenu-label">检查项管理</span></a></li>
										<li><a href="#"><span class="submenu-label">表单项管理</span></a></li>
										<li><a href="#"><span class="submenu-label">检查表配置</span></a></li>
									</ul></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">3</small>
										<span class="submenu-label">询问笔录配置</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">询问笔录模板管理</span></a></li>
										<li><a href="#"><span class="submenu-label">询问内容管理</span></a></li>
										<li><a href="#"><span class="submenu-label">询问笔录配置</span></a></li>
									</ul></li>
								<li><a href="dropzone.html"><span class="submenu-label">任务分配规则配置</span></a></li>
								<li class="openable"><a href="#"> <small
										class="badge badge-success badge-square bounceIn animation-delay2 m-left-xs pull-right">2</small>
										<span class="submenu-label">系统日志</span>
								</a>
									<ul class="submenu third-level">
										<li><a href="#"><span class="submenu-label">污染源更新日志</span></a></li>
										<li><a href="#"><span class="submenu-label">系统日志</span></a></li>
									</ul></li>
							</ul></li>
					</ul>
				</div>

			</div>
		</aside>

		<div class="main-container">
			<div class="padding-md">

				<div class="row">
					<!--任务办理-->
					<div class="col-lg-12">
						<div class="smart-widget widget-blue">

							<div class="smart-widget-inner table-responsive">



								<!--第一层任务办理row-->
								<div class="row">
									<!--按钮-->
									<div class="col-lg-12">
										<div class="smart-widget widget-light-grey">
											<div class="smart-widget-header">
												<i class="fa fa-comment"></i> 丽珠集团福州福兴医药有限公司： <span
													class="smart-widget-option" style="margin-top: -7px;">
													<a href="xczf-kybl-input.html"><button type="button"
															class="btn btn-info">返回查重结果</button></a> <span
													class="refresh-icon-animated"><i
														class="fa fa-circle-o-notch fa-spin"></i></span> <a href="#"
													class="widget-toggle-hidden-option"> <i
														class="fa fa-cog"></i></a> <a href="#"
													class="widget-collapse-option" data-toggle="collapse">
														<i class="fa fa-chevron-up"></i>
												</a> <a href="#" class="widget-refresh-option"> <i
														class="fa fa-refresh"></i></a>
												</span>
											</div>
											<div class="smart-widget-inner table-responsive">
												<div class="smart-widget-hidden-section">
													<ul class="widget-color-list clearfix">
														<li style="background-color: #20232b;"
															data-color="widget-dark"></li>
														<li style="background-color: #4c5f70;"
															data-color="widget-dark-blue"></li>
														<li style="background-color: #23b7e5;"
															data-color="widget-blue"></li>
														<li style="background-color: #2baab1;"
															data-color="widget-green"></li>
														<li style="background-color: #edbc6c;"
															data-color="widget-yellow"></li>
														<li style="background-color: #fbc852;"
															data-color="widget-orange"></li>
														<li style="background-color: #e36159;"
															data-color="widget-red"></li>
														<li style="background-color: #7266ba;"
															data-color="widget-purple"></li>
														<li style="background-color: #f5f5f5;"
															data-color="widget-light-grey"></li>
														<li style="background-color: #fff;" data-color="reset"></li>
													</ul>
												</div>

												<div class="smart-widget-body">
													<div class="smart-widget-body no-padding">

														<div class="form-group">
															<div class="col-md-12">
																<legend>
																	<button
																		onClick="javascript:window.location.href='zfdx-jbxx-bhq.html'"
																		class="btn bg-info">基本信息</button>
																	<button
																		onClick="javascript:window.location.href='zfdx-hjjc-bhq.html'"
																		class="btn bg-grey">环境监察</button>
																</legend>

															</div>
														</div>


														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label">开始时间</label> <input
																	type="text" placeholder="年/月/日"
																	class="form-control input-sm"
																	data-parsley-required="true">
															</div>
														</div>

														<div class="col-md-6">
															<div class="form-group">
																<label class="control-label">结束时间</label> <input
																	type="text" placeholder="年/月/日"
																	class="form-control input-sm"
																	data-parsley-required="true">
															</div>
														</div>

														<div class="text-right m-top-md"
															style="margin-right: 15px;">
															<div class="text-left m-top-md"
																style="margin-left: 20px;">温馨提示：点击工单摘要可以查看任务详情。</div>
															<button class="btn btn-info" type="submit"
																style="width: 100px;">查询</button>
														</div>

														<hr />




														<div class="col-md-12">
															<u>2016年9月</u> <br />
														</div>

														<div
															onclick="window.location.href= '../xczf.html';return false"
															class="col-md-3"
															style="margin-right: 50px; margin-left: 30px;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>



														<div
															onclick="window.location.href= '../xczf.html';return false"
															class="col-md-3"
															style="margin-right: 50px; margin-left: 30px; cursor: pointer;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>



														<div
															onclick="window.location.href= '../xczf.html';return false"
															class="col-md-3"
															style="margin-right: 50px; margin-left: 30px; cursor: pointer;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>

														<div class="col-md-3"
															style="margin-right: 50px; margin-left: 30px; cursor: pointer;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>


														<div class="col-md-12">

															<u>2016年8月</u>
														</div>

														<div class="col-md-3"
															style="margin-right: 50px; margin-left: 30px; cursor: pointer;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>



														<div class="col-md-12">

															<u>2016年5月</u>
														</div>

														<div class="col-md-3"
															style="margin-right: 50px; margin-left: 30px; cursor: pointer;">
															<div class="smart-widget">
																<div class="smart-widget-inner">
																	<div
																		style="background-color: #28AFE2; text-align: center; color: #ffffff; padding-bottom: 15px; padding-top: 15px;">
																		<b>任务编号:FJFZJA201XXXXXXX</b>
																	</div>
																	<div class="smart-widget-body">
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>任务来源：</span> <span>专项检查</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查开始时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																		<div class="form-group">
																			<div class="row" style="text-align: center;">
																				<span>检查结束时间：</span> <span>2016年x月x日</span>
																			</div>
																		</div>
																	

																	</div>
																</div>
															</div>
														</div>

														<!--./第一层快速查询row-->



														<!--第二层任务办理row-->

														<!--./待办任务-->

														<!--./第二层任务办理row-->
														<!--第三层翻页-->
														<div class="col-md-12">
															<div style="margin: -30px 0 20px 0; float: right;">
																<div style="float: left;">
																	<ul class="pagination pagination-split">
																		<li class="disabled"><a href="#">&laquo;</a></li>
																		<li class="active"><a href="#">1</a></li>
																		<li><a href="#">2</a></li>
																		<li><a href="#">3</a></li>
																		<li><a href="#">4</a></li>
																		<li><a href="#">5</a></li>
																		<li><a href="#">&raquo;</a></li>
																	</ul>
																</div>
																<div style="float: left; margin-top: 20px;">
																	<div class="form-group">
																		<select class="form-control">
																			<option value="5条">5条</option>
																			<option value="10条">10条</option>
																			<option value="20条">20条</option>
																			<option value="30条">30条</option>
																			<option value="50条">50条</option>
																		</select>
																	</div>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--版权信息-->


		<footer class="footer">
			<span class="footer-brand"> <strong class="text-danger">福建省环境保护厅</strong>
				版权所有
			</span>
			<p class="no-margin">
				&copy; 2017 <strong>北京长能环境大数据科技有限公司</strong> 研发并提供技术支持
			</p>
		</footer>
		<!--</./版权信息-->


		<a href="#" class="scroll-to-top hidden-print"><i
			class="fa fa-chevron-up fa-lg"></i></a>




		<!-- Le javascript
	    ================================================== -->
		<!-- Placed at the end of the document so the pages load faster -->

		<!-- Jquery -->
		<script src="../jquery/1.10.2/jquery.min.js"></script>

		<!-- Bootstrap -->
		<script src="../libs/bootstrap/3.3.4/js/bootstrap.min.js"></script>

		<!-- Flot -->
		<script src='../js/jquery.flot.min.js'></script>

		<!-- Slimscroll -->
		<script src='../js/jquery.slimscroll.min.js'></script>

		<!-- Morris -->
		<script src='../js/rapheal.min.js'></script>
		<script src='../js/morris.min.js'></script>

		<!-- Datepicker -->
		<script src='../js/uncompressed/datepicker.js'></script>

		<!-- Sparkline -->
		<script src='../js/sparkline.min.js'></script>

		<!-- Skycons -->
		<script src='../js/uncompressed/skycons.js'></script>

		<!-- Popup Overlay -->
		<script src='../js/jquery.popupoverlay.min.js'></script>

		<!-- Easy Pie Chart -->
		<script src='../js/jquery.easypiechart.min.js'></script>

		<!-- Sortable -->
		<script src='../js/uncompressed/jquery.sortable.js'></script>

		<!-- Owl Carousel -->
		<script src='../js/owl.carousel.min.js'></script>

		<!-- Modernizr -->
		<script src='../js/modernizr.min.js'></script>

		<!-- Simplify -->
		<script src="../js/simplify/simplify.js"></script>
		<script src="../js/simplify/simplify_dashboard.js"></script>


		<script>
			$(function() {
				$('.chart').easyPieChart({
					easing : 'easeOutBounce',
					size : '140',
					lineWidth : '7',
					barColor : '#7266ba',
					onStep : function(from, to, percent) {
						$(this.el).find('.percent').text(Math.round(percent));
					}
				});

				$('.sortable-list').sortable();

				$('.todo-checkbox').click(
						function() {

							var _activeCheckbox = $(this).find(
									'input[type="checkbox"]');

							if (_activeCheckbox.is(':checked')) {
								$(this).parent().addClass('selected');
							} else {
								$(this).parent().removeClass('selected');
							}

						});

				//Delete Widget Confirmation
				$('#deleteWidgetConfirm').popup({
					vertical : 'top',
					pagecontainer : '.container',
					transition : 'all 0.3s'
				});
			});
		</script>
</body>
</html>
