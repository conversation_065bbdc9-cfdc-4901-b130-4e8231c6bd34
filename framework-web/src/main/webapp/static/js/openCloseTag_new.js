// 关闭所有的大标签，关闭大标签里面的所有选择项 
	function ClickTag(){
		var outSpans = $(".tagOneClass"); 
		
		var outthreeSpans = $(".tagThreeClass");
		
		var spans = document.querySelectorAll(".submenu-label");
		
		for (i = 0; i < outSpans.length; i++) {
			outSpans[i].className="openable bg-palette2 tagOneClass";
			$(".tagOneClassUl")[i].style.display = 'none';
		}
		for (i = 0; i < outthreeSpans.length; i++) {
			outthreeSpans[i].style.display = "none";
		}
		for (i = 0; i < spans.length; i++) {
			spans[i].style.color = "#585858";
		}
		
	}
	// 指定打开标签  只能打开3级标签  
	function openClickTag(xtsyId,oneTagId,twoTagId,threeTagId){
		  // 3.指定打开标签 
		  var xtsyTag = document.getElementById(xtsyId);
		  if(xtsyTag===undefined || xtsyTag==null){
			  return ;
		  }
		  var oneMenuLabel = document.getElementById(oneTagId);
		  if(oneMenuLabel ===undefined || oneMenuLabel==null){
			  return ;
		  }
		  var twoMenuLabel = document.getElementById(twoTagId);
		  if(twoMenuLabel ===undefined || twoMenuLabel==null){
			  return ;
		  }
		  xtsyTag.className="bg-palette2";// 
		  
		  oneMenuLabel.className = "openable bg-palette2 tagOneClass open";
		  
		  oneMenuLabel.childNodes[3].style.display = 'block';
		  
		  if(threeTagId==''){
			  // 
			  twoMenuLabel.className="active";
			  //   
			  twoMenuLabel.childNodes[0].childNodes[0].style.color = "#2B7DBC";  
		  }else{
			  var threeMenuLabel = document.getElementById(threeTagId);
			  
			  twoMenuLabel.className="openable open";
			  
			  twoMenuLabel.childNodes[3].style.display = 'block';
			  
			  threeMenuLabel.className="active";
			  //   
			  threeMenuLabel.childNodes[1].childNodes[0].style.color = "#2B7DBC";
		  }
	}