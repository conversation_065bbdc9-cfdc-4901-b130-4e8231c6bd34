# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=袠薪薪懈泻懈 褋懈褉褝泄
previous_label=袠薪薪懈薪褝褝視懈
next.title=袗薪褘谐褘褋泻褘 褋懈褉褝泄
next_label=袗薪褘谐褘褋泻褘

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=袣褍褔褔邪褌
zoom_out_label=袣褍褔褔邪褌
zoom_in.title=校谢邪邪褌褘薪薪邪褉
zoom_in_label=校谢邪邪褌褘薪薪邪褉
zoom.title=校谢邪邪褌褘薪薪邪褉
presentation_mode.title=袣萤褉写萤褉萤褉 褝褉褝褋懈懈屑谣褝
presentation_mode_label=袣萤褉写萤褉萤褉 褝褉褝褋懈懈屑
open_file.title=袘懈谢褝薪懈 邪褉褘泄
open_file_label=袗褋
print.title=袘褝褔褝褝褌
print_label=袘褝褔褝褝褌
download.title=啸邪褔邪泄写邪邪一褘薪
download_label=啸邪褔邪泄写邪邪一褘薪
bookmark.title=袘懈谢懈谣谣懈 泻萤褋褌爷爷褌褝 (褏邪褌褘谢邪邪 褝斜褝褌褝褉 褋邪谣邪 褌爷薪薪爷泻泻褝 邪褉褘泄)
bookmark_label=袘懈谢懈谣谣懈 泻萤褋褌爷爷褌褝

# Secondary toolbar and context menu
tools.title=孝褝褉懈谢谢褝褉
tools_label=孝褝褉懈谢谢褝褉
first_page.title=袘邪褋褌邪泻褘 褋懈褉褝泄谐褝 泻萤褋
first_page.label=袘邪褋褌邪泻褘 褋懈褉褝泄谐褝 泻萤褋
first_page_label=袘邪褋褌邪泻褘 褋懈褉褝泄谐褝 泻萤褋
last_page.title=孝懈一褝褏 褋懈褉褝泄谐褝 泻萤褋
last_page.label=孝懈一褝褏 褋懈褉褝泄谐褝 泻萤褋
last_page_label=孝懈一褝褏 褋懈褉褝泄谐褝 泻萤褋
page_rotate_cw.title=效邪一褘 褏芯褌褍 褝褉谐懈褌
page_rotate_cw.label=效邪一褘 褏芯褌褍 褝褉谐懈褌
page_rotate_cw_label=效邪一褘 褏芯褌褍 褝褉谐懈褌
page_rotate_ccw.title=效邪一褘 褍褌邪褉褘 褝褉谐懈褌
page_rotate_ccw.label=效邪一褘 褍褌邪褉褘 褝褉谐懈褌
page_rotate_ccw_label=效邪一褘 褍褌邪褉褘 褝褉谐懈褌

hand_tool_enable.title=芦袠谢懈懈禄 写懈褝薪 褌褝褉懈谢懈 褏芯谢斜芯芯
hand_tool_enable_label=芦袠谢懈懈禄 写懈褝薪 褌褝褉懈谢懈 褏芯谢斜芯芯
hand_tool_disable.title=芦袠谢懈懈禄 写懈褝薪 褌褝褉懈谢懈 邪褉邪邪褉
hand_tool_disable_label=芦袠谢懈懈禄 写懈褝薪 褌褝褉懈谢懈 邪褉邪邪褉

# Document properties dialog box
document_properties.title=袛芯泻褍屑褍芯薪 褌褍褉褍芯褉褍褍谢邪褉邪...
document_properties_label=袛芯泻褍屑褍芯薪 褌褍褉褍芯褉褍褍谢邪褉邪...\u0020
document_properties_file_name=袘懈谢褝 邪邪褌邪:
document_properties_file_size=袘懈谢褝 泻褝褝屑褝泄褝:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} 袣袘 ({{size_b}} 斜邪邪泄褌)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} 袦袘 ({{size_b}} 斜邪邪泄褌)
document_properties_title=袘邪一邪:
document_properties_author=袗邪锌褌邪褉:
document_properties_subject=孝懈褝屑褝:
document_properties_keywords=袣爷谢爷爷褋 褌褘谢:
document_properties_creation_date=袨谣芯一褍谢谢褍斜褍褌 泻褝屑褝:
document_properties_modification_date=校谢邪褉褘褌褘谢谢褘斜褘褌 泻褝屑褝:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_producer=PDF 芯谣芯褉芯芯褔褔褍:
document_properties_version=PDF 斜邪褉褘谢邪:
document_properties_page_count=小懈褉褝泄 邪褏褋邪邪薪邪:
document_properties_close=小邪锌

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=袨泄芯視芯褋 褏邪锌褌邪谢褘 邪褉褘泄/褋邪锌
toggle_sidebar_label=袨泄芯視芯褋 褏邪锌褌邪谢褘 邪褉褘泄/褋邪锌
document_outline_label=袛萤泻爷屑爷萤薪 懈一懈薪褝褝視懈褌褝
attachments.title=袣褘斜褘褌褘泻褌邪褉褘 泻萤褉写萤褉
attachments_label=袣褘斜褘褌褘泻
thumbs.title=袨泄褍褍褔邪邪薪薪邪褉褘 泻萤褉写萤褉
thumbs_label=袨泄褍褍褔邪邪薪薪邪褉
findbar.title=袛萤泻爷屑爷萤薪褌褝薪 斜褍谢
findbar_label=袘褍谢

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=小懈褉褝泄 {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=小懈褉褝泄 芯泄褍褍褔邪邪薪邪 {{page}}

# Find panel button title and messages
find_label=袘褍谢:
find_previous.title=协褌懈懈 褌懈褝泻懈褋泻褝 斜褍 懈薪薪懈薪褝褝視懈 泻懈懈褉懈懈褌懈薪 斜褍谢
find_previous_label=袠薪薪懈薪褝褝視懈
find_next.title=协褌懈懈 褌懈褝泻懈褋泻褝 斜褍 泻褝薪薪懈薪褝褝視懈 泻懈懈褉懈懈褌懈薪 斜褍谢
find_next_label=袗薪褘谐褘褋泻褘
find_highlight=袘邪褉褘褌褘薪 褋褘褉写邪褌邪薪 泻萤褉写萤褉
find_match_case_label=袘褍褍泻褍斜邪 褍谢邪褏邪薪褘薪-泻褘褉邪褌褘薪 邪褉邪邪褉
find_reached_top=小懈褉褝泄 爷褉写爷谐褝褉 褌懈懈泄写懈谣, 褋邪谢谐褘褘褌邪 邪谢谢邪褉邪
find_reached_bottom=小懈褉褝泄 斜爷褌褌褝, 爷萤一褝 褋邪谢視邪薪薪邪
find_not_found=协褌懈懈 泻萤褋褌爷斜褝褌褝

# Error panel labels
error_more_info=小懈一懈谢懈懈
error_less_info=小懈一懈谢懈懈褌懈薪 泻懈褋褌褝褝
error_close=小邪锌
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (褏芯屑褍泄褍褍褌邪: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=协褌懈懈: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=小褌e泻: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=袘懈谢褝: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=校褋褌褍褉褍芯泻邪: {{line}}
rendering_error=小懈褉褝泄懈 邪泄邪褉谐邪 邪谢視邪褋 褌邪視褘褋褌邪.

# Predefined zoom values
page_scale_width=小懈褉褝泄 泻褝褌懈褌懈薪褝薪
page_scale_fit=小懈褉褝泄 泻褝褝屑褝泄懈薪褝薪
page_scale_auto=袗锌褌邪屑邪邪褌褘薪邪薪
page_scale_actual=袛褜懈谣薪褝褝褏 泻褝褝屑褝泄褝
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=袗谢視邪褋
loading_error=PDF-斜懈谢褝薪懈 褏邪褔邪泄写褘褘褉谐邪 邪谢視邪褋 褌邪視褘褋褌邪.
invalid_file_error=孝褍芯褏 褝褉褝 邪谢視邪褋褌邪邪褏 褝斜褝褌褝褉 邪谢写褜邪屑屑褘褌 PDF-斜懈谢褝.
missing_file_error=PDF-斜懈谢褝 褋褍芯褏.
unexpected_response_error=小懈褝褉斜褝褉 褏芯褉褍泄写邪邪斜邪褌.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 褌褍一褍薪邪薪]
password_label=袘褍 PDF-斜懈谢褝薪懈 邪褉褘泄邪褉谐邪 泻萤屑爷褋泻褝谢 褌褘谢褘 泻懈谢谢褝褉懈褝褏褌褝褝褏懈薪.
password_invalid=袣懈懈褉懈懈 褌褘谢 邪谢視邪褋褌邪邪褏. 袘褍泻邪 写懈褝薪, 褏邪褌褘谢邪邪薪 泻萤褉.
password_ok=小莹袩

printing_not_supported=小褝褉褝褌懈懈: 袘褍 斜褉邪褍蟹械褉 斜褝褔褝褝褌褌懈懈褉懈 褌芯谢芯褉褍 萤泄萤萤斜萤褌.
printing_not_ready=小褝褉褝褌懈懈: PDF 斜褝褔褝褝褌褌懈懈褉谐褝 褌芯谢芯褉褍 褏邪褔邪泄写邪薪邪 懈谢懈泻.
web_fonts_disabled=小懈褌懈屑-斜懈褔懈泻褌褝褉 邪褉邪邪褉褘谢谢褘邪褏褌邪褉邪: PDF 斜懈褔懈泻褌褝褉褝 泻褘邪泄邪薪 泻萤褋褌爷斜褝褌褌褝褉.
document_colors_not_allowed=PDF-写萤泻爷屑爷萤爷薪薪褝褉谐褝 斜褝泄褝谢褝褉懈薪 萤谣薪萤褉爷薪 褌褍褌褌邪褉 泻萤谣爷谢谢褝屑屑褝褌褝: "小懈褌懈屑-褋懈褉写褝褉 斜褝泄褝谢褝褉懈薪 萤谣薪萤褉爷薪 褌褍褌褌邪谢谢邪褉褘薪 泻萤谣爷谢谢爷爷褉谐褝" 写懈褝薪 斜褉邪褍蟹械褉谐邪 邪褉邪褏褋邪 褋褘谢写褜邪褉 褝斜懈褌.
