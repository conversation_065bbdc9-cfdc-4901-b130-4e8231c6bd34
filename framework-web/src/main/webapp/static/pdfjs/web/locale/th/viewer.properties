# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=喔笝喙夃覆喔佮箞喔笝喔笝喙夃覆
previous_label=喔佮箞喔笝喔笝喙夃覆
next.title=喔笝喙夃覆喔栢副喔斷箘喔�
next_label=喔栢副喔斷箘喔�

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=喔笝喙夃覆
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=喔堗覆喔� {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} 喔堗覆喔� {{pagesCount}})

zoom_out.title=喔嬥腹喔∴腑喔竵
zoom_out_label=喔嬥腹喔∴腑喔竵
zoom_in.title=喔嬥腹喔∴箑喔傕箟喔�
zoom_in_label=喔嬥腹喔∴箑喔傕箟喔�
zoom.title=喔嬥腹喔�
presentation_mode.title=喔弗喔编笟喙�喔涏箛喔權箓喔浮喔斷竵喔侧福喔權赋喙�喔笝喔�
presentation_mode_label=喙傕斧喔∴笖喔佮覆喔｀笝喔赤箑喔笝喔�
open_file.title=喙�喔涏复喔斷箘喔熰弗喙�
open_file_label=喙�喔涏复喔�
print.title=喔炧复喔∴笧喙�
print_label=喔炧复喔∴笧喙�
download.title=喔斷覆喔о笝喙屶箓喔弗喔�
download_label=喔斷覆喔о笝喙屶箓喔弗喔�
bookmark.title=喔∴父喔∴浮喔竾喔涏副喔堗笀喔膏笟喔编笝 (喔勦副喔斷弗喔竵喔福喔粪腑喙�喔涏复喔斷箖喔權斧喔權箟喔侧笗喙堗覆喔囙箖喔浮喙�)
bookmark_label=喔∴父喔∴浮喔竾喔涏副喔堗笀喔膏笟喔编笝

# Secondary toolbar and context menu
tools.title=喙�喔勦福喔粪箞喔竾喔∴阜喔�
tools_label=喙�喔勦福喔粪箞喔竾喔∴阜喔�
first_page.title=喙勦笡喔⑧副喔囙斧喔權箟喔侧箒喔｀竵
first_page.label=喙勦笡喔⑧副喔囙斧喔權箟喔侧箒喔｀竵
first_page_label=喙勦笡喔⑧副喔囙斧喔權箟喔侧箒喔｀竵
last_page.title=喙勦笡喔⑧副喔囙斧喔權箟喔侧釜喔膏笖喔椸箟喔侧涪
last_page.label=喙勦笡喔⑧副喔囙斧喔權箟喔侧釜喔膏笖喔椸箟喔侧涪
last_page_label=喙勦笡喔⑧副喔囙斧喔權箟喔侧釜喔膏笖喔椸箟喔侧涪
page_rotate_cw.title=喔浮喔膏笝喔曕覆喔∴箑喔傕箛喔∴笝喔侧脯喔脆竵喔�
page_rotate_cw.label=喔浮喔膏笝喔曕覆喔∴箑喔傕箛喔∴笝喔侧脯喔脆竵喔�
page_rotate_cw_label=喔浮喔膏笝喔曕覆喔∴箑喔傕箛喔∴笝喔侧脯喔脆竵喔�
page_rotate_ccw.title=喔浮喔膏笝喔椸抚喔權箑喔傕箛喔∴笝喔侧脯喔脆竵喔�
page_rotate_ccw.label=喔浮喔膏笝喔椸抚喔權箑喔傕箛喔∴笝喔侧脯喔脆竵喔�
page_rotate_ccw_label=喔浮喔膏笝喔椸抚喔權箑喔傕箛喔∴笝喔侧脯喔脆竵喔�

hand_tool_enable.title=喙�喔涏复喔斷箖喔娻箟喔囙覆喔權箑喔勦福喔粪箞喔竾喔∴阜喔福喔灌笡喔∴阜喔�
hand_tool_enable_label=喙�喔涏复喔斷箖喔娻箟喔囙覆喔權箑喔勦福喔粪箞喔竾喔∴阜喔福喔灌笡喔∴阜喔�
hand_tool_disable.title=喔涏复喔斷箖喔娻箟喔囙覆喔權箑喔勦福喔粪箞喔竾喔∴阜喔福喔灌笡喔∴阜喔�
hand_tool_disable_label=喔涏复喔斷箖喔娻箟喔囙覆喔權箑喔勦福喔粪箞喔竾喔∴阜喔福喔灌笡喔∴阜喔�

# Document properties dialog box
document_properties.title=喔勦父喔撪釜喔∴笟喔编笗喔脆箑喔竵喔覆喔ｂ��
document_properties_label=喔勦父喔撪釜喔∴笟喔编笗喔脆箑喔竵喔覆喔ｂ��
document_properties_file_name=喔娻阜喙堗腑喙勦笩喔ム箤:
document_properties_file_size=喔傕笝喔侧笖喙勦笩喔ム箤:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} 喔佮复喙傕弗喙勦笟喔曕箤 ({{size_b}} 喙勦笟喔曕箤)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} 喙�喔∴竵喔班箘喔氞笗喙� ({{size_b}} 喙勦笟喔曕箤)
document_properties_title=喔娻阜喙堗腑:
document_properties_author=喔溹腹喙夃釜喔｀箟喔侧竾:
document_properties_subject=喔娻阜喙堗腑喙�喔｀阜喙堗腑喔�:
document_properties_keywords=喔勦赋喔赋喔勦副喔�:
document_properties_creation_date=喔о副喔權笚喔掂箞喔福喙夃覆喔�:
document_properties_modification_date=喔о副喔權笚喔掂箞喙佮竵喙夃箘喔�:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=喔溹腹喙夃釜喔｀箟喔侧竾:
document_properties_producer=喔溹腹喙夃笢喔ム复喔� PDF:
document_properties_version=喔｀父喙堗笝 PDF:
document_properties_page_count=喔堗赋喔權抚喔權斧喔權箟喔�:
document_properties_close=喔涏复喔�

print_progress_message=喔佮赋喔ム副喔囙箑喔曕福喔掂涪喔∴箑喔竵喔覆喔｀釜喔赤斧喔｀副喔氞竵喔侧福喔炧复喔∴笧喙屸��
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=喔⑧竵喙�喔ム复喔�

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=喙�喔涏复喔�/喔涏复喔斷箒喔栢笟喔傕箟喔侧竾
toggle_sidebar_label=喙�喔涏复喔�/喔涏复喔斷箒喔栢笟喔傕箟喔侧竾
document_outline.title=喙佮釜喔斷竾喙�喔勦箟喔侧福喙堗覆喔囙箑喔竵喔覆喔� (喔勦弗喔脆竵喔腑喔囙竸喔｀副喙夃竾喙�喔炧阜喙堗腑喔傕涪喔侧涪/喔⑧父喔氞福喔侧涪喔佮覆喔｀笚喔编箟喔囙斧喔∴笖)
document_outline_label=喙�喔勦箟喔侧福喙堗覆喔囙箑喔竵喔覆喔�
attachments.title=喙佮釜喔斷竾喙勦笩喔ム箤喙佮笝喔�
attachments_label=喙勦笩喔ム箤喙佮笝喔�
thumbs.title=喙佮釜喔斷竾喔犩覆喔炧競喔權覆喔斷涪喙堗腑
thumbs_label=喔犩覆喔炧競喔權覆喔斷涪喙堗腑
findbar.title=喔勦箟喔權斧喔侧箖喔權箑喔竵喔覆喔�
findbar_label=喔勦箟喔權斧喔�

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=喔笝喙夃覆 {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=喔犩覆喔炧競喔權覆喔斷涪喙堗腑喔傕腑喔囙斧喔權箟喔� {{page}}

# Find panel button title and messages
find_label=喔勦箟喔權斧喔�:
find_previous.title=喔覆喔曕赋喙佮斧喔權箞喔囙竵喙堗腑喔權斧喔權箟喔侧競喔竾喔о弗喔�
find_previous_label=喔佮箞喔笝喔笝喙夃覆
find_next.title=喔覆喔曕赋喙佮斧喔權箞喔囙笘喔编笖喙勦笡喔傕腑喔囙抚喔ム傅
find_next_label=喔栢副喔斷箘喔�
find_highlight=喙�喔權箟喔權釜喔掂笚喔编箟喔囙斧喔∴笖
find_match_case_label=喔曕副喔о笧喔脆浮喔炧箤喙冟斧喔嵿箞喙�喔ム箛喔佮笗喔｀竾喔佮副喔�
find_reached_top=喔勦箟喔權斧喔侧笘喔多竾喔堗父喔斷箑喔｀复喙堗浮喔曕箟喔權競喔竾喔笝喙夃覆 喙�喔｀复喙堗浮喔勦箟喔權笗喙堗腑喔堗覆喔佮笖喙夃覆喔權弗喙堗覆喔�
find_reached_bottom=喔勦箟喔權斧喔侧笘喔多竾喔堗父喔斷釜喔脆箟喔權釜喔膏笖喔笝喙夃覆 喙�喔｀复喙堗浮喔勦箟喔權笗喙堗腑喔堗覆喔佮笖喙夃覆喔權笟喔�
find_not_found=喙勦浮喙堗笧喔氞抚喔ム傅

# Error panel labels
error_more_info=喔傕箟喔浮喔灌弗喙�喔炧复喙堗浮喙�喔曕复喔�
error_less_info=喔傕箟喔浮喔灌弗喔權箟喔涪喔ム竾
error_close=喔涏复喔�
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=喔傕箟喔竸喔о覆喔�: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=喔箒喔曕箛喔�: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=喙勦笩喔ム箤: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=喔氞福喔｀笚喔编笖: {{line}}
rendering_error=喙�喔佮复喔斷競喙夃腑喔溹复喔斷笧喔ム覆喔斷競喔撪赴喔佮赋喔ム副喔囙箑喔｀笝喙�喔斷腑喔｀箤喔笝喙夃覆

# Predefined zoom values
page_scale_width=喔勦抚喔侧浮喔佮抚喙夃覆喔囙斧喔權箟喔�
page_scale_fit=喔炧腑喔斷傅喔笝喙夃覆
page_scale_auto=喔嬥腹喔∴腑喔编笗喙傕笝喔∴副喔曕复
page_scale_actual=喔傕笝喔侧笖喔堗福喔脆竾
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=喔傕箟喔笢喔脆笖喔炧弗喔侧笖
loading_error=喙�喔佮复喔斷競喙夃腑喔溹复喔斷笧喔ム覆喔斷競喔撪赴喔佮赋喔ム副喔囙箓喔弗喔� PDF
invalid_file_error=喙勦笩喔ム箤 PDF 喙勦浮喙堗笘喔灌竵喔曕箟喔竾喔福喔粪腑喙�喔傅喔⑧斧喔侧涪
missing_file_error=喙勦笩喔ム箤 PDF 喔傕覆喔斷斧喔侧涪
unexpected_response_error=喔佮覆喔｀笗喔笟喔笝喔竾喔傕腑喔囙箑喔嬥复喔｀箤喔熰箑喔о腑喔｀箤喔椸傅喙堗箘喔∴箞喔勦覆喔斷竸喔脆笖

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[喔勦赋喔笜喔脆笟喔侧涪喔涏福喔班竵喔笟 {{type}}]
password_label=喔涏箟喔笝喔｀斧喔编釜喔溹箞喔侧笝喙�喔炧阜喙堗腑喙�喔涏复喔斷箘喔熰弗喙� PDF 喔權傅喙�
password_invalid=喔｀斧喔编釜喔溹箞喔侧笝喙勦浮喙堗笘喔灌竵喔曕箟喔竾 喙傕笡喔｀笖喔ム腑喔囙腑喔掂竵喔勦福喔编箟喔�
password_ok=喔曕竵喔ム竾
password_cancel=喔⑧竵喙�喔ム复喔�

printing_not_supported=喔勦赋喙�喔曕阜喔笝: 喙�喔氞福喔侧抚喙屶箑喔嬥腑喔｀箤喔權傅喙夃箘喔∴箞喙勦笖喙夃釜喔權副喔氞釜喔權父喔權竵喔侧福喔炧复喔∴笧喙屶腑喔⑧箞喔侧竾喙�喔曕箛喔∴笚喔掂箞
printing_not_ready=喔勦赋喙�喔曕阜喔笝: PDF 喙勦浮喙堗箘喔斷箟喔｀副喔氞竵喔侧福喙傕斧喔ム笖喔涪喙堗覆喔囙箑喔曕箛喔∴笚喔掂箞喔赋喔福喔编笟喔佮覆喔｀笧喔脆浮喔炧箤
web_fonts_disabled=喙佮笟喔氞腑喔编竵喔┼福喙�喔о箛喔氞笘喔灌竵喔涏复喔斷竵喔侧福喙冟笂喙夃竾喔侧笝: 喙勦浮喙堗釜喔侧浮喔侧福喔栢箖喔娻箟喙佮笟喔氞腑喔编竵喔┼福喔澿副喔囙笗喔编抚喙冟笝 PDF
document_colors_not_allowed=喙�喔竵喔覆喔� PDF 喙勦浮喙堗箘喔斷箟喔｀副喔氞腑喔權父喔嵿覆喔曕箖喔箟喙冟笂喙夃釜喔掂競喔竾喔曕副喔о箑喔竾: "喔笝喔膏笉喔侧笗喙冟斧喙夃斧喔權箟喔侧箑喔竵喔覆喔｀釜喔侧浮喔侧福喔栢箑喔ム阜喔竵喔傅喔傕腑喔囙笗喔编抚喙�喔竾" 喔栢腹喔佮笡喔脆笖喙冟笂喙夃竾喔侧笝喙冟笝喙�喔氞福喔侧抚喙屶箑喔嬥腑喔｀箤
