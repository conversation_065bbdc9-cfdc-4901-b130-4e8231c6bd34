# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=諉铡窄崭謤栅 乍栈炸
previous_label=諉铡窄崭謤栅炸
next.title=諃铡栈崭謤栅 乍栈炸
next_label=諃铡栈崭謤栅炸

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=苑栈.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}-斋謥\u0020
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}}-炸 {{pagesCount}})-斋謥

zoom_out.title=論崭謩謤铡謥斩榨宅
zoom_out_label=論崭謩謤铡謥斩榨宅
zoom_in.title=越崭辗崭謤铡謥斩榨宅
zoom_in_label=越崭辗崭謤铡謥斩榨宅
zoom.title=談铡战辗湛铡闸炸\u0020
presentation_mode.title=员斩謥斩榨宅 諉榨謤寨铡盏铡謥沾铡斩 榨詹铡斩铡寨斋斩
presentation_mode_label=諉榨謤寨铡盏铡謥沾铡斩 榨詹铡斩铡寨
open_file.title=圆铡謥榨宅 諙铡盏宅
open_file_label=圆铡謥榨宅
print.title=諒蘸榨宅
print_label=諒蘸榨宅
download.title=圆榨占斩榨宅
download_label=圆榨占斩榨宅
bookmark.title=愿斩诈铡謥斋寨 湛榨战謩崭站 (蘸铡湛粘榨斩榨宅 寨铡沾 闸铡謥榨宅 斩崭謤 蘸铡湛崭謧瞻铡斩崭謧沾)
bookmark_label=愿斩诈铡謥斋寨 湛榨战謩炸

# Secondary toolbar and context menu
tools.title=猿崭謤债斋謩斩榨謤
tools_label=猿崭謤债斋謩斩榨謤
first_page.title=员斩謥斩榨宅 铡占铡栈斋斩 乍栈斋斩
first_page.label=员斩謥斩榨宅 铡占铡栈斋斩 乍栈斋斩
first_page_label=员斩謥斩榨宅 铡占铡栈斋斩 乍栈斋斩
last_page.title=员斩謥斩榨宅 站榨謤栈斋斩 乍栈斋斩
last_page.label=员斩謥斩榨宅 站榨謤栈斋斩 乍栈斋斩
last_page_label=员斩謥斩榨宅 站榨謤栈斋斩 乍栈斋斩
page_rotate_cw.title=諍湛湛榨宅 炸战湛 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋
page_rotate_cw.label=諍湛湛榨宅 炸战湛 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋
page_rotate_cw_label=諍湛湛榨宅 炸战湛 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋
page_rotate_ccw.title=諍湛湛榨宅 瞻铡寨铡占铡寨 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋
page_rotate_ccw.label=諍湛湛榨宅 瞻铡寨铡占铡寨 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋
page_rotate_ccw_label=諍湛湛榨宅 瞻铡寨铡占铡寨 摘铡沾铡謥崭謧盏謥斋 战宅铡謩斋

hand_tool_enable.title=談斋铡謥斩榨宅 毡榨占謩斋 眨崭謤债斋謩炸
hand_tool_enable_label=談斋铡謥斩榨宅 毡榨占謩斋 眨崭謤债斋謩炸
hand_tool_disable.title=员斩栈铡湛榨宅 毡榨占謩斋 眨崭謤债斋謩炸
hand_tool_disable_label=员諉栈铡湛榨宅 毡榨占謩斋 眨崭謤债斋謩炸

# Document properties dialog box
document_properties.title=論铡战湛铡诈詹诈斋 瞻铡湛寨崭謧诈盏崭謧斩斩榨謤炸...
document_properties_label=論铡战湛铡诈詹诈斋 瞻铡湛寨崭謧诈盏崭謧斩斩榨謤炸...
document_properties_file_name=諙铡盏宅斋 铡斩崭謧斩炸.
document_properties_file_size=諙铡盏宅斋 展铡謨炸.
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} 钥圆 ({{size_b}} 闸铡盏诈)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} 談圆 ({{size_b}} 闸铡盏诈)
document_properties_title=諑榨謤斩铡眨斋謤.
document_properties_author=諃榨詹斋斩铡寨鈥�
document_properties_subject=諑榨謤斩铡眨斋謤.
document_properties_keywords=諃斋沾斩铡闸铡占.
document_properties_creation_date=諐湛榨詹债榨宅崭謧 铡沾战铡诈斋站炸.
document_properties_modification_date=論崭謨崭窄榨宅崭謧 铡沾战铡诈斋站炸.
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=諐湛榨詹债崭詹.
document_properties_producer=PDF-斋 瞻榨詹斋斩铡寨炸.
document_properties_version=PDF-斋 湛铡謤闸榨謤铡寨炸.
document_properties_page_count=苑栈榨謤斋 謩铡斩铡寨炸.
document_properties_close=論铡寨榨宅

print_progress_message=諉铡窄铡蘸铡湛謤铡战湛崭謧沾 乍 謨铡战湛铡诈崭謧詹诈炸 湛蘸榨宅崭謧斩...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=諌榨詹铡謤寨榨宅

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=圆铡謥榨宅/論铡寨榨宅 钥崭詹铡盏斋斩 站铡瞻铡斩铡寨炸
toggle_sidebar_notification.title=論崭窄铡斩栈铡湛榨宅 钥崭詹铡盏斋斩 眨崭湛斋斩 (謨铡战湛铡诈崭謧詹诈炸 蘸铡謤崭謧斩铡寨崭謧沾 乍 崭謧謤站铡眨斋债/寨謥崭謤栅)
toggle_sidebar_label=圆铡謥榨宅/論铡寨榨宅 钥崭詹铡盏斋斩 站铡瞻铡斩铡寨炸
document_outline.title=諔崭謧謥铡栅謤榨宅 謨铡战湛铡诈詹诈斋 崭謧謤站铡眨斋债炸 (寨謤寨斩铡寨斋 战榨詹沾榨謩諠 沾斋崭謧盏诈斩榨謤炸 炸斩栅铡謤毡铡寨榨宅崭謧/寨崭债寨榨宅崭謧 瞻铡沾铡謤)
document_outline_label=論铡战湛铡诈詹诈斋 闸崭站铡斩栅铡寨崭謧诈盏崭謧斩炸
attachments.title=諔崭謧謥铡栅謤榨宅 寨謥崭謤栅斩榨謤炸
attachments_label=钥謥崭謤栅斩榨謤
thumbs.title=諔崭謧謥铡栅謤榨宅 談铡斩謤铡蘸铡湛寨榨謤炸
thumbs_label=談铡斩謤铡蘸铡湛寨榨謤炸
findbar.title=猿湛斩榨宅 謨铡战湛铡诈詹诈崭謧沾
findbar_label=請謤崭斩崭謧沾

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=苑栈炸 {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=苑栈斋 沾铡斩謤铡蘸铡湛寨榨謤炸 {{page}}

# Find panel button title and messages
find_label=猿湛斩榨宅`
find_previous.title=猿湛斩榨宅 铡斩謤铡瞻铡盏湛崭謧诈盏铡斩 斩铡窄崭謤栅 瞻铡斩栅斋蘸崭謧沾炸
find_previous_label=諉铡窄崭謤栅炸
find_next.title=猿湛斋謤 铡謤湛铡瞻铡盏湛崭謧诈盏铡斩 瞻铡栈崭謤栅 瞻铡斩栅斋蘸崭謧沾炸
find_next_label=諃铡栈崭謤栅炸
find_highlight=猿崭謧斩铡斩辗榨宅 闸崭宅崭謤炸
find_match_case_label=談榨债(謨崭謩謤)铡湛铡占 瞻铡辗站斋 铡占斩榨宅
find_reached_top=諃铡战榨宅 榨謩 謨铡战湛铡诈詹诈斋 站榨謤謬斋斩, 寨辗铡謤崭謧斩铡寨站斋 斩榨謤謩謬斋謥
find_reached_bottom=諃铡战榨宅 榨謩 謨铡战湛铡诈詹诈斋 站榨謤栈斋斩, 寨辗铡謤崭謧斩铡寨站斋 站榨謤謬斋謥
find_not_found=员謤湛铡瞻铡盏湛崭謧诈盏崭謧斩炸 展眨湛斩站榨謥

# Error panel labels
error_more_info=员站榨宅斋 辗铡湛 湛榨詹榨寨崭謧诈盏崭謧斩
error_less_info=諗斋展 湛榨詹榨寨崭謧诈盏崭謧斩
error_close=論铡寨榨宅
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (寨铡占崭謧謥崭謧沾炸. {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=猿謤崭謧诈盏崭謧斩炸. {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=諊榨詹栈. {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=諙铡盏宅. {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=諒崭詹炸. {{line}}
rendering_error=諐窄铡宅諠 乍栈炸 战湛榨詹债榨宅斋战:

# Predefined zoom values
page_scale_width=苑栈斋 宅铡盏斩謩炸
page_scale_fit=諄眨榨宅 乍栈炸
page_scale_auto=曰斩謩斩铡辗窄铡湛
page_scale_actual=曰謤铡寨铡斩 展铡謨炸
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=諐窄铡宅
loading_error=諐窄铡宅諠 PDF 謫铡盏宅炸 闸铡謥榨宅斋战謮
invalid_file_error=諐窄铡宅 寨铡沾 闸斩铡战站铡债 PDF 謫铡盏宅:
missing_file_error=PDF 謫铡盏宅炸 闸铡謥铡寨铡盏崭謧沾 乍:
unexpected_response_error=諐蘸铡战铡謤寨斋展斋 铡斩战蘸铡战榨宅斋 蘸铡湛铡战窄铡斩:

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 跃铡斩崭诈崭謧诈盏崭謧斩]
password_label=談崭謧湛謩铡眨謤榨謩 PDF-斋 眨铡詹湛斩铡闸铡占炸:
password_invalid=猿铡詹湛斩铡闸铡占炸 战窄铡宅 乍: 钥謤寨斋斩 謨崭謤毡榨謩:
password_ok=约铡站
password_cancel=諌榨詹铡謤寨榨宅

printing_not_supported=远眨崭謧辗铡謥崭謧沾. 諒蘸榨宅炸 铡沾闸崭詹栈崭謧诈盏铡沾闸 展斋 铡栈铡寨謥站崭謧沾 栅斋湛铡謤寨斋展斋 寨崭詹沾斋謥謮
printing_not_ready=远眨崭謧辗铡謥崭謧沾. PDF-炸 铡沾闸崭詹栈崭謧诈盏铡沾闸 展斋 闸榨占斩铡站崭謤站榨宅 湛蘸榨宅崭謧 瞻铡沾铡謤:
web_fonts_disabled=諑榨闸-湛铡占铡湛榨战铡寨斩榨謤炸 铡斩栈铡湛站铡债 榨斩. 瞻斩铡謤铡站崭謤 展乍 謪眨湛铡眨崭謤债榨宅 斩榨謤寨铡占崭謧謥站铡债 PDF 湛铡占铡湛榨战铡寨斩榨謤炸:
document_colors_not_allowed=PDF 謨铡战湛铡诈詹诈榨謤斋斩 诈崭謧盏宅铡湛謤站铡债 展乍 謪眨湛铡眨崭謤债榨宅 斋謤榨斩謥 战榨謨铡寨铡斩 眨崭謧盏斩榨謤炸: 鈥溤拐钢傉嫡≌恐�榨宅 乍栈榨謤斋斩 炸斩湛謤榨宅 斋謤榨斩謥 战榨謨铡寨铡斩 眨崭謧盏斩榨謤炸鈥� 炸斩湛謤铡斩謩炸 铡斩栈铡湛站铡债 乍 栅斋湛铡謤寨斋展崭謧沾:
