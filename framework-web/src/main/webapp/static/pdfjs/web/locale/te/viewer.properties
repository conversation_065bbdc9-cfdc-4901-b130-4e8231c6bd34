# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=喟眮喟ㄠ眮喟盁喟� 喟眹喟溹眬
previous_label=喟曕睄喟班翱喟む皞
next.title=喟む鞍啾佮暗喟距挨 喟眹喟溹眬
next_label=喟む鞍啾佮暗喟距挨

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=喟眹喟溹眬
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=喟眾喟む睄喟む皞 {{pageCount}} 喟侧眿
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=(喟眾喟む睄喟む皞 {{pagesCount}} 喟侧眿 {{pageNumber}}喟掂唉喟�)

zoom_out.title=喟溹眰喟睄 喟む皸啾嵿皸喟苦皞喟氞眮
zoom_out_label=喟溹眰喟睄 喟む皸啾嵿皸喟苦皞喟氞眮
zoom_in.title=喟溹眰喟睄 喟氞眹喟翱
zoom_in_label=喟溹眰喟睄 喟氞眹喟翱
zoom.title=喟溹眰喟睄
presentation_mode.title=喟睄喟班唉喟班睄喟多皑喟� 喟班眬喟む翱喟曕翱 喟熬喟班眮
presentation_mode_label=喟睄喟班唉喟班睄喟多皑喟� 喟班眬喟む翱
open_file.title=喟眻喟侧睄 喟む眴喟班眮喟掂眮
open_file_label=喟む眴喟班眮喟掂眮
print.title=喟眮喟︵睄喟班翱喟傕皻啾�
print_label=喟眮喟︵睄喟班翱喟傕皻啾�
download.title=喟∴睂喟ㄠ眮喟侧眿喟∴眮
download_label=喟∴睂喟ㄠ眮喟侧眿喟∴眮
bookmark.title=喟睄喟班案啾嵿挨啾佮挨 喟︵鞍啾嵿岸喟ㄠ皞 (喟曕熬喟眬 喟氞眹喟翱 喟侧眹喟︵熬 喟曕眾喟む睄喟� 喟掂翱喟傕啊啾嬥安啾� 喟む眴喟班眮喟掂眮)
bookmark_label=喟睄喟班案啾嵿挨啾佮挨 喟︵鞍啾嵿岸喟ㄠ皞

# Secondary toolbar and context menu
tools.title=喟皑喟苦爱啾佮盁啾嵿安啾�
tools_label=喟皑喟苦爱啾佮盁啾嵿安啾�
first_page.title=喟眾喟︵盁喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
first_page.label=喟眾喟︵盁喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
first_page_label=喟眾喟︵盁喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
last_page.title=喟氞翱喟掂鞍喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
last_page.label=喟氞翱喟掂鞍喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
last_page_label=喟氞翱喟掂鞍喟� 喟眹喟溹眬喟曕翱 喟掂眴喟赤睄喟赤眮
page_rotate_cw.title=喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮
page_rotate_cw.label=喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮
page_rotate_cw_label=喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮
page_rotate_ccw.title=喟呧蔼喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮
page_rotate_ccw.label=喟呧蔼喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮
page_rotate_ccw_label=喟呧蔼喟膏暗啾嵿隘喟︵翱喟多安啾� 喟む翱喟睄喟眮

hand_tool_enable.title=喟氞眹喟む翱 喟膏熬喟о皑喟� 喟氞眹喟む皑喟苦皞喟氞眮
hand_tool_enable_label=喟氞眹喟む翱 喟膏熬喟о皑喟� 喟氞眹喟む皑喟苦皞喟氞眮
hand_tool_disable.title=喟氞眹喟む翱 喟膏熬喟о皑喟� 喟呧皻啾囙挨喟ㄠ翱喟傕皻啾�
hand_tool_disable_label=喟氞眹喟む翱 喟膏熬喟о皑喟� 喟呧皻啾囙挨喟ㄠ翱喟傕皻啾�

# Document properties dialog box
document_properties.title=喟挨啾嵿鞍喟眮 喟侧皶啾嵿胺喟｀熬喟侧眮...
document_properties_label=喟挨啾嵿鞍喟眮 喟侧皶啾嵿胺喟｀熬喟侧眮...
document_properties_file_name=喟︵案啾嵿挨啾嵿鞍喟� 喟眹喟班眮:
document_properties_file_size=喟︵案啾嵿挨啾嵿鞍喟� 喟鞍喟苦爱喟距埃喟�:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=喟多眬喟班睄喟粪翱喟�:
document_properties_author=喟眰喟侧皶喟班睄喟�:
document_properties_subject=喟掂翱喟粪隘喟�:
document_properties_keywords=喟曕眬 喟唉喟距安啾�:
document_properties_creation_date=喟膏眱喟粪睄喟熰翱喟傕皻喟苦皑 喟む眹喟︵眬:
document_properties_modification_date=喟膏暗喟班翱喟傕皻喟苦皑 喟む眹喟︵眬:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=喟膏眱喟粪睄喟熰翱喟曕鞍啾嵿挨:
document_properties_producer=PDF 喟夃挨啾嵿蔼喟距唉喟曕翱:
document_properties_version=PDF 喟掂鞍啾嵿胺喟ㄠ睄:
document_properties_page_count=喟眹喟溹眬喟� 喟膏皞喟栢睄喟�:
document_properties_close=喟眰喟膏翱喟掂眹喟翱

print_progress_message=喟眮喟︵睄喟班翱喟傕皻喟∴熬喟ㄠ翱喟曕翱 喟挨啾嵿鞍喟眮 喟膏翱喟︵睄喟о爱喟掂眮喟む眮喟ㄠ睄喟ㄠ唉喟库��
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=喟班唉啾嵿唉啾佮皻啾囙隘喟�

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=喟皶啾嵿皶喟盁啾嵿盁啾� 喟熬喟班睄喟氞眮
toggle_sidebar_label=喟皶啾嵿皶喟盁啾嵿盁啾� 喟熬喟班睄喟氞眮
document_outline.title=喟挨啾嵿鞍喟眮 喟班眰喟爱啾� 喟氞眰喟翱喟傕皻啾� (喟∴艾啾佮安啾� 喟曕睄喟侧翱喟曕睄 喟氞眹喟膏翱 喟呧皑啾嵿皑喟� 喟呧皞喟多熬喟侧皑啾� 喟掂翱喟膏睄喟む鞍喟苦皞喟氞眮/喟曕眰喟侧睄喟氞眮)
document_outline_label=喟挨啾嵿鞍喟眮 喟呧暗啾佮盁啾嶁�屶安啾堗皑啾�
attachments.title=喟呧皑啾佮艾喟傕哀喟距安啾� 喟氞眰喟眮
attachments_label=喟呧皑啾佮艾喟傕哀喟距安啾�
thumbs.title=喟ム皞喟睄鈥屶皑啾堗安啾嵿案啾� 喟氞眰喟眮
thumbs_label=喟ム皞喟睄鈥屶皑啾堗安啾嵿案啾�
findbar.title=喟挨啾嵿鞍喟眮喟侧眿 喟曕皑啾佮皸啾娻皑啾佮爱啾�
findbar_label=喟曕皑啾佮皸啾娻皑啾�

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=喟眹喟溹眬 {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=喟眹喟溹眬 {{page}} 喟眾喟曕睄喟� 喟ム皞喟睄鈥屶皑啾堗安啾�

# Find panel button title and messages
find_label=喟曕皑啾佮皸啾娻皑啾�:
find_previous.title=喟唉喟� 喟眾喟曕睄喟� 喟眮喟傕唉啾� 喟膏皞喟暗喟距皑啾嵿皑喟� 喟曕皑啾佮皸啾娻皑啾�
find_previous_label=喟眮喟ㄠ眮喟盁喟�
find_next.title=喟唉喟� 喟眾喟曕睄喟� 喟む鞍啾嵿暗喟距挨喟� 喟膏皞喟暗喟距皑啾嵿皑喟� 喟曕皑啾佮皸啾娻皑啾�
find_next_label=喟む鞍啾佮暗喟距挨
find_highlight=喟呧皑啾嵿皑喟苦盁喟苦皑喟� 喟夃唉啾嵿唉啾�喟皑喟� 喟氞眹喟眮喟眮
find_match_case_label=喟呧皶啾嵿胺喟班爱啾佮安 喟む眹喟∴熬喟む眿 喟眿喟侧睄喟氞眮
find_reached_top=喟眹喟溹眬 喟眻喟曕翱 喟氞眹喟班眮喟曕眮喟ㄠ睄喟ㄠ唉喟�, 喟曕睄喟班翱喟傕唉喟� 喟ㄠ眮喟傕啊喟� 喟曕眾喟ㄠ案喟距皸喟苦皞喟氞皞喟∴翱
find_reached_bottom=喟眹喟溹眬 喟氞翱喟掂鞍喟曕眮 喟氞眹喟班眮喟曕眮喟ㄠ睄喟ㄠ唉喟�, 喟眻喟ㄠ眮喟傕啊喟� 喟曕眾喟ㄠ案喟距皸喟苦皞喟氞皞喟∴翱
find_not_found=喟唉喟� 喟曕皑喟啊喟侧眹喟︵眮

# Error panel labels
error_more_info=喟鞍喟苦皞喟� 喟膏爱喟距皻喟距鞍喟�
error_less_info=喟む皶啾嵿皶啾佮暗 喟膏爱喟距皻喟距鞍喟�
error_close=喟眰喟膏翱喟掂眹喟翱
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=喟膏皞喟︵眹喟多皞: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=喟膏睄喟熰熬喟曕睄: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=喟眻喟侧眮: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=喟掂鞍啾佮案: {{line}}
rendering_error=喟眹喟溹眬喟ㄠ眮 喟班眴喟傕啊喟班睄 喟氞眹喟眮喟熰安啾� 喟掄皶 喟︵眿喟粪皞 喟庎唉啾佮鞍啾堗皞喟︵翱.

# Predefined zoom values
page_scale_width=喟眹喟溹眬 喟掂眴喟∴安啾嵿蔼啾�
page_scale_fit=喟眹喟溹眬 喟呧爱喟班睄喟眮
page_scale_auto=喟膏睄喟掂隘喟傕皻喟距安喟� 喟溹眰喟睄
page_scale_actual=喟哎喟距鞍啾嵿哀 喟鞍喟苦爱喟距埃喟�
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=喟︵眿喟粪皞
loading_error=PDF 喟侧眿喟∴暗啾佮皻啾佮皑啾嵿皑喟睄喟眮喟∴眮 喟掄皶 喟︵眿喟粪皞 喟庎唉啾佮鞍啾堗皞喟︵翱.
invalid_file_error=喟氞眴喟侧睄喟侧皑喟� 喟侧眹喟︵熬 喟熬喟∴眻喟� PDF 喟眻喟侧眮.
missing_file_error=喟︵眾喟班皶喟ㄠ翱 PDF 喟眻喟侧眮.
unexpected_response_error=喟呧皑啾佮皶啾嬥皑喟� 喟膏鞍啾嵿暗喟班睄 喟膏睄喟皞喟︵皑.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 喟熰眬喟曕熬]
password_label=喟� PDF 喟眻喟侧睄 喟む眴喟班眮喟氞眮喟熰皶啾� 喟膏皞喟曕眹喟む蔼喟︵皞 喟睄喟班暗啾囙岸喟眴喟熰睄喟熰眮喟眮.
password_invalid=喟膏皞喟曕眹喟む蔼喟︵皞 喟氞眴喟侧睄喟侧唉啾�. 喟︵隘喟氞眹喟膏翱 喟俺啾嵿俺啾� 喟睄喟班隘喟む睄喟ㄠ翱喟傕皻喟傕啊喟�.
password_ok=喟膏鞍啾�
password_cancel=喟班唉啾嵿唉啾佮皻啾囙隘喟�

printing_not_supported=喟灌眴喟氞睄喟氞鞍喟苦皶: 喟� 喟掂翱喟灌熬喟班翱喟｀翱 喟氞眹喟� 喟眮喟︵睄喟班埃 喟眰喟班睄喟む翱喟椸熬 喟む眿喟∴睄喟熬喟熰眮 喟侧眹喟︵眮.
printing_not_ready=喟灌眴喟氞睄喟氞鞍喟苦皶: 喟眮喟︵睄喟班埃 喟曕眾喟班皶啾� 喟� PDF 喟眰喟班睄喟む翱喟椸熬 喟侧眿喟∴暗喟侧眹喟︵眮.
web_fonts_disabled=喟掂眴喟睄 喟熬喟傕盁啾嵿安啾� 喟呧皻啾囙挨喟ㄠ翱喟傕皻喟啊啾嗋皑啾�: 喟庎皞喟眴喟∴眴喟∴睄 PDF 喟熬喟傕盁啾嵿安啾� 喟夃蔼喟眿喟椸翱喟傕皻喟侧眹喟� 喟眿喟翱喟傕唉喟�.
document_colors_not_allowed=PDF 喟挨啾嵿鞍喟距安啾� 喟掂熬喟熰翱 喟膏睄喟掂皞喟� 喟班皞喟椸眮喟侧皑啾� 喟夃蔼喟眿喟椸翱喟傕皻啾佮皶啾娻皑啾佮盁喟曕眮 喟呧皑啾佮爱喟む翱喟傕皻喟啊喟掂眮: 喟掂翱喟灌鞍喟｀翱 喟ㄠ皞喟︵眮 鈥溹蔼啾囙皽啾�喟侧皑啾� 喟掂熬喟熰翱 喟膏睄喟掂皞喟� 喟班皞喟椸眮喟侧皑啾� 喟庎皞喟氞眮喟曕眾喟ㄠ眮喟熰皶啾� 喟呧皑啾佮爱喟む翱喟傕皻啾佲�� 喟呧皻啾囙挨喟ㄠ皞 喟氞眹喟艾喟∴翱喟掂眮喟傕唉喟�.
