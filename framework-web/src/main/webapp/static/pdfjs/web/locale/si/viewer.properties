# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=喽膏窊喽� 喽脆窓喽� 喽脆窉喽о窋喾�
previous_label=喽脆窓喽�
next.title=喽膏窊喾呧稛 喽脆窉喽о窋喾�
next_label=喽膏窊喾呧稛

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=喽脆窉喽о窋喾�
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=喽氞窋喽┼窂 喽氞痘喽编穵喽�
zoom_out_label=喽氞窋喽┼窂 喽氞痘喽编穵喽�
zoom_in.title=喾�喾掄穪喾忇督 喽氞痘喽编穵喽�
zoom_in_label=喾�喾掄穪喾忇督 喽氞痘喽编穵喽�
zoom.title=喾�喾掄穪喾忇督喽逗
presentation_mode.title=喽夃动喾掄痘喾掄洞喽穵喽氞窉喽秽窊喽膏穵 喽脆穵鈥嵿痘喽氞窂喽秽逗 喾�喾權董 喽膏窂喽秽窋喾�喽编穵喽�
presentation_mode_label=喽夃动喾掄痘喾掄洞喽穵喽氞窉喽秽窊喽膏穵 喽脆穵鈥嵿痘喽氞窂喽秽逗
open_file.title=喽溹窚喽编窋喾� 喾�喾掄穩喾樴董 喽氞痘喽编穵喽�
open_file_label=喾�喾掄穩喾樴董 喽氞痘喽编穵喽�
print.title=喽膏窋喽穵鈥嵿痘喽逗
print_label=喽膏窋喽穵鈥嵿痘喽逗
download.title=喽多窂喽溹侗喾娻侗
download_label=喽多窂喽溹侗喾娻侗
bookmark.title=喽窅喽编锭 喽囙董喾� 喽穬喾斷侗 (喽脆窉喽о洞喽穵 喽氞痘喽编穵喽� 喾勦窛 喽编穩 喽氞穩喾斷穮喾斷穩喽� 喾�喾掄穩喾樴董 喽氞痘喽编穵喽�)
bookmark_label=喽窅喽编锭 喽囙董喾� 喽穬喾斷侗

# Secondary toolbar and context menu
tools.title=喽膏窓喾�喽洁陡喾�
tools_label=喽膏窓喾�喽洁陡喾�
first_page.title=喽膏窋喽洁穵 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
first_page.label=喽膏窋喽洁穵 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
first_page_label=喽膏窋喽洁穵 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
last_page.title=喽呧穩喾冟侗喾� 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
last_page.label=喽呧穩喾冟侗喾� 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
last_page_label=喽呧穩喾冟侗喾� 喽脆窉喽о窋喾�喽� 喽亨侗喾娻侗
page_rotate_cw.title=喽稓喾娻穪喾掄东喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�
page_rotate_cw.label=喽稓喾娻穪喾掄东喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�
page_rotate_cw_label=喽稓喾娻穪喾掄东喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�
page_rotate_ccw.title=喾�喾忇陡喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�
page_rotate_ccw.label=喾�喾忇陡喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�
page_rotate_ccw_label=喾�喾忇陡喾忇穩喽秽穵喽穩 喽粪穵鈥嵿痘喽膏东喽�

hand_tool_enable.title=喾勦穬喾娻董 喽膏窓喾�喽洁陡 喾冟稓喾娾�嵿痘喾撪逗
hand_tool_enable_label=喾勦穬喾娻董 喽膏窓喾�喽洁陡 喾冟稓喾娾�嵿痘喾撪逗
hand_tool_disable.title=喾勦穬喾娻董 喽膏窓喾�喽洁陡 喽呧稓喾娾�嵿痘喾撪逗
hand_tool_disable_label=喾勦穬喾娻董 喽膏窓喾�喽洁陡 喽呧稓喾娾�嵿痘喾撪逗

# Document properties dialog box
document_properties.title=喽洁窔喽涏侗 喾�喽穵喽氞陡喾�...
document_properties_label=喽洁窔喽涏侗 喾�喽穵喽氞陡喾�...
document_properties_file_name=喽溹窚喽编窋 喽编陡:
document_properties_file_size=喽溹窚喽编窋 喽脆穵鈥嵿痘喽膏窂喽逗:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} 喽多逗喾掄锭)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} 喽多逗喾掄锭)
document_properties_title=喾冟窉喽秽穬喾娻董喽洁逗:
document_properties_author=喽氞董喾�
document_properties_subject=喽膏窂喽窐喽氞窂喾�:
document_properties_keywords=喽亨董喾斷痘喾� 喾�喽侗喾�:
document_properties_creation_date=喽编窉喽秽穵喽膏窉喽� 喽窉喽编逗:
document_properties_modification_date=喾�喾權侗喾冟穵喽氞督 喽窉喽编逗:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=喽编窉喽秽穵喽膏窂喽脆稓:
document_properties_producer=PDF 喽编窉喾佮穵喽脆窂喽稓:
document_properties_version=PDF 喽编窉喽氞窋喽窋喾�:
document_properties_page_count=喽脆窉喽о窋 喽溹东喽�:
document_properties_close=喾�喾冟侗喾娻侗

print_progress_message=喽洁窔喽涏侗喽� 喽膏窋喽穵鈥嵿痘喽逗 喾冟冻喾勦窂 喾冟窎喽窂喽编陡喾� 喽氞痘喽膏窉喽编穵鈥�
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_close=喽呧穩喽洁秱喽溹窋 喽氞痘喽编穵喽�

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=喽脆窅喽窉 喽窊喽秽窋喾�喽� 喽膏窂喽秽窋喾�喽编穵喽�
toggle_sidebar_label=喽脆窅喽窉 喽窊喽秽窋喾�喽� 喽膏窂喽秽窋喾�喽编穵喽�
attachments.title=喽囙陡喾掄东喾斷陡喾� 喽脆窓喽编穵喾�喽编穵喽�
attachments_label=喽囙陡喾掄东喾斷陡喾�
thumbs.title=喾冟窉喽熰窉喽窉 喽秽窎 喽脆窓喽编穵喾�喽编穵喽�
thumbs_label=喾冟窉喽熰窉喽窉 喽秽窎
findbar.title=喽洁窔喽涏侗喽� 喽窋喾� 喾冟窚喽亨侗喾娻侗
findbar_label=喾冟窚喽亨侗喾娻侗

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=喽脆窉喽о窋喾� {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=喽脆窉喽о窋喾�喾� 喾冟窉喽熰窉喽� 喽秽窎喾� {{page}}

# Find panel button title and messages
find_label=喾冟窚喽亨侗喾娻侗:
find_previous.title=喽膏窔 喾�喾忇稓喾娾�嵿逗 喽涏东喾娻订喽� 喽膏窊喽� 喽脆窓喽� 喽亨窓喽窋喽窋 喾冟穵喽窂喽编逗 喾冟窚喽亨侗喾娻侗
find_previous_label=喽脆窓喽�:
find_next.title=喽膏窔 喾�喾忇稓喾娾�嵿逗 喽涏东喾娻订喽� 喽膏窊喾呧稛喽� 喽亨窓喽窓喽� 喾冟穵喽窂喽编逗 喾冟窚喽亨侗喾娻侗
find_next_label=喽膏窊喾呧稛
find_highlight=喾冟窉喽亨督喾娻督 喽嬥动喾娻动喾撪洞喽编逗
find_match_case_label=喽呧稓喾斷痘喾� 喽溹穮喽脆侗喾娻侗
find_reached_top=喽脆窉喽о窋喾�喾� 喽夃穭喾� 喽氞窓喾呧穩喽秽锭 喽洁稖喾忇穩喾掄逗, 喽脆穭喾� 喾冟窉喽� 喽夃动喾掄痘喾掄逗喽� 喽亨陡喾掄侗喾�
find_reached_bottom=喽脆窉喽о窋喾�喾� 喽脆穭喾� 喽氞窓喾呧穩喽秽锭 喽洁稖喾忇穩喾掄逗, 喽夃穭喾� 喾冟窉喽� 喽夃动喾掄痘喾掄逗喽� 喽亨陡喾掄侗喾�
find_not_found=喽斷抖 喾冟窓喾�喾� 喾�喽犩侗 喾勦陡喾� 喽编窚喾�喾撪逗

# Error panel labels
error_more_info=喽多窚喾勦窛 喽窚喽秽董喾斷痘喾�
error_less_info=喽呧穩喽� 喽窚喽秽董喾斷痘喾�
error_close=喾�喾冟侗喾娻侗
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (喽编窉喽氞窋喽窋喾�: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=喽脆东喾掄穩喾掄订喽�: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=喽溹窚喽编窋喾�: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=喽脆窔喾呧窉喽�: {{line}}
rendering_error=喽脆窉喽о窋喾� 喽秽窓喽编穵喽┼痘喾� 喾�喾掄陡喾氞动喾� 喽溹窅喽о督喾斷穩喽氞穵 喾勦锭 喽溹窅喽编窋喽窉.

# Predefined zoom values
page_scale_width=喽脆窉喽о窋喾�喾� 喽脆穮喽�
page_scale_fit=喽脆窉喽о窋喾�喽� 喾冟窋喽窋喾冟窋 喽洁窓喾�
page_scale_auto=喾冟穵喾�喽亨秱喽氞穵鈥嵿痘喾撪逗 喾�喾掄穪喾忇督喽逗
page_scale_actual=喽编窉喽亨陡喾掄董 喽脆穵鈥嵿痘喽膏窂喽逗
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=喽窛喾傕逗
loading_error=PDF 喽脆窎喽秽东喽� 喾�喾掄陡喾氞动喾� 喽窛喾傕逗喽氞穵 喾勦锭 喽溹窅喽编窋喽窉.
invalid_file_error=喽窎喾佮窉喽� 喾勦窛 喾冟窂喾�喽穵鈥嵿逗 PDF 喽溹窚喽编窋喾�.
missing_file_error=喽编窅喽窉喾�喾� PDF 喽溹窚喽编窋喾�.
unexpected_response_error=喽多督喾忇洞喾溹痘喾溹董喾娻董喾� 喽编窚喾�喾� 喾冟窔喾�喾忇动喾忇逗喽� 喽脆穵鈥嵿痘喽窉喽犩窂喽秽逗.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 鈥� Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} 喾�喾掄穬喾娻董喽秽逗]
password_label=喽膏窓喽� PDF 喽溹窚喽编窋喾� 喾�喾掄穩喾樴董 喽氞窉喽秽窊喽膏锭 喽膏窋喽秽洞喽逗 喽囙董喾斷穮喽穵 喽氞痘喽编穵喽�.
password_invalid=喾�喾愢痘喽窉 喽膏窋喽秽洞喽逗喽氞穵. 喽氞痘喾斷东喾忇稓喽� 喽编窅喾�喽� 喽嬥董喾娻穬喾� 喽氞痘喽编穵喽�.
password_ok=喾勦痘喾�
password_cancel=喽戉洞喾�

printing_not_supported=喽呧穩喾�喾忇动喽亨逗喾�: 喽膏窓喽� 喽溹穩喾氞穪喽氞逗 喽膏窋喽穵鈥嵿痘喽逗 喾冟冻喾勦窂 喾冟陡喾娻洞喾栢痘喾娻东喽亨窓喽编穵 喾冟穭喽� 喽编窚喽稓喾娻穩喽亨窉.
printing_not_ready=喽呧穩喾�喾忇动喽亨逗喾�: 喽膏窋喽穵鈥嵿痘喽逗 喾冟冻喾勦窂 PDF 喾冟陡喾娻洞喾栢痘喾娻东喽亨窓喽编穵 喽脆窎喽秽穵喽逗 喾�喾� 喽编窚喽膏窅喽�.
web_fonts_disabled=喽⑧窂喽� 喽呧稓喾斷痘喾� 喽呧稓喾娾�嵿痘喾撪逗喽亨窉: 喽窉喾呧窅喽洁窉 PDF 喽呧稓喾斷痘喾� 喽粪窂喾�喾掄董 喽氞穮 喽编窚喾勦窅喽�.
