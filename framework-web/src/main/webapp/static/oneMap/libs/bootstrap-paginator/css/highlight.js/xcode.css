/*

XCode style (c) <PERSON> <angel<PERSON><EMAIL>>

*/

pre code {
  display: block; padding: 0.5em;
  background: #fff; color: black;
}

pre .comment,
pre .template_comment,
pre .javadoc,
pre .comment * {
  color: rgb(0,106,0);
}

pre .keyword,
pre .literal,
pre .nginx .title {
  color: rgb(170,13,145);
}
pre .method,
pre .list .title,
pre .tag .title,
pre .setting .value,
pre .winutils,
pre .tex .command,
pre .http .title,
pre .request,
pre .status {
  color: #008;
}

pre .envvar,
pre .tex .special {
  color: #660;
}

pre .string {
  color: rgb(196,26,22);
}
pre .tag .value,
pre .cdata,
pre .filter .argument,
pre .attr_selector,
pre .apache .cbracket,
pre .date,
pre .regexp {
  color: #080;
}

pre .sub .identifier,
pre .pi,
pre .tag,
pre .tag .keyword,
pre .decorator,
pre .ini .title,
pre .shebang,
pre .prompt,
pre .hexcolor,
pre .rules .value,
pre .css .value .number,
pre .symbol,
pre .symbol .string,
pre .number,
pre .css .function,
pre .clojure .title,
pre .clojure .built_in {
  color: rgb(28,0,207);
}

pre .class .title,
pre .haskell .type,
pre .smalltalk .class,
pre .javadoctag,
pre .yardoctag,
pre .phpdoc,
pre .typename,
pre .tag .attribute,
pre .doctype,
pre .class .id,
pre .built_in,
pre .setting,
pre .params,
pre .clojure .attribute {
  color: rgb(92,38,153);
}

pre .variable {
 color: rgb(63,110,116);
}
pre .css .tag,
pre .rules .property,
pre .pseudo,
pre .subst {
  color: #000;
}

pre .css .class, pre .css .id {
  color: #9B703F;
}

pre .value .important {
  color: #ff7700;
  font-weight: bold;
}

pre .rules .keyword {
  color: #C5AF75;
}

pre .annotation,
pre .apache .sqbracket,
pre .nginx .built_in {
  color: #9B859D;
}

pre .preprocessor,
pre .preprocessor * {
  color: rgb(100,56,32);
}

pre .tex .formula {
  background-color: #EEE;
  font-style: italic;
}

pre .diff .header,
pre .chunk {
  color: #808080;
  font-weight: bold;
}

pre .diff .change {
  background-color: #BCCFF9;
}

pre .addition {
  background-color: #BAEEBA;
}

pre .deletion {
  background-color: #FFC8BD;
}

pre .comment .yardoctag {
  font-weight: bold;
}

pre .method .id {
  color: #000;
}
