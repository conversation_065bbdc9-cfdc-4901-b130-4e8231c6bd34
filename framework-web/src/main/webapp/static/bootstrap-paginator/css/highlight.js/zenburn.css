/*

Zenburn style from voldmar.ru (c) <PERSON> <<EMAIL>>
based on dark.css by <PERSON>

*/

pre code {
  display: block; padding: 0.5em;
  background: #3F3F3F;
  color: #DCDCDC;
}

pre .keyword,
pre .tag,
pre .css .class,
pre .css .id,
pre .lisp .title,
pre .nginx .title,
pre .request,
pre .status,
pre .clojure .attribute {
  color: #E3CEAB;
}

pre .django .template_tag,
pre .django .variable,
pre .django .filter .argument {
  color: #DCDCDC;
}

pre .number,
pre .date {
  color: #8CD0D3;
}

pre .dos .envvar,
pre .dos .stream,
pre .variable,
pre .apache .sqbracket {
  color: #EFDCBC;
}

pre .dos .flow,
pre .diff .change,
pre .python .exception,
pre .python .built_in,
pre .literal,
pre .tex .special {
  color: #EFEFAF;
}

pre .diff .chunk,
pre .subst {
  color: #8F8F8F;
}

pre .dos .keyword,
pre .python .decorator,
pre .title,
pre .haskell .type,
pre .diff .header,
pre .ruby .class .parent,
pre .apache .tag,
pre .nginx .built_in,
pre .tex .command,
pre .prompt {
    color: #efef8f;
}

pre .dos .winutils,
pre .ruby .symbol,
pre .ruby .symbol .string,
pre .ruby .string {
  color: #DCA3A3;
}

pre .diff .deletion,
pre .string,
pre .tag .value,
pre .preprocessor,
pre .built_in,
pre .sql .aggregate,
pre .javadoc,
pre .smalltalk .class,
pre .smalltalk .localvars,
pre .smalltalk .array,
pre .css .rules .value,
pre .attr_selector,
pre .pseudo,
pre .apache .cbracket,
pre .tex .formula {
  color: #CC9393;
}

pre .shebang,
pre .diff .addition,
pre .comment,
pre .java .annotation,
pre .template_comment,
pre .pi,
pre .doctype {
  color: #7F9F7F;
}

pre .coffeescript .javascript,
pre .javascript .xml,
pre .tex .formula,
pre .xml .javascript,
pre .xml .vbscript,
pre .xml .css,
pre .xml .cdata {
  opacity: 0.5;
}

