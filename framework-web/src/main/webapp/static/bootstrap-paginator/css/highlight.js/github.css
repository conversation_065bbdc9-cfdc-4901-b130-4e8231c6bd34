/*

github.com style (c) <PERSON><PERSON> <<EMAIL>>

*/

pre code {
  display: block; padding: 0.5em;
  color: #333;
  background: #f8f8ff
}

pre .comment,
pre .template_comment,
pre .diff .header,
pre .javadoc {
  color: #998;
  font-style: italic
}

pre .keyword,
pre .css .rule .keyword,
pre .winutils,
pre .javascript .title,
pre .nginx .title,
pre .subst,
pre .request,
pre .status {
  color: #333;
  font-weight: bold
}

pre .number,
pre .hexcolor,
pre .ruby .constant {
  color: #099;
}

pre .string,
pre .tag .value,
pre .phpdoc,
pre .tex .formula {
  color: #d14
}

pre .title,
pre .id {
  color: #900;
  font-weight: bold
}

pre .javascript .title,
pre .lisp .title,
pre .clojure .title,
pre .subst {
  font-weight: normal
}

pre .class .title,
pre .haskell .type,
pre .vhdl .literal,
pre .tex .command {
  color: #458;
  font-weight: bold
}

pre .tag,
pre .tag .title,
pre .rules .property,
pre .django .tag .keyword {
  color: #000080;
  font-weight: normal
}

pre .attribute,
pre .variable,
pre .lisp .body {
  color: #008080
}

pre .regexp {
  color: #009926
}

pre .class {
  color: #458;
  font-weight: bold
}

pre .symbol,
pre .ruby .symbol .string,
pre .lisp .keyword,
pre .tex .special,
pre .prompt {
  color: #990073
}

pre .built_in,
pre .lisp .title,
pre .clojure .built_in {
  color: #0086b3
}

pre .preprocessor,
pre .pi,
pre .doctype,
pre .shebang,
pre .cdata {
  color: #999;
  font-weight: bold
}

pre .deletion {
  background: #fdd
}

pre .addition {
  background: #dfd
}

pre .diff .change {
  background: #0086b3
}

pre .chunk {
  color: #aaa
}
