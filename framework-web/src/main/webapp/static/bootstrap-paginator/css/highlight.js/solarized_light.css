/*

Orginal Style from ethanschoonover.com/solarized (c) <PERSON> <<EMAIL>>

*/

pre code {
  display: block; padding: 0.5em;
  background: #fdf6e3; color: #657b83;
}

pre .comment,
pre .template_comment,
pre .diff .header,
pre .doctype,
pre .pi,
pre .lisp .string,
pre .javadoc {
  color: #93a1a1;
  font-style: italic;
}

pre .keyword,
pre .winutils,
pre .method,
pre .addition,
pre .css .tag,
pre .request,
pre .status,
pre .nginx .title {
  color: #859900;
}

pre .number,
pre .command,
pre .string,
pre .tag .value,
pre .phpdoc,
pre .tex .formula,
pre .regexp,
pre .hexcolor {
  color: #2aa198;
}

pre .title,
pre .localvars,
pre .chunk,
pre .decorator,
pre .built_in,
pre .identifier,
pre .vhdl .literal,
pre .id {
  color: #268bd2;
}

pre .attribute,
pre .variable,
pre .lisp .body,
pre .smalltalk .number,
pre .constant,
pre .class .title,
pre .parent,
pre .haskell .type {
  color: #b58900;
}

pre .preprocessor,
pre .preprocessor .keyword,
pre .shebang,
pre .symbol,
pre .symbol .string,
pre .diff .change,
pre .special,
pre .attr_selector,
pre .important,
pre .subst,
pre .cdata,
pre .clojure .title {
  color: #cb4b16;
}

pre .deletion {
  color: #dc322f;
}

pre .tex .formula {
  background: #eee8d5;
}
