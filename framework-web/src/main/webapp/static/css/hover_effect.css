body{background-color: #F1F1F1}
.demo{
    padding: 3em 0;
}
.box{
    position: relative;
    perspective: 1000px;
}
.box .box-img{
    width: 100%;
    height: 100%;
    transform: rotateY(90deg);
    transition: all 0.50s ease-in-out 0s;
}
.box:hover .box-img{
    transform: rotateY(0);
}
.box .box-back{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: 10% 20px;
    text-align: center;
    background: rgba(52,169,233,1.0);
	transform: rotateY(0);
    transition: all 0.50s ease-in-out 0s;
}
.box .box-back-g{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: 10% 20px;
    text-align: center;
    background: rgba(242,242,242,1.0);
	transform: rotateY(0);
    transition: all 0.50s ease-in-out 0s;
}
.box:hover .box-back{
    transform: rotateY(-90deg);
}
.box .box-content{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    padding: 10% 20px;
    text-align: center;
    background: rgba(35,183,229,0.5);
    transform: rotateY(90deg);
    transition: all 0.50s ease-in-out 0s;
}
.box:hover .box-content{
    transform: rotateY(0);
}
.box .title{
    font-size: 20px;
    text-transform: uppercase;
	color:#FFF;
}
.box .description{
    font-size: 16px;
    line-height: 24px;
	color:#FFF;
}
.box .title:after,
.box .description:after{
    content: "";
    width: 90%;
    display: block;
    border-bottom: 1px solid #fff;
    margin: 10px auto;
}
.box .title-g{
    font-size: 20px;
    text-transform: uppercase;
	color:#333;
}
.box .description-g{
    font-size: 16px;
    line-height: 24px;
	color:#333;
}
.box .title-g:after,
.box .description-g:after{
    content: "";
    width: 90%;
    display: block;
    border-bottom: 1px solid #333;
    margin: 10px auto;
}
.box .social-links{
    margin: 0;
    padding: 0;
    list-style: none;
}
.box .social-links li{
    display: inline-block;
    margin: 0 10px;
}
.box .social-links li a{
    font-size: 20px;
    color: #a6a6a6;
}
.box .social-links li a:hover{
    text-decoration: none;
    color: #fff;
}
@media only screen and (max-width: 990px) {
    .box{  margin-bottom:20px; }
}
@media only screen and (max-width: 479px) {
    .box .box-content{ padding: 20px; }
}