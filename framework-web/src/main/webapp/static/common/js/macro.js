var macroMgr = new MacroClass();

 


$(document).ready(function(){
	//设置内容窗口高度
	/*if(user_is_first_login == "1"){
		var alertHtml = "首次登录，请修改以下信息：<br/>";
		alertHtml += "1、为了安全请修改登录密码；<br/>";
		alertHtml += "2、完善个人信息；<br/>";
		business.openInfoAlert(alertHtml, function(){
			macroMgr.personalSetingClick();
		}, "提示");
	}*/
});
 

function MacroClass(){
	 
	
	//点击一级菜单调用
	this.onLevelOneMenuClick = function(menuId, menuFun,vdata){
		 
		if(menuFun){
			business.addMainContaierHtml(WEBPATH +"/"+ menuFun,vdata);
		} else {
			business.addMainContaierHtml(WEBPATH +"/sysuser_showMenuOne.do?menuId="+menuId,vdata);
		}
	};
	//点击二级菜单调用
	this.onLevelTwoMenuClick = function(menuId, menuFun,vdata){
		if(menuFun){
			if(menuId!=null&&menuId!=undefined){
				business.addMainContentParserHtml(WEBPATH +"/"+ menuFun+"?menuId="+menuId,vdata);
			}else{
				business.addMainContentParserHtml(WEBPATH +"/"+ menuFun,vdata);
			}
		} else {
			business.addMainContentParserHtml(null);
		}
	};
	//-----------------------------------------------------------------------------------------------
	 
	//-----------------------------------------------------------------------------------------------
	 
}